package model

import (
	"time"
)

type V3Allotstock struct {
	ID          uint       `gorm:"primaryKey;autoIncrement;column:id" json:"id"` // Changed to uint, added autoIncrement
	ModelID     int        `gorm:"column:model_id;comment:机型id" json:"model_id"`
	Model       string     `gorm:"column:model;size:32;index;comment:机型" json:"model"`
	Barcode     string     `gorm:"column:barcode;size:32;not null" json:"barcode"`
	Number      string     `gorm:"column:number;size:48;index" json:"number"`
	Imei        string     `gorm:"column:imei;size:16;index" json:"imei"`
	CustCodeOld string     `gorm:"column:cust_code_old;size:16;not null;index;comment:原客户编码" json:"cust_code_old"`
	CustNameOld string     `gorm:"column:cust_name_old;size:64;not null;comment:原客户名称" json:"cust_name_old"`
	CustCode    string     `gorm:"column:cust_code;size:16;not null;comment:新客户编码" json:"cust_code"`
	CustName    string     `gorm:"column:cust_name;size:64;not null;comment:新客户名称" json:"cust_name"`
	BillDate    *time.Time `gorm:"column:bill_date;type:date;not null;index;comment:调拨日期" json:"bill_date"`
	Status      *int8      `gorm:"column:status;size:4;not null;default:1;index;comment:状态：1正常，0已删除，2无效调拨（调出客户跟原发货客户一致）" json:"status"` // Use int8 for tinyint
	AddTime     *time.Time `gorm:"column:add_time;default:CURRENT_TIMESTAMP;comment:记录创建时间" json:"add_time"`
	UpdateTime  *time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP;comment:记录更新时间" json:"update_time"`
}

func (V3Allotstock) TableName() string {
	return "v3_allotstock"
}
