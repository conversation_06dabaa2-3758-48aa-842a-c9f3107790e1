package model

import (
	"time"
)

type EndpointMaterialSupport struct {
	ID               uint      `gorm:"column:id;primaryKey;autoIncrement"`
	ApplicationID    int       `gorm:"column:application_id;not null"`
	Name             string    `gorm:"column:name;size:255;not null"`
	Num              int       `gorm:"column:num;not null"`
	Pic              string    `gorm:"column:pic;size:255"`
	Price            float64   `gorm:"column:price;type:decimal(10,2);not null"`
	ProductionNumber string    `gorm:"column:production_number;size:50"`
	Thumbnail        string    `gorm:"column:thumbnail;size:255"`
	CreatedAt        time.Time `gorm:"column:created_at;autoCreateTime"`
	UpdatedAt        time.Time `gorm:"column:updated_at;autoUpdateTime"`
}

func (EndpointMaterialSupport) TableName() string {
	return "endpoint_material_support"
}
