package base

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type RegionService interface {
	GetRegionList(c *gin.Context, regionType string, parentId int) (any, error)
}

type regionService struct {
	db *gorm.DB
}

func NewRegionService(db *gorm.DB) RegionService {
	return &regionService{db: db}
}

// GetRegionList 获取地区列表
func (r *regionService) GetRegionList(c *gin.Context, regionType string, parentId int) (any, error) {
	type region struct {
		RegionId   int    `json:"region_id"`
		RegionName string `json:"region_name"`
		ParentId   int    `json:"parent_id"`
	}
	var list []region
	query := r.db.WithContext(c).Table("region").
		Select("region_id", "region_name", "parent_id")

	if parentId == 0 {
		//0：省级；1：市级；2：区（县）级；3：镇（乡）级
		query = query.Where("region_type = ?", regionType)
	} else {
		query = query.Where("parent_id =?", parentId)
	}

	err := query.Where("deleted_at is null").Find(&list).Error
	if err != nil {
		return nil, err
	}
	return list, nil
}
