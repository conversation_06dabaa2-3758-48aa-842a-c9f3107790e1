package model

import (
	"time"
)

type ActionSalesList struct {
	ID         int       `gorm:"column:id;primary_key;AUTO_INCREMENT;comment:主键ID"`
	ActionID   int       `gorm:"column:action_id;not null;comment:活动ID"`
	WarrantyID int       `gorm:"column:warranty_id;not null;comment:保卡ID"`
	BuyDate    time.Time `gorm:"column:buy_date;comment:购机日期"`
	CreatedAt  time.Time `gorm:"column:created_at;comment:创建时间"`
}

func (a *ActionSalesList) TableName() string {
	return "action_sales_list"
}
