package reimbursement

import (
	"fmt"
	"github.com/gin-gonic/gin"
	api "marketing/internal/api/reimbursement"
	"marketing/internal/dao"
	dao1 "marketing/internal/dao/reimbursement"
	"marketing/internal/model"
	"marketing/internal/pkg/errors"
	"time"
)

type BalanceService interface {
	//GetBalanceStandard 核销标准
	GetBalanceStandard(c *gin.Context, req *api.BalanceStandardReq) (*api.ReimbursementBalanceStandardResp, error)
	// GetBalanceList 获取结算列表
	GetBalanceList(c *gin.Context, req *api.BalanceListReq) (map[string]interface{}, error)
	ImportBalanceStandard(c *gin.Context, policyID int, data []api.BalanceImport) error
	ResetBalanceStandard(c *gin.Context, req *api.BalanceResetRep) error
}

type balanceService struct {
	repo              dao1.BalanceRepository
	policyRepo        dao1.PolicyRepository
	kingDeeAgencyRepo dao.KingDeeAgencyDao
}

func NewBalanceService(repo dao1.BalanceRepository, policyRepo dao1.PolicyRepository, kingDeeAgencyRepo dao.KingDeeAgencyDao) BalanceService {
	return &balanceService{
		repo:              repo,
		policyRepo:        policyRepo,
		kingDeeAgencyRepo: kingDeeAgencyRepo,
	}
}

// GetBalanceStandard 获取核销标准
func (a *balanceService) GetBalanceStandard(c *gin.Context, req *api.BalanceStandardReq) (*api.ReimbursementBalanceStandardResp, error) {
	//获取核销政策
	policyInfo, err := a.policyRepo.GetPolicyByID(req.PolicyID)
	if err != nil {
		return nil, err
	}
	data, err := a.repo.GetBalanceStandard(c, req.PolicyID, req.CompanyID, policyInfo.StandardType)
	if err != nil {
		return nil, err
	}
	result := api.ReimbursementBalanceStandardResp{
		Balance: data.Balance,
	}
	return &result, err
}

func (a *balanceService) GetBalanceList(c *gin.Context, req *api.BalanceListReq) (map[string]interface{}, error) {
	// 获取政策信息
	policyInfo, err := a.policyRepo.GetPolicyByID(req.PolicyID)
	if err != nil {
		return nil, err
	}
	if policyInfo == nil {
		return nil, errors.NewErr("未找到此政策")
	}

	// 获取结算列表数据
	data, _, err := a.repo.GetBalanceList(c, req)
	if err != nil {
		return nil, err
	}
	// 获取结算列表统计数据
	stat, err := a.repo.GetBalanceListStat(c, req)

	// 返回结果
	return map[string]interface{}{
		"reimbursement_balance_list":      data,
		"reimbursement_balance_list_stat": stat,
	}, nil
}

// ImportBalanceStandard 导入代理商核销剩余额度
func (a *balanceService) ImportBalanceStandard(c *gin.Context, policyID int, data []api.BalanceImport) error {
	now := time.Now()
	uid := c.GetUint("uid")
	// 读取政策信息
	policyInfo, err := a.policyRepo.GetPolicyByID(policyID)
	if err != nil {
		return errors.NewErr("获取政策信息失败: " + err.Error())
	}
	if policyInfo == nil {
		return errors.NewErr("未找到此政策")
	}

	// 数据有误的行号
	var errorRows []int

	// 遍历导入数据
	for i, item := range data {
		if item.Code == "" || item.Company == "" || item.Balance <= 0 {
			errorRows = append(errorRows, i+1)
			continue
		}
		agency, err := a.kingDeeAgencyRepo.GetKingDeeAgency(c, item.Code)
		if err != nil || agency.Level != 1 {
			errorRows = append(errorRows, i+1)
			continue
		}
		// 导入数据到数据库
		balanceStandard := &model.ReimbursementBalanceStandard{
			UID:          uid,
			PolicyID:     policyID,
			TopAgency:    agency.TopAgency,
			CompanyID:    agency.ID,
			Code:         item.Code,
			Company:      item.Company,
			Balance:      item.Balance,
			Month:        now,
			NormQuantity: item.Balance, // 标准数量默认为0
			StandardType: policyInfo.StandardType,
			Remark:       "标准导入",
			CreatedAt:    now,
			Type:         1,
		}
		err = a.repo.ImportBalanceStandard(c, balanceStandard)
		if err != nil {
			errorRows = append(errorRows, i+1)
		}
	}

	// 如果有错误行，返回错误信息
	if len(errorRows) > 0 {
		return errors.NewErr(fmt.Sprintf("导入过程中有错误行: %v", errorRows))
	}

	return nil

}

func (a *balanceService) ResetBalanceStandard(c *gin.Context, req *api.BalanceResetRep) error {
	uid := c.GetUint("uid")
	// 获取政策信息
	policyInfo, err := a.policyRepo.GetPolicyByID(req.PolicyID)
	if err != nil {
		return err
	}
	if policyInfo == nil {
		return errors.NewErr("未找到此政策")
	}
	if len(req.CompanyID) == 0 && req.ResetAll != 1 {
		return errors.NewErr("请选择要重置的公司")
	}
	if req.ResetAll == 1 {
		err := a.repo.ResetBalanceStandardAll(c, req.PolicyID, policyInfo.StandardType, uid)
		if err != nil {
			return err
		}
	} else {
		err := a.repo.ResetBalanceStandardByCompany(c, req.CompanyID, req.PolicyID, policyInfo.StandardType, uid)
		if err != nil {
			return err
		}
	}
	return nil
}
