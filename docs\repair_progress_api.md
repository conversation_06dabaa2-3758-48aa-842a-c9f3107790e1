# 寄修进度查询接口文档

## 接口描述
查询寄修进度信息，支持通过加密的手机号进行查询。接口使用MD5签名验证和AES加密保护参数安全。

## 请求信息

**请求URL:** `GET https://marketing-api-test.readboy.com/third_party/repair/query`

**请求方法:** `GET`

## 认证机制

### 1. AES加密
- 所有敏感参数（phone）需要使用AES-CBC模式加密
- 加密后使用Base64编码
- AES密钥长度：16字节跟MD5密钥相同

### 2. MD5签名验证
- 将所有查询参数（除MD5外）按字母顺序排列
- 格式：`phone=加密值`
- 拼接MD5密钥：`参数字符串 + md5key`
- 计算MD5哈希值作为签名

## 请求参数

| 参数名     | 必传 | 参数类型 | 描述                     |
|---------|----|----------|------------------------|
| appid   | Y  | string | 400-hollycrm   |
| phone   | Y  | string | AES加密后的手机号（Base64编码）   |
| keyword | Y  | string | 用户输入                   |
| MD5     | Y  | string | MD5签名，用于验证请求合法性        |

## 返回示例

### 成功响应
```json
{
    "title": "审核通过，等待用户发货",
    "date": "2025-02-07 16:45:04",
    "order_sn": "20250207164403318774",
    "ok": 1
}
```

### 失败响应
```json
{
    "msg": "请求失败: 网络错误",
    "ok": 0
}
```

## 返回参数说明

### 成功响应字段说明

| 参数名 | 类型 | 描述 |
|--------|------|------|
| title | string | 寄修状态标题 |
| date | string | 最新状态更新时间 |
| order_sn | string | 寄修订单号 |
| ok | int | 状态码，1表示成功 |

### 失败响应字段说明

| 参数名 | 类型 | 描述 |
|--------|------|------|
| msg | string | 错误消息 |
| ok | int | 状态码，0表示失败 |


## 错误码说明

- `ok: 1` - 查询成功
- `ok: 0` - 查询失败，具体原因见msg字段
