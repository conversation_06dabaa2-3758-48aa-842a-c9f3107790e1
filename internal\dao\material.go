package dao

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"marketing/internal/model"
	"marketing/internal/pkg/db"
	"marketing/internal/pkg/utils"
	"time"
)

type GetMaterialListParam struct {
	ProductionNumber string `json:"production_number"` // 产品编号
	Name             string `json:"name"`              // 物料名称
	Category         int    `json:"category" `         // 分类id
	IsPutAway        int    `json:"is_putaway"`        // 是否上架
	Categories       []int  `json:"categories"`
	PageNum          int    `json:"page_num"`  // 页码
	PageSize         int    `json:"page_size"` // 页幅
}

type GetRepairMaterialListParam struct {
	Key             string  `json:"key"`                //
	PriceStart      float64 `json:"price_start"`        //
	PriceEnd        float64 `json:"price_end"`          //
	WithPrice       int     `json:"with_price"`         //
	UpdateTimeStart string  ` json:"update_time_start"` //
	UpdateTimeEnd   string  `json:"update_time_end"`    //
	PageNum         int     `json:"page_num"`           // 页码
	PageSize        int     `json:"page_size"`          // 页幅
}

type MaterialDao interface {
	CreateMaterial(c *gin.Context, material *model.Material) error
	DeleteMaterial(c *gin.Context, id int) error
	UpdateMaterial(c *gin.Context, id int, uMap map[string]interface{}) error
	GetMaterialById(c *gin.Context, id int) *model.Material
	GetMaterialByProNum(c *gin.Context, productionNumber string) *model.Material
	GetMaterialList(c *gin.Context, req *GetMaterialListParam) ([]*model.Material, int64)
	CountMaterialNumGbyCategory(c *gin.Context, categoryIds ...int) (list []*model.MaterialCategoryNum)
	GetRepairMaterialList(c *gin.Context, param *GetRepairMaterialListParam) ([]*model.RepairMaterial, int64)
}

// MaterialDaoImpl 实现 MaterialDao 接口
type MaterialDaoImpl struct {
	db *gorm.DB
}

// NewMaterialDao 创建 MaterialDao 实例
func NewMaterialDao() MaterialDao {
	return &MaterialDaoImpl{
		db: db.GetDB(""),
	}
}

func (d *MaterialDaoImpl) CreateMaterial(c *gin.Context, material *model.Material) error {
	t := time.Now()
	material.CreatedAt = t
	material.UpdatedAt = t
	return d.db.WithContext(c).Create(material).Error
}

func (d *MaterialDaoImpl) DeleteMaterial(c *gin.Context, id int) error {
	return d.db.WithContext(c).Delete(&model.Material{}, "id = ?", id).Error
}

func (d *MaterialDaoImpl) UpdateMaterial(c *gin.Context, id int, uMap map[string]interface{}) error {
	uMap["updated_at"] = time.Now()
	return d.db.WithContext(c).Model(&model.Material{}).Where("id = ?", id).Updates(uMap).Error
}

func (d *MaterialDaoImpl) GetMaterialById(c *gin.Context, id int) *model.Material {
	var material model.Material
	err := d.db.WithContext(c).Model(&model.Material{}).Where("id = ?", id).First(&material).Error
	if err != nil {
		return nil
	}
	return &material
}

func (d *MaterialDaoImpl) GetMaterialByProNum(c *gin.Context, productionNumber string) *model.Material {
	var material model.Material
	err := d.db.WithContext(c).Model(&model.Material{}).Where("production_number = ?", productionNumber).First(&material).Error
	if err != nil {
		return nil
	}
	return &material
}

func (d *MaterialDaoImpl) GetMaterialList(c *gin.Context, req *GetMaterialListParam) ([]*model.Material, int64) {
	query := d.db.WithContext(c).Model(&model.Material{})

	if len(req.ProductionNumber) > 0 {
		query = query.Where("production_number = ?", req.ProductionNumber)
	}

	if len(req.Name) > 0 {
		query = query.Where("name like '%" + req.Name + "%'")
	}

	if req.Category > 0 {
		query = query.Where("category = ?", req.Category)
	}

	if req.IsPutAway >= 0 {
		query = query.Where("is_putaway = ?", req.IsPutAway)
	}
	if len(req.Categories) > 0 {
		query = query.Where("category in (?)", req.Categories)
	}

	data, total := utils.PaginateQueryV1(query.Order("created_at desc"), req.PageNum, req.PageSize, new([]*model.Material))
	for i, p := range *data {
		(*data)[i].CreateTime = utils.GetTimeStr(p.CreatedAt)
		(*data)[i].UpdateTime = utils.GetTimeStr(p.UpdatedAt)
	}

	return *data, total
}

func (d *MaterialDaoImpl) CountMaterialNumGbyCategory(c *gin.Context, categoryIds ...int) (list []*model.MaterialCategoryNum) {
	query := d.db.WithContext(c).Model(&model.Material{}).Select("category,count(*)num")

	if len(categoryIds) > 0 {
		query.Where("category in (?)", categoryIds)
	}

	query.Group("category").Find(&list)

	return
}

func (d *MaterialDaoImpl) GetRepairMaterialList(c *gin.Context, param *GetRepairMaterialListParam) ([]*model.RepairMaterial, int64) {
	query := db.GetDB("post_repair").WithContext(c).Model(&model.RepairMaterial{})

	if len(param.Key) > 0 {
		query = query.Where("code like '%" + param.Key + "%'" + " or name like '%" + param.Key + "%'" + " or specification like '%" + param.Key + "%'")
	}

	if param.WithPrice == 0 {
		query = query.Where("price_second is null")
	}

	if param.WithPrice == 1 {
		query = query.Where("price is not null")
	}

	if param.PriceStart > float64(0) {
		query = query.Where("price >= ?", param.PriceStart)
	}

	if param.PriceEnd > float64(0) {
		query = query.Where("price <= ?", param.PriceEnd)
	}

	if len(param.UpdateTimeStart) > 0 && len(param.UpdateTimeEnd) > 0 {
		query = query.Where("updated_at between ? and ?", param.UpdateTimeStart, param.UpdateTimeEnd)
	}

	data, total := utils.PaginateQueryV1(query, param.PageNum, param.PageSize, new([]*model.RepairMaterial))

	return *data, total
}
