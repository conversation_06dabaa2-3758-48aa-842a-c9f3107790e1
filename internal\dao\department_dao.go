package dao

import (
	"errors"
	"marketing/internal/pkg/db"
	"marketing/internal/pkg/utils"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type Department struct {
	ID        int64     `json:"id" gorm:"primaryKey"`
	Name      string    `json:"name" gorm:"column:name"`
	CreatedAt time.Time `json:"created_at" gorm:"created_at"` // CreatedAt 是分区的创建时间
	UpdatedAt time.Time `json:"updated_at" gorm:"updated_at"` // CreatedAt 是分区的创建时间
}

// DepartmentDAO 用于访问分区数据的对象
type DepartmentDAO struct {
	db *gorm.DB // 数据库连接实例
}

// NewDepartmentDAO 创建并返回一个新的 DepartmentDAO 实例
func NewDepartmentDAO() *DepartmentDAO {
	return &DepartmentDAO{db: db.GetDB("rbcare")} // 获取 "rbcare" 数据库连接
}

// GetDepartmentByID 根据 ID 获取部门信息
func (dao *DepartmentDAO) GetDepartmentByID(id int64) (*Department, error) {
	if id == 0 {
		// 如果 ID 是 0，返回带有默认名称 "未知" 的部门对象
		return &Department{Name: "未知"}, nil
	}

	var department Department
	result := dao.db.Table("department").First(&department, id)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			// 如果找不到记录，返回带有默认名称 "未知" 的部门对象
			return &Department{Name: "未知"}, nil
		}
		// 对于其他类型的错误，返回错误本身
		return nil, result.Error
	}

	return &department, nil
}

// GetDepartmentByID 根据 ID 获取部门信息
func (dao *DepartmentDAO) GetDepartmentNameByID(id int64) (string, error) {
	if id == 0 {
		// 如果 ID 是 0，返回带有默认名称 "未知" 的部门对象
		return "未知", nil
	}

	var department Department
	result := dao.db.Table("department").First(&department, id)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			// 如果找不到记录，返回带有默认名称 "未知" 的部门对象
			return "未知", nil
		}
		// 对于其他类型的错误，返回错误本身
		return "未知", result.Error
	}

	return department.Name, nil
}

// FindDepartmentss 查找符合条件的分区记录
func (dao *DepartmentDAO) FindDepartments(c *gin.Context, name string) ([]Department, int64, int, int, error) {
	query := dao.db.Table("department") // 设置数据库表为 "departments"
	if name != "" {                     // 如果提供了名称，则添加模糊查询条件
		query = query.Where("name LIKE ?", "%"+name+"%")
	}

	var departments []Department // 分区结果的切片
	// 使用 utils.PaginateQuery 对查询结果进行分页
	data, total, page, pageSize, err := utils.PaginateQuery(query, c, &departments)
	if err != nil { // 如果出现错误，返回错误信息
		return nil, 0, 0, 0, err
	}

	return *data, total, page, pageSize, nil // 返回查询的结果和分页信息
}
