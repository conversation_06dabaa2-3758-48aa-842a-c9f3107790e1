package prototype

import (
	"marketing/internal/api"
	"marketing/internal/pkg/types"
)

// UpCounterSearch 上柜机型查询
type UpCounterSearch struct {
	api.PaginationParams
	Model           string `json:"model" form:"model"`
	IsUpCounter     *int   `json:"is_up_counter" form:"is_up_counter"`
	MarketDateStart string `json:"market_date_start" form:"market_date_start"`
	MarketDateEnd   string `json:"market_date_end" form:"market_date_end"`
}

// UpCounterAdd 上柜机型新增
type UpCounterAdd struct {
	Model     string `json:"model" form:"model" binding:"required"`
	TopAgency int    `json:"top_agency" form:"top_agency" binding:"required"`
	Counter   int64  `json:"counter" form:"counter" binding:"required"`
}

// UpCounterUpdate 上柜机型更新
type UpCounterUpdate struct {
	ID      int   `json:"id" form:"id" binding:"required"`
	Counter int64 `json:"counter" form:"counter" binding:"required"`
}

// UpCounterStatusUpdate 更新样机上柜状态请求
type UpCounterStatusUpdate struct {
	ID          int     `json:"model_id" form:"model_id" binding:"required,min=1"`
	IsUpCounter *int    `json:"is_up_counter" form:"is_up_counter" binding:"required,oneof=0 1"`
	MarketDate  string  `json:"market_date" form:"market_date"`
	Price       float64 `json:"price" form:"price"`
}

type UpdateDiscontinuedReq struct {
	ID           int  `json:"model_id" form:"model_id" binding:"required,min=1"`
	Discontinued *int `json:"discontinued" form:"discontinued" binding:"required,oneof=0 1"`
}

// UpCounterListVo 上柜机型
type UpCounterListVo struct {
	//model.MachineTypeRelation
	ModelID          int              `json:"model_id"`           // 型号ID
	Name             string           `json:"name"`               // 型号
	CategoryID       int              `json:"category_id"`        // 分类ID
	Declare          int              `json:"declare"`            // 允许申报 0:不允许 1:允许
	Stock            int              `json:"stock"`              // 允许备货 0:不允许 1:允许
	Delist           int              `json:"delist"`             // 是否下架 0:不下架 1:下架
	DelistOnOffTime  types.CustomTime `json:"delist_on_off_time"` // 下架时间
	IsUpCounter      int              `json:"is_up_counter"`      // 是否上报 0:不上报 1:上报
	Price            float64          `json:"price"`              // 售价
	UpCounterTime    types.CustomTime `json:"up_counter_time"`    // 上报时间
	DownCounterTime  types.CustomTime `json:"down_counter_time"`  // 下报时间
	MarketDate       types.DateOnly   `json:"market_date"`        // 上市时间
	DelistTime       types.DateOnly   `json:"delist_time"`        // 下市时间
	Discontinued     int              `json:"discontinued"`       // 演示样机是否下市 0:不 1:是
	DiscontinuedDate types.DateOnly   `json:"discontinued_date"`  // 演示样机下市时间
	CategoryName     string           `json:"category_name"`
	PassedAudit      int              `json:"passed_audit"`
	FailedAudit      int              `json:"failed_audit"`
	NotAudit         int              `json:"not_audit"`
	NotUpload        int              `json:"not_upload"`
}
