package service

import (
	"fmt"
	"marketing/internal/consts"
	"marketing/internal/dao"
	userDao "marketing/internal/dao/admin_user"
	"marketing/internal/dao/endpoint"
	"marketing/internal/model"
	"marketing/internal/pkg/errors"
	"marketing/internal/pkg/log"
	"marketing/internal/pkg/types"
	"marketing/internal/pkg/utils"
	"marketing/internal/pkg/wecom"
	"marketing/internal/service/system"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"github.com/spf13/cast"
	"go.uber.org/zap"
)

type PushNotificationSvc interface {
	GetPushNotificationList(c *gin.Context, pageNum, pageSize, typeID int, content string) ([]*PushNotificationResp, int64, error)
	GetNotificationTags(c *gin.Context) ([]*NotificationTagResp, error)
	GetActiveRoles(c *gin.Context) ([]*RoleResp, error)
	GetNewestTrains(c *gin.Context, limit int) ([]*TrainResp, error)
	GetNewestNotices(c *gin.Context, limit int) ([]*NoticeResp, error)
	SearchUsers(c *gin.Context, search string, page, pageSize int) ([]*UserResp, error)
	PushNotification(c *gin.Context, params *PushNotificationParams) error
	RevokeNotification(c *gin.Context, id int) error
	GetNotifyDetail(c *gin.Context, id int) (*PushNotificationResp, error)
}

type pushNotificationSvc struct {
	pushDao     dao.PushNotificationDao
	appSvc      system.AppSystemSvc
	userDao     userDao.UserDao
	typeDao     dao.AppNotificationTypeDao
	trainDao    dao.TrainDao
	endpointDao endpoint.EndpointDao
	roleDao     dao.AdminRoleDao
	redisClient *redis.Client
}

func NewPushNotificationSvc(pushDao dao.PushNotificationDao, appSvc system.AppSystemSvc,
	userDao userDao.UserDao, typeDao dao.AppNotificationTypeDao, trainDao dao.TrainDao,
	endpointDao endpoint.EndpointDao, roleDao dao.AdminRoleDao, redisClient *redis.Client) PushNotificationSvc {
	return &pushNotificationSvc{
		pushDao:     pushDao,
		appSvc:      appSvc,
		userDao:     userDao,
		typeDao:     typeDao,
		trainDao:    trainDao,
		endpointDao: endpointDao,
		roleDao:     roleDao,
		redisClient: redisClient,
	}
}

// GetPushNotificationList 获取推送消息列表
func (s *pushNotificationSvc) GetPushNotificationList(c *gin.Context, pageNum, pageSize, typeID int, content string) ([]*PushNotificationResp, int64, error) {
	notifications, total, err := s.pushDao.GetPushNotificationList(c, pageNum, pageSize, typeID, content)
	if err != nil {
		return nil, 0, errors.NewErr("获取推送消息列表失败: " + err.Error())
	}

	// 转换为响应格式
	result := make([]*PushNotificationResp, len(notifications))
	for i, notification := range notifications {
		// 解析内容获取标题
		var contentStr string
		contentData := notification.Content
		if title, ok := contentData["title"]; ok {
			contentStr = cast.ToString(title)
			if v, ok := contentData["content"]; ok {
				contentStr += cast.ToString(v)
			}
		}

		// 限制内容长度
		if len(contentStr) > 50 {
			contentStr = contentStr[:50] + "..."
		}

		result[i] = &PushNotificationResp{
			ID:       int(notification.ID),
			TypeName: notification.TypeName,
			Content:  contentStr,
			Audience: notification.Audience,
			Total:    int(notification.Total),
			Fetched:  int(notification.Fetched),
			Read:     int(notification.Read),
			Checked:  int(notification.Checked),
			Revoked:  int(notification.Revoked),
			Platform: types.JSONStringArray(notification.Platform),
		}
	}

	return result, total, nil
}

// GetNotificationTags 获取通知标签
func (s *pushNotificationSvc) GetNotificationTags(c *gin.Context) ([]*NotificationTagResp, error) {
	tags, err := s.pushDao.GetWecomTags(c)
	if err != nil {
		return nil, errors.NewErr("获取通知标签失败: " + err.Error())
	}

	result := make([]*NotificationTagResp, len(tags))
	for i, tag := range tags {
		result[i] = &NotificationTagResp{
			ID:   tag.ID,
			Text: tag.TagName,
		}
	}

	return result, nil
}

// GetActiveRoles 获取活跃角色
func (s *pushNotificationSvc) GetActiveRoles(c *gin.Context) ([]*RoleResp, error) {
	roles, err := s.pushDao.GetActiveRoles(c)
	if err != nil {
		return nil, errors.NewErr("获取角色列表失败: " + err.Error())
	}

	result := make([]*RoleResp, len(roles))
	for i, role := range roles {
		result[i] = &RoleResp{
			Slug: role.Slug,
			Name: role.Name,
		}
	}

	return result, nil
}

// GetNewestTrains 获取最新培训资源
func (s *pushNotificationSvc) GetNewestTrains(c *gin.Context, limit int) ([]*TrainResp, error) {
	trains, err := s.trainDao.GetNewestTrains(c, limit)
	if err != nil {
		return nil, errors.NewErr("获取培训资源失败: " + err.Error())
	}

	result := make([]*TrainResp, len(trains))
	for i, train := range trains {
		result[i] = &TrainResp{
			ID:   int(train.ID),
			Name: train.Name,
		}
	}

	return result, nil
}

// GetNewestNotices 获取最新通知
func (s *pushNotificationSvc) GetNewestNotices(c *gin.Context, limit int) ([]*NoticeResp, error) {
	notices, err := s.pushDao.GetNewestNotices(c, limit)
	if err != nil {
		return nil, errors.NewErr("获取通知列表失败: " + err.Error())
	}

	result := make([]*NoticeResp, len(notices))
	for i, notice := range notices {
		result[i] = &NoticeResp{
			ID:    notice.ID,
			Title: notice.Title,
		}
	}

	return result, nil
}

// SearchUsers 搜索用户
func (s *pushNotificationSvc) SearchUsers(c *gin.Context, search string, page, pageSize int) ([]*UserResp, error) {
	users, err := s.pushDao.SearchUsers(c, search, page, pageSize)
	if err != nil {
		return nil, errors.NewErr("搜索用户失败: " + err.Error())
	}

	result := make([]*UserResp, len(users))
	for i, user := range users {
		result[i] = &UserResp{
			ID:   user.ID,
			Text: user.Username,
		}
	}

	return result, nil
}

// PushNotification 推送通知
func (s *pushNotificationSvc) PushNotification(c *gin.Context, params *PushNotificationParams) error {
	// 获取推送目标用户
	userIDs, err := s.getNotifyUserIDs(c, params.Audience)
	if err != nil {
		return err
	}
	// 构建企微推送内容
	content := s.buildWecomContent(params)

	// 推送到企业微信
	msgids, err := s.pushToWecom(c, userIDs, content, params.Slug, params)
	if err != nil {
		return err
	}

	// 保存推送记录到数据库
	err = s.saveNotificationRecord(c, params, userIDs, msgids)
	if err != nil {
		return err
	}

	return nil
}

// RevokeNotification 撤回通知
func (s *pushNotificationSvc) RevokeNotification(c *gin.Context, id int) error {
	notification, err := s.pushDao.GetPushNotificationByID(c, id)
	if err != nil {
		return errors.NewErr("查询消息失败: " + err.Error())
	}
	if notification == nil {
		return errors.NewErr("消息不存在")
	}

	// 检查是否可以撤回（24小时内，企业微信限制）
	if time.Since(notification.CreatedAt) > 24*time.Hour {
		return errors.NewErr("推送超过24小时的消息不能撤回")
	}

	if notification.Revoked == 1 {
		return nil // 已经撤回
	}

	// 撤回企业微信消息
	if notification.Msgid != "" {
		err := s.recallWecomMessages(c, notification.Msgid)
		if err != nil {
			log.Error("撤回企业微信消息失败", zap.Error(err), zap.String("msgid", notification.Msgid))
			// 不阻断流程，继续更新数据库状态
		}
	}

	// 更新撤回状态
	if err := s.pushDao.UpdatePushNotificationRevoke(c, id); err != nil {
		return errors.NewErr("撤回消息失败: " + err.Error())
	}

	return nil
}

// GetNotifyDetail 获取通知详情
func (s *pushNotificationSvc) GetNotifyDetail(c *gin.Context, id int) (*PushNotificationResp, error) {
	detail, err := s.pushDao.GetPushNotificationByID(c, id)
	if err != nil {
		return nil, errors.NewErr("获取通知详情失败: " + err.Error())
	}
	if detail == nil {
		return nil, errors.NewErr("消息不存在")
	}

	// 转换为响应格式
	audience, err := s.processAudience(c, detail.Audience)
	if err != nil {
		return nil, err
	}

	result := &PushNotificationResp{
		ID:       int(detail.ID),
		Content:  detail.Content,
		Audience: audience,
		Revoked:  int(detail.Revoked),
		Platform: types.JSONStringArray(detail.Platform),
	}

	return result, nil
}

func (s *pushNotificationSvc) processAudience(c *gin.Context, audience map[string]interface{}) (map[string]interface{}, error) {
	if audience != nil {
		// 处理用户受众
		if userSlice, ok := s.extractInterfaceSlice(audience, "users"); ok && len(userSlice) > 0 {
			if s.isSimpleType(userSlice[0]) {
				uids := cast.ToUintSlice(userSlice)
				users, err := s.userDao.GetByIDs(c, uids)
				if err != nil {
					return nil, errors.NewErr("获取用户信息失败: " + err.Error())
				}
				audience = gin.H{"users": s.convertUsersToMap(users)}
			}
			return audience, nil // 提前返回，避免后续判断
		}

		// 处理终端受众
		if endpointSlice, ok := s.extractInterfaceSlice(audience, "endpoints"); ok && len(endpointSlice) > 0 {
			endpointSlices, err := s.endpointDao.GetEndpointMap(c, cast.ToIntSlice(endpointSlice))
			if err != nil {
				return nil, errors.NewErr("获取终端信息失败: " + err.Error())
			}
			audience = gin.H{"endpoints": s.convertEndpointsToMap(endpointSlices)}
			return audience, nil // 提前返回
		}

		// 处理角色受众
		if roleSlice, ok := s.extractInterfaceSlice(audience, "roles"); ok && len(roleSlice) > 0 {
			roles, err := s.roleDao.GetAdminRoleByIDs(c, cast.ToIntSlice(roleSlice))
			if err != nil {
				return nil, errors.NewErr("获取角色信息失败: " + err.Error())
			}
			audience = gin.H{"roles": s.convertRolesToMap(roles)}
			return audience, nil // 提前返回
		}
	}
	return audience, nil
}

// 辅助函数：从map中提取interface slice
func (s *pushNotificationSvc) extractInterfaceSlice(audienceMap map[string]interface{}, key string) ([]interface{}, bool) {
	if value, ok := audienceMap[key]; ok {
		if slice, ok := value.([]interface{}); ok {
			return slice, true
		}
	}
	return nil, false
}

// 辅助函数：检查是否为简单类型
func (s *pushNotificationSvc) isSimpleType(value interface{}) bool {
	switch value.(type) {
	case float64, int64, int, string:
		return true
	default:
		return false
	}
}

// 辅助函数：转换用户列表到map
func (s *pushNotificationSvc) convertUsersToMap(users []model.AdminUsers) []map[string]interface{} {
	var userList []map[string]interface{}
	for _, user := range users {
		userList = append(userList, map[string]interface{}{
			"id":   user.ID,
			"text": user.Username,
		})
	}
	return userList
}

// 辅助函数：转换终端列表到map
func (s *pushNotificationSvc) convertEndpointsToMap(endpointSlices map[uint]string) []map[string]interface{} {
	var endpoints []map[string]interface{}
	for eid, text := range endpointSlices {
		endpoints = append(endpoints, map[string]interface{}{
			"id":   eid,
			"text": text,
		})
	}
	return endpoints
}

// 辅助函数：转换角色列表到map
func (s *pushNotificationSvc) convertRolesToMap(roles []*model.AdminRoles) []map[string]interface{} {
	var roleList []map[string]interface{}
	for _, role := range roles {
		roleList = append(roleList, map[string]interface{}{
			"slug": role.Slug,
			"text": role.Name,
		})
	}
	return roleList
}

// getNotifyUserIDs 获取通知用户ID列表
func (s *pushNotificationSvc) getNotifyUserIDs(c *gin.Context, audience map[string]interface{}) ([]uint, error) {
	return s.pushDao.GetUserIDsByAudience(c, audience)
}

// buildWecomContent 构建企微推送内容
func (s *pushNotificationSvc) buildWecomContent(params *PushNotificationParams) string {
	content := fmt.Sprintf("%s\n%s", params.Title, params.Content)

	if params.ActionText != "" && params.URL != "" {
		content += fmt.Sprintf("\n\n%s: %s", params.ActionText, params.URL)
	}

	return content
}

// pushToWecom 推送到企业微信
func (s *pushNotificationSvc) pushToWecom(c *gin.Context, userIDs []uint, content, slug string, params *PushNotificationParams) ([]string, error) {
	if len(userIDs) == 0 {
		return nil, nil
	}
	app, err := s.appSvc.GetAppSystemByKey(c, "app-notice")
	if err != nil {
		return nil, errors.NewErr("获取应用配置失败: " + err.Error())
	}
	wecomClient := wecom.NewWeComClient(app.CorpID, app.CorpSecret)

	// 获取用户的企微ID
	users, err := s.pushDao.GetUsersByIDs(c, userIDs)
	if err != nil {
		return nil, errors.NewErr("获取用户企微ID失败: " + err.Error())
	}

	var qwUserIDs []string
	for _, user := range users {
		if user.QWUserID != nil && *user.QWUserID != "" {
			qwUserIDs = append(qwUserIDs, *user.QWUserID)
		}
	}

	if len(qwUserIDs) == 0 {
		return nil, errors.NewErr("没有找到有效的企微用户ID")
	}

	//====== 推送到企业微信🚀 分批发送，每批最多1000个 ======
	const maxBatch = 1000
	var msgids []string
	for i := 0; i < len(qwUserIDs); i += maxBatch {
		end := i + maxBatch
		if end > len(qwUserIDs) {
			end = len(qwUserIDs)
		}
		toUser := strings.Join(qwUserIDs[i:end], "|")
		var url string
		url = fmt.Sprintf("%s?id=%s", consts.NoticeDetailTest, params.sourceID)
		if gin.Mode() == gin.ReleaseMode {
			url = fmt.Sprintf("%s?id=%s", consts.NoticeDetail, params.NoticeID)
		}

		if slug == "notice" {
			// 图文消息
			cover := consts.NoticeCover
			articles := []wecom.Article{
				{
					Title:       params.Title,
					Description: params.Content,
					URL:         url,
					PicURL:      cover,
				},
			}
			if sendResult, err := wecomClient.SendNewsMessage(toUser, "", "", app.AgentID, articles); err != nil {
				return nil, errors.NewErr(fmt.Sprintf("推送到企业微信失败 (batch %d-%d): %s", i, end, err.Error()))
			} else {
				msgids = append(msgids, sendResult.MsgID)
			}
		} else {
			if sendResult, err := wecomClient.SendTextCardMessage(
				toUser, "", "", app.AgentID,
				params.Title, content, url, params.ActionText,
			); err != nil {
				return nil, errors.NewErr(fmt.Sprintf("推送到企业微信失败 (batch %d-%d): %s", i, end, err.Error()))
			} else {
				msgids = append(msgids, sendResult.MsgID)
			}
		}
	}

	return msgids, nil
}

// saveNotificationRecord 保存通知记录
func (s *pushNotificationSvc) saveNotificationRecord(c *gin.Context, params *PushNotificationParams, userIDs []uint, msgids []string) error {
	// 获取通知类型
	notificationType, err := s.typeDao.GetAppNotificationTypeBySlug(c, params.Slug)
	if err != nil {
		return errors.NewErr("获取通知类型失败: " + err.Error())
	}
	// 构建通知内容JSON
	contentData := map[string]interface{}{
		"group_name":  notificationType.Name,
		"group_icon":  notificationType.Icon,
		"msg_type":    notificationType.MsgType,
		"action_text": notificationType.ActionText,
		"action":      params.Action,
		"content":     params.Content,
		"title":       params.Title,
		"popup":       0,
		"banner":      0,
		"slug":        params.Slug,
		"platform":    params.Platform,
	}
	if notificationType.Slug == "notice" {
		contentData["notice_id"] = params.NoticeID
		contentData["url"] = params.URL
	}

	// 保存通知记录
	notification := &model.AppNotification{
		TypeID:    notificationType.ID,
		Content:   types.JSONMap(contentData),
		Platform:  params.Platform,
		Audience:  types.JSONMap(params.Audience),
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		Msgid:     strings.Join(msgids, ","),
	}

	if err := s.pushDao.CreatePushNotification(c, notification); err != nil {
		return errors.NewErr("保存通知记录失败: " + err.Error())
	}

	// 提前生成收件箱ID，防止消息发送后ID生成失败
	inboxIDs, err := utils.GenInboxIdsWithRedis(c, s.redisClient, len(userIDs))
	if err != nil {
		return errors.NewErr("生成收件箱ID失败: " + err.Error())
	}

	// 保存用户收件箱记录（使用预生成的收件箱ID）
	var inboxRecords []*model.AppNotificationInbox
	now := time.Now()
	for i, userID := range userIDs {
		if i < len(inboxIDs) { // 确保索引不越界
			inboxRecords = append(inboxRecords, &model.AppNotificationInbox{
				ID:             cast.ToUint64(inboxIDs[i]), // 👈 使用预生成的ID
				UserID:         cast.ToUint32(userID),
				NotificationID: cast.ToUint32(notification.ID),
				CreatedAt:      now,
			})
		}
	}

	if len(inboxRecords) > 0 {
		if err := s.pushDao.CreateNotificationInboxBatch(c, inboxRecords); err != nil {
			return errors.NewErr("保存收件箱记录失败: " + err.Error())
		}
	}

	return nil
}

// recallWecomMessages 撤回企业微信消息
func (s *pushNotificationSvc) recallWecomMessages(c *gin.Context, msgids string) error {
	if msgids == "" {
		return nil
	}

	// 获取企业微信应用配置
	app, err := s.appSvc.GetAppSystemByKey(c, "app-notice")
	if err != nil {
		return fmt.Errorf("获取企业微信应用配置失败: %w", err)
	}

	// 创建企业微信客户端
	wecomClient := wecom.NewWeComClient(app.CorpID, app.CorpSecret)
	if wecomClient == nil {
		return fmt.Errorf("创建企业微信客户端失败")
	}

	// 分割消息ID（可能有多个，用逗号分隔）
	msgIDList := strings.Split(msgids, ",")
	var failedMsgIDs []string

	for _, msgID := range msgIDList {
		msgID = strings.TrimSpace(msgID)
		if msgID == "" {
			continue
		}

		// 调用企业微信撤回接口
		resp, err := wecomClient.RecallMessage(msgID)
		if err != nil {
			log.Error("撤回企业微信消息失败", zap.Error(err), zap.String("msgid", msgID))
			failedMsgIDs = append(failedMsgIDs, msgID)
			continue
		}

		if resp.ErrCode != 0 {
			log.Error("企业微信撤回消息接口返回错误",
				zap.Int("errcode", resp.ErrCode),
				zap.String("errmsg", resp.ErrMsg),
				zap.String("msgid", msgID))
			failedMsgIDs = append(failedMsgIDs, msgID)
		} else {
			log.Info("成功撤回企业微信消息", zap.String("msgid", msgID))
		}
	}

	// 如果有失败的消息ID，返回错误信息
	if len(failedMsgIDs) > 0 {
		return fmt.Errorf("部分消息撤回失败，失败的消息ID: %s", strings.Join(failedMsgIDs, ","))
	}

	return nil
}

// PushNotificationParams 推送通知参数
type PushNotificationParams struct {
	Title           string                 `json:"title"`
	Content         string                 `json:"content"`
	Platform        []string               `json:"platform"`
	Audience        map[string]interface{} `json:"audience"`
	Slug            string                 `json:"type_id"`
	Action          string                 `json:"action"`
	ActionText      string                 `json:"action_text"`
	URL             string                 `json:"url"`
	Popup           int                    `json:"popup"`
	Banner          int                    `json:"banner"`
	BannerStartTime string                 `json:"banner_start_time"`
	BannerEndTime   string                 `json:"banner_end_time"`
	SourceID        string                 `json:"source_id"`
}

// 响应结构体
type PushNotificationResp struct {
	ID       int      `json:"id"`
	TypeName string   `json:"type_name"`
	Content  any      `json:"content"`
	Platform []string `json:"platform"`
	Audience any      `json:"audience"`
	Total    int      `json:"total"`
	Fetched  int      `json:"fetched"`
	Read     int      `json:"read"`
	Checked  int      `json:"checked"`
	Revoked  int      `json:"revoked"`
}

type NotificationTagResp struct {
	ID   uint   `json:"id"`
	Text string `json:"text"`
}

type RoleResp struct {
	Slug string `json:"slug"`
	Name string `json:"name"`
}

type TrainResp struct {
	ID   int    `json:"id"`
	Name string `json:"name"`
}

type NoticeResp struct {
	ID    int    `json:"id"`
	Title string `json:"title"`
}

type UserResp struct {
	ID   uint   `json:"id"`
	Text string `json:"text"`
}
