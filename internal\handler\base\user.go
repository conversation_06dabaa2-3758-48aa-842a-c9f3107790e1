package base

import (
	"github.com/gin-gonic/gin"
	api2 "marketing/internal/api/system"
	"marketing/internal/handler"
	"marketing/internal/pkg/errors"
	"marketing/internal/service/system"
)

type UserHandler interface {
	//GetUserOptions 获取用户操作选项
	GetUserOptions(c *gin.Context)
}

type userHandler struct {
	service system.AdminUserInterface
}

func NewUserHandler(service system.AdminUserInterface) UserHandler {
	return &userHandler{
		service: service,
	}
}

// GetUserOptions 获取用户操作选项
func (h *userHandler) GetUserOptions(c *gin.Context) {
	var req api2.SearchUserReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, errors.NewErr("参数错误: "+err.Error()))
		return
	}
	req.SetDefaults()
	options, err := h.service.SearchUsers(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, options)
}
