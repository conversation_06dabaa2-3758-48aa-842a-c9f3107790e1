package action

import (
	"marketing/internal/api"
	"marketing/internal/pkg/types"
	"time"
)

type PromotionAddReq struct {
	Name         string    `json:"name"`
	StartTime    time.Time `json:"start_time"`
	EndTime      time.Time `json:"end_time"`
	Model        []string  `json:"model"`
	IsSamePhone  uint8     `json:"is_same_phone"`
	ReceiptDay   string    `json:"receipt_day"`
	Prize        string    `json:"prize"`
	HourInterval float32   `json:"hour_interval"`
	PrizePic     []string  `json:"prize_pic"`
	Rule         string    `json:"rule"`
	Type         uint8     `json:"type"`
}

type PromotionUpdateReq struct {
	ID              uint64 `json:"id"`
	PromotionAddReq `json:"promotion_add_req"`
}

// BarcodeResp 条码查询响应结构
type BarcodeResp struct {
	ID             int
	WarrantyID     int       `json:"warranty_id"`
	Barcode        string    `json:"barcode"`
	Model          string    `json:"model"`
	Endpoint       int       `json:"endpoint"`
	ModelID        int       `json:"model_id"`
	BuyDate        time.Time `json:"buy_date"`
	StudentUID     string    `json:"student_uid"`
	StudentName    string    `json:"student_name"`
	AgencyName     string    `json:"agency_name"`
	ActivatedAt    time.Time `json:"activated_at"`
	Number         string    `json:"number"`
	CustomerName   string    `json:"customer_name"`
	CustomerPhone  string    `json:"customer_phone"`
	ActivatedID    string    `json:"activated_id"`
	HourInterval   int       `json:"hour_interval"`
	EndpointCode   string    `json:"endpoint_code"`
	EndpointName   string    `json:"endpoint_name"`
	Manager        string    `json:"manager"`
	ReturnAt       time.Time `json:"return_at"`
	ActivatedPhone string    `json:"activated_phone"`
}

type DetailBarcode struct {
	ID             int       `json:"id"`
	Barcode        string    `json:"barcode"`
	Model          string    `json:"model"`
	ModelID        int       `json:"model_id"`
	Endpoint       int       `json:"endpoint"`
	BuyDate        time.Time `json:"buy_date"`
	CustomerName   string    `json:"customer_name"`
	CustomerPhone  string    `json:"customer_phone"`
	ActivatedID    int       `json:"activated_id"`
	ActivatedPhone string    `json:"activated_phone"`
	IsSamePhone    int       `json:"is_same_phone"`
}
type SiftWarranty struct {
	WarrantyId     int       `json:"warranty_id"`
	Endpoint       int       `json:"endpoint"`
	Model          string    `json:"model"`
	ModelID        int       `json:"model_id"`
	BuyDate        time.Time `json:"buy_date"`
	CustomerName   string    `json:"customer_name"`
	CustomerPhone  string    `json:"customer_phone"`
	Barcode        string    `json:"barcode"`
	ActivatedID    int       `json:"activated_id"`
	IsSamePhone    int       `json:"is_same_phone"`
	ActivatedPhone string    `json:"activated_phone"`
}

type ExportList struct {
	Receipt        interface{}      `json:"receipt" gorm:"-"`
	Number         string           `json:"number"`
	HourInterval   float32          `json:"hour_interval"`
	RegionName     string           `json:"region_name"`
	AgencyName     string           `json:"agency_name"`
	EndpointName   string           `json:"endpoint_name"`
	EndpointCode   string           `json:"endpoint_code"`
	EndpointPhone  string           `json:"endpoint_phone"`
	Manager        string           `json:"manager"`
	Address        string           `json:"address"`
	StudentName    string           `json:"student_name"`
	StudentUid     string           `json:"student_uid"`
	ActivatedAt    types.CustomTime `json:"activated_at"`
	ActivatedPhone string           `json:"activated_phone"`
	ReturnAt       types.CustomTime `json:"return_at"`
	CustomerPhone  string           `json:"customer_phone"`
	CustomerName   string           `json:"customer_name"`
	Barcode        string           `json:"barcode"`
	IsReceipt      int              `json:"is_receipt"`
	BuyDate        types.CustomTime `json:"buy_date"`
	Model          string           `json:"model"`
}

type PromotionList struct {
	ID            int         `json:"id"`
	AgencyName    string      `json:"agency_name"`
	Code          string      `json:"code"`
	Barcode       string      `json:"barcode"`
	CustomerName  string      `json:"customerName"`
	CustomerPhone string      `json:"customerPhone"`
	EndpointName  string      `json:"endpointName"`
	Receipt       interface{} `json:"receipt" gorm:"-"`
	Model         string      `json:"model"`
	IsReceipt     int         `json:"is_receipt"`
	WarrantyID    int         `json:"-"`
}

// PromotionListReceiptResp 回执图片
type PromotionListReceiptResp struct {
	SalesPromotionListId int    `json:"-"`
	Receipt              string `json:"receipt"`
	Number               string `json:"number"`
	Count                int    `json:"count"` //图片数量
}

type UpdatePromotionReq struct {
	ID      int    `json:"id" binding:"required"`
	Receipt string `json:"receipt" binding:"required,datetime=2006-01-02"`
}

type PromotionListApp struct {
	List        PromotionList `json:"list"`
	StudentName string        `json:"student_name"`
	ActivatedAt string        `json:"activated_at"`
	Number      string        `json:"number"`
}

type PromotionsReq struct {
	api.PaginationParams
	Type       uint   `form:"type" json:"type"`
	Name       string `form:"name" json:"name"`
	AgencyID   uint   `form:"agency_id" json:"agency_id"`
	EndpointID uint   `form:"endpoint_id" json:"endpoint_id"`
}

type NumberRepeat struct {
	PromotionID int    `json:"promotion_id"`
	Promotion   string `json:"promotion"`
	Barcode     string `json:"barcode"`
}
