package action

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/api/action"
	"marketing/internal/handler"
	"marketing/internal/service"
	"strconv"
)

type HandlerActionType interface {
	Upsert(c *gin.Context)
	GetActionTypeList(c *gin.Context)
	GetByID(c *gin.Context)
	ReviseTypeName(c *gin.Context)
	ReviseTypeStatus(c *gin.Context)
	DropDownAll(c *gin.Context)
	SearchType(c *gin.Context)
	Update(c *gin.Context)
	GetActionTypeTips(c *gin.Context)
	GetAgencyTips(c *gin.Context)
}

type GormHandlerActionType struct {
	svc service.ActionTypeService
}

func (a *GormHandlerActionType) GetAgencyTips(c *gin.Context) {
	name := c.Query("agency")
	tips := a.svc.GetAgencyTips(c, name)
	handler.Success(c, gin.H{
		"tips": tips,
	})
}

func (a *GormHandlerActionType) GetActionTypeTips(c *gin.Context) {
	tips := a.svc.GetActionTypeTips(c)
	handler.Success(c, gin.H{
		"tips": tips,
	})
}

func (a *GormHandlerActionType) Update(c *gin.Context) {
	var req action.UpdateType
	err := c.ShouldBind(&req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	result := a.svc.Update(c, req)
	handler.Success(c, gin.H{
		"res": result,
	})
}

func (a *GormHandlerActionType) SearchType(c *gin.Context) {
	Page, _ := strconv.Atoi(c.Query("page"))
	PageSize, _ := strconv.Atoi(c.Query("page_size"))
	if PageSize == 0 {
		PageSize = 20
	}
	if Page == 0 {
		Page = 1
	}
	Name := c.Query("name")
	Slug := c.Query("slug")
	list, total, err := a.svc.SearchType(c, Page, PageSize, Name, Slug)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"list":      list,
		"total":     total,
		"page":      Page,
		"page_size": PageSize,
	})
}

func (a *GormHandlerActionType) DropDownAll(c *gin.Context) {
	list := a.svc.DropDownName(c)
	handler.Success(c, gin.H{
		"list": list,
	})
}

func (a *GormHandlerActionType) ReviseTypeName(c *gin.Context) {
	id, _ := strconv.Atoi(c.Query("id"))
	name, _ := strconv.Atoi(c.Query("name"))
	if err := a.svc.ReviseTypeName(c, id, name); err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{})
}

func (a *GormHandlerActionType) ReviseTypeStatus(c *gin.Context) {
	id, _ := strconv.Atoi(c.Query("id"))
	status, _ := strconv.Atoi(c.Query("status"))
	if err := a.svc.ReviseStatus(c, id, status); err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c)
}

// GetActionTypeList 获取活动类型列表
func (a *GormHandlerActionType) GetActionTypeList(c *gin.Context) {
	page, _ := strconv.Atoi(c.Query("page"))
	size, _ := strconv.Atoi(c.Query("page_size"))
	if page <= 0 {
		page = 1
	}
	if size <= 0 {
		size = 20
	}
	list, total := a.svc.GetActionTypeList(c, page, size)
	handler.Success(c, gin.H{
		"list":      list,
		"page":      page,
		"page_size": size,
		"total":     total,
	})
}

// GetByID 根据id获取活动类型
func (a *GormHandlerActionType) GetByID(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		handler.Error(c, err)
		return
	}
	act, err := a.svc.GetByID(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, act)
}

// Upsert 添加或修改活动类型
func (a *GormHandlerActionType) Upsert(c *gin.Context) {
	var act action.TypeAction
	if err := c.ShouldBind(&act); err != nil {
		handler.Error(c, err)
		return
	}
	id, err := a.svc.Upsert(c, act)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"id": id,
	})
}

func NewHandleActionType(svc service.ActionTypeService) HandlerActionType {
	return &GormHandlerActionType{
		svc: svc,
	}
}
