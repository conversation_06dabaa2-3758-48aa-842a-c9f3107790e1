package dao

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"marketing/internal/model"
	"marketing/internal/pkg/db"
	"marketing/internal/pkg/utils"
)

type BugFreeLabelDao interface {
	CreateBugFreeLabel(c *gin.Context, label *model.BugFreeLabel) error
	CreateBugFreeLabelCategory(c *gin.Context, labelCategory *model.BugFreeLabelCategory) error
	UpdateBugFreeLabel(c *gin.Context, id int, uMap map[string]interface{}) error
	UpdateBugFreeLabelCategory(c *gin.Context, id int, uMap map[string]interface{}) error
	DeleteBugFreeLabel(c *gin.Context, id int) error
	DeleteBugFreeLabelCategory(c *gin.Context, id int) error
	GetBugFreeLabel(c *gin.Context, labelType, category int) (list []*model.BugFreeLabel)
	GetBugFreeLabelCategory(c *gin.Context) (list []*model.BugFreeLabelCategory)
	GetBugFreeLabelByTyIdWithType(c *gin.Context, id, labelType int) *model.BugFreeLabel
	GetBugFreeLabelById(c *gin.Context, id int) *model.BugFreeLabel
	GetBugFreeLabelCategoryById(c *gin.Context, id int) *model.BugFreeLabelCategory
}

// BugFreeLabelImpl 实现 BugFreeLabelDao 接口
type BugFreeLabelImpl struct {
	db *gorm.DB
}

// NewBugFreeLabelDao 创建 BugFreeLabelDao 实例
func NewBugFreeLabelDao() BugFreeLabelDao {
	return &BugFreeLabelImpl{
		db: db.GetDB(""),
	}
}

func (d *BugFreeLabelImpl) CreateBugFreeLabel(c *gin.Context, label *model.BugFreeLabel) error {
	return d.db.WithContext(c).Create(label).Error
}

func (d *BugFreeLabelImpl) CreateBugFreeLabelCategory(c *gin.Context, labelCategory *model.BugFreeLabelCategory) error {
	return d.db.WithContext(c).Create(labelCategory).Error
}

func (d *BugFreeLabelImpl) UpdateBugFreeLabel(c *gin.Context, id int, uMap map[string]interface{}) error {
	return d.db.WithContext(c).Model(&model.BugFreeLabel{}).Where("id = ?", id).Updates(uMap).Error
}

func (d *BugFreeLabelImpl) UpdateBugFreeLabelCategory(c *gin.Context, id int, uMap map[string]interface{}) error {
	return d.db.WithContext(c).Model(&model.BugFreeLabelCategory{}).Where("id = ?", id).Updates(uMap).Error
}

func (d *BugFreeLabelImpl) DeleteBugFreeLabel(c *gin.Context, id int) error {
	return d.db.WithContext(c).Delete(&model.BugFreeLabel{}, "id = ?", id).Error
}

func (d *BugFreeLabelImpl) DeleteBugFreeLabelCategory(c *gin.Context, id int) error {
	return d.db.WithContext(c).Delete(&model.BugFreeLabelCategory{}, "id = ?", id).Error
}

func (d *BugFreeLabelImpl) GetBugFreeLabel(c *gin.Context, labelType, category int) (list []*model.BugFreeLabel) {
	d.db.WithContext(c).Model(&model.BugFreeLabel{}).
		Where("type = ? and category = ? and visibility = 1", labelType, category).
		Order("`order`").
		Find(&list)

	for _, l := range list {
		l.CreateTime = utils.GetTimeStr(l.CreatedAt)
		l.UpdateTime = utils.GetTimeStr(l.UpdatedAt)
	}

	return
}

func (d *BugFreeLabelImpl) GetBugFreeLabelCategory(c *gin.Context) (list []*model.BugFreeLabelCategory) {
	d.db.WithContext(c).Model(&model.BugFreeLabelCategory{}).Where("visibility = 1").Order("`order`").Find(&list)

	for _, l := range list {
		l.CreateTime = utils.GetTimeStr(l.CreatedAt)
		l.UpdateTime = utils.GetTimeStr(l.UpdatedAt)
	}

	return
}

func (d *BugFreeLabelImpl) GetBugFreeLabelByTyIdWithType(c *gin.Context, id, labelType int) *model.BugFreeLabel {
	var label model.BugFreeLabel
	err := d.db.WithContext(c).Model(&model.BugFreeLabel{}).Where("id = ? and type = ?", id, labelType).First(&label).Error
	if err != nil {
		return nil
	}

	return &label
}

func (d *BugFreeLabelImpl) GetBugFreeLabelById(c *gin.Context, id int) *model.BugFreeLabel {
	var label model.BugFreeLabel
	err := d.db.WithContext(c).Model(&model.BugFreeLabel{}).Where("id = ?", id).First(&label).Error
	if err != nil {
		return nil
	}

	return &label
}

func (d *BugFreeLabelImpl) GetBugFreeLabelCategoryById(c *gin.Context, id int) *model.BugFreeLabelCategory {
	var label model.BugFreeLabelCategory
	err := d.db.WithContext(c).Model(&model.BugFreeLabelCategory{}).Where("id = ?", id).First(&label).Error
	if err != nil {
		return nil
	}

	return &label
}
