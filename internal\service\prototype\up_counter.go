package prototype

import (
	"errors"
	"marketing/internal/api/prototype"
	"marketing/internal/dao"
	dao1 "marketing/internal/dao/prototype"
	"marketing/internal/model"
	appError "marketing/internal/pkg/errors"
	"time"

	"gorm.io/gorm"

	"github.com/gin-gonic/gin"
)

// UpCounterService 上柜机型服务
type UpCounterService interface {
	GetList(ctx *gin.Context, req *prototype.UpCounterSearch) ([]*prototype.UpCounterListVo, int64, error)
	UpdateStatus(ctx *gin.Context, req *prototype.UpCounterStatusUpdate) error
}

type upCounterService struct {
	upCounterDao           dao1.UpCounter
	categoryRepo           dao.ModelCategoryDao
	machineTypeRepo        dao.MachineTypeDao
	machineTypeRelationDao dao.MachineTypeRelationDao
}

// NewUpCounterService 创建服务实例
func NewUpCounterService(upCounterDao dao1.UpCounter, categoryRepo dao.ModelCategoryDao, machineTypeRepo dao.MachineTypeDao, machineTypeRelationDao dao.MachineTypeRelationDao) UpCounterService {
	return &upCounterService{
		upCounterDao:           upCounterDao,
		categoryRepo:           categoryRepo,
		machineTypeRepo:        machineTypeRepo,
		machineTypeRelationDao: machineTypeRelationDao,
	}
}

func (s *upCounterService) GetList(ctx *gin.Context, req *prototype.UpCounterSearch) ([]*prototype.UpCounterListVo, int64, error) {

	// 获取机型列表
	list, count, err := s.upCounterDao.GetList(ctx, req)
	if err != nil {
		return nil, 0, err
	}

	// 获取分类信息
	categoryList := s.categoryRepo.GetAllModelCategory(ctx)
	categoryMap := make(map[int]string)
	for _, v := range categoryList {
		categoryMap[v.Id] = v.Name
	}

	// 获取所有机型名称
	var modelNames []string
	for _, item := range list {
		modelNames = append(modelNames, item.Name)
	}

	// 获取统计数据
	stats, err := s.upCounterDao.GetCounterStats(ctx, modelNames)
	if err != nil {
		return nil, 0, err
	}

	// 组装数据
	for i, item := range list {

		if categoryName, ok := categoryMap[item.CategoryID]; ok {
			list[i].CategoryName = categoryName
		}
		if stat, ok := stats[item.Name]; ok {
			list[i].PassedAudit = int(stat.PassedAudit)
			list[i].FailedAudit = int(stat.FailedAudit)
			list[i].NotAudit = int(stat.NotAudit)
			list[i].NotUpload = int(stat.NotUpload)
		}
	}

	return list, count, nil
}

// UpdateStatus 更新样机上柜状态
func (s *upCounterService) UpdateStatus(ctx *gin.Context, req *prototype.UpCounterStatusUpdate) error {
	// 获取机型信息
	machineType := s.machineTypeRepo.GetMachineTypeByModelId(ctx, req.ID)
	if machineType == nil || machineType.Id == 0 {
		return appError.NewErr("机型不存在")
	}

	// 查询该机型是否已在配置表
	relation, err := s.upCounterDao.GetMachineTypeRelationByName(ctx, machineType.Name)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return appError.NewErr("获取机型关联信息失败: " + err.Error())
	}

	// 准备更新数据
	nowTime := time.Now()

	// 只有当 is_up_counter 为 1 时才处理市场日期和价格
	if *req.IsUpCounter == 1 {
		// 解析市场日期
		marketDate, err := time.Parse("2006-01-02", req.MarketDate)
		if err != nil {
			return appError.NewErr("日期格式错误，请使用YYYY-MM-DD格式")
		}

		// 获取 MachineTypeRelationDao 实例

		if relation == nil {
			// 创建新的关联记录
			newRelation := &model.MachineTypeRelation{
				IsUpCounter:   *req.IsUpCounter,
				Name:          machineType.Name,
				CategoryID:    machineType.CategoryId,
				Declare:       machineType.Declare,
				Stock:         machineType.Stock,
				Price:         req.Price,
				UpCounterTime: &nowTime,
				MarketDate:    &marketDate,
				CreatedAt:     time.Now(),
				UpdatedAt:     time.Now(),
			}

			if err := s.machineTypeRelationDao.Create(ctx, newRelation); err != nil {
				return appError.NewErr("创建关联记录失败: " + err.Error())
			}

		} else {
			// 更新现有记录的市场日期和价格
			updateData := map[string]interface{}{
				"market_date":     marketDate,
				"price":           req.Price,
				"updated_at":      time.Now(),
				"up_counter_time": &nowTime,
				"is_up_counter":   req.IsUpCounter,
			}

			if err := s.machineTypeRelationDao.Update(ctx, machineType.Name, updateData); err != nil {
				return appError.NewErr("更新市场日期和价格失败: " + err.Error())
			}
		}
		return nil
	}

	// 使用 machineTypeRelationDao 更新
	updateData := map[string]interface{}{
		"is_up_counter":     req.IsUpCounter,
		"updated_at":        nowTime,
		"down_counter_time": nowTime,
	}
	if err := s.machineTypeRelationDao.Update(ctx, machineType.Name, updateData); err != nil {
		return appError.NewErr("更新失败: " + err.Error())
	}

	return nil
}
