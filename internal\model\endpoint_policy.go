package model

import (
	"time"
)

// EndpointPolicy represents the endpoint_policy table in your database.
type EndpointPolicy struct {
	ID              int        `gorm:"primaryKey;autoIncrement;column:id"`
	Name            string     `gorm:"size:255;not null;comment:'政策名称';column:name"`
	Description     string     `gorm:"size:255;comment:'政策描述';column:description"`
	File            string     `gorm:"type:text;column:file"`
	StartDate       time.Time  `gorm:"column:start_date"`
	EndDate         time.Time  `gorm:"column:end_date"`
	UpdatedAt       *time.Time `gorm:"column:updated_at"`
	CreatedAt       time.Time  `gorm:"column:created_at"`
	MaterialSupport int        `gorm:"type:tinyint;default:0;comment:'是否支持物料，0为否，1为是';column:material_support"`
	AmountSupport   int        `gorm:"default:0;comment:'支持的金额，单位元';column:amount_support"`
	Installments    int        `gorm:"default:0;comment:'分期期数';column:installments"`
	EndpointType    int        `gorm:"not null;default:0;comment:'终端类型，0-未知，1-专柜，2-运营商渠道，3-专卖店，4-城市综合体 5-商超 6-书店';column:endpoint_type"`
	Maximum         int        `gorm:"not null;default:0;comment:'数量上限';column:maximum"`
	WriteOffTable   string     `gorm:"size:255;null;default:null;comment:'批复表模板';column:write_off_table"`
	Template        string     `gorm:"size:50;null;default:null;comment:'前端模板，跟流程节点和界面显示有关系';column:template"`
	States          string     `gorm:"size:255;null;default:null;comment:'状态流转，按流转顺序逗号分隔';column:states"`
	Enabled         int        `gorm:"not null;default:1;comment:'0禁用 1启用,政策必须在有效期内才有效，也可以提前禁用政策';column:enabled"`
}

// TableName overrides the table name used by GORM.
func (EndpointPolicy) TableName() string {
	return "endpoint_policy"
}
