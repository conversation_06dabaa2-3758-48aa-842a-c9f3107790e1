package wecom

import (
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"marketing/internal/pkg/errors"
	"marketing/internal/pkg/log"
)

// UserInfoResp UserInfoResp用于解析获取用户信息的响应
type UserInfoResp struct {
	ErrCode    int    `json:"errcode"`
	ErrMsg     string `json:"errmsg"`
	UserID     string `json:"userid"`
	Name       string `json:"name"`
	UserTicket string `json:"user_ticket"` // 用户票据
}

// GetUserIDByCode 通过 code 获取 UserID
func (client *Client) GetUserIDByCode(code string) (string, error) {
	accessToken, err := client.GetAccessToken()
	if err != nil {
		return "", err
	}
	url := fmt.Sprintf("https://qyapi.weixin.qq.com/cgi-bin/user/getuserinfo?access_token=%s&code=%s", accessToken, code)

	resp, err := client.httpClient.R().Get(url)
	if err != nil {
		return "", err
	}
	//正式不应该输出
	log.Info("企微code登录：", zap.Any("企微code响应", resp))

	var userInfo UserInfoResp
	if err := json.Unmarshal(resp.Body(), &userInfo); err != nil {
		log.Error("微信code请求用户信息错误,json解析错误：", zap.Error(err))
		return "", err
	}

	if userInfo.ErrCode != 0 {
		log.Error("微信code请求用户信息错误，微信接口错误：", zap.Int("errcode", userInfo.ErrCode), zap.String("errmsg", userInfo.ErrMsg), zap.String("URL", url))
		return "", &WeChatAPIError{ErrCode: userInfo.ErrCode, ErrMsg: userInfo.ErrMsg}
	}

	if userInfo.UserID == "" {
		return "", errors.NewErr("非企业成员，不允许访问")
	}
	return userInfo.UserID, nil
}

// UserReq 成员信息请求结构体
type UserReq struct {
	UserID         string `json:"userid"`                      // 成员UserID（必填）
	Name           string `json:"name,omitempty"`              // 成员名称
	Department     []int  `json:"department,omitempty"`        // 部门ID列表
	Mobile         string `json:"mobile,omitempty"`            // 手机号码
	Email          string `json:"email,omitempty"`             // 邮箱
	Enable         int    `json:"enable,omitempty"`            // 启用/禁用成员
	Position       string `json:"position,omitempty"`          // 职务信息
	IsLeaderInDept []int  `json:"is_leader_in_dept,omitempty"` // 是否为部门负责人
	ToInvite       bool   `json:"to_invite,omitempty"`         // 是否邀请该成员使用企业微信（将通过微信服务通知或短信或邮件下发邀请，每天自动下发一次，最多持续3个工作日）
	MainDepartment int    `json:"main_department,omitempty"`   // 主部门ID
}

// UserResp 成员操作响应
type UserResp struct {
	ErrCode int    `json:"errcode"`
	ErrMsg  string `json:"errmsg"`
}

// CreateUser 创建成员
func (client *Client) CreateUser(req *UserReq) error {
	// 创建成员时的参数验证
	if err := client.validateCreateUser(req); err != nil {
		return err
	}
	return client.doUserRequest("create", req)
}

// UpdateUser 更新成员
func (client *Client) UpdateUser(req *UserReq) error {
	// 更新成员时的参数验证
	if err := client.validateUpdateUser(req); err != nil {
		return err
	}
	return client.doUserRequest("update", req)
}

// validateCreateUser 创建成员参数验证
func (client *Client) validateCreateUser(req *UserReq) error {
	if req.UserID == "" {
		return fmt.Errorf("成员UserID不能为空")
	}
	if req.Name == "" {
		return fmt.Errorf("成员名称不能为空")
	}
	if req.Mobile == "" && req.Email == "" {
		return fmt.Errorf("手机号码和邮箱不能同时为空")
	}
	if len(req.IsLeaderInDept) > 0 && len(req.IsLeaderInDept) != len(req.Department) {
		return fmt.Errorf("部门负责人数组长度必须与部门数组长度一致")
	}
	return nil
}

// validateUpdateUser 更新成员参数验证
func (client *Client) validateUpdateUser(req *UserReq) error {
	if req.UserID == "" {
		return fmt.Errorf("成员UserID不能为空")
	}
	if len(req.IsLeaderInDept) > 0 && len(req.Department) > 0 &&
		len(req.IsLeaderInDept) != len(req.Department) {
		return fmt.Errorf("部门负责人数组长度必须与部门数组长度一致")
	}
	return nil
}

// doUserRequest 执行成员相关请求
func (client *Client) doUserRequest(action string, req *UserReq) error {
	accessToken, err := client.GetAccessToken()
	if err != nil {
		return err
	}

	url := fmt.Sprintf("https://qyapi.weixin.qq.com/cgi-bin/user/%s?access_token=%s", action, accessToken)

	resp, err := client.httpClient.R().
		SetBody(req).
		Post(url)
	if err != nil {
		return fmt.Errorf("请求企业微信接口失败：%v", err)
	}

	log.Debug("企微操作成员：",
		zap.String("操作类型", action),
		zap.Any("请求参数", req),
		zap.Any("企微响应", resp))

	var userResp UserResp
	if err := json.Unmarshal(resp.Body(), &userResp); err != nil {
		log.Error("解析企业微信响应失败：", zap.Error(err))
		return fmt.Errorf("解析企业微信响应失败：%v", err)
	}

	if userResp.ErrCode != 0 {
		log.Error("企业微信接口返回错误：",
			zap.String("操作类型", action),
			zap.Int("错误码", userResp.ErrCode),
			zap.String("错误信息", userResp.ErrMsg),
			zap.String("接口地址", url))
		return &WeChatAPIError{
			ErrCode: userResp.ErrCode,
			ErrMsg:  userResp.ErrMsg,
		}
	}

	return nil
}
