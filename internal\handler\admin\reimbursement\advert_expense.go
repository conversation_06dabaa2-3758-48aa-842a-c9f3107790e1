package reimbursement

import (
	"github.com/gin-gonic/gin"
	api "marketing/internal/api/reimbursement"
	"marketing/internal/handler"
	"marketing/internal/pkg/errors"
	service "marketing/internal/service/reimbursement"
)

// AdvertExpense handles reimbursement application related requests
type AdvertExpense interface {
	// GetAdvertExpenseQuickPickStat returns quick pick statistics for advert expense
	GetAdvertExpenseQuickPickStat(c *gin.Context)
	// GetAdvertExpenseList returns advert expense list
	GetAdvertExpenseList(c *gin.Context)
	// GetAdvertExpenseDetail returns advert expense detail
	GetAdvertExpenseDetail(c *gin.Context)
	// InvalidAdvertExpense handles advert expense invalid
	InvalidAdvertExpense(c *gin.Context)
	// AuditAdvertExpense handles advert expense audit
	AuditAdvertExpense(c *gin.Context)
	//AdvertExpenseOrderSplit handles splitting advert expense orders
	AdvertExpenseOrderSplit(c *gin.Context)
}

type advert struct {
	policyService        service.PolicyService
	reimbursementService service.AdvertService
}

// NewAdvertExpense creates a new ApplyHandler
func NewAdvertExpense(policyService service.PolicyService, reimbursementService service.AdvertService) AdvertExpense {
	return &advert{
		policyService:        policyService,
		reimbursementService: reimbursementService,
	}
}

// GetAdvertExpenseQuickPickStat returns quick pick statistics for advert expense
func (a *advert) GetAdvertExpenseQuickPickStat(c *gin.Context) {
	// Parse request parameters
	var req api.AdvertExpenseQuickPickStatReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}

	// Call service to get advert expense quick pick statistics
	stats, err := a.reimbursementService.GetAdvertExpenseQuickPickStat(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	// Return statistics
	handler.Success(c, stats)
}

// GetAdvertExpenseList 获取广告费用列表
func (a *advert) GetAdvertExpenseList(c *gin.Context) {
	var req api.AdvertExpenseListReq

	// 绑定查询参数
	if err := c.ShouldBindQuery(&req); err != nil {
		handler.Error(c, errors.NewErr("参数错误: "+err.Error()))
		return
	}

	// 验证必需参数
	if req.PolicyID == 0 {
		handler.Error(c, errors.NewErr("请选择政策"))
		return
	}

	// 验证时间参数
	if (req.StartTime != "" && req.EndTime == "") || (req.StartTime == "" && req.EndTime != "") {
		handler.Error(c, errors.NewErr("时间选择有误"))
		return
	}

	// 调用服务获取数据
	resp, err := a.reimbursementService.GetAdvertExpenseList(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, resp)
}

// GetAdvertExpenseDetail 获取广告费用详情
func (a *advert) GetAdvertExpenseDetail(c *gin.Context) {
	var req api.OrderReq

	// 绑定查询参数
	if err := c.ShouldBindQuery(&req); err != nil {
		handler.Error(c, errors.NewErr("参数错误: "+err.Error()))
		return
	}

	// 调用服务获取详情数据
	detail, err := a.reimbursementService.GetAdvertExpenseDetail(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	if detail == nil {
		handler.Error(c, errors.NewErr("未找到数据"))
		return
	}

	handler.Success(c, detail)
}

// InvalidAdvertExpense 广告费用申请单作废
func (a *advert) InvalidAdvertExpense(c *gin.Context) {
	var req api.OrderReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, errors.NewErr("参数错误"))
		return
	}

	// 执行作废操作
	success, err := a.reimbursementService.InvalidAdvertExpense(c, req.ID)
	if err != nil {
		handler.Error(c, err)
		return
	}

	if success {
		handler.Success(c)
	} else {
		handler.Error(c, errors.NewErr("系统出错"))
	}
}

// AuditAdvertExpense 广告费用申请单审核
func (a *advert) AuditAdvertExpense(c *gin.Context) {
	var req api.AuditAdvertExpenseReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, errors.NewErr("参数错误"))
		return
	}
	if req.Status != 1 && req.Status != -1 {
		handler.Error(c, errors.NewErr("审核状态有误"))
		return
	}
	if req.Status == -1 && req.Remark == "" {
		handler.Error(c, errors.NewErr("请输入审批备注"))
		return
	}

	// 执行审核操作
	if _, err := a.reimbursementService.AuditAdvertExpense(c, &req); err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c)
}

// AdvertExpenseOrderSplit 广告费用订单拆分
func (a *advert) AdvertExpenseOrderSplit(c *gin.Context) {
	var req *api.AdvertExpenseOrderSplitReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, errors.NewErr("参数错误: "+err.Error()))
		return
	}
	if len(req.SplitAmount) <= 1 {
		handler.Error(c, errors.NewErr("拆分金额格式错误"))
		return
	}
	for _, amount := range req.SplitAmount {
		if amount <= 0 {
			handler.Error(c, errors.NewErr("拆分金额必须大于0"))
			return
		}
	}
	// 调用服务进行订单拆分
	err := a.reimbursementService.AdvertExpenseOrderSplit(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c)
}
