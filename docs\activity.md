# 活动管理 API 文档

## 1. 获取活动类型列表

##### 请求URL
- `/admin/activity/type/list`  # 改为 type/list

## 2. 新增活动类型

##### 请求URL
- `/admin/activity/type/save`  # 改为 type/save

## 3. 更新活动类型

##### 请求URL
- `/admin/activity/type/update-name`  # 改为 type/update-name

## 4. 更新活动类型状态

##### 请求URL
- `/admin/activity/type/update-status`  # 改为 type/update-status

## 5. 获取活动详情

##### 请求URL
- `/admin/activity/type/info/:id`  # 改为 type/info/:id 