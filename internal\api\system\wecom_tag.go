package system

import (
	"marketing/internal/api"
	"marketing/internal/pkg/types"
)

type AddWecomTagReq struct {
	ID      uint   `json:"id"`
	TagName string `json:"tag_name" binding:"required"`
}

type WecomTagReq struct {
	TagName string `json:"tag_name"`
	api.PaginationParams
}

type WecomTagResp struct {
	ID        uint             `json:"id"`
	TagName   string           `json:"tag_name"`
	CreatedAt types.CustomTime `json:"created_at"`
}
