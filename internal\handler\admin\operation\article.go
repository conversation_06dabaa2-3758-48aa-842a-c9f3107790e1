package operation

import (
	"errors"
	"marketing/internal/api/operation"
	"marketing/internal/handler"
	"marketing/internal/pkg/e"
	error "marketing/internal/pkg/errors"
	"marketing/internal/pkg/utils"
	"marketing/internal/service"
	operationSvc "marketing/internal/service/operation"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

type ArticleHandle struct {
	svc service.ArticleService
}

func NewArticleHandle(svc service.ArticleService) *ArticleHandle {
	return &ArticleHandle{svc: svc}
}

// 内容
func (a *ArticleHandle) Create(c *gin.Context) {
	var req operation.ArticleCreateReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}

	if req.AttachmentType == operation.ArticleAttachmentTypeLink && req.SyncMarketing == 1 {
		handler.Error(c, errors.New("链接类型不可同步到营销资料"))
		return
	}

	uid := c.GetUint("uid")

	if err := a.svc.CheckUserPublisher(c, uid, req.PublisherID); err != nil {
		handler.Error(c, err)
		return
	}

	req.CreatedBy = uid

	id, err := a.svc.Create(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"id": id,
	})
}

func (a *ArticleHandle) Update(c *gin.Context) {

}

//	func (a *ArticleHandle) Upsert(c *gin.Context) {
//		var req operation.Article
//		if err := c.ShouldBind(&req); err != nil {
//			handler.Error(c, err)
//			return
//		}
//		id, err := a.svc.Upsert(c, req)
//		if err != nil {
//			handler.Error(c, err)
//			return
//		}
//		handler.Success(c, gin.H{
//			"id": id,
//		})
//	}
func (a *ArticleHandle) List(c *gin.Context) {
	var req operation.ArticleListReq
	req.Page = cast.ToInt(c.DefaultQuery("page", "1"))
	req.PageSize = cast.ToInt(c.DefaultQuery("page_size", "10"))
	req.OrderBy = c.DefaultQuery("order_by", "created_at")
	req.Order = c.DefaultQuery("order", "desc")
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	if req.PageSize == 0 {
		req.PageSize = 10
	}
	if req.Page == 0 {
		req.Page = 1
	}

	rsp, err := a.svc.List(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, rsp)
}
func (a *ArticleHandle) Creators(c *gin.Context) {
	rsp, err := a.svc.Creators(c)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, rsp)
}
func (a *ArticleHandle) Detail(c *gin.Context) {
	id := cast.ToUint(c.Param("id"))
	rsp, err := a.svc.Detail(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, rsp)
}
func (a *ArticleHandle) Delete(c *gin.Context) {}

// 评论
func (a *ArticleHandle) CommentList(c *gin.Context) {
	var req operation.ArticleCommentListReq
	req.ID = cast.ToUint(c.Param("id"))
	req.Page = cast.ToInt(c.DefaultQuery("page", "1"))
	req.PageSize = cast.ToInt(c.DefaultQuery("page_size", "10"))
	req.ReplySize = cast.ToInt(c.DefaultQuery("reply_size", "5"))
	req.Type = c.DefaultQuery("type", "all")
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}

	rsp, err := a.svc.CommentList(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, rsp)
}

// 类别
func (a *ArticleHandle) CategoryList(c *gin.Context) {
	rsp, err := a.svc.CategoryList(c)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, rsp)
}

// 标签
func (a *ArticleHandle) TagList(c *gin.Context) {
	rsp, err := a.svc.TagList(c)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, rsp)
}

type OpArticle struct {
	svc operationSvc.OpArticleSvcInterface
}

func NewOpArticle(svc operationSvc.OpArticleSvcInterface) *OpArticle {
	return &OpArticle{
		svc: svc,
	}
}

func (o *OpArticle) GetOpArticleList(c *gin.Context) {
	list, total := o.svc.GetOpArticleList(c, operation.ReqOpArticleParam{
		Keyword:    e.ReqParamStr(c, "keyword"),
		Enabled:    e.ReqParamInt(c, "enabled", -1),
		Top:        e.ReqParamInt(c, "top", -1),
		CreatorID:  uint(e.ReqParamInt(c, "creator_id")),
		CategoryID: uint(e.ReqParamInt(c, "category_id")),
		OrderBy:    e.ReqParamStr(c, "order_by", "created_at"),
		Order:      e.ReqParamStr(c, "order", "desc"),
		PageNum:    e.ReqParamInt(c, "page_num"),
		PageSize:   e.ReqParamInt(c, "page_size"),
	})

	handler.Success(c, gin.H{
		"list":  list,
		"total": total,
	})
}

func (o *OpArticle) EditOpArticle(c *gin.Context) {
	req := operation.ReqCreateArticleParam{
		ID:                  uint(e.ReqParamInt(c, "id")),
		Title:               e.ReqParamStr(c, "title"),
		Content:             e.ReqParamStr(c, "content"),
		Sleight:             e.ReqParamStr(c, "sleight"),
		CategoryID:          uint(e.ReqParamInt(c, "category_id")),
		PublisherID:         uint(e.ReqParamInt(c, "publisher_id")),
		Shareable:           uint8(e.ReqParamInt(c, "shareable")),
		WeWorkShareable:     uint8(e.ReqParamInt(c, "wework_shareable")),
		CommentSetting:      uint8(e.ReqParamInt(c, "comment_setting")),
		Enabled:             uint8(e.ReqParamInt(c, "enabled")),
		Top:                 uint8(e.ReqParamInt(c, "top")),
		AttachmentType:      uint8(e.ReqParamInt(c, "attachment_type")),
		CreatedBy:           c.GetUint("uid"), //  7235
		SyncMarketing:       uint8(e.ReqParamInt(c, "sync_marketing")),
		MarketingEnabled:    uint8(e.ReqParamInt(c, "marketing_enabled")),
		MarketingCategoryId: uint(e.ReqParamInt(c, "marketing_category_id")),
	}

	utils.JsonStrToObjectList(e.ReqParamStr(c, "attachment"), &req.Attachment)
	utils.JsonStrToObjectList(e.ReqParamStr(c, "tag_ids"), &req.TagIds)

	if req.AttachmentType == operation.ArticleAttachmentTypeLink && req.SyncMarketing == 1 {
		handler.Error(c, errors.New("链接类型不可同步到营销资料"))
		return
	}

	if err := o.svc.CheckUserPublisher(c, req.CreatedBy, req.PublisherID); err != nil {
		handler.Error(c, error.NewErr(err.Error()))
		return
	}

	err := o.svc.EditOpArticle(c, req)
	if err != nil {
		handler.Error(c, error.NewErr(err.Error()))
		return
	}

	handler.Success(c, gin.H{})
}

func (o *OpArticle) DeleteOpArticle(c *gin.Context) {
	err := o.svc.DeleteOpArticle(c, e.ReqParamInt(c, "id"))
	if err != nil {
		handler.Error(c, error.NewErr(err.Error()))
		return
	}

	handler.Success(c, gin.H{})
}

// UpdateShareable 更新文章分享状态
func (o *OpArticle) UpdateShareable(c *gin.Context) {
	type Request struct {
		Shareable int  `json:"shareable" form:"shareable" binding:"oneof=0 1"`
		ID        uint `json:"id" form:"id" binding:"required"`
	}
	var req Request
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, error.NewErr(err.Error()))
		return
	}
	id := int(req.ID)
	shareable := uint8(req.Shareable)

	if id == 0 {
		handler.Error(c, error.NewErr("ID 不能为空"))
		return
	}

	err := o.svc.UpdateShareable(c, id, shareable)
	if err != nil {
		handler.Error(c, error.NewErr(err.Error()))
		return
	}

	handler.Success(c, gin.H{})
}

// UpdateEnabled 更新文章发布状态
func (o *OpArticle) UpdateEnabled(c *gin.Context) {
	type Request struct {
		Enabled int  `json:"enabled" form:"enabled" binding:"oneof=0 1"`
		ID      uint `json:"id" form:"id" binding:"required"`
	}
	var req Request
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, error.NewErr(err.Error()))
		return
	}
	id := int(req.ID)
	enabled := uint8(req.Enabled)

	if id == 0 {
		handler.Error(c, error.NewErr("ID 不能为空"))
		return
	}

	err := o.svc.UpdateEnabled(c, id, enabled)
	if err != nil {
		handler.Error(c, error.NewErr(err.Error()))
		return
	}

	handler.Success(c, gin.H{})
}

// UpdateTop 更新文章置顶状态
func (o *OpArticle) UpdateTop(c *gin.Context) {
	type Request struct {
		Top int  `json:"top" form:"top" binding:"oneof=0 1"`
		ID  uint `json:"id" form:"id" binding:"required"`
	}
	var req Request
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, error.NewErr(err.Error()))
		return
	}
	id := int(req.ID)
	top := uint8(req.Top)

	if id == 0 {
		handler.Error(c, error.NewErr("ID 不能为空"))
		return
	}

	err := o.svc.UpdateTop(c, id, top)
	if err != nil {
		handler.Error(c, error.NewErr(err.Error()))
		return
	}

	handler.Success(c, gin.H{})
}
