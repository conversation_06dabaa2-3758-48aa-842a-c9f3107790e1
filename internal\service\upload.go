package service

import (
	"github.com/gin-gonic/gin"
	myErrors "marketing/internal/pkg/errors"
	"marketing/internal/pkg/log"
	"marketing/internal/pkg/upload"
	"math/rand"
	"mime/multipart"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"
)

var (
	OldPrefix = "https://dt1.readboy.com/"
	NowPrefix = "https://static.readboy.com/"
)

type UploadService interface {
	UploadFile(c *gin.Context, dir string, file *multipart.FileHeader, md5 string) (string, error)
	DeleteFile(c *gin.Context, url string) error
}
type OssUploadService struct {
	Oss upload.FileLoadService
}

// UploadFile 上传文件
func (o *OssUploadService) UploadFile(c *gin.Context, dir string, file *multipart.FileHeader, md5 string) (string, error) {
	var fileName string
	//保存到临时目录
	if md5 != "" {
		fileName = md5 + filepath.Ext(file.Filename)
	} else {
		fileName = randName(file.Filename, true)
	}
	savePath := os.TempDir() + "\\" + fileName
	//保存文件到临时目录
	err := c.SaveUploadedFile(file, savePath)
	defer func() {
		err = os.Remove(savePath)
		if err != nil {
			//记录下日志，临时文件删除失败
			log.New().Error("[文件上传]-临时文件删除失败")

		}
	}()
	if err != nil {
		return "", err
	}
	dirName := dir + "/" + fileName
	//上传到oss
	url, err := o.Oss.UploadOne(dirName, savePath)
	if err != nil {
		log.Error("[文件上传]-上传到oss失败" + err.Error())
		return "", myErrors.NewErr("上传失败")
	}
	return url, err
}

// DeleteFile 删除文件
func (o *OssUploadService) DeleteFile(c *gin.Context, url string) error {
	url = o.removePrefix(url)
	if url == "" {
		return myErrors.NewErr("文件地址不合法")
	}
	err := o.Oss.DeleteOne(url)
	if err != nil {
		return err
	}
	return nil
}

// 文件前缀处理
func (o *OssUploadService) removePrefix(url string) string {
	if strings.HasPrefix(url, OldPrefix) {
		url = strings.Replace(url, OldPrefix, "", 1)
	} else if strings.HasPrefix(url, NowPrefix) {
		url = strings.Replace(url, NowPrefix, "", 1)
	} else {
		return ""
	}
	return url
}
func NewOssUploadService(oss upload.FileLoadService) UploadService {
	return &OssUploadService{
		Oss: oss,
	}
}

// 随机文件名
func randName(filename string, randomlyRename bool) string {
	name := filename
	if randomlyRename {
		name = strings.ToLower(strconv.FormatInt(time.Now().UnixNano(), 36) + strconv.FormatInt(int64(rand.Intn(2176782335)), 36))
		name = name + filepath.Ext(filename)
	}
	return name
}
