package operation

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"marketing/internal/api/operation"
	"marketing/internal/model"
	"marketing/internal/pkg/db"
)

type OpUserDao interface {
	CreateOpUser(c *gin.Context, u *model.OpUser) error
	UpdateOpUser(c *gin.Context, userIds []int, uMap map[string]interface{}) error
	GetOpUserList(c *gin.Context, Keyword string, blocked int, pageNum, pageSize int) (list []operation.OpUserInfo, total int64)
	CheckAdminUserIs(c *gin.Context, userIds []int) (hasUserIds []int)
	GetOpUserByUserIds(c *gin.Context, userIds []int) (list []*model.OpUser)
	CountOpUserNum(c *gin.Context) (total int64)
	CountBlockedOpUserNum(c *gin.Context) (total int64)
}

// OpUserDaoImpl 实现 OpUserDao 接口
type OpUserDaoImpl struct {
	db *gorm.DB
}

// NewOpUserDao 创建 OpUserDao 实例
func NewOpUserDao() OpUserDao {
	return &OpUserDaoImpl{
		db: db.GetDB(""),
	}
}

func (d *OpUserDaoImpl) CreateOpUser(c *gin.Context, u *model.OpUser) error {
	return d.db.WithContext(c).Create(u).Error
}

func (d *OpUserDaoImpl) UpdateOpUser(c *gin.Context, userIds []int, uMap map[string]interface{}) error {
	return d.db.WithContext(c).Model(&model.OpUser{}).Where("user_id in (?)", userIds).Updates(uMap).Error
}

func (d *OpUserDaoImpl) GetOpUserList(c *gin.Context, Keyword string, blocked int, pageNum, pageSize int) (list []operation.OpUserInfo, total int64) {
	query := d.db.WithContext(c).Table("admin_users").
		Select("admin_users.id,name,username,IFNULL(op_user.blocked, 0) blocked").
		Joins("left join op_user on admin_users.id = op_user.user_id").
		Where("admin_users.status = 1")

	if blocked > 0 {
		query = query.Where("op_user.blocked = 1")
	} else {
		query = query.Where("op_user.blocked = 0 or op_user.blocked is null")
	}

	if len(Keyword) > 0 {
		query = query.Where("admin_users.name like ? or admin_users.username like ?", "%"+Keyword+"%", "%"+Keyword+"%")
	}

	if pageNum < 1 {
		pageNum = 1
	}

	if pageSize < 1 {
		pageSize = 20
	}

	query.Count(&total)
	query.Offset((pageNum - 1) * pageSize).Limit(pageSize).Order("admin_users.id desc").Find(&list)

	return
}

func (d *OpUserDaoImpl) CheckAdminUserIs(c *gin.Context, userIds []int) (hasUserIds []int) {
	var list []struct {
		Id int `gorm:"column:id"`
	}

	d.db.WithContext(c).Table("admin_users").Where("id in (?) and status = 1", userIds).Find(&list)

	for _, l := range list {
		hasUserIds = append(hasUserIds, l.Id)
	}

	return
}

func (d *OpUserDaoImpl) GetOpUserByUserIds(c *gin.Context, userIds []int) (list []*model.OpUser) {
	d.db.WithContext(c).Model(&model.OpUser{}).Where("user_id in (?)", userIds).Find(&list)
	return
}

func (d *OpUserDaoImpl) CountOpUserNum(c *gin.Context) (total int64) {
	query := d.db.WithContext(c).Table("admin_users").
		Select("admin_users.id,name,username,IFNULL(ou.blocked, 0) blocked").
		Joins("left join op_user on admin_users.id = op_user.user_id").
		Where("admin_users.status = 1")
	query.Count(&total)
	return
}

func (d *OpUserDaoImpl) CountBlockedOpUserNum(c *gin.Context) (blockNum int64) {
	query := d.db.WithContext(c).Table("admin_users").
		Select("admin_users.id,name,username,IFNULL(ou.blocked, 0) blocked").
		Joins("left join op_user on admin_users.id = op_user.user_id").
		Where("admin_users.status = 1 and op_user.blocked = 1")
	query.Count(&blockNum)
	return
}
