package dao

import (
	"marketing/internal/model"
	"marketing/internal/pkg/db"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type AgencyDao interface {
	CreateAgency(c *gin.Context, agency *model.Agency, regionIds, kingDeeIds []int) error
	DeleteAgency(c *gin.Context, id uint) error
	UpdateAgency(c *gin.Context, id uint, uMap map[string]interface{}, delKingDeeIds, delRegionIds, addKingDeeIds, addRegionIds []int) error
	GetAgencyList(c *gin.Context) (list []*model.Agency)
	GetAgencyById(c *gin.Context, id uint) *model.Agency
	GetAgencyByIds(c *gin.Context, ids []int) (list []*model.Agency)
	GetAgencyKingDeeByAgencyId(c *gin.Context, agencyId int) (list []*model.AgencyKingDee)
	GetAgencyRegionByAgencyId(c *gin.Context, agencyId int) (list []*model.AgencyRegion)
	GetAgencyKingDeeList(c *gin.Context) (list []*model.AgencyKingDeeInfo)
	GetAgencyCompanyList(c *gin.Context, agencyId int) (list []*model.AgencyCompanyInfo, err error)
	GetAgencyRegionList(c *gin.Context) (list []*model.AgencyRegionHistory)
}

// AgencyDaoImpl 实现 AgencyDao 接口
type AgencyDaoImpl struct {
	db *gorm.DB
}

// NewAgencyDao 创建 AgencyDao 实例
func NewAgencyDao() AgencyDao {
	return &AgencyDaoImpl{
		db: db.GetDB(""),
	}
}

func (d *AgencyDaoImpl) CreateAgency(c *gin.Context, agency *model.Agency, kingDeeIds, regionIds []int) error {
	tx := d.db.Begin()

	if dbErr := tx.WithContext(c).Create(agency).Error; dbErr != nil {
		tx.Rollback()
		return dbErr
	}

	for _, kingDeeId := range kingDeeIds {
		agencyKingDee := &model.AgencyKingDee{
			AgencyId:  int(agency.ID),
			KingDeeId: kingDeeId,
		}
		if dbErr := tx.WithContext(c).Create(agencyKingDee).Error; dbErr != nil {
			tx.Rollback()
			return dbErr
		}
	}

	for _, regionId := range regionIds {
		agencyRegion := &model.AgencyRegion{
			AgencyId: int(agency.ID),
			RegionId: regionId,
		}
		if dbErr := tx.WithContext(c).Create(agencyRegion).Error; dbErr != nil {
			tx.Rollback()
			return dbErr
		}
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}

	return nil
}

func (d *AgencyDaoImpl) DeleteAgency(c *gin.Context, id uint) error {
	tx := d.db.WithContext(c).Begin()

	if dbErr := tx.Delete(&model.Agency{}, "id = ?", id).Error; dbErr != nil {
		tx.Rollback()
		return dbErr
	}

	if dbErr := tx.Delete(&model.AgencyKingDee{}, "agency_id = ?", id).Error; dbErr != nil {
		tx.Rollback()
		return dbErr
	}

	if dbErr := tx.Delete(&model.AgencyRegion{}, "agency_id = ?", id).Error; dbErr != nil {
		tx.Rollback()
		return dbErr
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}

	return nil
}

func (d *AgencyDaoImpl) UpdateAgency(c *gin.Context, id uint, uMap map[string]interface{}, delKingDeeIds, delRegionIds, addKingDeeIds, addRegionIds []int) error {
	tx := d.db.WithContext(c).Begin()

	if dbErr := tx.Model(&model.Agency{}).Where("id = ?", id).Updates(uMap).Error; dbErr != nil {
		tx.Rollback()
		return dbErr
	}

	if len(delKingDeeIds) > 0 {
		if dbErr := tx.Delete(&model.AgencyKingDee{}, "agency_id = ? and kingdee_id in (?)", id, delKingDeeIds).Error; dbErr != nil {
			tx.Rollback()
			return dbErr
		}
	}

	if len(delRegionIds) > 0 {
		if dbErr := tx.Delete(&model.AgencyRegion{}, "agency_id = ? and region_id in (?)", id, delRegionIds).Error; dbErr != nil {
			tx.Rollback()
			return dbErr
		}
	}

	if len(addKingDeeIds) > 0 {
		for _, addKingDeeId := range addKingDeeIds {
			agencyKingDee := new(model.AgencyKingDee)
			agencyKingDee.AgencyId = int(id)
			agencyKingDee.KingDeeId = addKingDeeId
			if dbErr := tx.Create(agencyKingDee).Error; dbErr != nil {
				tx.Rollback()
				return dbErr
			}
		}
	}

	if len(addRegionIds) > 0 {
		for _, addRegionId := range addRegionIds {
			agencyRegion := new(model.AgencyRegion)
			agencyRegion.AgencyId = int(id)
			agencyRegion.RegionId = addRegionId
			if dbErr := tx.Create(agencyRegion).Error; dbErr != nil {
				tx.Rollback()
				return dbErr
			}
		}
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}

	return nil
}

func (d *AgencyDaoImpl) GetAgencyList(c *gin.Context) (list []*model.Agency) {
	d.db.WithContext(c).Model(&model.Agency{}).Where("deleted_at is null").Find(&list)
	return
}

func (d *AgencyDaoImpl) GetAgencyById(c *gin.Context, id uint) *model.Agency {
	var agency model.Agency
	err := d.db.WithContext(c).Model(&model.Agency{}).Where("id = ? and deleted_at is null", id).First(&agency).Error
	if err != nil {
		return nil
	}
	return &agency
}

func (d *AgencyDaoImpl) GetAgencyByIds(c *gin.Context, ids []int) (list []*model.Agency) {
	d.db.WithContext(c).Model(&model.Agency{}).Where("id in (?) and deleted_at is null", ids).Find(&list)
	return
}

func (d *AgencyDaoImpl) GetAgencyKingDeeByAgencyId(c *gin.Context, agencyId int) (list []*model.AgencyKingDee) {
	d.db.WithContext(c).Model(&model.AgencyKingDee{}).Where("agency_id = ?", agencyId).Find(&list)
	return
}

func (d *AgencyDaoImpl) GetAgencyRegionByAgencyId(c *gin.Context, agencyId int) (list []*model.AgencyRegion) {
	d.db.WithContext(c).Model(&model.AgencyRegion{}).Where("agency_id = ?", agencyId).Find(&list)
	return
}

func (d *AgencyDaoImpl) GetAgencyKingDeeList(c *gin.Context) (list []*model.AgencyKingDeeInfo) {
	d.db.WithContext(c).Model(&model.AgencyKingDee{}).
		Select("agency_id,kingdee_id,name,short_name").
		InnerJoins("join kingdee_agency on agency_kingdee.kingdee_id = kingdee_agency.id").
		Find(&list)
	return
}

func (d *AgencyDaoImpl) GetAgencyRegionList(c *gin.Context) (list []*model.AgencyRegionHistory) {
	sql := "SELECT max(agency_region_history.id) FROM `agency` " +
		"left join agency_region on agency.id = agency_region.agency_id " +
		"left join agency_region_history on agency_region.region_id = agency_region_history.region_id " +
		"where agency.deleted_at is null group by agency_region_history.agency_id,agency_region_history.region_id"
	d.db.WithContext(c).Model(&model.AgencyRegionHistory{}).Where("id in(" + sql + ")").Find(&list)
	return
}

// GetAgencyCompanyList 获取代理公司列表
func (d *AgencyDaoImpl) GetAgencyCompanyList(c *gin.Context, agencyId int) (list []*model.AgencyCompanyInfo, err error) {
	query := d.db.WithContext(c).
		Table("kingdee_agency ka").
		Select("ka.id as company_id, ka.name as company_name, a.id as agency_id, a.name as agency_name").
		Joins("RIGHT JOIN agency_kingdee ak ON ka.id = ak.kingdee_id").
		Joins("RIGHT JOIN agency a ON ak.agency_id = a.id").
		Where("a.pid = ? AND a.deleted_at IS NULL", 0)

	// 如果传入了 agencyId，添加筛选条件
	if agencyId > 0 {
		query = query.Where("a.id = ?", agencyId)
	}

	err = query.Scan(&list).Error
	return
}
