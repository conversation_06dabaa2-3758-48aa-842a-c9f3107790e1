package model

import (
	"time"
)

// TModelCategory 机型分类表
type TModelCategory struct {
	Id         int       `json:"id" gorm:"column:id"`       // id
	Name       string    `json:"name" gorm:"column:name"`   // 名称
	Pid        int       `json:"pid" gorm:"column:pid"`     // 父id
	Level      int       `json:"level" gorm:"column:level"` // 等级
	Order      int       `json:"order" gorm:"column:order"` // 排序
	Image      string    `json:"image" gorm:"column:image"` // 图片
	CreatedAt  time.Time `json:"-" gorm:"created_at"`       // CreatedAt 创建时间
	CreateTime string    `json:"create_time" gorm:"-"`      // 创建时间
	UpdatedAt  time.Time `json:"-" gorm:"updated_at"`       // UpdatedAt 修改时间
	UpdateTime string    `json:"update_time" gorm:"-"`      // 修改时间
}

func (TModelCategory) TableName() string {
	return "model_category"
}

// RepairMachineCategory 维修机型分类表
type RepairMachineCategory struct {
	Id      int    `json:"id" gorm:"column:id"`           // id
	Name    string `json:"name" gorm:"column:name"`       // 名称
	Visible int    `json:"visible" gorm:"column:visible"` // 是否启用故障表 0:不启用 1:启用
	Image   string `json:"-" gorm:"column:image"`         // 图标
}

func (RepairMachineCategory) TableName() string {
	return "machine_category"
}
