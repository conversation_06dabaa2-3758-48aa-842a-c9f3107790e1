package agency

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/handler"
	"marketing/internal/pkg/e"
	"marketing/internal/pkg/errors"
	"marketing/internal/service"
)

// Channel 是处理渠道 HTTP 请求的结构体
type Channel struct {
	svc service.ChannelSvc
}

func NewChannel(svc service.ChannelSvc) *Channel {
	return &Channel{
		svc: svc,
	}
}

func (channel *Channel) GetChannelList(c *gin.Context) {
	code := e.ReqParamStr(c, "code")
	pageNum := e.ReqParamInt(c, "page_num")
	pageSize := e.ReqParamInt(c, "page_size")
	list, total := channel.svc.GetChannelList(c, code, pageNum, pageSize)
	handler.Success(c, gin.H{
		"list":  list,
		"total": total,
	})
}

func (channel *Channel) GetAllChannel(c *gin.Context) {
	list := channel.svc.GetAllChannelList(c)
	handler.Success(c, gin.H{
		"list": list,
	})
}

func (channel *Channel) GetChannelById(c *gin.Context) {
	id := e.ReqParamInt(c, "id")
	channelInfo := channel.svc.GetChannelById(c, id)
	if channelInfo == nil {
		handler.Error(c, errors.NewErr("渠道不存在"))
		return
	}

	handler.Success(c, channelInfo)
}

func (channel *Channel) EditChannel(c *gin.Context) {
	id := e.ReqParamInt(c, "id")
	code := e.ReqParamStr(c, "code")
	name := e.ReqParamStr(c, "name")
	err := channel.svc.EditChannel(c, id, code, name)
	if err != nil {
		handler.Error(c, errors.NewErr(err.Error()))
		return
	}

	handler.Success(c, gin.H{})
}

func (channel *Channel) DeleteChannel(c *gin.Context) {
	id := e.ReqParamInt(c, "id")
	err := channel.svc.DeleteChannel(c, id)
	if err != nil {
		handler.Error(c, errors.NewErr(err.Error()))
		return
	}

	handler.Success(c, gin.H{})
}
