package information

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/api/materials"
	"marketing/internal/handler"
	"marketing/internal/service"
)

type PaperHandle struct {
	svc service.PaperService
}

func (h *PaperHandle) List(c *gin.Context) {
	var param materials.PaperListReq
	if err := c.ShouldBind(&param); err != nil {
		handler.Error(c, err)
		return
	}
	if param.Page == 0 {
		param.Page = 1
	}
	if param.PageSize == 0 {
		param.PageSize = 10
	}
	list, total, err := h.svc.List(c, param)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"list":      list,
		"total":     total,
		"page":      param.Page,
		"page_size": param.PageSize,
	})
}

func (h *PaperHandle) Detail(c *gin.Context) {
	id := c.Param("id")
	detail, err := h.svc.Detail(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, detail)
}

func (h *PaperHandle) Upsert(c *gin.Context) {
	var paper materials.PaperUpsertReq
	if err := c.ShouldBindJSON(&paper); err != nil {
		handler.Error(c, err)
		return
	}
	id, err := h.svc.Upsert(c, paper)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"id": id,
	})
}

// Delete 删除试卷
func (h *PaperHandle) Delete(c *gin.Context) {
	id := c.Param("id")
	if err := h.svc.Delete(c, id); err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

func (h *PaperHandle) Status(c *gin.Context) {
	var param materials.PaperStatusReq
	if err := c.ShouldBindJSON(&param); err != nil {
		handler.Error(c, err)
		return
	}
	if err := h.svc.Status(c, param); err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

func (h *PaperHandle) QuestionUpsert(c *gin.Context) {
	var question materials.QuestionUpsertReq
	if err := c.ShouldBindJSON(&question); err != nil {
		handler.Error(c, err)
		return
	}
	id, err := h.svc.QuestionUpsert(c, question)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"id": id,
	})
}

func (h *PaperHandle) QuestionTips(c *gin.Context) {
	tips, err := h.svc.QuestionTips(c)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"tips": tips,
	})
}

func (h *PaperHandle) QuestionList(c *gin.Context) {
	var param materials.QuestionListReq
	if err := c.ShouldBind(&param); err != nil {
		handler.Error(c, err)
		return
	}
	if param.Page == 0 {
		param.Page = 1
	}
	if param.PageSize == 0 {
		param.PageSize = 10
	}
	list, total, err := h.svc.QuestionList(c, param)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"list":      list,
		"total":     total,
		"page":      param.Page,
		"page_size": param.PageSize,
	})

}

func (h *PaperHandle) QuestionDetail(c *gin.Context) {
	id := c.Param("id")
	detail, err := h.svc.QuestionDetail(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, detail)
}

func (h *PaperHandle) QuestionDelete(c *gin.Context) {
	id := c.Param("id")
	if err := h.svc.QuestionDelete(c, id); err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

func (h *PaperHandle) QuestionTypeTips(c *gin.Context) {
	tips, err := h.svc.QuestionTypeTips(c)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"tips": tips,
	})
}

func NewPaperHandle(svc service.PaperService) *PaperHandle {
	return &PaperHandle{svc: svc}
}
