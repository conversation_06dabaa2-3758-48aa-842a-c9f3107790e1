package aftersales

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/handler"
	"marketing/internal/pkg/e"
	"marketing/internal/pkg/errors"
	"marketing/internal/service/aftersales"
)

type TMachineMalfunction struct {
	svc aftersales.TMachineMalfunctionSvcInterface
}

func NewMachineMalfunction(svc aftersales.TMachineMalfunctionSvcInterface) *TMachineMalfunction {
	return &TMachineMalfunction{
		svc: svc,
	}
}

func (m *TMachineMalfunction) MachineMalfunctionList(c *gin.Context) {
	list := m.svc.GetAllMachineMalfunction(c)
	handler.Success(c, gin.H{
		"list": list,
	})
}

func (m *TMachineMalfunction) RefreshMachineMalfunction(c *gin.Context) {
	list := m.svc.RefreshMachineMalfunction(c)
	handler.Success(c, gin.H{
		"list": list,
	})
}

func (m *TMachineMalfunction) EditMachineMalfunction(c *gin.Context) {
	id := e.ReqParamInt(c, "id", -1)
	pid := e.ReqParamInt(c, "pid")
	title := e.ReqParamStr(c, "title")
	description := e.ReqParamStr(c, "description")
	order := e.ReqParamInt(c, "order")

	if id != -1 {
		err := m.svc.EditMachineMalfunction(c, id, pid, title, description, order)
		if err != nil {
			handler.Error(c, errors.NewErr(err.Error()))
			return
		}
	} else {
		err := m.svc.AddMachineMalfunction(c, pid, title, description, order)
		if err != nil {
			handler.Error(c, errors.NewErr(err.Error()))
			return
		}
	}

	handler.Success(c, gin.H{})
}

func (m *TMachineMalfunction) DeleteMachineMalfunction(c *gin.Context) {
	id := e.ReqParamInt(c, "id")
	err := m.svc.DelMachineMalfunction(c, id)
	if err != nil {
		handler.Error(c, errors.NewErr("删除物料标签失败"))
		return
	}

	handler.Success(c, gin.H{})
}
