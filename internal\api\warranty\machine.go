package warranty

import (
	"marketing/internal/api"
	"marketing/internal/model"
	"time"
)

type MachineLifeResp struct {
	WarrantiesInfo      *WarrantiesResp            `json:"warranties_info"`
	ActivatedDeviceInfo []*ActivatedDeviceInfo     `json:"activated_device_info"`
	PrototypeInfo       []*PrototypeForMachineLife `json:"prototype_info"`
	PackInfo            []*MesPackInfo             `json:"pack_info"`
	K3Info              *K3Info                    `json:"k3_info"`
}

type MachineAccessoryResponse struct {
	Price    string `json:"price"`
	Title    string `json:"title"`
	Count    uint   `json:"count"`
	IsCharge uint8  `json:"is_charge"`
}

type MachinesHistoriesReq struct {
	api.PaginationParams
	Barcode   string `form:"barcode,omitempty" json:"barcode,omitempty"`
	Model     string `form:"model,omitempty" json:"model,omitempty"`
	Number    string `form:"number,omitempty" json:"number,omitempty"`
	NumberNew string `form:"number_new,omitempty" json:"numberNew,omitempty"`
	From      *int   `form:"from,omitempty" json:"from,omitempty"`     // 来源：0-未知，1-寄修服务，2-用户反馈
	Status    *int   `form:"status,omitempty" json:"status,omitempty"` // 处理状态 0-已处理，1-未处理
}

type MachinesHistoriesResp struct {
	Page     int                     `json:"page"`
	PageSize int                     `json:"page_size"`
	Total    int64                   `json:"total"`
	List     []*model.DevicesHistory `json:"list"`
}

type MachineHistoryResp struct {
	Id         uint       `json:"id"`
	ModelId    int        `json:"model_id"`
	Model      string     `json:"model"`
	Barcode    string     `json:"barcode"`
	Number     string     `json:"number"`
	NumberNew  string     `json:"number_new"`
	Remark     string     `json:"remark"`
	From       int        `json:"from"`
	Status     int        `json:"status"`
	AddTime    *time.Time `json:"add_time"`
	UpdateTime *time.Time `json:"update_time"`
}

type PrototypeForMachineLife struct {
	Prototype model.Prototype `gorm:"embedded;" json:"prototype"`
	// endpoint
	EndpointName string `json:"endpoint_name"`
	// admin_user
	AdminUserName string `json:"admin_user_name"`
	// agency
	TopAgency    string `json:"top_agency"`
	SecondAgency string `json:"second_agency"`
	// order
	OrderTime time.Time `json:"order_time"`
}

type MachineHistoryUpdReq struct {
	Barcode   string `json:"barcode" binding:"required"`
	NumberNew string `json:"number_new" binding:"required"`
	Remark    string `json:"remark"`
}

type ActivatedDeviceInfo struct {
	Model     string `json:"model"`
	Imei      string `json:"imei"`
	Number    string `json:"number"`
	Barcode   string `json:"barcode"`
	NavModel  string `json:"nav_model"`
	Origin    string `json:"origin"`
	Status    int    `json:"status"`
	BindAt    string `json:"bindat"`
	Location  string `json:"location"`
	IP        string `json:"ip"`
	CreatedAt string `json:"createat"`
	Address   string `json:"address"`
}

type MesPackInfo struct {
	Model    string `json:"model"`
	Imei     string `json:"imei"`
	Number   string `json:"number"`
	Barcode  string `json:"barcode"`
	PackTime string `json:"pack_time"`
	AddTime  string `json:"add_time"`
}

type K3Info struct {
	InStock     []*K3InStock     `json:"k3_in_stock"`
	OutStock    []*K3OutStock    `json:"k3_out_stock"`
	ReturnStock []*K3Returnstock `json:"k3_return_stock"`
	AllotStock  []*K3Allotstock  `json:"k3_allot_stock"`
}

type K3InStock struct {
	Model    string `json:"model"`
	Barcode  string `json:"barcode"`
	Number   string `json:"number"`
	Imei     string `json:"imei"`
	BillDate string `json:"bill_date"`
}

type K3OutStock struct {
	Model    string `json:"model"`
	Barcode  string `json:"barcode"`
	Number   string `json:"number"`
	Imei     string `json:"imei"`
	BillDate string `json:"bill_date"`
	CustName string `json:"cust_name"`
	CustCode string `json:"cust_code"`
}

type K3Returnstock struct {
	Model    string `json:"model"`
	Barcode  string `json:"barcode"`
	Number   string `json:"number"`
	Imei     string `json:"imei"`
	BillDate string `json:"bill_date"`
	CustName string `json:"cust_name"`
	CustCode string `json:"cust_code"`
}

type K3Allotstock struct {
	Model       string `json:"model"`
	Barcode     string `json:"barcode"`
	Number      string `json:"number"`
	Imei        string `json:"imei"`
	CustCodeOld string `json:"cust_code_old"`
	CustNameOld string `json:"cust_name_old"`
	CustCode    string `json:"cust_code"`
	CustName    string `json:"cust_name"`
	BillDate    string `json:"bill_date"`
}
