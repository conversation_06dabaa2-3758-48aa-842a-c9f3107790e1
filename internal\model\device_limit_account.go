package model

import (
	"time"
)

type DeviceLimitAccount struct {
	ID        uint32     `gorm:"primaryKey;autoIncrement" json:"id"`
	Model     string     `gorm:"type:varchar(32);default:''" json:"model"`             // 机型
	Barcode   string     `gorm:"type:varchar(32);not null;uniqueIndex" json:"barcode"` // 条码SN
	Number    string     `gorm:"type:varchar(48);not null;uniqueIndex" json:"number"`  // 序列号
	Phone     string     `gorm:"type:varchar(100)" json:"phone"`                       // 电话号码
	Remark    string     `gorm:"type:varchar(200);not null;default:''" json:"remark"`  // 备注说明
	Status    *int       `gorm:"type:tinyint(4);not null;default:1" json:"status"`     // 状态：1-正常，0-禁用，-1待添加手机号码
	CreatedAt *time.Time `gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt *time.Time `gorm:"type:timestamp;default:NULL ON UPDATE CURRENT_TIMESTAMP" json:"updated_at"`
}

func (DeviceLimitAccount) TableName() string { return "device_limit_account" }
