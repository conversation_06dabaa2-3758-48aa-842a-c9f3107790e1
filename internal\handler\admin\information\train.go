package information

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/api/materials"
	"marketing/internal/handler"
	"marketing/internal/service"
)

type TrainHandle struct {
	svc service.TrainService
}

func (h *TrainHandle) Upsert(c *gin.Context) {
	var param materials.KindAddReq
	if err := c.ShouldBindJSON(&param); err != nil {
		handler.Error(c, err)
		return
	}
	id, err := h.svc.Upsert(c, param, "train_type")
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"id": id,
	})
}

func (h *TrainHandle) Save(c *gin.Context) {
	var param []materials.KindSaveReq
	if err := c.ShouldBindJSON(&param); err != nil {
		handler.Error(c, err)
		return
	}
	if err := h.svc.Save(c, param, "train_type"); err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

func (h *TrainHandle) List(c *gin.Context) {
	list, err := h.svc.List(c, "train_type")
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, list)
}

func (h *TrainHandle) Detail(c *gin.Context) {
	id := c.Param("id")
	detail, err := h.svc.Detail(c, id, "train_type")
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"detail": detail,
	})
}

func (h *TrainHandle) Tips(c *gin.Context) {
	tips, err := h.svc.Tips(c, "train_type")
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, tips)
}

func (h *TrainHandle) Delete(c *gin.Context) {
	id := c.Param("id")
	if err := h.svc.Delete(c, id, "train_type"); err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

func (h *TrainHandle) InfoList(c *gin.Context) {
	var param materials.TrainInfoListReq
	if err := c.ShouldBind(&param); err != nil {
		handler.Error(c, err)
		return
	}
	if param.Page <= 0 {
		param.Page = 1
	}
	if param.PageSize <= 0 {
		param.PageSize = 10
	}
	list, total, err := h.svc.InfoList(c, param)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"list":      list,
		"total":     total,
		"page":      param.Page,
		"page_size": param.PageSize,
	})
}

func (h *TrainHandle) InfoUpdate(c *gin.Context) {
	var param materials.TrainInfoUpdateReq
	if err := c.ShouldBindJSON(&param); err != nil {
		handler.Error(c, err)
		return
	}
	if err := h.svc.InfoUpdate(c, param); err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

func (h *TrainHandle) InfoUpdates(c *gin.Context) {
	var param materials.TrainInfoUpdate[[]string]
	if err := c.ShouldBindJSON(&param); err != nil {
		handler.Error(c, err)
		return
	}
	if err := h.svc.InfoUpdates(c, param); err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

func (h *TrainHandle) InfoDetail(c *gin.Context) {
	id := c.Param("id")
	detail, err := h.svc.InfoDetail(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"detail": detail,
	})
}

func (h *TrainHandle) VideoUpsert(c *gin.Context) {
	var param materials.VideoAddReq[[]string]
	if err := c.ShouldBindJSON(&param); err != nil {
		handler.Error(c, err)
		return
	}
	id, err := h.svc.VideoUpsert(c, param)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"id": id,
	})
}

func (h *TrainHandle) VideoDelete(c *gin.Context) {
	id := c.Param("id")
	if err := h.svc.VideoDelete(c, id); err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

func (h *TrainHandle) VideoList(c *gin.Context) {
	var param materials.TrainVideoListReq
	if err := c.ShouldBind(&param); err != nil {
		handler.Error(c, err)
		return
	}
	if param.Page <= 0 {
		param.Page = 1
	}
	if param.PageSize <= 0 {
		param.PageSize = 10
	}
	list, total, err := h.svc.VideoList(c, param)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"list":      list,
		"total":     total,
		"page":      param.Page,
		"page_size": param.PageSize,
	})
}

func (h *TrainHandle) VideoDetail(c *gin.Context) {
	id := c.Param("id")
	detail, err := h.svc.VideoDetail(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"detail": detail,
	})
}

func (h *TrainHandle) AllTips(c *gin.Context) {
	list, err := h.svc.AllTips(c, "train_type")
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, list)
}

func (h *TrainHandle) Upload(c *gin.Context) {
	id := c.Param("id")
	if err := h.svc.Upload(c, id); err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

func NewTrainHandle(svc service.TrainService) *TrainHandle {
	return &TrainHandle{svc: svc}
}
