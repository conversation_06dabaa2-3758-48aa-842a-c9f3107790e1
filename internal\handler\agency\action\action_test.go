package action

import (
	"bytes"
	"context"
	"encoding/json"
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"go.uber.org/zap"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"marketing/internal/api/action"
	"marketing/internal/dao"
	"marketing/internal/model"
	"marketing/internal/pkg/log"
	"marketing/internal/pkg/utils"
	"marketing/internal/service"
	"marketing/internal/service/cache"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"
)

type TestActionTestSuit struct {
	suite.Suite
	server *gin.Engine
	db     *gorm.DB
	redis  redis.Cmdable
}

func TestAction(t *testing.T) {
	suite.Run(t, &TestActionTestSuit{})
}

func (s *TestActionTestSuit) SetupSuite() {
	s.db = commonDB()
	s.redis = commonRedis()
	server := gin.Default()
	r := server.Group("/agency")
	r.Use(func(c *gin.Context) {
		c.Set("uid", 1666)
	})
	{
		activityRouter := r.Group("/activity")
		activityDao := dao.NewTestActionGorm(s.db)
		actionDao := dao.NewTestActionTypeDao(s.db)
		redisCache := cache.NewActionRedisCache(s.redis)
		activityService := service.NewGormActionService(activityDao, actionDao, redisCache)
		activityController := NewHandleActionAgency(activityService)
		activityRouter.POST("/action/apply", activityController.ApplyAction)
		activityRouter.POST("/action/finish", activityController.FinishAction)
		activityRouter.POST("/action/delete", activityController.DeleteAction)
		activityRouter.POST("/action/drop_down", activityController.DropDown)
		activityRouter.POST("/action/update", activityController.UpdateAction)
	}
	s.server = server
}

// 活动申请测试
func (s *TestActionTestSuit) TestApplyAction01() {
	T := s.T()
	testCases := []struct {
		name string
		// 要提前准备数据
		before func(t *testing.T)
		// 验证并且删除数据
		after func(t *testing.T)
		act   action.ApplyActionV1
		// 预期响应
		wantCode int
		wantRes  Result[map[string]any]
	}{
		{
			name: "申请活动-旧时间类型-成功",
			before: func(t *testing.T) {
				s.db.Exec("TRUNCATE TABLE actions")
				s.db.Exec("TRUNCATE TABLE action_type")
				s.db.Create(&model.ActionType{
					ID:        1,
					Name:      "旧时间类型1",
					Enabled:   1,
					StartDate: "12-01",
					EndDate:   "12-31",
					Slug:      "TEST",
					Ceiling:   100,
				})
				s.redis.Del(context.TODO(), "action:apply:1")
			},
			after: func(t *testing.T) {
				var act model.Actions
				//输入数据校验
				s.db.Table("actions").Where("id = ?", 3).Find(&act)
				act.DateStart = act.DateStart[:10]
				act.DateEnd = act.DateEnd[:10]
				assert.Equal(t, "2024-12-02", act.DateStart)
				assert.Equal(t, "2024-12-10", act.DateEnd)
				assert.Equal(t, "1234567890", act.Phone)
				assert.Equal(t, "A公司", act.WriteOffCorporation)
				assert.Equal(t, "测试地点", act.Location)
				assert.Equal(t, "这是一个测试计划", act.Plan)
				assert.Equal(t, "一些工作人员", act.Staff)
				assert.Equal(t, "一个测试活动描述", act.Description)
				photo, _ := utils.UrlImageTrans(act.SitePhoto)
				assert.Equal(t, "https://static.readboy.com/aa/bb/cc", photo[0])
				assert.Equal(t, "约翰·多伊", act.Principal)
				assert.Equal(t, "寒假活动", act.Name)
				assert.Equal(t, 1, act.Type)
				assert.Equal(t, 1, act.ActivityID)
				assert.Equal(t, uint(1666), act.UID)
				assert.Equal(t, uint(100), act.Space)
				assert.Equal(t, uint8(0), act.PayGiftSupport)
				assert.Equal(t, uint(111), act.EndpointID)
				assert.Equal(t, uint(222), act.SecondAgencyID)
				assert.Equal(t, uint(333), act.TopAgencyID)
				//状态校验
				assert.Equal(t, uint8(1), act.Status)
				assert.Equal(t, uint(1666), act.UID)
				assert.NotEqual(t, "", act.Number)
			},
			act: action.ApplyActionV1{
				ApplyAction: action.ApplyAction{
					Name:                "寒假活动",
					Level:               1,
					Phone:               "1234567890",
					Principal:           "约翰·多伊",
					Plan:                "这是一个测试计划",
					Staff:               "一些工作人员",
					SitePhoto:           []string{"https://static.readboy.com/aa/bb/cc"},
					Space:               100,
					DateStart:           "2024-12-02",
					DateEnd:             "2024-12-10",
					WriteOffCorporation: "A公司",
					Location:            "测试地点",
					Type:                1,
					EndpointID:          111,
					SecondAgencyID:      222,
					TopAgencyID:         333,
				},
			},
			wantCode: http.StatusOK,
			wantRes: Result[map[string]any]{
				OK:      1,
				Message: "ok",
				Data: map[string]any{
					"id": float64(3),
				},
			},
		},
		{
			name: "申请活动-新类型-成功",
			before: func(t *testing.T) {
				s.db.Exec("TRUNCATE TABLE actions")
				s.db.Exec("TRUNCATE TABLE action_type")
				s.db.Create(&model.ActionType{
					ID:              1,
					Name:            "新类型1",
					Enabled:         1,
					StartDate:       time.Now().Format("2006-01-02"),
					EndDate:         time.Now().AddDate(0, 0, 30).Format("2006-01-02"),
					Slug:            "TEST1",
					Ceiling:         100,
					GiftSupport:     1,
					Lines:           1000,
					EndpointCeiling: 1,
				})
				//删除缓存
				s.redis.Del(context.TODO(), "action:apply:1")
			},
			after: func(t *testing.T) {
				var act model.Actions
				//输入数据校验
				s.db.Table("actions").Where("id = ?", 1).Find(&act)
				act.DateStart = act.DateStart[:10]
				act.DateEnd = act.DateEnd[:10]
				assert.Equal(t, time.Now().AddDate(0, 0, 6).Format("2006-01-02"), act.DateStart)
				assert.Equal(t, time.Now().AddDate(0, 0, 10).Format("2006-01-02"), act.DateEnd)
				assert.Equal(t, "1234567890", act.Phone)
				assert.Equal(t, "A公司", act.WriteOffCorporation)
				assert.Equal(t, "测试地点", act.Location)
				assert.Equal(t, "这是一个测试计划", act.Plan)
				assert.Equal(t, "一些工作人员", act.Staff)
				assert.Equal(t, `["aa/bb/cc"]`, act.SitePhoto)
				assert.Equal(t, "约翰·多伊", act.Principal)
				assert.Equal(t, "寒假活动", act.Name)
				assert.Equal(t, 1, act.Type)
				assert.Equal(t, uint(1666), act.UID)
				assert.Equal(t, uint(100), act.Space)
				assert.Equal(t, uint(111), act.EndpointID)
				assert.Equal(t, uint(333), act.SecondAgencyID)
				assert.Equal(t, uint(222), act.TopAgencyID)
				//状态校验
				assert.Equal(t, uint8(1), act.Status)
				assert.Equal(t, uint(1666), act.UID)
				assert.NotEqual(t, "", act.Number)
			},
			act: action.ApplyActionV1{
				ApplyAction: action.ApplyAction{
					Name:                "寒假活动",
					Level:               1,
					Phone:               "1234567890",
					Principal:           "约翰·多伊",
					Plan:                "这是一个测试计划",
					Staff:               "一些工作人员",
					SitePhoto:           []string{"https://static.readboy.com/aa/bb/cc"},
					Space:               100,
					DateStart:           time.Now().AddDate(0, 0, 6).Format("2006-01-02"),
					DateEnd:             time.Now().AddDate(0, 0, 10).Format("2006-01-02"),
					WriteOffCorporation: "A公司",
					Location:            "测试地点",
					Type:                1,
					EndpointID:          111,
					SecondAgencyID:      333,
					TopAgencyID:         222,
				},
			},
			wantCode: http.StatusOK,
			wantRes: Result[map[string]any]{
				OK:      1,
				Message: "ok",
				Data: map[string]any{
					"id": float64(1),
				},
			},
		},
		{
			name: "申请活动-失败-申请日期晚了",
			before: func(t *testing.T) {
				s.db.Exec("TRUNCATE TABLE actions")
				s.db.Exec("TRUNCATE TABLE action_type")
				s.db.Create(&model.ActionType{
					ID:        1,
					Name:      "类型2",
					Enabled:   1,
					StartDate: "2025-01-01",
					EndDate:   "2025-12-31",
					Slug:      "TEST2",
					Ceiling:   100,
				})
				s.redis.Del(context.TODO(), "action:apply:1")
			},
			after: func(t *testing.T) {

			},
			act: action.ApplyActionV1{
				ApplyAction: action.ApplyAction{
					Name:                "寒假活动",
					Level:               1,
					Phone:               "1234567890",
					Principal:           "约翰·多伊",
					Plan:                "这是一个测试计划",
					Staff:               "一些工作人员",
					SitePhoto:           []string{"https://static.readboy.com/aa/bb/cc"},
					Space:               100,
					DateStart:           "2024-12-16",
					DateEnd:             "2024-12-18",
					WriteOffCorporation: "A公司",
					Location:            "测试地点",
					Type:                1,
					EndpointID:          111,
					SecondAgencyID:      333,
					TopAgencyID:         222,
				},
			},
			wantCode: http.StatusOK,
			wantRes: Result[map[string]any]{
				OK:      0,
				Message: "活动类型已停止申请",
			},
		},
		{
			name: "申请活动-失败-活动类型禁用了",
			before: func(t *testing.T) {
				s.db.Exec("TRUNCATE TABLE actions")
				s.db.Exec("TRUNCATE TABLE action_type")
				s.db.Create(&model.ActionType{
					ID:        1,
					Name:      "新类型4",
					Enabled:   0,
					StartDate: "2025-12-01",
					EndDate:   "2025-12-31",
					Slug:      "TEST2",
					Ceiling:   100,
				})
				s.redis.Del(context.TODO(), "action:apply:1")
			},
			after: func(t *testing.T) {

			},
			act: action.ApplyActionV1{
				ApplyAction: action.ApplyAction{
					Name:                "寒假活动",
					Level:               1,
					Phone:               "1234567890",
					Principal:           "约翰·多伊",
					Plan:                "这是一个测试计划",
					Staff:               "一些工作人员",
					SitePhoto:           []string{"https://static.readboy.com/aa/bb/cc"},
					Space:               100,
					DateStart:           "2024-12-16",
					DateEnd:             "2024-12-18",
					WriteOffCorporation: "A公司",
					Location:            "测试地点",
					Type:                1,
					EndpointID:          111,
					SecondAgencyID:      333,
					TopAgencyID:         222,
				},
			},
			wantCode: http.StatusOK,
			wantRes: Result[map[string]any]{
				OK:      0,
				Message: "活动类型已停止申请",
			},
		},
		{
			name: "申请活动-失败-活动类型申请达上限",
			before: func(t *testing.T) {
				s.db.Exec("TRUNCATE TABLE actions")
				s.db.Exec("TRUNCATE TABLE action_type")
				s.db.Create(&model.ActionType{
					ID:        1,
					Name:      "新类型4",
					Enabled:   0,
					StartDate: "2025-12-01",
					EndDate:   "2025-12-31",
					Slug:      "TEST2",
				})
				s.redis.Del(context.TODO(), "action:apply:1")
			},
			after: func(t *testing.T) {

			},
			act: action.ApplyActionV1{

				ApplyAction: action.ApplyAction{
					Name:                "寒假活动",
					Level:               1,
					Phone:               "1234567890",
					Principal:           "约翰·多伊",
					Plan:                "这是一个测试计划",
					Staff:               "一些工作人员",
					SitePhoto:           []string{"https://static.readboy.com/aa/bb/cc"},
					Space:               100,
					DateStart:           "2024-12-16",
					DateEnd:             "2024-12-18",
					WriteOffCorporation: "A公司",
					Location:            "测试地点",
					Type:                1,
					EndpointID:          111,
					SecondAgencyID:      333,
					TopAgencyID:         222,
				},
			},
			wantCode: http.StatusOK,
			wantRes: Result[map[string]any]{
				OK:      0,
				Message: "活动申请已达上限",
			},
		},
		{
			name: "申请活动-失败-活动时间不足两天",
			before: func(t *testing.T) {
				s.db.Exec("TRUNCATE TABLE actions")
				s.db.Exec("TRUNCATE TABLE action_type")
				s.db.Create(&model.ActionType{
					ID:        1,
					Name:      "新类型4",
					Enabled:   1,
					StartDate: "2025-12-01",
					EndDate:   "2025-12-31",
					Slug:      "TEST2",
					Ceiling:   100,
				})
				s.redis.Del(context.TODO(), "action:apply:1")
			},
			after: func(t *testing.T) {

			},
			act: action.ApplyActionV1{

				ApplyAction: action.ApplyAction{
					Name:                "寒假活动",
					Level:               1,
					Phone:               "1234567890",
					Principal:           "约翰·多伊",
					Plan:                "这是一个测试计划",
					Staff:               "一些工作人员",
					SitePhoto:           []string{"https://static.readboy.com/aa/bb/cc"},
					Space:               100,
					DateStart:           "2024-12-16",
					DateEnd:             "2024-12-18",
					WriteOffCorporation: "A公司",
					Location:            "测试地点",
					Type:                1,
					EndpointID:          111,
					SecondAgencyID:      333,
					TopAgencyID:         222,
				},
			},
			wantCode: http.StatusOK,
			wantRes: Result[map[string]any]{
				OK:      0,
				Message: "活动类型已停止申请",
			},
		},
	}
	for _, tc := range testCases {
		T.Run(tc.name, func(t *testing.T) {
			tc.before(t)
			//1 构造请求
			reqBody, err := json.Marshal(tc.act)
			assert.NoError(t, err)
			req, err := http.NewRequest(http.MethodPost, "/agency/activity/action/apply",
				bytes.NewBuffer(reqBody))
			require.NoError(t, err)
			req.Header.Set("Content-Type", "application/json")
			//2 执行请求
			resp := httptest.NewRecorder()
			s.server.ServeHTTP(resp, req)
			assert.Equal(t, tc.wantCode, resp.Code)
			//3 验证结果
			var webResult Result[map[string]any]
			err = json.NewDecoder(resp.Body).Decode(&webResult)
			assert.NoError(t, err)
			assert.Equal(t, tc.wantRes, webResult)
			tc.after(t)
		})
	}
}

// 活动完成
func (s *TestActionTestSuit) TestFinishAction02() {
	T := s.T()
	testCases := []struct {
		name string
		// 要提前准备数据
		before func(t *testing.T)
		// 验证并且删除数据
		after func(t *testing.T)
		act   action.FinishAction

		// 预期响应
		wantCode int
		wantRes  Result[map[string]string]
	}{
		{
			name: "完成活动",
			before: func(t *testing.T) {
				s.db.Exec("TRUNCATE TABLE warranty")
				s.db.Create(&warranty1)
				s.db.Create(&warranty2)
				s.db.Exec("TRUNCATE TABLE actions")
				s.db.Exec("TRUNCATE TABLE action_type")
				test := model.Actions{}
				test.ID = 1
				test.Status = action.ActionStatusApprovedByLeader.ToUint()
				now := time.Now()
				test.CreateAt = now
				test.UpdateAt = now
				time.Sleep(time.Second)
				test.DateStart = time.Now().AddDate(0, 0, 6).Format("2006-01-02")
				test.DateEnd = time.Now().AddDate(0, 0, 26).Format("2006-01-02")
				test.EndpointID = 111
				s.db.Create(&test)
			},
			after: func(t *testing.T) {
				var act model.Actions
				s.db.Table("actions").Where("id = ?", 1).Find(&act)
				assert.Equal(t, uint8(4), act.Status)
				assert.Equal(t, "测试主题", act.Theme)
				assert.Equal(t, "这是测试内容", act.Content)
				assert.Equal(t, "测试总结内容", act.Summary)
				assert.Equal(t, uint8(1), *act.Finish)
				assert.Equal(t, "销售详情描述", act.SalesDetails)
				photoPreparing, _ := utils.UrlImageTrans(act.PhotoPreparing)
				assert.Equal(t, "https://static.readboy.com/photopreparing/photo", photoPreparing[0])
				advertise, _ := utils.UrlImageTrans(act.Advertise)
				assert.Equal(t, "https://static.readboy.com/advertise/photo", advertise[0])
				photoFinished, _ := utils.UrlImageTrans(act.PhotoFinished)
				assert.Equal(t, "https://static.readboy.com/photofinshed/photo", photoFinished[0])
				video, _ := utils.UrlImageTrans(act.Video)
				assert.Equal(t, "https://static.readboy.com/video/photo", video[0])
				expenseAttach, _ := utils.UrlImageTrans(act.ExpenseAttach)
				assert.Equal(t, "https://static.readboy.com/expenseattach/photo", expenseAttach[0])
				photo, _ := utils.UrlImageTrans(act.Photo)
				assert.Equal(t, "https://static.readboy.com/photos/photo", photo[0])
			},
			act: action.FinishAction{
				ID:             1,
				Theme:          "测试主题",
				Content:        "这是测试内容",
				PhotoPreparing: []string{"https://static.readboy.com/photopreparing/photo"},
				Summary:        "测试总结内容",
				Advertise:      []string{"https://static.readboy.com/advertise/photo"},
				PhotoFinished:  []string{"https://static.readboy.com/photofinshed/photo"},
				Video:          []string{"https://static.readboy.com/video/photo"},
				ExpenseAttach:  []string{"https://static.readboy.com/expenseattach/photo"},
				Finish:         1,
				Photo:          []string{"https://static.readboy.com/photos/photo"},
				SalesDetails:   "销售详情描述",
			},
			wantCode: http.StatusOK,
			wantRes: Result[map[string]string]{
				OK:      1,
				Message: "ok",
				Data: map[string]string{
					"res": "ok",
				},
			},
		},
	}
	for _, tc := range testCases {
		T.Run(tc.name, func(t *testing.T) {
			tc.before(t)
			//1 构造请求
			reqBody, err := json.Marshal(tc.act)
			assert.NoError(t, err)
			req, err := http.NewRequest(http.MethodPost, "/agency/activity/action/finish",
				bytes.NewBuffer(reqBody))
			require.NoError(t, err)
			req.Header.Set("Content-Type", "application/json")
			//2 执行请求
			resp := httptest.NewRecorder()
			s.server.ServeHTTP(resp, req)
			assert.Equal(t, tc.wantCode, resp.Code)
			//3 验证结果
			var webResult Result[map[string]string]
			err = json.NewDecoder(resp.Body).Decode(&webResult)
			assert.NoError(t, err)
			assert.Equal(t, tc.wantRes, webResult)
			tc.after(t)
		})
	}
}

// 修改活动
func (s *TestActionTestSuit) TestUpdateAction() {
	T := s.T()
	testCases := []struct {
		name     string
		before   func(t *testing.T)
		after    func(t *testing.T)
		act      action.UpdateAction
		wantCode int
		wantRes  Result[string]
	}{
		{
			name: "修改活动",
			before: func(t *testing.T) {
				s.db.Create(&model.ActionType{
					ID:        2,
					Name:      "测试活动类型",
					Enabled:   1,
					StartDate: "2025-01-01",
					EndDate:   "2025-12-31",
					Ceiling:   100,
					Slug:      "TEST",
				})
			},
			after: func(t *testing.T) {
				var act model.Actions
				s.db.First(&act, 1)
				assert.Equal(t, "寒假活动-修改", act.Name)
				assert.Equal(t, 2, act.Level)
				assert.Equal(t, "12345678901", act.Phone)
				assert.Equal(t, "约翰·多伊叫", act.Principal)
				assert.Equal(t, "这是一个测试计划-修改", act.Plan)
				assert.Equal(t, "一些工作人员-修改", act.Staff)
				assert.Equal(t, "一个测试活动描述-修改", act.Description)
				assert.Equal(t, []string{"aa/bb/cc"}, act.SitePhoto)
				assert.Equal(t, 101, act.Space)
				assert.Equal(t, "2025-12-17", act.DateStart)
				assert.Equal(t, "2025-12-18", act.DateEnd)
				assert.Equal(t, "A公司-修改", act.WriteOffCorporation)
				assert.Equal(t, "测试地点-修改", act.Location)
				assert.Equal(t, 2, act.Type)
				assert.Equal(t, 1, act.ActivityID)
				assert.Equal(t, 1, act.PayGiftSupport)
				assert.Equal(t, 333, act.EndpointID)
				assert.Equal(t, 111, act.TopAgencyID)
				assert.Equal(t, 222, act.SecondAgencyID)
			},
			act: action.UpdateAction{
				ID: 1,
				ApplyAction: action.ApplyAction{
					Name:                "寒假活动-修改",
					Level:               2,
					Phone:               "12345678901",
					Principal:           "约翰·多伊叫",
					Plan:                "这是一个测试计划-修改",
					Staff:               "一些工作人员-修改",
					SitePhoto:           []string{"aa/bb/cc"},
					Space:               101,
					DateStart:           "2025-12-17",
					DateEnd:             "2025-12-18",
					WriteOffCorporation: "A公司-修改",
					Location:            "测试地点-修改",
					Type:                2,
					EndpointID:          333,
					TopAgencyID:         111,
					SecondAgencyID:      222,
				},
			},
			wantCode: http.StatusOK,
			wantRes: Result[string]{
				OK:      1,
				Message: "ok",
				Data:    "",
			},
		},
	}
	for _, tc := range testCases {
		T.Run(tc.name, func(t *testing.T) {
			tc.before(t)
			//1 构造请求
			reqBody, err := json.Marshal(tc.act)
			assert.NoError(t, err)
			req, err := http.NewRequest(http.MethodPost, "/agency/activity/action/update",
				bytes.NewBuffer(reqBody))
			require.NoError(t, err)
			req.Header.Set("Content-Type", "application/json")
			//2 执行请求
			resp := httptest.NewRecorder()
			s.server.ServeHTTP(resp, req)
			assert.Equal(t, tc.wantCode, resp.Code)
			//3 验证结果
			var webResult Result[string]
			err = json.NewDecoder(resp.Body).Decode(&webResult)
			assert.NoError(t, err)
			assert.Equal(t, tc.wantRes, webResult)
			/*	tc.after(t)*/
		})
	}
}

/*
	func (s *TestActionTestSuit) TearDownSuite() {
		s.db.Exec("TRUNCATE TABLE actions")
		s.db.Exec("TRUNCATE TABLE action_type")
	}
*/

func commonDB() *gorm.DB {
	db, err := gorm.Open(mysql.Open("root:@tcp(localhost:3306)/admin?charset=utf8mb4&parseTime=True&loc=Local"), &gorm.Config{})
	if err != nil {
		panic(err)
	}
	return db
}

func commonRedis() *redis.Client {
	Client := redis.NewClient(&redis.Options{
		Addr:     "**************:6379",
		Password: "ILq38ppdVT96TDtj", // 没有密码，默认值
		DB:       7,                  // 默认DB 0
	})
	if err := Client.Ping(context.Background()).Err(); err != nil {
		log.Fatal("redis ping err", zap.Error(err))
	}
	return Client
}

type Result[T any] struct {
	OK      int    `json:"ok"`
	Message string `json:"msg"`
	Data    T      `json:"data,omitempty"`
}
type Warranty struct {
	ID                  int        `gorm:"primaryKey;column:id"`
	ModelID             int        `gorm:"column:model_id"`
	Model               string     `gorm:"column:model"`
	Barcode             string     `gorm:"column:barcode"`
	Number              string     `gorm:"column:number"`
	Imei                string     `gorm:"column:imei"`
	ExtBarcode          string     `gorm:"column:ext_barcode"`
	Source              int        `gorm:"column:source"`
	Endpoint            int        `gorm:"column:endpoint"`
	BuyDate             time.Time  `gorm:"column:buy_date"`
	WarrantyPeriod      *time.Time `gorm:"column:warranty_period"`
	ProductDate         time.Time  `gorm:"column:product_date"`
	CreatedAtNew        *time.Time `gorm:"column:created_at_new"`
	CreatedAt           *time.Time `gorm:"column:created_at"`
	ActivatedAtOld      *time.Time `gorm:"column:activated_at_old"`
	ActivatedID         uint       `gorm:"column:activated_id"`
	DeletedAt           *time.Time `gorm:"column:deleted_at"`
	Realsale            bool       `gorm:"column:realsale"`
	Assessment          uint       `gorm:"column:assessment"`
	Status              int        `gorm:"column:status"`
	SalesmanID          int        `gorm:"column:salesman_id"`
	Salesman            string     `gorm:"column:salesman"`
	CustomerPrice       int        `gorm:"column:customer_price"`
	CustomerName        string     `gorm:"column:customer_name"`
	CustomerSex         string     `gorm:"column:customer_sex"`
	CustomerPhone       string     `gorm:"column:customer_phone"`
	CustomerAddr        string     `gorm:"column:customer_addr"`
	StudentUID          int        `gorm:"column:student_uid"`
	StudentName         string     `gorm:"column:student_name"`
	StudentSex          string     `gorm:"column:student_sex"`
	StudentSchoolID     int        `gorm:"column:student_school_id"`
	StudentSchool       string     `gorm:"column:student_school"`
	StudentSchoolAdcode int        `gorm:"column:student_school_adcode"`
	StudentGrade        string     `gorm:"column:student_grade"`
	StudentBirthday     *time.Time `gorm:"column:student_birthday"`
	CallBack            bool       `gorm:"column:call_back"`
	PurchaseWay         string     `gorm:"column:purchase_way"`
	Recommender         string     `gorm:"column:recommender"`
	RecommenderPhone    string     `gorm:"column:recommender_phone"`
	State               int        `gorm:"column:state"`
	InBillDate          *time.Time `gorm:"column:in_bill_date"`
	OutBillDate         *time.Time `gorm:"column:out_bill_date"`
	OutCustCode         string     `gorm:"column:out_cust_code"`
	Type                int        `gorm:"column:type"`
	Prototype           bool       `gorm:"column:prototype"`
	UpdatedAt           *time.Time `gorm:"column:updated_at"`
	EcCreatedAt         *time.Time `gorm:"column:ec_created_at"`
	EcType              int        `gorm:"column:ec_type"`
	EcEndpoint          int        `gorm:"column:ec_endpoint"`
	Channel             string     `gorm:"column:channel"`
	ActivatedStatus     int        `gorm:"column:activated_status"`
	ActivatedAt         *time.Time `gorm:"column:activated_at"`
	LngActivated        string     `gorm:"column:lng_activated"`
	LatActivated        string     `gorm:"column:lat_activated"`
	Lng                 string     `gorm:"column:lng"`
	Lat                 string     `gorm:"column:lat"`
}

func (w *Warranty) TableName() string {
	return "warranty"
}

var warranty1 = Warranty{
	ModelID:          1,
	Model:            "Model A",
	Barcode:          "BC123456789",
	Number:           "SN123456789",
	Imei:             "123456789012345",
	ExtBarcode:       "",
	Source:           0,
	Endpoint:         111,
	BuyDate:          time.Now().AddDate(0, 0, 0),
	WarrantyPeriod:   nil,
	ProductDate:      time.Now(),
	CreatedAtNew:     nil,
	CreatedAt:        nil,
	ActivatedAtOld:   nil,
	ActivatedID:      0,
	Realsale:         false,
	Assessment:       0,
	Status:           1,
	SalesmanID:       101,
	Salesman:         "John Doe",
	CustomerPrice:    2000,
	CustomerName:     "Alice",
	CustomerSex:      "f",
	CustomerPhone:    "12345678901",
	CustomerAddr:     "123 Main St",
	StudentUID:       0,
	StudentName:      "",
	StudentSex:       "",
	StudentSchoolID:  0,
	StudentSchool:    "",
	StudentGrade:     "",
	CallBack:         false,
	PurchaseWay:      "random_shop",
	Recommender:      "",
	RecommenderPhone: "",
	State:            1,
	InBillDate:       nil,
	OutBillDate:      nil,
	OutCustCode:      "",
	Type:             1,
	Prototype:        false,
	Channel:          "agency",
	ActivatedStatus:  0,
}
var warranty2 = Warranty{
	ModelID:          2,
	Model:            "Model B",
	Barcode:          "BC987654321",
	Number:           "SN987654321",
	Imei:             "543210987654321",
	ExtBarcode:       "",
	Source:           0,
	Endpoint:         111,
	BuyDate:          time.Now().AddDate(0, 0, 0),
	WarrantyPeriod:   nil,
	ProductDate:      time.Now(),
	CreatedAtNew:     nil,
	CreatedAt:        nil,
	ActivatedAtOld:   nil,
	ActivatedID:      0,
	Realsale:         false,
	Assessment:       0,
	Status:           1,
	SalesmanID:       102,
	Salesman:         "Jane Smith",
	CustomerPrice:    2500,
	CustomerName:     "Bob",
	CustomerSex:      "m",
	CustomerPhone:    "98765432109",
	CustomerAddr:     "456 Elm St",
	StudentUID:       0,
	StudentName:      "",
	StudentSex:       "",
	StudentSchoolID:  0,
	StudentSchool:    "",
	StudentGrade:     "",
	CallBack:         false,
	PurchaseWay:      "old_for_new",
	Recommender:      "",
	RecommenderPhone: "",
	State:            1,
	InBillDate:       nil,
	OutBillDate:      nil,
	OutCustCode:      "",
	Type:             1,
	Prototype:        false,
	Channel:          "e_commerce",
	ActivatedStatus:  0,
}
