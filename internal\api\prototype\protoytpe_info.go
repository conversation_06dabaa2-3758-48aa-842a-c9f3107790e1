package prototype

type WithMTAndPC struct {
	Number       string `json:"number"`
	Status       int    `json:"column:status"`
	Type         int    `json:"column:type"`
	Endpoint     int    `json:"column:endpoint"`
	TopAgency    int    `json:"column:top_agency"`
	SecondAgency int    `json:"column:second_agency"`
	CategoryID   int    `json:"column:category_id"`  // machine_type表获得
	Discontinued int    `json:"column:discontinued"` // prototype_config表获得
}
