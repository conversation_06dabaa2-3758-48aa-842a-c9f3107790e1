package machine

import "time"

type MesDeviceResp struct {
	Barcode     string    `json:"barcode"`
	Number      string    `json:"number"`
	Color       string    `json:"color"`
	Meid        string    `json:"meid"`
	Imei1       string    `json:"imei1"`
	Imei2       string    `json:"imei2"`
	Model       string    `json:"model"`
	ModelID     int       `json:"model_id"`
	State       int       `json:"state"`
	Status      int       `json:"status"`
	ProductDate time.Time `json:"product_date"`

	ExtBarcodeCount int `json:"ext_barcode_count"` // mes_device_type 表
}
