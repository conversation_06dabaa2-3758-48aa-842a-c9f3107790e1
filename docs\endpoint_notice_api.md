# 终端通知管理 API 文档

## 简介
终端通知管理模块提供了对终端通知的完整CRUD操作，包括创建、查询、更新和删除通知。通知内容支持富文本格式。

## API 接口列表

### 1. 获取通知列表

**请求URL:** `/admin/endpoint/notices`

**请求方法:** `GET`

**参数:**

| 参数名 | 是否必需 | 类型 | 描述 |
|--------|----------|------|------|
| title | 可选 | string | 通知标题（模糊搜索） |
| author | 可选 | string | 发布人（模糊搜索） |
| page_num | 可选 | int | 页码，默认为1 |
| page_size | 可选 | int | 每页数量，默认为20 |

**返回示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "data": [
      {
        "id": 1,
        "title": "系统维护通知",
        "author": "管理员",
        "content": "<p>系统将于今晚进行维护...</p>",
        "created_at": "2023-12-01T10:00:00Z",
        "updated_at": "2023-12-01T10:00:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "page_size": 20
  }
}
```

**返回参数说明:**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| id | int | 通知ID |
| title | string | 通知标题 |
| author | string | 发布人 |
| content | string | 通知内容（富文本） |
| created_at | string | 创建时间 |
| updated_at | string | 更新时间 |
| total | int | 总记录数 |
| page | int | 当前页码 |
| page_size | int | 每页数量 |

### 2. 获取指定通知

**请求URL:** `/admin/endpoint/notices/{id}`

**请求方法:** `GET`

**参数:**

| 参数名 | 是否必需 | 类型 | 描述 |
|--------|----------|------|------|
| id | 必需 | int | 通知ID |

**返回示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "data": {
      "id": 1,
      "title": "系统维护通知",
      "author": "管理员",
      "content": "<p>系统将于今晚进行维护...</p>",
      "created_at": "2023-12-01T10:00:00Z",
      "updated_at": "2023-12-01T10:00:00Z"
    }
  }
}
```

### 3. 创建通知

**请求URL:** `/admin/endpoint/notices`

**请求方法:** `POST`

**参数:**

| 参数名 | 是否必需 | 类型 | 描述 |
|--------|----------|------|------|
| title | 必需 | string | 通知标题 |
| author | 必需 | string | 发布人 |
| content | 必需 | string | 通知内容（支持富文本） |

**请求示例:**
```json
{
  "title": "系统维护通知",
  "author": "管理员",
  "content": "<p>系统将于今晚进行维护，请提前保存工作。</p>"
}
```

**返回示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1
  }
}
```

### 4. 更新通知

**请求URL:** `/admin/endpoint/notices/{id}`

**请求方法:** `PUT`

**参数:**

| 参数名 | 是否必需 | 类型 | 描述 |
|--------|----------|------|------|
| id | 必需 | int | 通知ID（URL路径参数） |
| title | 必需 | string | 通知标题 |
| author | 必需 | string | 发布人 |
| content | 必需 | string | 通知内容（支持富文本） |

**请求示例:**
```json
{
  "title": "系统维护通知（更新）",
  "author": "管理员",
  "content": "<p>系统维护已完成，感谢您的耐心等待。</p>"
}
```

**返回示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 5. 删除通知

**请求URL:** `/admin/endpoint/notices/{id}`

**请求方法:** `DELETE`

**参数:**

| 参数名 | 是否必需 | 类型 | 描述 |
|--------|----------|------|------|
| id | 必需 | int | 通知ID |

**返回示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

## 注意事项

1. 所有接口都需要管理员权限认证
2. 通知内容支持富文本格式，前端可以使用富文本编辑器
3. 列表接口支持按标题和发布人进行模糊搜索
4. 删除操作不可恢复，请谨慎操作
5. 创建和更新时，标题、发布人和内容都是必填字段

## 错误码说明

| 错误码 | 描述 |
|--------|------|
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 404 | 通知不存在 |
| 500 | 服务器内部错误 |
