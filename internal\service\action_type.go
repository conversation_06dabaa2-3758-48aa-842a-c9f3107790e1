package service

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"marketing/internal/api/action"
	api "marketing/internal/api/action"
	"marketing/internal/dao"
	"marketing/internal/model"
	"marketing/internal/pkg/db"
	"marketing/internal/pkg/log"
	"marketing/internal/service/cache"
	"strconv"
	"time"
)

// ActionTypeService 活动管理
type ActionTypeService interface {
	Upsert(ctx *gin.Context, action action.TypeAction) (id uint, err error)
	GetByID(c *gin.Context, id int) (action action.TypeAction, err error)
	ReviseStatus(c *gin.Context, id int, status int) error
	ReviseTypeName(c *gin.Context, id int, name int) error
	DropDownName(c *gin.Context) []string
	GetActionTypeList(c *gin.Context, page int, size int) (actions []action.TypeActionList, total int64)
	SearchType(c *gin.Context, page int, size int, name string, slug string) ([]action.TypeActionList, int, error)
	Update(c *gin.Context, req action.UpdateType) bool
	GetActionTypeTips(c *gin.Context) []string
	GetAgencyTips(c *gin.Context, name string) []map[string]any
}

// GormActionTypeService  DB实现
type GormActionTypeService struct {
	dao   dao.ActionTypeDao
	db    *gorm.DB
	cache cache.ActionCache
}

func (a *GormActionTypeService) GetAgencyTips(c *gin.Context, name string) []map[string]any {
	var tips []map[string]any
	a.db.WithContext(c).Table("agency").
		Select("id", "name").
		Where("name like ?", "%"+name+"%").
		Where("level = 1").
		Where("deleted_at IS NULL").
		Order("CONVERT(SUBSTR(name, 1, 1) USING gbk) COLLATE gbk_chinese_ci").
		Find(&tips)
	newTips := make([]map[string]any, 0, len(tips)+1)
	newTips = append(newTips, map[string]any{"id": 0, "name": "全部"})
	for _, v := range tips {
		newTips = append(newTips, v)
	}
	return newTips
}

// GetActionTypeTips 获取活动类型 api/action/TypeName配置
func (a *GormActionTypeService) GetActionTypeTips(c *gin.Context) []string {
	var tip action.TypeName
	names := tip.Range()
	return names
}

// Update 更新活动类型
func (a *GormActionTypeService) Update(c *gin.Context, req action.UpdateType) bool {
	var agency string
	//结束活动不可更新
	if a.dao.CheckEnd(c, uint(req.ID)) {
		return false
	}
	//可申请总代理
	if len(req.AvailableTopAgency) == 0 {
		agency = ""
	} else {
		for _, v := range req.AvailableTopAgency {
			agency += strconv.Itoa(v) + ","
		}
	}
	var act = model.ActionType{
		ID:                 uint(req.ID),
		Name:               req.Name,
		UpdatedAt:          time.Now(),
		StartDate:          req.StartDate,
		EndDate:            req.EndDate,
		Enabled:            uint8(req.Enabled),
		Ceiling:            uint16(req.Ceiling),
		Slug:               req.Slug,
		AvailableTopAgency: &agency,
		EndpointCeiling:    req.EndpointCeiling,
		Lines:              req.Lines,
	}
	//修改了活动
	res := a.dao.Update(c, act)
	if res {
		//修改了场次上限
		go func() {
			remain, _ := a.dao.GetActionSession(c, uint(req.ID))
			err := a.cache.Set(c, uint(req.ID), remain)
			if err != nil {
				log.Error("活动缓存设置失败", zap.Error(err))
			}
		}()
		return true
	}
	return false
}

func (a *GormActionTypeService) SearchType(c *gin.Context, page int, size int, name string, slug string) ([]action.TypeActionList, int, error) {
	var acts []action.TypeActionList
	if slug != "" {
		slug = action.TypeName(slug).ToSlug()
	}
	acts, total, err := a.dao.SearchType(c, page, size, name, slug)
	for k, act := range acts {
		acts[k].Slug = api.TypeName(act.Slug).TrancesString()
	}
	if err != nil {
		return nil, 0, err
	}
	return acts, total, err
}

func (a *GormActionTypeService) DropDownName(c *gin.Context) []string {
	return a.dao.DropDownName(c)
}

func (a *GormActionTypeService) ReviseStatus(c *gin.Context, id, status int) error {
	return a.dao.ReviseStatus(c, id, status)
}

func (a *GormActionTypeService) ReviseTypeName(c *gin.Context, id int, name int) error {
	return a.dao.ReviseTypeName(c, id, name)
}

func (a *GormActionTypeService) GetByID(c *gin.Context, id int) (action action.TypeAction, err error) {
	acts, err := a.dao.GetByID(c, id)
	return a.toApi(acts), err
}

// GetActionTypeList 获取活动列表
func (a *GormActionTypeService) GetActionTypeList(c *gin.Context, page int, size int) (actions []action.TypeActionList, total int64) {
	acts, total := a.dao.GetActionTypeList(c, page, size)
	for k, act := range acts {
		acts[k].Slug = api.TypeName(act.Slug).TrancesString()
	}
	return acts, total
}

// Upsert 创建或更新活动
func (a *GormActionTypeService) Upsert(ctx *gin.Context, action action.TypeAction) (id uint, err error) {
	now := time.Now()
	var agency string
	if len(action.AvailableTopAgency) == 0 {
		agency = ""
	} else {
		for _, v := range action.AvailableTopAgency {
			agency += strconv.Itoa(v) + ","
		}
	}
	var act = model.ActionType{
		Name:               action.Name,
		StartDate:          action.StartDate,
		EndDate:            action.EndDate,
		Ceiling:            action.Ceiling,
		Enabled:            action.Enabled,
		Slug:               api.TypeName(action.Slug).ToSlug(),
		AvailableTopAgency: &agency,
		GiftSupport:        action.GiftSupport,
		EndpointCeiling:    action.EndpointCeiling,
		Lines:              action.Lines,
		UpdatedAt:          now,
		CreatedAt:          now,
	}
	return a.dao.Create(ctx, act)
}

func (a *GormActionTypeService) toApi(actionType model.ActionType) action.TypeAction {
	return action.TypeAction{
		ID:        actionType.ID,
		Name:      actionType.Name,
		StartDate: actionType.StartDate,
		EndDate:   actionType.EndDate,
		Ceiling:   actionType.Ceiling,
		Enabled:   actionType.Enabled,
		Slug:      actionType.Slug,
		UpdatedAt: actionType.UpdatedAt.Format("2006-01-02"),
		CreatedAt: actionType.CreatedAt.Format("2006-01-02"),
	}
}

func (a *GormActionTypeService) toModel(actionType action.TypeAction) model.ActionType {
	return model.ActionType{
		ID:        actionType.ID,
		Name:      actionType.Name,
		StartDate: actionType.StartDate,
		EndDate:   actionType.EndDate,
		Ceiling:   actionType.Ceiling,
		Enabled:   actionType.Enabled,
	}
}

func NewGormActionTypeService(dao dao.ActionTypeDao, cache cache.ActionCache) ActionTypeService {
	return &GormActionTypeService{
		dao:   dao,
		db:    db.DB,
		cache: cache,
	}
}
