package machine

import (
	"errors"
	"github.com/gin-gonic/gin"
	"marketing/internal/dao"
	"marketing/internal/model"
)

type TMachineAccessoryRelationSvcInterface interface {
	GetMachineAccessoryRelationList(c *gin.Context, modelId, pageNum, pageSize int) ([]*model.MachineAccessoryRelation, int64)
	GetMachineAccessoryRelationByMid(c *gin.Context, modelId int) []*model.MachineAccessoryRelation
	CreateMachineAccessoryRelation(c *gin.Context, modelId, accessoryId int, price float64) error
	DeleteMachineAccessoryRelation(c *gin.Context, modelId, accessoryId int) error
}

type TMachineAccessoryRelationSvcImpl struct {
	machineAccessoryRepo         dao.MachineAccessoryDao
	machineAccessoryRelationRepo dao.MachineAccessoryRelationDao
}

func NewMachineAccessoryRelationService(accessoryRepo dao.MachineAccessoryDao, relationRepo dao.MachineAccessoryRelationDao) TMachineAccessoryRelationSvcInterface {
	return &TMachineAccessoryRelationSvcImpl{
		machineAccessoryRepo:         accessoryRepo,
		machineAccessoryRelationRepo: relationRepo,
	}
}

func (s *TMachineAccessoryRelationSvcImpl) GetMachineAccessoryRelationList(c *gin.Context, modelId, pageNum, pageSize int) ([]*model.MachineAccessoryRelation, int64) {
	accessoryIds := make([]int, 0)
	list, total := s.machineAccessoryRelationRepo.GetMachineAccessoryRelationList(c, modelId, pageNum, pageSize)
	for _, l := range list {
		accessoryIds = append(accessoryIds, l.AccessoryId)
	}

	accessoryList := s.machineAccessoryRepo.GetMachineAccessoryByIds(c, accessoryIds)
	for _, l := range list {
		for _, accessory := range accessoryList {
			if l.AccessoryId == accessory.Id {
				l.AccessoryName = accessory.Title
				break
			}
		}
	}

	return list, total
}

func (s *TMachineAccessoryRelationSvcImpl) GetMachineAccessoryRelationByMid(c *gin.Context, modelId int) []*model.MachineAccessoryRelation {
	accessoryIds := make([]int, 0)
	list := s.machineAccessoryRelationRepo.GetMachineAccessoryRelationByMid(c, modelId)
	for _, l := range list {
		accessoryIds = append(accessoryIds, l.AccessoryId)
	}

	accessoryList := s.machineAccessoryRepo.GetMachineAccessoryByIds(c, accessoryIds)
	for _, l := range list {
		for _, accessory := range accessoryList {
			if l.AccessoryId == accessory.Id {
				l.AccessoryName = accessory.Title
				break
			}
		}
	}

	return list
}

func (s *TMachineAccessoryRelationSvcImpl) CreateMachineAccessoryRelation(c *gin.Context, modelId, accessoryId int, price float64) error {
	if s.machineAccessoryRelationRepo.GetMachineAccessoryRelation(c, modelId, accessoryId) != nil {
		return nil
	}

	if s.machineAccessoryRepo.GetMachineAccessoryById(c, accessoryId) == nil {
		return errors.New("配件不存在")
	}

	return s.machineAccessoryRelationRepo.CreateMachineAccessoryRelation(c, &model.MachineAccessoryRelation{
		ModelId:     modelId,
		AccessoryId: accessoryId,
		Price:       price,
	})
}

func (s *TMachineAccessoryRelationSvcImpl) DeleteMachineAccessoryRelation(c *gin.Context, modelId, accessoryId int) error {
	return s.machineAccessoryRelationRepo.DeleteMachineAccessoryRelation(c, modelId, accessoryId)
}
