package admin

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/dao"
	"marketing/internal/handler/admin/declare"
	"marketing/internal/service"
)

type DeclareRouter struct{}

func NewDeclareRouter() *DeclareRouter {
	return &DeclareRouter{}
}

func (m *DeclareRouter) Register(r *gin.RouterGroup) {
	declareRouter := r.Group("/declare")
	{
		declareDao := dao.NewDeclareDao()
		declareService := service.NewDeclareSvc(declareDao)
		declareController := declare.NewDeclare(declareService)
		declareRouter.GET("/list", declareController.GetDeclareList)
	}
}
