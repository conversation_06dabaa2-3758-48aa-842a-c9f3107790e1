package dao

import (
	"errors"
	"marketing/internal/model"
	appErr "marketing/internal/pkg/errors"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type PushNotificationDao interface {
	// GetPushNotificationList 获取推送消息列表（分页）
	GetPushNotificationList(c *gin.Context, pageNum, pageSize, typeID int, content string) ([]*PushNotificationWithStats, int64, error)
	// CreatePushNotification 创建推送消息记录
	CreatePushNotification(c *gin.Context, notification *model.AppNotification) error
	// GetPushNotificationByID 根据ID获取推送消息
	GetPushNotificationByID(c *gin.Context, id int) (*model.AppNotification, error)
	// UpdatePushNotificationRevoke 更新推送消息撤回状态
	UpdatePushNotificationRevoke(c *gin.Context, id int) error
	// CreateNotificationInboxBatch 批量创建收件箱记录
	CreateNotificationInboxBatch(c *gin.Context, records []*model.AppNotificationInbox) error
	// GetUserIDsByAudience 根据推送目标获取用户ID列表
	GetUserIDsByAudience(c *gin.Context, audience map[string]interface{}) ([]uint, error)
	// GetWecomTags 获取企微标签列表
	GetWecomTags(c *gin.Context) ([]*model.WecomTag, error)
	// GetActiveRoles 获取活跃角色列表
	GetActiveRoles(c *gin.Context) ([]*model.AdminRoles, error)
	// GetNewestNotices 获取最新通知
	GetNewestNotices(c *gin.Context, limit int) ([]*model.EndpointNotice, error)
	// SearchUsers 搜索用户
	SearchUsers(c *gin.Context, search string, page, pageSize int) ([]*model.AdminUsers, error)
	// GetUsersByIDs 根据ID列表获取用户企微信息
	GetUsersByIDs(c *gin.Context, userIDs []uint) ([]*model.AdminUsers, error)
	//UpdateNotificationMsgids 更新推送消息的msgid
	UpdateNotificationMsgids(c *gin.Context, id uint, msgids string) error
}

type pushNotificationDao struct {
	db *gorm.DB
}

func NewPushNotificationDao(db *gorm.DB) PushNotificationDao {
	return &pushNotificationDao{
		db: db,
	}
}

// PushNotificationWithStats 带统计信息的推送消息
type PushNotificationWithStats struct {
	model.AppNotification
	TypeName string `json:"type_name"`
	Total    int64  `json:"total"`
	Fetched  int64  `json:"fetched"`
	Read     int64  `json:"read"`
	Checked  int64  `json:"checked"`
}

// GetPushNotificationList 获取推送消息列表（分页）
func (d *pushNotificationDao) GetPushNotificationList(c *gin.Context, pageNum, pageSize, typeID int, content string) ([]*PushNotificationWithStats, int64, error) {
	var total int64

	// 构建查询条件
	query := d.db.WithContext(c).Model(&model.AppNotification{}).Table("app_notification n").
		Select("n.*, t.name as type_name").
		Joins("LEFT JOIN app_notification_type t ON n.type_id = t.id")

	// 添加筛选条件
	if typeID > 0 {
		query = query.Where("type_id = ?", typeID)
	}
	if content != "" {
		query = query.Where("content LIKE ?", "%"+content+"%")
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询，关联查询消息类型
	var results []struct {
		model.AppNotification
		TypeName string `gorm:"column:type_name"`
	}

	offset := (pageNum - 1) * pageSize
	err := query.
		Order("n.id DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&results).Error

	if err != nil {
		return nil, 0, err
	}

	// 获取统计信息
	notificationIDs := make([]uint, len(results))
	for i, result := range results {
		notificationIDs[i] = result.ID
	}

	statsMap, err := d.getNotificationStats(c, notificationIDs)
	if err != nil {
		return nil, 0, err
	}

	// 组装结果
	notifications := make([]*PushNotificationWithStats, len(results))
	for i, result := range results {
		stats := statsMap[result.ID]
		notifications[i] = &PushNotificationWithStats{
			AppNotification: result.AppNotification,
			TypeName:        result.TypeName,
			Total:           stats.Total,
			Fetched:         stats.Fetched,
			Read:            stats.Read,
			Checked:         stats.Checked,
		}
	}

	return notifications, total, nil
}

// getNotificationStats 获取推送消息统计信息
func (d *pushNotificationDao) getNotificationStats(c *gin.Context, notificationIDs []uint) (map[uint]struct {
	Total   int64
	Fetched int64
	Read    int64
	Checked int64
}, error) {
	if len(notificationIDs) == 0 {
		return make(map[uint]struct {
			Total   int64
			Fetched int64
			Read    int64
			Checked int64
		}), nil
	}

	var stats []struct {
		NotificationID uint  `gorm:"column:notification_id"`
		Total          int64 `gorm:"column:total"`
		Fetched        int64 `gorm:"column:fetched"`
		Read           int64 `gorm:"column:read"`
		Checked        int64 `gorm:"column:checked"`
	}

	err := d.db.WithContext(c).
		Model(&model.AppNotificationInbox{}).
		Select("notification_id, "+
			"COUNT(*) as total, "+
			"SUM(CASE WHEN fetched = 1 THEN 1 ELSE 0 END) as fetched, "+
			"SUM(CASE WHEN `read` = 1 THEN 1 ELSE 0 END) as `read`, "+
			"SUM(CASE WHEN checked = 1 THEN 1 ELSE 0 END) as checked").
		Where("notification_id IN ?", notificationIDs).
		Group("notification_id").
		Find(&stats).Error

	if err != nil {
		return nil, err
	}

	// 转换为map
	statsMap := make(map[uint]struct {
		Total   int64
		Fetched int64
		Read    int64
		Checked int64
	})

	for _, stat := range stats {
		statsMap[stat.NotificationID] = struct {
			Total   int64
			Fetched int64
			Read    int64
			Checked int64
		}{
			Total:   stat.Total,
			Fetched: stat.Fetched,
			Read:    stat.Read,
			Checked: stat.Checked,
		}
	}

	return statsMap, nil
}

// CreatePushNotification 创建推送消息记录
func (d *pushNotificationDao) CreatePushNotification(c *gin.Context, notification *model.AppNotification) error {
	return d.db.WithContext(c).Create(notification).Error
}

// GetPushNotificationByID 根据ID获取推送消息
func (d *pushNotificationDao) GetPushNotificationByID(c *gin.Context, id int) (*model.AppNotification, error) {
	var notification model.AppNotification
	err := d.db.WithContext(c).First(&notification, id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &notification, nil
}

// UpdatePushNotificationRevoke 更新推送消息撤回状态
func (d *pushNotificationDao) UpdatePushNotificationRevoke(c *gin.Context, id int) error {
	return d.db.WithContext(c).Model(&model.AppNotification{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"revoked":    1,
			"revoked_at": gorm.Expr("NOW()"),
		}).Error
}

// UpdateNotificationMsgids 更新推送消息的msgid
func (d *pushNotificationDao) UpdateNotificationMsgids(c *gin.Context, id uint, msgids string) error {
	return d.db.WithContext(c).Model(&model.AppNotification{}).
		Where("id = ?", id).
		Update("msgid", msgids).Error
}

// CreateNotificationInboxBatch 批量创建收件箱记录
func (d *pushNotificationDao) CreateNotificationInboxBatch(c *gin.Context, records []*model.AppNotificationInbox) error {
	if len(records) == 0 {
		return nil
	}
	return d.db.WithContext(c).CreateInBatches(records, 500).Error
}

// GetUserIDsByAudience 根据推送目标获取用户ID列表
func (d *pushNotificationDao) GetUserIDsByAudience(c *gin.Context, audience map[string]interface{}) ([]uint, error) {
	var userIDs []uint

	if all, ok := audience["all"]; ok && all == "all" {
		// 推送给所有用户
		var users []*model.AdminUsers
		if err := d.db.WithContext(c).Where("status = ?", 1).Select("id").Find(&users).Error; err != nil {
			return nil, appErr.NewErr("获取用户列表失败: " + err.Error())
		}
		for _, user := range users {
			userIDs = append(userIDs, user.ID)
		}
	} else if tags, ok := audience["tags"].([]string); ok {
		// 根据标签推送
		var tagNames []string
		tagNames = tags

		if len(tagNames) > 0 {
			var tagUserRelations []*model.WecomUserTag
			if err := d.db.WithContext(c).
				Joins("JOIN wecom_tags ON wecom_user_tag.tag_id = wecom_tags.id").
				Where("wecom_tags.tag_name IN ?", tagNames).
				Select("wecom_user_tag.user_id").
				Find(&tagUserRelations).Error; err != nil {
				return nil, appErr.NewErr("根据标签获取用户失败: " + err.Error())
			}
			for _, relation := range tagUserRelations {
				userIDs = append(userIDs, relation.UserID)
			}
		}
	} else if users, ok := audience["users"].([]uint); ok {
		// 推送给指定用户
		userIDs = users
	} else if roles, ok := audience["roles"].([]string); ok {
		// 根据角色推送
		var roleSlugs []string
		roleSlugs = roles

		if len(roleSlugs) > 0 {
			var userRoles []*model.AdminUserRole
			if err := d.db.WithContext(c).
				Joins("JOIN admin_roles ON admin_user_roles.role_id = admin_roles.id").
				Where("admin_roles.slug IN ?", roleSlugs).
				Select("admin_user_roles.user_id").
				Find(&userRoles).Error; err != nil {
				return nil, appErr.NewErr("根据角色获取用户失败: " + err.Error())
			}
			for _, userRole := range userRoles {
				userIDs = append(userIDs, userRole.UserID)
			}
		}
	}

	return userIDs, nil
}

// GetWecomTags 获取企微标签列表
func (d *pushNotificationDao) GetWecomTags(c *gin.Context) ([]*model.WecomTag, error) {
	var tags []*model.WecomTag
	err := d.db.WithContext(c).Find(&tags).Error
	return tags, err
}

// GetActiveRoles 获取活跃角色列表
func (d *pushNotificationDao) GetActiveRoles(c *gin.Context) ([]*model.AdminRoles, error) {
	var roles []*model.AdminRoles
	err := d.db.WithContext(c).Where("status = ? AND app_type >= ?", 1, 0).Find(&roles).Error
	return roles, err
}

// GetNewestNotices 获取最新通知
func (d *pushNotificationDao) GetNewestNotices(c *gin.Context, limit int) ([]*model.EndpointNotice, error) {
	var notices []*model.EndpointNotice
	err := d.db.WithContext(c).Order("updated_at DESC").Limit(limit).Find(&notices).Error
	return notices, err
}

// SearchUsers 搜索用户
func (d *pushNotificationDao) SearchUsers(c *gin.Context, search string, page, pageSize int) ([]*model.AdminUsers, error) {
	offset := (page - 1) * pageSize
	var users []*model.AdminUsers
	err := d.db.WithContext(c).
		Where("status = ? AND username LIKE ?", 1, "%"+search+"%").
		Offset(offset).
		Limit(pageSize).
		Find(&users).Error
	return users, err
}

// GetUsersByIDs 根据ID列表获取用户企微信息
func (d *pushNotificationDao) GetUsersByIDs(c *gin.Context, userIDs []uint) ([]*model.AdminUsers, error) {
	var users []*model.AdminUsers
	err := d.db.WithContext(c).Where("id IN ?", userIDs).Select("id, qw_userid").Find(&users).Error
	return users, err
}
