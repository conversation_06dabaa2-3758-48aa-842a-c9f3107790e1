package model

import (
	"time"

	"gorm.io/gorm"
)

type TrainSubject struct {
	ID                   uint           `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Title                string         `gorm:"column:title;size:100;not null" json:"title"`
	StartTime            time.Time      `gorm:"column:start_time;default:CURRENT_TIMESTAMP" json:"start_time"`
	EndTime              time.Time      `gorm:"column:end_time;default:'0000-00-00 00:00:00'" json:"end_time"`
	Enabled              uint8          `gorm:"column:enabled;default:0" json:"enabled"`
	NumCourses           uint8          `gorm:"column:num_courses;default:0" json:"num_courses"`
	NumPapers            uint8          `gorm:"column:num_papers;default:0" json:"num_papers"`
	CreatedAt            time.Time      `gorm:"column:created_at;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt            time.Time      `gorm:"column:updated_at;default:'0000-00-00 00:00:00'" json:"updated_at"`
	Sort                 uint8          `gorm:"column:sort;default:0" json:"sort"`
	DeletedAt            gorm.DeletedAt `gorm:"column:deleted_at" json:"deleted_at"`
	Type                 string         `gorm:"column:type;type:enum('normal','live');default:'normal'" json:"type"`
	Lecturer             string         `gorm:"column:lecturer;size:10;default:''" json:"lecturer"`
	LecturerAvatar       string         `gorm:"column:lecturer_avatar;size:100;default:''" json:"lecturer_avatar"`
	BriefIntro           string         `gorm:"column:brief_intro;type:text;not null" json:"brief_intro"`
	Tag                  string         `gorm:"column:tag;size:5;default:''" json:"tag"`
	Agencies             string         `gorm:"column:agencies;type:text;not null" json:"agencies"`
	CompoundVisible      uint8          `gorm:"column:compound_visible;default:1" json:"compound_visible"`
	VisibleEndpointTypes string         `gorm:"column:visible_endpoint_types;size:15;default:'1,2,3,4'" json:"visible_endpoint_types"`
	VisibleRoles         string         `gorm:"column:visible_roles;size:20;default:'endpoint,salesclerk'" json:"visible_roles"`
	Tactic               uint8          `gorm:"column:tactic;default:1" json:"tactic"`
	HasCertificate       uint8          `gorm:"column:has_certificate;default:0" json:"has_certificate"`
	CreatedBy            uint           `gorm:"column:created_by;default:0" json:"created_by"`
	EnabledAt            *time.Time     `gorm:"column:enabled_at" json:"enabled_at"`
	TrainCourse          []TrainCourse  `gorm:"many2many:train_subject_course;joinForeignKey:subject_id;references:id"`
}

type TrainCourse struct {
	ID            uint           `gorm:"column:id;primaryKey;autoIncrement"`
	Name          string         `gorm:"column:name;type:varchar(50);not null"`
	Description   string         `gorm:"column:description;type:varchar(100);not null"`
	Issuer        string         `gorm:"column:issuer;type:varchar(20);not null;comment:发布人"`
	ResourceURL   string         `gorm:"column:resource_url;type:varchar(100);not null;comment:资源路径"`
	CreatedAt     time.Time      `gorm:"column:created_at;autoCreateTime"`
	UpdatedAt     time.Time      `gorm:"column:updated_at;"`
	DeletedAt     *time.Time     `gorm:"column:deleted_at"`
	LiveStartTime time.Time      `gorm:"column:live_start_time;default:0000-00-00 00:00:00;comment:直播课时开始时间"`
	LiveEndTime   time.Time      `gorm:"column:live_end_time;default:0000-00-00 00:00:00;comment:直播课时结束时间"`
	IsUse         int8           `gorm:"column:is_use;type:tinyint(4)"`
	TrainSubjects []TrainSubject `gorm:"many2many:train_subject_course;joinForeignKey:course_id;references:id"`
}
type TrainSubjectCourse struct {
	ID                 uint    `gorm:"column:id;primaryKey;autoIncrement"`
	SubjectID          uint    `gorm:"column:subject_id;not null"`
	CourseID           uint    `gorm:"column:course_id;not null"`
	PaperID            uint    `gorm:"column:paper_id;not null;default:0"`
	Credit             float64 `gorm:"column:credit;type:decimal(5,2);not null;default:0.00;comment:学分"`
	CreditLearningTime uint16  `gorm:"column:credit_learning_time;not null;default:0;comment:获得学分所需学习时长，秒"`
	Sort               uint8   `gorm:"column:sort;not null;default:0"`
}

type TrainSubjectEndpoint struct {
	ID         uint      `gorm:"column:id;primaryKey;autoIncrement"`
	SubjectID  uint      `gorm:"column:subject_id;not null"`
	EndpointID uint      `gorm:"column:endpoint_id;not null"`
	CreatedAt  time.Time `gorm:"column:created_at;default:CURRENT_TIMESTAMP;autoCreateTime"`
}

// TrainPractice 定义与 train_practice 表对应的 GORM 模型
type TrainPractice struct {
	ID          uint    `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	SubjectID   uint    `gorm:"column:subject_id" json:"subject_id"`
	Name        string  `gorm:"column:name;type:varchar(100)" json:"name"`
	Description string  `gorm:"column:description;type:text" json:"description"`
	Publisher   int     `gorm:"column:publisher" json:"publisher"`
	MinCredit   uint    `gorm:"column:min_credit" json:"min_credit"`
	MaxCredit   uint    `gorm:"column:max_credit" json:"max_credit"`
	CreatedAt   string  `gorm:"column:created_at;type:varchar(100)" json:"created_at"`
	DeletedAt   *string `gorm:"column:deleted_at;type:varchar(100)" json:"deleted_at"`
	IsUse       int8    `gorm:"column:is_use" json:"is_use"`
}

// TableName 指定表名
func (TrainCourse) TableName() string {
	return "train_course"
}
func (TrainSubject) TableName() string {
	return "train_subject"
}
func (TrainSubjectCourse) TableName() string {
	return "train_subject_course"
}
func (TrainPractice) TableName() string {
	return "train_practice"
}
func (TrainSubjectEndpoint) TableName() string {
	return "train_subject_endpoint"
}

type Subject struct {
	TrainSubject
	TrainCourse []TrainCourse `gorm:"many2many:train_subject_course;joinForeignKey:subject_id;references:id"`
	Practice    TrainPractice
}
