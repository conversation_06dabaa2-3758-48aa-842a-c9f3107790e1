package model

import (
	"strconv"
	"strings"
	"time"
)

type AppSystemV2 struct {
	ID         int       `gorm:"primaryKey;autoIncrement;column:id"`
	AppKey     string    `gorm:"uniqueIndex;size:100;column:app_key"`
	JwtKey     string    `gorm:"uniqueIndex;size:100;column:jwt_key"`
	Parent     string    `gorm:"size:100;column:parent;default:NULL;comment:父应用标识"`
	CorpID     string    `gorm:"size:100;column:corp_id;comment:企微应用企业ID"`
	AgentID    int       `gorm:"size:100;column:agent_id;comment:企微应用应用ID"`
	CorpSecret string    `gorm:"size:100;column:corp_secret;comment:企微应用企微登录密钥"`
	Name       string    `gorm:"size:100;column:name;comment:名称"`
	Desc       string    `gorm:"size:128;column:desc;comment:描述"`
	Type       string    `gorm:"type:enum('web','wecom','h5');default:web;column:type;comment:类型"`
	Index      string    `gorm:"size:256;column:index;comment:地址"`
	Icon       string    `gorm:"size:255;not null;column:icon;comment:图片地址"`
	Visibility uint      `gorm:"not null;default:1;column:visibility;comment:是否可见"`
	Rank       int       `gorm:"not null;default:0;column:rank;comment:排序"`
	UserGroup  string    `gorm:"size:128;column:user_group;comment:应用系统可访问用户组，如果的wecom应用可以留空根据是否可见来访问"`
	CreatedAt  time.Time `gorm:"not null;default:CURRENT_TIMESTAMP;column:created_at"`
	UpdatedAt  time.Time `gorm:"column:updated_at;default:NULL;autoUpdateTime"`
}

// TableName specifies table name for AppSystemV2 model.
func (AppSystemV2) TableName() string {
	return "app_system_v2"
}

// SetUserGroup 将切片转换为逗号分隔的字符串
func (a *AppSystemV2) SetUserGroup(groups []int) {
	strGroups := make([]string, len(groups))
	for i, g := range groups {
		strGroups[i] = strconv.Itoa(g)
	}
	a.UserGroup = strings.Join(strGroups, ",")
}

// GetUserGroup 将逗号分隔的字符串解析为切片
func (a *AppSystemV2) GetUserGroup() ([]int, error) {
	if a.UserGroup == "" {
		return nil, nil
	}

	strGroups := strings.Split(a.UserGroup, ",")
	groups := make([]int, len(strGroups))
	for i, str := range strGroups {
		num, err := strconv.Atoi(str)
		if err != nil {
			return nil, err
		}
		groups[i] = num
	}
	return groups, nil
}
