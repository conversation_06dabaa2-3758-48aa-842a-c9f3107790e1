package middleware

import (
	"crypto/md5"
	"fmt"
	"marketing/internal/dao/app_auth"
	"marketing/internal/pkg/db"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

const (
	ErrInvalidSignature = "签名验证失败"
	ErrInvalidTimestamp = "时间戳无效"
	ErrMissingParams    = "缺少必要参数"
	SignatureTimeout    = 300 // 5分钟超时
)

// SignatureMiddleware 签名验证中间件
func SignatureMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取请求参数
		appKey := c.Query("app_key")
		timestamp := c.Query("timestamp")
		signature := c.Query("signature")

		// 验证必要参数
		if appKey == "" || timestamp == "" || signature == "" {
			c.JSON(http.StatusBadRequest, &RespBody{
				OK:      0,
				Message: ErrMissingParams,
			})
			c.Abort()
			return
		}

		// 从数据库获取应用认证信息
		appAuthDao := app_auth.NewAppAuthDao(db.GetDB())
		appAuth, err := appAuthDao.GetByAppKey(c, appKey)
		if err != nil {
			c.JSON(http.StatusUnauthorized, &RespBody{
				OK:      0,
				Message: ErrInvalidSignature,
			})
			c.Abort()
			return
		}

		// 验证时间戳
		ts, err := strconv.ParseInt(timestamp, 10, 64)
		if err != nil {
			c.JSON(http.StatusBadRequest, &RespBody{
				OK:      0,
				Message: ErrInvalidTimestamp,
			})
			c.Abort()
			return
		}

		// 检查时间戳是否在有效范围内（防重放攻击）
		now := time.Now().Unix()
		if now-ts > SignatureTimeout || ts-now > SignatureTimeout {
			c.JSON(http.StatusBadRequest, &RespBody{
				OK:      0,
				Message: ErrInvalidTimestamp,
			})
			c.Abort()
			return
		}

		// 验证签名（使用AppKey作为密钥）
		if !verifySignature(c, appAuth.AppKey, signature) {
			c.JSON(http.StatusUnauthorized, &RespBody{
				OK:      0,
				Message: ErrInvalidSignature,
			})
			c.Abort()
			return
		}

		// 签名验证通过，继续处理请求
		c.Set("app_key", appKey)
		c.Next()
	}
}

// verifySignature 验证签名
func verifySignature(c *gin.Context, appSecret, signature string) bool {
	// 获取所有查询参数
	params := make(map[string]string)
	for key, values := range c.Request.URL.Query() {
		if key != "signature" && len(values) > 0 {
			params[key] = values[0]
		}
	}

	// 如果是POST请求，也包含POST参数
	if c.Request.Method == "POST" {
		c.Request.ParseForm()
		for key, values := range c.Request.PostForm {
			if len(values) > 0 {
				params[key] = values[0]
			}
		}
	}

	// 生成签名字符串
	signString := generateSignString(params, appSecret)

	// 计算MD5签名
	expectedSignature := fmt.Sprintf("%x", md5.Sum([]byte(signString)))

	return strings.ToLower(signature) == strings.ToLower(expectedSignature)
}

// generateSignString 生成签名字符串
func generateSignString(params map[string]string, appSecret string) string {
	// 获取所有参数名并排序
	keys := make([]string, 0, len(params))
	for key := range params {
		keys = append(keys, key)
	}
	sort.Strings(keys)

	// 构建签名字符串
	var signParts []string
	for _, key := range keys {
		signParts = append(signParts, fmt.Sprintf("%s=%s", key, params[key]))
	}

	// 添加app_secret
	signString := strings.Join(signParts, "&") + "&app_secret=" + appSecret

	return signString
}
