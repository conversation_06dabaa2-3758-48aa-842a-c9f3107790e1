package service

/*
import (
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"

	"marketing/internal/dao"
)

// EndpointImageHistoryService 服务层，处理终端形象历史记录的业务逻辑
type EndpointImageHistoryService struct {
	endpointImageHistoryDAO *dao.EndpointImageHistoryDAO
	locationService         *LocationService
}

// NewEndpointImageHistoryService 创建 EndpointImageHistoryService 实例
func NewEndpointImageHistoryService(endpointImageHistoryDAO *dao.EndpointImageHistoryDAO, locationService *LocationService) *EndpointImageHistoryService {
	return &EndpointImageHistoryService{
		endpointImageHistoryDAO: endpointImageHistoryDAO,
		locationService:         locationService,
	}
}

// GetEndpointImageHistories 获取终端形象历史记录
func (service *EndpointImageHistoryService) GetEndpointImageHistories(endpointId int) ([]map[string]interface{}, error) {
	// 获取数据库数据
	endpointImageHistories, err := dao.NewEndpointImageHistoryDAO().GetbyImageHistoryId(endpointId)

	if err != nil {
		return nil, err
	}

	endpoint, err := dao.EndpointDao.GetEndpointByID(*gin.Context, uint(endpointId))
	if err != nil {
		return nil, err
	}

	agencyNames, err := dao.NewEndpointImageHistoryDAO().GetAgencyNames([]int{endpoint.TopAgency, endpoint.SecondAgency})

	if err != nil {
		fmt.Println(err, "1111111111111111111111")
		return nil, err
	}

	// 业务处理和数据格式化
	fields := map[string]string{
		"storefront":         "门头",
		"product_image":      "产品列区",
		"product_experience": "产品体验区",
		"culture_wall":       "文化墙",
		"cashier":            "收银区",
		"rest_area":          "休息区",
		"spare_parts":        "礼品配件区",
		"books_borrow":       "图书借阅区",
		"training_room":      "培训教室",
		"other":              "其它",
	}

	data := make([]map[string]interface{}, len(endpointImageHistories))
	for i, record := range endpointImageHistories {
		images := make(map[string]interface{})
		for _, label := range fields {
			imagesForField := []map[string]string{}
			decodedImages := []string{}
			// 假设字段内容为 JSON 字符串
			if err := json.Unmarshal([]byte(record.Storefront), &decodedImages); err == nil {
				for _, img := range decodedImages {
					imagesForField = append(imagesForField, map[string]string{
						"thumbnail": fmt.Sprintf("https://dt1.readboy.com/%s?x-oss-process=image/resize,h_80,w_80", img),
						"original":  fmt.Sprintf("https://dt1.readboy.com/%s", img),
					})
				}
			}
			images[label] = imagesForField
		}

		// 获取坐标
		coordinate := service.locationService.GetCoordinate(record)

		var secondAgencyName string
		if endpoint.SecondAgency == 0 || agencyNames[endpoint.SecondAgency] == "" {
			secondAgencyName = "直营"
		} else {
			secondAgencyName = agencyNames[endpoint.SecondAgency]
		}

		// 格式化返回数据
		data[i] = map[string]interface{}{
			"date":                 fmt.Sprintf("%d年%d月", record.Year, record.Month),
			"endpoint":             fmt.Sprintf("【%s】%s - %s", endpoint.Code, endpoint.Name, endpoint.Address),
			"endpoint_create_time": endpoint.CreatedAt.Format("2006-01-02"),
			"updated_at":           record.UpdatedAt.Format("2006-01-02"),
			"region":               fmt.Sprintf("%s-%s", agencyNames[endpoint.TopAgency], secondAgencyName),
			"status":               record.Status,
			"location":             record.Location,
			"manual_status":        record.ManualStatus,
			"coordinate":           coordinate,
			"images":               images,
		}
	}
	return data, nil

}
*/
