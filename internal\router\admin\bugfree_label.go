package admin

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/dao"
	"marketing/internal/handler/admin/bug"
	"marketing/internal/service"
)

type BugFreeLabelRouter struct{}

func NewBugFreeLabelRouter() *BugFreeLabelRouter {
	return &BugFreeLabelRouter{}
}

func (m *BugFreeLabelRouter) Register(r *gin.RouterGroup) {
	bugLabelRouter := r.Group("/bug_label")
	{
		bugFreeLabelDao := dao.NewBugFreeLabelDao()
		bugFreeLabelService := service.NewBugFreeLabelService(bugFreeLabelDao)
		materialController := bug.NewMachineAccessoryRelation(bugFreeLabelService)
		bugLabelRouter.GET("/list", materialController.BugFreeLabelList)
		bugLabelRouter.POST("/edit", materialController.EditBugFreeLabel)
		bugLabelRouter.DELETE("/delete", materialController.DeleteFreeLabel)
	}
}
