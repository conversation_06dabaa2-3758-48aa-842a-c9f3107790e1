package prototype

import (
	"marketing/internal/api"
	"marketing/internal/pkg/types"
)

// UpCounterEndpointSearch 上柜终端查询
type UpCounterEndpointSearch struct {
	api.PaginationParams
	EndpointName    string `json:"endpoint_name" form:"endpoint_name"`
	EndpointCode    string `json:"endpoint_code" form:"endpoint_code"`
	EndpointAddress string `json:"endpoint_address" form:"endpoint_address"`
	TopAgencyID     int    `json:"top_agency_id" form:"top_agency_id"`
	SecondAgencyID  int    `json:"second_agency_id" form:"second_agency_id"`
	ModelName       string `json:"model_name" form:"model_name"`
	ApproveStatus   int    `json:"approve_status" form:"approve_status"`
	Status          string `json:"status" form:"status"`
	IsEndpointMain  bool   `json:"is_endpoint_main" form:"is_endpoint_main"`
}

type UpCounterApprove struct {
	Id     int    `json:"id" form:"id" binding:"required"`
	Status int    `json:"status" form:"status" binding:"required,oneof=2 -2"`
	Reason string `json:"reason" form:"reason"`
}

// UpCounterEndpointListVo 上柜终端列表
type UpCounterEndpointListVo struct {
	ID            int              `json:"id"`
	Code          string           `json:"code"`
	Name          string           `json:"name"`
	Address       string           `json:"address"`
	Manager       string           `json:"manager"`
	Phone         string           `json:"phone"`
	Type          int              `json:"type"`
	TypeName      string           `json:"type_name"`
	Barcode       string           `json:"barcode"`
	ApproveStatus int              `json:"approve_status"`
	ApproveTime   types.CustomTime `json:"approve_time"`
	Reason        string           `json:"reason"`
	Photos        string           `json:"photos"`
	Model         string           `json:"model"`
}
