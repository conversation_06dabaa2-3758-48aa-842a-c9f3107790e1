package endpoint

import (
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	api "marketing/internal/api/endpoint"
	"marketing/internal/consts"
	"marketing/internal/handler"
	"marketing/internal/pkg/errors"
	service "marketing/internal/service/endpoint"
)

type SettingHandler interface {
	Lists(c *gin.Context)
	UpdateSetting(c *gin.Context)
}

type settingHandler struct {
	service service.SettingService
}

func NewSettingHandler(service service.SettingService) SettingHandler {
	return &settingHandler{service: service}
}

func (s *settingHandler) Lists(c *gin.Context) {
	var req api.ListEndpointSettingReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	if req.Page == 0 {
		req.Page = consts.DefaultPage
	}
	if req.PageSize == 0 {
		req.PageSize = consts.DefaultPageSize
	}
	list, total, err := s.service.List(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"list":      list,
		"total":     total,
		"page":      req.Page,
		"page_size": req.PageSize,
	})
}

func (s *settingHandler) UpdateSetting(c *gin.Context) {
	var req *api.UpdateEndpointSettingReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		handler.Error(c, errors.NewErr("id is required"))
		return
	}
	err := s.service.Update(c, id, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}
