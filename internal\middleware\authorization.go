package middleware

import (
	"errors"
	"github.com/golang-jwt/jwt/v5"
	"marketing/internal/service/auth"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

const (
	ErrUnauthorized     = "未登录"
	ErrTokenExpired     = "登录已经失效，请重新登录"
	ErrPermissionDenied = "没有权限"
	ErrToken            = "非法token"
	BearerPrefix        = "Bearer "
)

// RespBody response body.统一返回响应格式（不确定要不要调用api包的的先放这里吧）
type RespBody struct {
	// http code
	OK int `json:"ok"`
	// response message
	Message string `json:"msg"`
	// response data
	Data any `json:"data,omitempty"`
}

func AuthToken(authSvc auth.ServiceInterface) gin.HandlerFunc {
	return func(c *gin.Context) {
		systemType := c.GetHeader("x-gate-type")

		token := c.<PERSON>eader("Authorization")

		if token == "" {
			c.<PERSON><PERSON>(http.StatusBadRequest, &RespBody{
				OK:      0,
				Message: ErrUnauthorized,
			})
			c.Abort()
			return
		}
		tokenString := strings.Replace(token, BearerPrefix, "", 1)
		// 调用 ValidateToken 来验证 token
		claims, err := authSvc.ValidateToken(c, tokenString)
		if err != nil {
			if errors.Is(err, jwt.ErrTokenExpired) {
				c.JSON(http.StatusUnauthorized, &RespBody{
					OK:      0,
					Message: ErrTokenExpired,
				})
				c.Abort()
				return
			}
			c.JSON(http.StatusForbidden, &RespBody{
				OK:      0,
				Message: ErrToken,
			})
			c.Abort()
			return
		}
		uid := claims.UserID

		if claims == nil {
			c.JSON(http.StatusForbidden, &RespBody{
				OK:      0,
				Message: ErrToken,
			})
			c.Abort()
			return
		}
		// Token 验证通过，可以继续处理请求
		c.Set("uid", uid)
		c.Set("system_type", systemType)
		c.Set("token", tokenString)
		c.Next()
	}
}

func CheckAdminPermission(authSvc auth.ServiceInterface) gin.HandlerFunc {
	return func(c *gin.Context) {
		pass := authSvc.CheckPermissionByUrlMethod(c)
		if !pass {
			c.JSON(http.StatusForbidden, &RespBody{
				OK:      0,
				Message: ErrPermissionDenied,
			})
			c.Abort()
			return
		}
		c.Next()
	}
}

func CheckTokenSystem(authSvc auth.ServiceInterface, systemType string) gin.HandlerFunc {
	return func(c *gin.Context) {
		token := c.GetString("token")
		claims, err := authSvc.ValidateToken(c, token)
		appSystem, err := authSvc.GetAppSystemByKey(c, systemType)
		if appSystem.Parent != "" {
			systemType = appSystem.Parent
		}
		if err != nil || claims == nil || claims.SystemType != systemType {
			c.JSON(http.StatusForbidden, &RespBody{
				OK:      0,
				Message: ErrPermissionDenied,
			})
			c.Abort()
			return
		}
		c.Next()
	}
}
