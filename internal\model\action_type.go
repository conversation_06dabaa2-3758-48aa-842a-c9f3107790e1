package model

import (
	"time"
)

// ActionType 代表action_type的活动类型信息
type ActionType struct {
	ID                 uint      `json:"id" gorm:"column:id;primary_key;auto_increment;comment:活动类型ID"`
	Name               string    `json:"name" gorm:"column:name;size:50;not null;default:'';comment:类型名称"`
	CreatedAt          time.Time `json:"created_at" gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP;comment:创建时间"`
	UpdatedAt          time.Time `json:"updated_at" gorm:"column:updated_at;not null;default:null;comment:更新时间"`
	Enabled            uint8     `json:"enabled" gorm:"column:enabled;not null;default:1;comment:是否启用"`
	StartDate          string    `json:"start_date" gorm:"column:start_date;not null;default:'';comment:开始时间"`
	EndDate            string    `json:"end_date" gorm:"column:end_date;not null;default:'';comment:结束时间"`
	Ceiling            uint16    `json:"ceiling" gorm:"column:ceiling;not null;default:0;comment:上限"`
	AvailableTopAgency *string   `json:"available_top_agency" gorm:"column:available_top_agency;comment:可参与的大区"`
	Slug               string    `json:"slug" gorm:"column:slug;not null;default:'';comment:类型标识"`
	GiftSupport        uint8     `json:"gift_support" gorm:"column:gift_support;not null;default:0;comment:是否支持礼品"`
	EndpointCeiling    uint16    `json:"endpoint_ceiling" gorm:"column:endpoint_ceiling;not null;default:0;comment:Endpoint上限"`
	Lines              int       `json:"lines" gorm:"column:lines;not null;default:0;comment:额度"`
}

func (ActionType) TableName() string {
	return "action_type"
}
