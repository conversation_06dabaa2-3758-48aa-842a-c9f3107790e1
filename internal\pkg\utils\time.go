package utils

import (
	"time"
)

// GetTimeStr 转化成字符串
func GetTimeStr(t time.Time) string {
	return t.In(time.Now().Location()).Format("2006-01-02 15:04:05")
}

// GetTimeStrDay 转化成字符串
func GetTimeStrDay(t time.Time) string {
	return t.In(time.Now().Location()).Format("2006-01-02")
}

// GetTime 字符串转化成t
func GetTime(timeStr string) (t time.Time, err error) {
	t, err = time.ParseInLocation("2006-01-02 15:04:05", timeStr, time.Now().Location())
	return
}

// GetTimeDay 字符串转化成t
func GetTimeDay(timeStr string) (t time.Time, err error) {
	t, err = time.ParseInLocation("2006-01-02", timeStr, time.Now().Location())
	return
}
