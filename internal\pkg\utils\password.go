package utils

import (
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"github.com/golang-jwt/jwt/v5"
	"go.uber.org/zap"
	"golang.org/x/crypto/bcrypt"
	"marketing/internal/pkg/log"
	"math/rand"
	"time"
)

type Claims struct {
	UserID     uint   `json:"user_id"`
	UserName   string `json:"user_name"`
	SystemType string `json:"system_type"`
	jwt.RegisteredClaims
}

//var jwtKey = []byte("J3Hn5F+e6Q7W2SPm8iFkz8dHGV1KbsU3RPeqA==")

// HashPassword 将明文密码转换为哈希密码
func HashPassword(password string) string {
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		log.Error("生成hash密码出错：", zap.Error(err))
		return ""
	}
	return string(hashedPassword)
}

// PasswordVerify verifies the provided password against the hashed password.
func PasswordVerify(password string, hash string) bool {
	// Compare the provided password with the hashed password
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	if err != nil {
		log.Error("校验hash密码出错：", zap.Error(err))
	}
	return err == nil
}

// GenerateToken 生成token(废弃)
func GenerateToken(username, password string) string {
	now := time.Now().Unix() // 获取当前时间戳
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	randomNum := r.Intn(900000) + 100000
	data := fmt.Sprintf("%s%s%d%d", username, password, now, randomNum) // 将用户名、密码和时间戳组合
	hash := sha256.New()
	hash.Write([]byte(data))                 // 计算 SHA-256 哈希值
	return hex.EncodeToString(hash.Sum(nil)) // 返回十六进制哈希结果
}

// GenerateAccessToken generates a new access token
func GenerateAccessToken(uid uint, username string, systemType string, accessTokenExpiresIn time.Duration, jwtKey string) (string, error) {
	expirationTime := time.Now().Add(accessTokenExpiresIn)
	claims := &Claims{
		UserID:     uid,
		UserName:   username,
		SystemType: systemType,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expirationTime),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(jwtKey))
}

// GenerateRefreshToken generates a new refresh token
func GenerateRefreshToken(uid uint, username string, systemType string, refreshTokenExpiresIn time.Duration, jwtKey string) (string, error) {
	expirationTime := time.Now().Add(refreshTokenExpiresIn)
	claims := &Claims{
		UserID:     uid,
		UserName:   username,
		SystemType: systemType,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expirationTime),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(jwtKey))
}

func ValidateToken(tokenString, jwtKey string) (*Claims, error) {
	claims := &Claims{}
	token, err := jwt.ParseWithClaims(tokenString, claims, func(token *jwt.Token) (interface{}, error) {
		return []byte(jwtKey), nil
	})

	if err != nil || !token.Valid {
		return nil, err
	}
	return claims, nil
}
