package model

import (
	"marketing/internal/pkg/types"
)

type UserAgency struct {
	ID           int              `gorm:"primaryKey;autoIncrement"`
	UID          uint             `gorm:"not null;comment:'用户id,这个id来自admin_users表'"`
	TopAgency    uint             `gorm:"comment:'一级代理id'"`
	SecondAgency uint             `gorm:"default:0;comment:'二级代理id'"`
	Partition    uint             `gorm:"comment:'大区id'"`
	RoleID       uint             `gorm:"not null;default:0;comment:'角色id,8是活动管理员,拥有管理员的权限,9是大区经理,10是总代'"`
	WeixinID     string           `gorm:"type:varchar(255);comment:'微信昵称'"`
	Remark       string           `gorm:"type:text;not null;comment:'备注'"`
	Email        string           `gorm:"type:varchar(60);not null;comment:'邮箱'"`
	UpdatedAt    types.CustomTime `gorm:"type:timestamp"`
	CreatedAt    types.CustomTime `gorm:"type:timestamp"`
	Status       int8             `gorm:"not null;default:1;comment:'状态,0为禁用,1为启用'"`
	Phone        string           `gorm:"type:varchar(50);comment:'电话号码'"`
}
