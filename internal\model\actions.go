package model

import "time"

// Actions 代表actions 活动表
type Actions struct {
	ID                  uint      `gorm:"column:id;type:int(11) unsigned;not null;autoIncrement;comment:'唯一id'"`
	Number              string    `gorm:"column:number;type:varchar(50);not null;default:'';comment:'活动编号'"`
	UID                 uint      `gorm:"column:uid;type:int(11) unsigned;not null;default:0;comment:'活动申请用户'"`
	Type                int       `gorm:"column:type;type:int(11);not null;default:0;comment:'活动类型'"`
	Name                string    `gorm:"column:name;type:varchar(50);not null;default:'';comment:'活动名称'"`
	Duration            string    `gorm:"column:duration;type:varchar(50);not null;default:'';comment:'活动时间'"`
	DateStart           string    `gorm:"column:date_start;type:date;not null;comment:'活动开始时间'"`
	DateEnd             string    `gorm:"column:date_end;type:date;not null;comment:'活动结束时间'"`
	Space               uint      `gorm:"column:space;type:int(11) unsigned;not null;default:0;comment:'活动面积'"`
	EndpointID          uint      `gorm:"column:endpoint_id;type:int(11) unsigned;not null;default:0;comment:'终端id'"`
	SecondAgencyID      uint      `gorm:"column:second_agency_id;type:int(11) unsigned;not null;default:0;comment:'二级代理id'"`
	TopAgencyID         uint      `gorm:"column:top_agency_id;type:int(11) unsigned;not null;default:0;comment:'一级代理id'"`
	Level               int       `gorm:"column:level;type:int(11);not null;default:0;comment:'活动等级'"`
	Principal           string    `gorm:"column:principal;type:varchar(50);not null;default:'';comment:'负责人'"`
	Phone               string    `gorm:"column:phone;type:varchar(50);not null;default:'';comment:'电话'"`
	Plan                string    `gorm:"column:plan;type:text;not null;comment:'计划内容'"`
	Staff               string    `gorm:"column:staff;type:text;not null;comment:'人员配置'"`
	Description         string    `gorm:"column:description;type:text;not null;comment:'活动方案阐述'"`
	SitePhoto           string    `gorm:"column:site_photo;type:text;not null;comment:'场地图片'"`
	Advertise           string    `gorm:"column:advertise;type:text;not null;comment:'前期宣传彩页'"`
	PrepareAttach       string    `gorm:"column:prepare_attach;type:text;not null;comment:'准备附件'"`
	Theme               string    `gorm:"column:theme;type:varchar(50);not null;default:'';comment:'活动主题'"`
	Content             string    `gorm:"column:content;type:text;not null;comment:'活动内容'"`
	Total               uint      `gorm:"column:total;type:int(11) unsigned;not null;default:0;comment:'销售台数'"`
	Amount              uint      `gorm:"column:amount;type:int(11) unsigned;not null;default:0;comment:'销售金额'"`
	Finish              *uint8    `gorm:"column:finish;type:tinyint(3) unsigned;default:null;comment:'是否已完成活动'"`
	PhotoPreparing      string    `gorm:"column:photo_preparing;type:text;not null;comment:'活动前期准备图片'"`
	Photo               string    `gorm:"column:photo;type:text;not null;comment:'活动图片'"`
	PhotoFinished       string    `gorm:"column:photo_finished;type:text;not null;comment:'活动结束图片'"`
	Video               string    `gorm:"column:video;type:varchar(100);not null;default:'';comment:'活动视频'"`
	SalesDetails        string    `gorm:"column:sales_details;type:text;not null;comment:'销售明细'"`
	Summary             string    `gorm:"column:summary;type:text;not null;comment:'活动总结'"`
	ExpenseAttach       string    `gorm:"column:expense_attach;type:text;not null;comment:'费用附件'"`
	DQAuditUserID       *uint     `gorm:"column:dq_audit_user_id;type:int(11) unsigned;default:null;comment:'大区审核人id'"`
	DQAuditTime         string    `gorm:"column:dq_audit_time;type:varchar(50);default:'';comment:'大区审核时间'"`
	DQAuditOpinion      string    `gorm:"column:dq_audit_opinion;type:text;comment:'大区审核意见'"`
	DQAuditPass         *uint8    `gorm:"column:dq_audit_pass;type:tinyint(4);default:0;comment:'大区审核通过'"`
	AuditUserID         *uint     `gorm:"column:audit_user_id;type:int(11) unsigned;default:null;comment:'审核人id'"`
	AuditTime           string    `gorm:"column:audit_time;type:varchar(50);default:'';comment:'审核时间'"`
	AuditOpinion        string    `gorm:"column:audit_opinion;type:text;comment:'审核意见'"`
	AuditPass           *uint8    `gorm:"column:audit_pass;type:tinyint(4);default:0;comment:'审核通过'"`
	PayUserID           *uint     `gorm:"column:pay_user_id;type:int(11) unsigned;default:null;comment:'报销人id'"`
	PayTime             string    `gorm:"column:pay_time;type:varchar(50);default:'';comment:'报销时间'"`
	PayOpinion          string    `gorm:"column:pay_opinion;type:text;comment:'报销意见'"`
	PayGiftSupport      uint8     `gorm:"column:pay_gift_support;type:tinyint(3) unsigned;not null;default:0;comment:'赠品支持，0-不支持，1-免费书包，2-半价书包，3-半价补习袋'"`
	PayPass             *uint8    `gorm:"column:pay_pass;type:tinyint(4);default:0;comment:'报销通过'"`
	Status              uint8     `gorm:"column:status;type:int(11) unsigned;not null;default:0;comment:'活动状态'"`
	WriteOffCorporation string    `gorm:"column:write_off_corporation;type:varchar(50);not null;default:'';comment:'申请核销公司'"`
	ActivityID          int       `gorm:"column:activity_id;type:int(11);not null;default:0;comment:'关联的终端服务活动的id，0-不关联'"`
	Location            string    `gorm:"column:location;type:varchar(100);not null;default:'';comment:'活动地点'"`
	GiftNum             uint8     `gorm:"column:gift_num;type:smallint(5) unsigned;not null;default:0;comment:'申请赠品数量'"`
	GiftMoney           uint      `gorm:"column:gift_money;type:int(10) unsigned;not null;default:0;comment:'赠品支持金额，单位分'"`
	GiftReceiver        string    `gorm:"column:gift_receiver;type:varchar(20);not null;default:'';comment:'赠品收件人'"`
	GiftReceiverAddress string    `gorm:"column:gift_receiver_address;type:varchar(100);not null;default:'';comment:'赠品收件人地址'"`
	GiftReceiverPhone   string    `gorm:"column:gift_receiver_phone;type:varchar(30);not null;default:'';comment:'收件人联系电话'"`
	GiftProof           string    `gorm:"column:gift_proof;type:text;not null;comment:'赠品订购付款凭证及出货单'"`
	Materials           string    `gorm:"column:materials;type:text;not null;comment:'物料清单'"`
	AccountUserID       uint      `gorm:"column:account_user_id;type:int(10) unsigned;not null;default:0;comment:'入账人，关联admin_users'"`
	AccountOpinion      string    `gorm:"column:account_opinion;type:varchar(200);not null;default:'';comment:'入账意见'"`
	SupportMoney        string    `gorm:"column:support_money;type:varchar(20);not null;default:'';comment:'活动支持金额'"`
	AccountTime         time.Time `gorm:"column:account_time;type:datetime;default:null;comment:'入账时间'"`
	ApplicationYear     int       `gorm:"column:application_year;type:smallint(5) unsigned;not null;default:0;comment:'申请年度，区分不同的业务逻辑'"`
	PrizeBike           string    `gorm:"column:prize_bike;type:text;comment:'类型：2024-Second-Special下的电动车抽奖实拍图片'"`
	DouyinPhoto         string    `gorm:"column:douyin_photo;type:text;comment:'类型：2024-Second-Special下的抖音投流照片'"`
	ActivityTheme       string    `gorm:"column:activity_theme;type:text;comment:'类型：2024-Comprehensive-Support下活动主题背景照片'"`
	CreateAt            time.Time `gorm:"column:created_at;type:int(11);not null;default:0;comment:'创建时间'"`
	UpdateAt            time.Time `gorm:"column:updated_at;type:int(11);not null;default:0;comment:'更新时间'"`
}

// FinishInfoAction 完成活动信息
type FinishInfoAction struct {
	UpdatedAt       time.Time `gorm:"updated_at"`
	PhotoPreparing  string    `gorm:"column:photo_preparing"`
	Photo           string    `gorm:"column:photo"`
	PhotoFinished   string    `gorm:"column:photo_finished"`
	Video           string    `gorm:"column:video"`
	Summary         string    `gorm:"column:summary"`
	SalesDetails    string    `gorm:"column:sales_details"`
	Theme           string    `gorm:"column:theme"`
	Content         string    `gorm:"column:content"`
	Total           uint      `gorm:"column:total"`
	Amount          uint      `gorm:"column:amount;"`
	Advertise       string    `gorm:"column:advertise"`
	ExpenseAttach   string    `gorm:"column:expense_attach;"`
	PrepareAttach   string    `gorm:"column:prepare_attach"`
	ApplicationYear int       `gorm:"column:application_year"`
	Materials       string    `gorm:"column:materials;"`
	DouyinPhoto     string    `gorm:"column:douyin_photo;"`
	Type            int       `gorm:"column:type;"`
}
type ApplyInfoActions struct { //21
	UID                 uint      `gorm:"column:uid"`
	Type                int       `gorm:"column:type"`
	Name                string    `gorm:"column:name"`
	DateStart           time.Time `gorm:"column:date_start"`
	DateEnd             time.Time `gorm:"column:date_end"`
	Space               uint      `gorm:"column:space"`
	EndpointID          uint      `gorm:"column:endpoint_id"`
	SecondAgencyID      uint      `gorm:"column:second_agency_id"`
	TopAgencyID         uint      `gorm:"column:top_agency_id"`
	Level               int       `gorm:"column:level"`
	Principal           string    `gorm:"column:principal"`
	Phone               string    `gorm:"column:phone"`
	Plan                string    `gorm:"column:plan"`
	Staff               string    `gorm:"column:staff"`
	Description         string    `gorm:"column:description"`
	SitePhoto           string    `gorm:"column:site_photo"`
	WriteOffCorporation string    `gorm:"column:write_off_corporation"`
	ActivityID          int       `gorm:"column:activity_id"`
	Location            string    `gorm:"column:location"`
	CreateAt            time.Time `gorm:"column:created_at"`
	EndpointName        string    `gorm:"column:endpoint_name"`
	ApplicationYear     int       `gorm:"column:application_year"`
	TypeName            string    `gorm:"type_name"`
	GiftSupport         uint8     `gorm:"gift_support"`
}
type InfoActionType struct {
	Name string `gorm:"column:name"`
	Slug string `gorm:"column:slug"`
}
type ActionJoin struct {
	Actions
	TypeName     string `gorm:"column:type_name"`
	EndpointName string `gorm:"column:endpoint_name"`
}
