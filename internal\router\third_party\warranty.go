package third_party

import (
	"gorm.io/gorm"
	"marketing/internal/dao/warranty"
	"marketing/internal/handler/third_party"
	service "marketing/internal/service/warranty"

	"github.com/gin-gonic/gin"
)

type WarrantyRouter struct {
	Db *gorm.DB
}

func NewWarrantyRouter(Db *gorm.DB) *WarrantyRouter {
	return &WarrantyRouter{
		Db: Db,
	}
}

func (a *WarrantyRouter) Register(r *gin.RouterGroup) {

	//活动管理模块
	warrantyRouter := r.Group("/warranty")
	{
		// 创建warranty相关组件
		warrantyDao := warranty.NewWarrantyDao(a.Db)
		warrantyService := service.NewWarrantyService(warrantyDao)
		warrantyHandler := third_party.NewWarrantyHandler(warrantyService)

		// 公开接口（无需认证）
		warrantyRouter.GET("/get-by-phone", warrantyHandler.GetByPhone)

	}

}
