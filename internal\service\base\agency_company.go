package base

import (
	"github.com/spf13/cast"
	"marketing/internal/dao"
	"marketing/internal/dao/admin_user"
	appError "marketing/internal/pkg/errors"

	"github.com/gin-gonic/gin"
)

// AgencyCompanyResponse 代理公司响应结构
type AgencyCompanyResponse struct {
	ID       int                         `json:"id"`
	Name     string                      `json:"name"`
	Children []AgencyCompanyChildrenResp `json:"children"`
}

// AgencyCompanyChildrenResp 代理公司子项响应结构
type AgencyCompanyChildrenResp struct {
	ID   int    `json:"id"`
	Name string `json:"name"`
}

// AgencyCompanyService 代理公司服务接口
type AgencyCompanyService interface {
	GetAgencyCompanyList(c *gin.Context) ([]AgencyCompanyResponse, error)
	GetAgencyCompany(c *gin.Context) ([]map[string]any, error)
}

type agencyCompanyService struct {
	agencyDao dao.AgencyDao
	userDao   admin_user.UserDao
}

// NewAgencyCompanyService 创建代理公司服务实例
func NewAgencyCompanyService(userDao admin_user.UserDao) AgencyCompanyService {
	return &agencyCompanyService{
		agencyDao: dao.NewAgencyDao(),
		userDao:   userDao,
	}
}

// GetAgencyCompanyList 获取代理公司列表，管理端
func (s *agencyCompanyService) GetAgencyCompanyList(c *gin.Context) ([]AgencyCompanyResponse, error) {
	// 调用DAO层获取数据
	results, err := s.agencyDao.GetAgencyCompanyList(c, 0)
	if err != nil {
		return nil, err
	}

	// 处理数据，按代理ID分组
	agencyMap := make(map[int]*AgencyCompanyResponse)
	for _, result := range results {
		if result.CompanyID != nil && result.CompanyName != nil {
			if agency, exists := agencyMap[result.AgencyID]; exists {
				// 代理已存在，添加公司信息
				agency.Children = append(agency.Children, AgencyCompanyChildrenResp{
					ID:   *result.CompanyID,
					Name: *result.CompanyName,
				})
			} else {
				// 创建新的代理记录
				agencyMap[result.AgencyID] = &AgencyCompanyResponse{
					ID:   result.AgencyID,
					Name: result.AgencyName,
					Children: []AgencyCompanyChildrenResp{
						{
							ID:   *result.CompanyID,
							Name: *result.CompanyName,
						},
					},
				}
			}
		}
	}

	// 转换为切片返回
	var response []AgencyCompanyResponse
	for _, agency := range agencyMap {
		response = append(response, *agency)
	}

	return response, nil
}

// GetAgencyCompany 获取代理公司,代理商端
func (s *agencyCompanyService) GetAgencyCompany(c *gin.Context) ([]map[string]any, error) {

	uid := c.GetUint("uid")

	agency, err := s.userDao.UserAgency(c, uid)
	if err != nil {
		return nil, err
	}
	if agency == nil || agency.ID == 0 {
		return nil, appError.NewErr("用户未绑定代理商")
	}
	agencyId := cast.ToInt(agency.ID)
	// 调用DAO层获取数据
	results, err := s.agencyDao.GetAgencyCompanyList(c, agencyId)
	if err != nil {
		return nil, err
	}

	// 处理数据，按代理ID分组
	var agencyMap []map[string]any
	for _, v := range results {
		temp := make(map[string]any)
		temp["id"] = v.CompanyID
		temp["name"] = v.CompanyName
		agencyMap = append(agencyMap, temp)
	}

	return agencyMap, nil
}
