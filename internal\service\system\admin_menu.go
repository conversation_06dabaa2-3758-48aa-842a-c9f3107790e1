package system

import (
	"errors"
	api "marketing/internal/api/system"
	"marketing/internal/model"
	appError "marketing/internal/pkg/errors"
	"sort"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type AdminMenuInterface interface {
	Add(c *gin.Context, req api.AddMenuReq) error
	Lists(c *gin.Context, param api.AdminMenuReq) ([]api.AdminMenuResp, error)
	Update(c *gin.Context, req api.AddMenuReq) error
	Delete(c *gin.Context, id int) error
	BuildMenuTree(items []model.AdminMenuV2) []*api.MenuItem
}
type adminMenuSvc struct {
	db *gorm.DB
}

func NewAdminMenuSvc(db *gorm.DB) AdminMenuInterface {
	return &adminMenuSvc{
		db: db,
	}
}

func (svc *adminMenuSvc) Add(c *gin.Context, req api.AddMenuReq) error {
	//判断是否存在
	var existMenu model.AdminMenuV2

	err := svc.db.WithContext(c).Where("name = ?", req.Name).First(&existMenu).Error

	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}
	if existMenu.ID > 0 {
		return appError.NewErr("菜单路由标志：【" + req.Name + "】已经存在")
	}
	//创建用户
	var data = &model.AdminMenuV2{
		ParentID:        req.ParentID,
		MenuType:        req.MenuType,
		Title:           req.Title,
		Name:            req.Name,
		Path:            req.Path,
		Component:       req.Component,
		Rank:            req.Rank,
		Redirect:        req.Redirect,
		Icon:            req.Icon,
		ExtraIcon:       req.ExtraIcon,
		EnterTransition: req.EnterTransition,
		LeaveTransition: req.LeaveTransition,
		ActivePath:      req.ActivePath,
		Permissions:     req.Permissions,
		FrameSrc:        req.FrameSrc,
		FrameLoading:    req.FrameLoading,
		KeepAlive:       req.KeepAlive,
		HiddenTag:       req.HiddenTag,
		FixedTag:        req.FixedTag,
		ShowLink:        req.ShowLink,
		ShowParent:      req.ShowParent,
		SystemType:      req.SystemType,
		CreatedAt:       time.Now().Format(time.DateTime), // 假设创建时间为当前时间
		UpdatedAt:       time.Now().Format(time.DateTime), // 假设修改时间为当前时间
	}
	return svc.db.WithContext(c).Create(data).Error
}

func (svc *adminMenuSvc) Lists(c *gin.Context, param api.AdminMenuReq) ([]api.AdminMenuResp, error) {

	var list []api.AdminMenuResp

	query := svc.db.WithContext(c).Model(&model.AdminMenuV2{})

	//条件处理
	query = query.Where("system_type = ?", param.SystemType)
	if param.Name != "" {
		query = query.Where("name = ?", param.Name)
	}
	if param.Title != "" {
		query = query.Where("title like ?", "%"+param.Title+"%")
	}

	err := query.Scan(&list).Error
	return list, err
}

func (svc *adminMenuSvc) Update(c *gin.Context, req api.AddMenuReq) error {
	//判断是否存在
	var existMenu model.AdminMenuV2

	err := svc.db.WithContext(c).Where("id = ?", req.ID).First(&existMenu).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return appError.NewErr("菜单不存在")
	} else if err != nil {
		return err
	}

	//修改菜单
	existMenu.ParentID = req.ParentID
	existMenu.MenuType = req.MenuType
	existMenu.Title = req.Title
	existMenu.Name = req.Name
	existMenu.Path = req.Path
	existMenu.Component = req.Component
	existMenu.Rank = req.Rank
	existMenu.Redirect = req.Redirect
	existMenu.Icon = req.Icon
	existMenu.ExtraIcon = req.ExtraIcon
	existMenu.EnterTransition = req.EnterTransition
	existMenu.LeaveTransition = req.LeaveTransition
	existMenu.ActivePath = req.ActivePath
	existMenu.Permissions = req.Permissions
	existMenu.FrameSrc = req.FrameSrc
	existMenu.FrameLoading = req.FrameLoading
	existMenu.KeepAlive = req.KeepAlive
	existMenu.HiddenTag = req.HiddenTag
	existMenu.FixedTag = req.FixedTag
	existMenu.ShowLink = req.ShowLink
	existMenu.ShowParent = req.ShowParent
	existMenu.SystemType = req.SystemType
	existMenu.UpdatedAt = time.Now().Format(time.DateTime)

	return svc.db.WithContext(c).Save(existMenu).Error
}

func (svc *adminMenuSvc) Delete(c *gin.Context, id int) error {
	//判断是否已经存在
	var existMenu model.AdminMenuV2

	err := svc.db.WithContext(c).Where("id = ?", id).First(&existMenu).Error

	if errors.Is(err, gorm.ErrRecordNotFound) {
		return appError.NewErr("菜单不存在")
	}

	if err != nil {
		return err
	}
	return svc.db.WithContext(c).Delete(&existMenu).Error
}

// BuildMenuTree 根据 parent_id 组装菜单树
func (svc *adminMenuSvc) BuildMenuTree(items []model.AdminMenuV2) []*api.MenuItem {
	// 检查输入是否为空
	if len(items) == 0 {
		return []*api.MenuItem{}
	}
	// 根据 rank 排序
	sort.SliceStable(items, func(i, j int) bool {
		return items[i].Rank < items[j].Rank
	})
	// 创建一个映射，用于存储所有菜单项
	itemMap := make(map[uint]*api.MenuItem)
	var roots []*api.MenuItem
	// 将所有菜单项放入映射
	for _, v := range items {
		var perm []string
		if v.Permissions != "" {
			perm = strings.Split(v.Permissions, ",")
		}

		tempMeta := api.MetaData{
			Icon:        v.Icon,
			Title:       v.Title,
			Rank:        v.Rank,
			KeepAlive:   v.KeepAlive,
			ShowLink:    v.ShowLink,
			Permissions: perm,
		}
		temp := api.MenuItem{
			ID:        v.ID,
			ParentID:  v.ParentID,
			Path:      v.Path,
			Component: v.Component,
			Meta:      tempMeta,
			Children:  []*api.MenuItem{},
		}

		itemMap[v.ID] = &temp
	}

	// 根据 parent_id 构建树状结构
	for _, item := range items {
		if item.ParentID == 0 {
			// 没有父级，认为是根节点
			roots = append(roots, itemMap[item.ID])
		} else {
			// 有父级，找到父菜单，将当前菜单添加到父菜单的子菜单中
			if parent, exists := itemMap[item.ParentID]; exists {
				parent.Children = append(parent.Children, itemMap[item.ID])
			}
		}
	}

	return roots
}
