package dao

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/spf13/cast"
	"gorm.io/gorm/clause"
	"marketing/internal/api/materials"
	"marketing/internal/model"
	"sort"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type TrainSubjectDao interface {
	List(c *gin.Context, req materials.TrainSubjectListReq) ([]materials.TrainSubjectList, int64, error)
	Detail(c *gin.Context, id int) (model.Subject, error)
	Create(c *gin.Context, m model.TrainSubject) (int, error)
	Update(c *gin.Context, m model.TrainSubject) (int, error)
	AdminUser(c *gin.Context, id int) (string, error)
	EndpointType(c *gin.Context, ids []string) (string, error)
	EndpointTypesTips(c *gin.Context) ([]map[string]any, error)
	RegionTips(c *gin.Context) ([]materials.AgencyTips, error)
	CourseCreate(c *gin.Context, param []model.TrainCourse, con []materials.SubjectWithPaper) ([]uint, error)
	PracticeCreate(c *gin.Context, param materials.TrainPractice) (int, error)
	PracticeUpdate(c *gin.Context, param materials.TrainPractice) (int, error)
	SubjectWithCourse(c *gin.Context, subjectID int) ([]model.TrainSubjectCourse, error)
	DeleteSubjectWithCourse(c *gin.Context, subject int, course int) error
	CreateSubjectWithCourse(c *gin.Context, i uint, id uint, id2 uint) error
	CourseDetail(c *gin.Context, id uint) (model.TrainCourse, error)
	CourseUpdate(c *gin.Context, param model.TrainCourse, con materials.SubjectWithPaper) error
	PracticeDelete(c *gin.Context, id int, subject int) error
	SubjectWithPractice(c *gin.Context, id int) (model.TrainPractice, error)
	SaveToCourse(c *gin.Context, id int, courses []materials.TrainCourse, courseType string) error
	SaveToPractice(c *gin.Context, id int, practice materials.TrainPractice) error
	BindToEndpoint(c *gin.Context, endpoint *model.Endpoint) error
}
type GormTrainSubjectDao struct {
	db *gorm.DB
}

func (g *GormTrainSubjectDao) SaveToCourse(c *gin.Context, id int, courses []materials.TrainCourse, courseType string) error {
	// 查看原有课时
	secs, err := g.SubjectWithCourse(c, id)
	if err != nil {
		return fmt.Errorf("获取课时关联失败: %w", err)
	}

	// 将课程关联切片转换为映射，用于跟踪哪些课程需要删除
	existingCourses := make(map[uint]bool)
	for _, course := range secs {
		existingCourses[course.CourseID] = true
	}

	return g.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		var newCourses []model.TrainCourse
		var subjectWithPapers []materials.SubjectWithPaper
		// 1. 处理课程关联
		for _, v := range courses {
			if v.CourseID != 0 {
				// 更新课程信息
				row := tx.Model(&model.TrainCourse{}).Where("id = ?", v.CourseID).Updates(map[string]interface{}{
					"name":            v.Name,
					"is_use":          v.IsUse,
					"resource_url":    v.Url,
					"description":     v.Description,
					"issuer":          v.Issuer,
					"live_start_time": v.LiveStartTime,
					"live_end_time":   v.LiveEndTime,
				})

				if row.Error != nil {
					return fmt.Errorf("更新课程信息失败: %w", err)
				}
				// 检查课程是否存在
				if row.RowsAffected == 0 {
					return fmt.Errorf("课程不存在: %d", v.CourseID)
				}

				if !existingCourses[v.CourseID] {
					// 新增课时联系
					if err := tx.WithContext(c).Create(&model.TrainSubjectCourse{
						SubjectID:          uint(id),
						CourseID:           v.CourseID,
						PaperID:            v.PaperID,
						Credit:             v.Credit,
						CreditLearningTime: uint16(v.CreditLearningTime),
					}).Error; err != nil {
						return fmt.Errorf("新增课时关联失败: %w", err)
					}
				} else {
					// 更新课时关联
					if err := tx.Model(&model.TrainSubjectCourse{}).
						Where("subject_id = ? AND course_id = ?", id, v.CourseID).
						Updates(map[string]interface{}{
							"paper_id":             v.PaperID,
							"credit":               v.Credit,
							"credit_learning_time": uint16(v.CreditLearningTime),
						}).Error; err != nil {
						return fmt.Errorf("更新课时关联失败: %w", err)
					}
				}
				existingCourses[v.CourseID] = false
			} else if v.CourseID == 0 {
				// 创建新的model.TrainCourse对象
				newCourse := model.TrainCourse{
					// 移除ID赋值，让数据库自动生成ID
					Name:        v.Name,
					IsUse:       v.IsUse,
					ResourceURL: v.Url,
					Description: v.Description,
					Issuer:      v.Issuer,
				}
				// 处理直播课时间
				if v.LiveStartTime != "" {
					startTime, _ := time.Parse("2006-01-02 15:04:05", v.LiveStartTime)
					newCourse.LiveStartTime = startTime
				}
				if v.LiveEndTime != "" {
					endTime, _ := time.Parse("2006-01-02 15:04:05", v.LiveEndTime)
					newCourse.LiveEndTime = endTime
				}
				newCourses = append(newCourses, newCourse)

				// 准备SubjectWithPaper数据
				subjectWithPapers = append(subjectWithPapers, materials.SubjectWithPaper{
					SubjectID:          uint(id),
					PaperID:            v.PaperID,
					Credit:             v.Credit,
					CreditLearningTime: v.CreditLearningTime,
				})
			}
		}

		// 批量创建新课程
		if len(newCourses) > 0 {
			// 批量新增课程
			if err := tx.Model(&model.TrainCourse{}).Create(&newCourses).Error; err != nil {
				return fmt.Errorf("新增课时错误: %w", err)
			}

			// 创建 TrainSubjectCourse 切片
			subjectCourses := make([]model.TrainSubjectCourse, len(newCourses))
			for k, v := range newCourses {
				subjectCourses[k] = model.TrainSubjectCourse{
					SubjectID:          subjectWithPapers[k].SubjectID,
					PaperID:            subjectWithPapers[k].PaperID,
					Credit:             subjectWithPapers[k].Credit,
					CreditLearningTime: uint16(subjectWithPapers[k].CreditLearningTime),
					CourseID:           v.ID,
				}
			}

			// 批量新增课程关联
			if err := tx.Model(&model.TrainSubjectCourse{}).Create(&subjectCourses).Error; err != nil {
				return fmt.Errorf("新增课时关联错误: %w", err)
			}
		}

		// 3. 处理删除课程关联
		for courseID, used := range existingCourses {
			if used {
				if err := tx.Where("subject_id = ? and course_id = ?", id, courseID).Delete(&model.TrainSubjectCourse{}).Error; err != nil {
					return fmt.Errorf("删除课时联系错误: %w", err)
				}
			}
		}

		return nil
	})
}

func (g *GormTrainSubjectDao) SaveToPractice(c *gin.Context, id int, practice materials.TrainPractice) error {
	// 查看原有大实战
	practices, err := g.SubjectWithPractice(c, id)
	if err != nil {
		return fmt.Errorf("获取大实战信息失败: %w", err)
	}

	return g.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		// 删除原有的大实战
		if practices.ID != 0 && practices.ID != practice.ID {
			err = tx.Delete(&model.TrainPractice{}, "id = ? AND subject_id = ?", practices.ID, id).Error
			if err != nil {
				return fmt.Errorf("删除大实战错误: %w", err)
			}
		}

		// 实战课程处理
		if practice.Name != "" {
			if practice.ID == 0 {
				// 创建新的大实战
				now := time.Now()
				p := model.TrainPractice{
					SubjectID:   practice.SubjectID,
					IsUse:       practice.IsUse,
					Description: practice.Description,
					MinCredit:   practice.MinCredit,
					Name:        practice.Name,
					MaxCredit:   practice.MaxCredit,
					CreatedAt:   now.Format("2006-01-02 15:04:05"),
				}
				if err := tx.Create(&p).Error; err != nil {
					return fmt.Errorf("新增大实战错误: %w", err)
				}
			} else {
				// 更新现有大实战
				if err := tx.Model(&model.TrainPractice{}).Where("id = ?", practice.ID).Updates(map[string]interface{}{
					"subject_id":  practice.SubjectID,
					"is_use":      practice.IsUse,
					"description": practice.Description,
					"min_credit":  practice.MinCredit,
					"name":        practice.Name,
					"max_credit":  practice.MaxCredit,
				}).Error; err != nil {
					return fmt.Errorf("更新大实战错误: %w", err)
				}
			}
		}

		return nil
	})
}

func (g *GormTrainSubjectDao) SubjectWithPractice(c *gin.Context, id int) (model.TrainPractice, error) {
	var subject model.TrainPractice
	err := g.db.WithContext(c).Where("subject_id = ?", id).Where("deleted_at IS NULL").Find(&subject).Error
	return subject, err
}

func (g *GormTrainSubjectDao) PracticeDelete(c *gin.Context, id int, subject int) error {
	err := g.db.WithContext(c).Delete(&model.TrainPractice{}, "id = ? AND subject_id", id, subject).Error
	return err
}

func (g *GormTrainSubjectDao) CourseUpdate(c *gin.Context, param model.TrainCourse, con materials.SubjectWithPaper) error {
	return g.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		row := tx.Model(&model.TrainCourse{}).Where("id = ?", param.ID).
			Updates(map[string]interface{}{
				"name":            param.Name,
				"is_use":          param.IsUse,
				"resource_url":    param.ResourceURL,
				"live_start_time": param.LiveStartTime,
				"live_end_time":   param.LiveEndTime,
				"issuer":          param.Issuer,
				"description":     param.Description,
			})
		if row.Error != nil {
			return row.Error
		}
		if row.RowsAffected == 0 {
			return gorm.ErrRecordNotFound
		}
		err := tx.Model(&model.TrainSubjectCourse{}).
			Where("course_id = ? AND subject_id=?", param.ID, con.SubjectID).
			Updates(map[string]interface{}{
				"paper_id":             con.PaperID,
				"credit":               con.Credit,
				"credit_learning_time": con.CreditLearningTime,
			}).Error
		return err
	})
}

func (g *GormTrainSubjectDao) CourseDetail(c *gin.Context, id uint) (model.TrainCourse, error) {
	var res model.TrainCourse
	err := g.db.WithContext(c).Where("id = ?", id).First(&res).Error
	return res, err
}

func (g *GormTrainSubjectDao) CreateSubjectWithCourse(c *gin.Context, i uint, id uint, id2 uint) error {
	return g.db.WithContext(c).Create(&model.TrainSubjectCourse{
		SubjectID: i,
		CourseID:  id,
		PaperID:   id2,
	}).Error
}

func (g *GormTrainSubjectDao) DeleteSubjectWithCourse(c *gin.Context, subject int, course int) error {

	err := g.db.WithContext(c).Where("subject_id = ? and course_id = ?", subject, course).Delete(&model.TrainSubjectCourse{}).Error
	return err
}

func (g *GormTrainSubjectDao) SubjectWithCourse(c *gin.Context, subjectID int) ([]model.TrainSubjectCourse, error) {
	var res []model.TrainSubjectCourse
	err := g.db.WithContext(c).Where("subject_id = ?", subjectID).Find(&res).Error
	return res, err
}

func (g *GormTrainSubjectDao) PracticeUpdate(c *gin.Context, param materials.TrainPractice) (int, error) {
	err := g.db.WithContext(c).Model(&model.TrainPractice{}).Where("id = ?", param.ID).Updates(map[string]interface{}{
		"subject_id":  param.SubjectID,
		"is_use":      param.IsUse,
		"description": param.Description,
		"min_credit":  param.MinCredit,
		"name":        param.Name,
		"max_credit":  param.MaxCredit,
	}).Error
	return int(param.ID), err
}

func (g *GormTrainSubjectDao) PracticeCreate(c *gin.Context, param materials.TrainPractice) (int, error) {
	var p = model.TrainPractice{
		SubjectID:   param.SubjectID,
		IsUse:       param.IsUse,
		Description: param.Description,
		MinCredit:   param.MinCredit,
		Name:        param.Name,
		MaxCredit:   param.MaxCredit,
	}
	err := g.db.WithContext(c).Create(&p).Error
	return int(p.ID), err
}

// CourseCreate 批量新增课时与课时联系
func (g *GormTrainSubjectDao) CourseCreate(c *gin.Context, param []model.TrainCourse, con []materials.SubjectWithPaper) ([]uint, error) {
	var ids []uint
	err := g.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		// 批量新增课程
		if err := tx.Model(&model.TrainCourse{}).Create(&param).Error; err != nil {
			return err
		}
		// 创建 TrainSubjectCourse 切片
		subjectCourses := make([]model.TrainSubjectCourse, len(param))
		for k, v := range param {
			ids = append(ids, v.ID)
			subjectCourses[k] = model.TrainSubjectCourse{
				SubjectID:          con[k].SubjectID,
				PaperID:            con[k].PaperID,
				Credit:             con[k].Credit,
				CreditLearningTime: uint16(con[k].CreditLearningTime),
				CourseID:           v.ID,
			}
		}
		// 批量新增课程关联
		if err := tx.Model(&model.TrainSubjectCourse{}).Create(&subjectCourses).Error; err != nil {
			return err
		}
		return nil
	})
	return ids, err
}

func (g *GormTrainSubjectDao) RegionTips(c *gin.Context) ([]materials.AgencyTips, error) {
	//查询所有代理
	var all []struct {
		ID    int
		Name  string
		Pid   int
		Level int
	}
	err := g.db.WithContext(c).Model(&model.Agency{}).
		Select("id", "name", "pid", "level").
		Where("deleted_at IS NULL").
		Order("id ASC").Find(&all).Error
	// 用于存储一级代理和其对应的二级代理映射关系
	firstAgencyMap := make(map[int]materials.AgencyTips)
	// 先遍历所有数据，将一级代理数据放入 firstAgencyMap 中
	for _, agency := range all {
		if agency.Level == 1 {
			firstAgencyMap[agency.ID] = materials.AgencyTips{
				ID:   agency.ID,
				Name: agency.Name,
				SecondAgency: []materials.SecondAgency{
					{
						ID:   0,
						Name: "直营",
						Pid:  agency.ID,
					},
				},
			}
		}
	}
	// 再次遍历所有数据，将二级代理数据添加到对应的一级代理中
	for _, agency := range all {
		if agency.Pid != 0 {
			if firstAgency, exists := firstAgencyMap[agency.Pid]; exists {
				firstAgency.SecondAgency = append(firstAgency.SecondAgency, materials.SecondAgency{
					ID:   agency.ID,
					Name: agency.Name,
					Pid:  agency.Pid,
				})
				firstAgencyMap[agency.Pid] = firstAgency
			}
		}
	}
	// 将 firstAgencyMap 中的数据转换为切片
	var result []materials.AgencyTips
	for _, tip := range firstAgencyMap {
		result = append(result, tip)
	}
	// 按照 ID 对结果切片进行排序
	sort.Slice(result, func(i, j int) bool {
		return result[i].ID < result[j].ID
	})

	return result, err
}

func (g *GormTrainSubjectDao) Update(c *gin.Context, m model.TrainSubject) (int, error) {
	err := g.db.WithContext(c).Model(&model.TrainSubject{}).Where("id=?", m.ID).Updates(map[string]any{
		"start_time":       m.StartTime,
		"end_time":         m.EndTime,
		"type":             m.Type,
		"enabled":          m.Enabled,
		"agencies":         m.Agencies,
		"brief_intro":      m.BriefIntro,
		"tag":              m.Tag,
		"compound_visible": m.CompoundVisible,
		"has_certificate":  m.HasCertificate,
		"lecturer":         m.Lecturer,
		"lecturer_avatar":  m.LecturerAvatar,
		"title":            m.Title,
	}).Error
	return int(m.ID), err
}

// Create 创建专题
func (g *GormTrainSubjectDao) Create(c *gin.Context, m model.TrainSubject) (int, error) {
	err := g.db.WithContext(c).Create(&m).Error
	return int(m.ID), err
}

// AdminUser 查询创建人名称
func (g *GormTrainSubjectDao) AdminUser(c *gin.Context, id int) (string, error) {
	var result string
	err := g.db.WithContext(c).Table("admin_users").
		Select("name").Where("id=?", id).First(&result).Error
	return result, err
}

// Detail 获取培训课程详情
func (g *GormTrainSubjectDao) Detail(c *gin.Context, id int) (model.Subject, error) {
	var subject model.Subject
	err := g.db.WithContext(c).Table("train_subject").
		Where("id=?", id).First(&subject).Error
	if err != nil {
		return subject, err
	}
	err = g.db.WithContext(c).Table("train_practice").
		Where("subject_id=?", id).
		Where("deleted_at IS NULL").
		Find(&subject.Practice).Error
	if err != nil {
		return subject, err
	}
	err = g.db.WithContext(c).Table("train_subject_course AS sc").
		Joins("LEFT JOIN train_course AS c ON sc.course_id=c.id").
		Where("sc.subject_id=?", id).
		Where("c.deleted_at IS NULL").
		Select("c.*").Find(&subject.TrainCourse).Error
	return subject, nil
}

// List 获取培训课程列表
func (g *GormTrainSubjectDao) List(c *gin.Context, req materials.TrainSubjectListReq) ([]materials.TrainSubjectList, int64, error) {
	curDB := g.db.WithContext(c).Table("train_subject AS s").
		Joins("LEFT JOIN train_subject_course AS sc ON s.id=sc.subject_id").
		Joins("LEFT JOIN train_certificate_drew AS cd ON s.id=cd.subject_id").
		Joins("LEFT JOIN train_practice AS p ON s.id=p.subject_id").
		Select("s.id, s.title,s.enabled, s.created_at,s.updated_at,s.type,num_papers,s.num_courses," +
			"COUNT(DISTINCT sc.id) AS had_course,COUNT(DISTINCT cd.id) AS had_certificate,COUNT(DISTINCT p.id) AS had_practice").
		Where("s.deleted_at IS NULL").
		Group("s.id")
	if req.Type != "" {
		curDB.Where("s.type=?", req.Type)
	}
	if req.Name != "" {
		curDB.Where("s.title LIKE ?", "%"+req.Name+"%")
	}
	if req.Enabled != "" {
		curDB.Where("s.enabled=?", req.Enabled)
	}
	var total int64
	curDB.Count(&total)
	var list []materials.TrainSubjectList
	curDB.Order("enabled DESC,id DESC").Offset((req.Page - 1) * req.PageSize).Limit(req.PageSize).Find(&list)
	return list, total, nil
}

// EndpointType 获取终端类型
func (g *GormTrainSubjectDao) EndpointType(c *gin.Context, ids []string) (string, error) {
	var result string
	err := g.db.WithContext(c).Table("endpoint_types").
		Select("GROUP_CONCAT(name)").
		Where("id IN (?)", ids).Find(&result).Error
	return result, err
}

// EndpointTypesTips 终端类型遍历
func (g *GormTrainSubjectDao) EndpointTypesTips(c *gin.Context) ([]map[string]any, error) {
	var result []map[string]any
	err := g.db.WithContext(c).Table("endpoint_types").
		Select("id, name").Find(&result).Error
	return result, err
}

// BindToEndpoint 绑定课程到新终端
func (g *GormTrainSubjectDao) BindToEndpoint(c *gin.Context, endpoint *model.Endpoint) error {
	if endpoint.ID == 0 {
		return nil
	}

	// 1. 删除原有绑定
	if err := g.db.WithContext(c).Where("endpoint_id = ?", endpoint.ID).
		Delete(&model.TrainSubjectEndpoint{}).Error; err != nil {
		return err
	}

	// 2. 分块处理所有课程
	batchSize := 100
	offset := 0

	for {
		var subjects []model.TrainSubject
		// 按批次查询
		if err := g.db.WithContext(c).Order("id").Offset(offset).Limit(batchSize).Find(&subjects).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				break
			}
			return err
		}

		if len(subjects) == 0 {
			break
		}

		// 准备批量插入数据
		var records []model.TrainSubjectEndpoint
		now := time.Now()

		for _, subject := range subjects {
			if subject.Agencies == "" {
				continue
			}

			// 解析机构数据
			var agencies map[string][]string
			if err := json.Unmarshal([]byte(subject.Agencies), &agencies); err != nil {
				continue
			}

			// 检查匹配条件
			for top, seconds := range agencies {
				if cast.ToInt(top) == endpoint.TopAgency {
					if endpoint.SecondAgency > 0 {
						for _, second := range seconds {
							if cast.ToInt(second) == endpoint.SecondAgency {
								// 找到匹配项，添加记录
								records = append(records, model.TrainSubjectEndpoint{
									SubjectID:  subject.ID,
									EndpointID: cast.ToUint(endpoint.ID),
									CreatedAt:  now,
								})
								break
							}
						}
					} else {
						records = append(records, model.TrainSubjectEndpoint{
							SubjectID:  subject.ID,
							EndpointID: cast.ToUint(endpoint.ID),
							CreatedAt:  now,
						})
					}
					break // 匹配后跳出当前课程循环
				}
			}
		}

		// 3. 批量插入 (使用 INSERT IGNORE)
		if len(records) > 0 {
			if err := g.db.WithContext(c).Clauses(clause.Insert{Modifier: "IGNORE"}).
				Create(&records).Error; err != nil {
				return err
			}
		}

		if len(subjects) < batchSize {
			break
		}
		offset += batchSize
	}

	return nil
}

func NewGormTrainSubjectDao(db *gorm.DB) TrainSubjectDao {
	return &GormTrainSubjectDao{db: db}
}
