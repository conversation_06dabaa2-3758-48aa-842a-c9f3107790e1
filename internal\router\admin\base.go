package admin

import (
	"marketing/internal/config"
	"marketing/internal/dao"
	"marketing/internal/handler/admin/endpoint"
	"marketing/internal/handler/admin/system"
	"marketing/internal/handler/base"
	"marketing/internal/pkg/oss"
	"marketing/internal/pkg/upload"
	"marketing/internal/provider"
	"marketing/internal/service"
	baseSvc "marketing/internal/service/base"

	"github.com/gin-gonic/gin"
)

type BaseRouter struct {
	regionHandler   base.RegionHandler
	endpointHandler endpoint.Endpoint
	svc             *provider.ServiceProvider
	cfg             *config.Config
	daoProvide      *provider.DaoProvider
}

func NewBaseRouter(regionHandler base.RegionHandler, endpointHandler endpoint.Endpoint, daoProvide *provider.DaoProvider, svc *provider.ServiceProvider, cfg *config.Config) *BaseRouter {
	return &BaseRouter{
		regionHandler:   regionHandler,
		endpointHandler: endpointHandler,
		daoProvide:      daoProvide,
		svc:             svc,
		cfg:             cfg,
	}
}

func (b *BaseRouter) Register(r *gin.RouterGroup) {
	//公共模块
	baseRouter := r.Group("/base")
	{
		client := oss.NewClient()
		uploadService := service.NewOssUploadService(upload.NewOssUploadServiceV2(client))
		uploadController := base.NewHandlerUploadFileV2(uploadService)
		baseRouter.POST("/upload_file/:path", uploadController.UploadFile)
		baseRouter.DELETE("/delete_file", uploadController.DeleteFile)

		//获取地区
		baseRouter.GET("/region", b.regionHandler.RegionList)

		//获取代理
		baseRouter.GET("/top-agencies", b.endpointHandler.GetTopAgency)
		baseRouter.GET("/second-agencies", b.endpointHandler.GetSecondAgency)
		baseRouter.GET("/endpoints", b.endpointHandler.GetAgencyEndpoint)

		//获取渠道类型
		channelDao := dao.NewChannelDao()
		channelService := service.NewChannelService(channelDao)
		agencyHandler := base.NewAgencyHandler(channelService, b.daoProvide.UserDao)
		baseRouter.GET("/channel-dropdown", agencyHandler.ChannelList)

		//获取代理公司列表
		baseRouter.GET("/agency-company", agencyHandler.AgencyCompanyList)

		//资源组下拉
		groupHandler := system.NewResourceGroupHandler(b.svc.ResourceGroupService)
		baseRouter.GET("/resource-groups/dropdown", groupHandler.GetResourceGroupsDropdown)

		//oss 临时凭证
		ossService := baseSvc.NewOssService(b.cfg)
		ossHandler := base.NewOssHandler(ossService)
		baseRouter.GET("/oss/sts", ossHandler.GetStsToken)

		//机型下拉列表
		machineTypeHandler := base.NewMachineHandler()
		baseRouter.GET("/models", machineTypeHandler.MachineTypeList)

		//获取用户下拉
		userHandler := base.NewUserHandler(b.svc.AdminUserService)
		baseRouter.GET("/users", userHandler.GetUserOptions)
	}
}
