package upload

import (
	"context"
	"fmt"

	"github.com/aliyun/alibabacloud-oss-go-sdk-v2/oss"
	"github.com/spf13/viper"
)

type FileLoadService interface {
	UploadOne(filename, filepath string, options ...func(options2 *oss.Options)) (url string, err error)
	DeleteOne(path string) error
	DeleteMultiple(keys []string) error // 新增批量删除接口
}

type OssUploadServiceV2 struct {
	*oss.Client
	BaseUrl string
	Bucket  string //"存储桶"
	Dir     string //"文件夹"
}

func (o *OssUploadServiceV2) UploadOne(filename, filepath string, options ...func(options2 *oss.Options)) (url string, err error) {
	filename = o.Dir + "/" + filename
	putReq := &oss.PutObjectRequest{
		Bucket: oss.Ptr(o.Bucket),
		Key:    oss.Ptr(filename),
	}
	_, err = o.PutObjectFromFile(context.TODO(), putReq, filepath, options...)
	if err != nil {
		return "", err
	}
	url = o.BaseUrl + filename
	return url, err
}

func (o *OssUploadServiceV2) DeleteOne(key string) error {
	req := &oss.DeleteObjectRequest{
		Bucket: oss.Ptr(o.Bucket),
		Key:    oss.Ptr(key),
	}
	_, err := o.Client.DeleteObject(context.TODO(), req)
	fmt.Println(err)
	if err != nil {
		return err
	}
	return nil
}

func (o *OssUploadServiceV2) DeleteMultiple(keys []string) error {
	if len(keys) == 0 {
		return nil
	}

	var deleteObjects []oss.DeleteObject
	for _, key := range keys {
		deleteObjects = append(deleteObjects, oss.DeleteObject{
			Key: oss.Ptr(o.Dir + "/" + key),
		})
	}

	req := &oss.DeleteMultipleObjectsRequest{
		Bucket:  oss.Ptr(o.Bucket),
		Objects: deleteObjects,
	}

	_, err := o.Client.DeleteMultipleObjects(context.TODO(), req)
	if err != nil {
		return fmt.Errorf("OSS多文件删除失败: %w", err)
	}
	return nil
}

func NewOssUploadServiceV2(client *oss.Client) FileLoadService {
	return &OssUploadServiceV2{
		BaseUrl: viper.GetString("oss.url"),
		Bucket:  viper.GetString("oss.bucket"),
		Client:  client,
		Dir:     viper.GetString("oss.path"),
	}
}
