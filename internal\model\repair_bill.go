package model

import (
	"time"
)

type RepairBillDetails struct {
	RepairBill
	Name            string                             `json:"name" gorm:"-"`             // 维修人员名称
	ModelName       string                             `json:"model_name" gorm:"-"`       // 模型名称
	EndpointName    string                             `json:"endpoint_name" gorm:"-"`    // 维修终端名称
	EndpointAddress string                             `json:"endpoint_address" gorm:"-"` // 维修终端地址
	EndpointPhone   string                             `json:"endpoint_phone" gorm:"-"`   // 维修终端电话
	MalfunctionList []RepairBillMachineMalfunctionInfo `json:"malfunction_list" gorm:"-"` // 故障信息列表
	AccessoryList   []RepairBillMachineAccessoryInfo   `json:"accessory_list" gorm:"-"`   // 配件信息列表
}

type RepairBill struct {
	Id                    int       `json:"id" gorm:"id"`                                 // id
	BillNumber            string    `json:"bill_number" gorm:"bill_number"`               // 工单编号
	BuyDate               time.Time `json:"-" gorm:"buy_date"`                            // 购机日期
	BuyDateStr            string    `json:"buy_date" gorm:"-"`                            // 购机日期
	ReceiveMachineDate    time.Time `json:"-" gorm:"receive_machine_date"`                // 维修收机日期
	ReceiveMachineDateStr string    `json:"receive_machine_date" gorm:"-"`                // 维修收机日期
	RepairSource          string    `json:"repair_source" gorm:"repair_source"`           // 维修来源
	SnCode                string    `json:"sn_code" gorm:"sn_code"`                       // sn码
	CustomerName          string    `json:"customer_name" gorm:"customer_name"`           // 顾客名字
	CustomerPhone         string    `json:"customer_phone" gorm:"customer_phone"`         // 顾客电话
	ModelId               string    `json:"model_id" gorm:"model_id"`                     // 机型id
	Description           string    `json:"description" gorm:"description"`               // 维修描述
	RepairReasonType      int       `json:"repair_reason_type" gorm:"repair_reason_type"` // 维修原因类型
	RepairType            int       `json:"repair_type" gorm:"repair_type"`               // 保修形式
	ExpressDate           time.Time `json:"-" gorm:"express_date"`                        // 快递寄出日期
	ExpressDateStr        string    `json:"express_date" gorm:"-"`                        // 快递寄出日期
	ExpressNumber         string    `json:"express_number" gorm:"express_number"`         // 快递单号
	RepairCost            float64   `json:"repair_cost" gorm:"repair_cost"`               // 维修费用
	CreateUid             int       `json:"create_uid" gorm:"create_uid"`                 // 创建维修单的uid
	RepairDeal            string    `json:"repair_deal" gorm:"repair_deal"`               // 维修处理
	Remark                string    `json:"remark" gorm:"remark"`                         // 维修备注
	IsChecked             int       `json:"is_checked" gorm:"is_checked"`                 // 是否出检查单
	EndpointId            int       `json:"endpoint_id" gorm:"endpoint_id"`               // 维修点id
	CreatedAt             time.Time `json:"-" gorm:"created_at"`                          // 创建时间
	CreateTime            string    `json:"create_time" gorm:"-"`                         // 创建时间
	UpdatedAt             time.Time `json:"-" gorm:"updated_at"`                          // 修改时间
	UpdateTime            string    `json:"update_time" gorm:"-"`                         // 修改时间
}

func (RepairBill) TableName() string {
	return "repair_bill"
}

type RepairBillMachineMalfunctionRelation struct {
	Id             int       `json:"id" gorm:"id"`                           // id
	RepairBillId   int       `json:"repair_bill_id" gorm:"repair_bill_id"`   // 维修订单id
	MalfunctionTop int       `json:"malfunction_top" gorm:"malfunction_top"` // 一级故障标签
	MalfunctionId  int       `json:"malfunction_id" gorm:"malfunction_id"`   // 二级故障标签
	CreatedAt      time.Time `json:"-" gorm:"created_at"`                    // 创建时间
	CreateTime     string    `json:"create_time" gorm:"-"`                   // 创建时间
	UpdatedAt      time.Time `json:"-" gorm:"updated_at"`                    // 修改时间
	UpdateTime     string    `json:"update_time" gorm:"-"`                   // 修改时间
}

func (RepairBillMachineMalfunctionRelation) TableName() string {
	return "repair_bill_machine_malfunction_relation"
}

type RepairBillMachineAccessoryRelation struct {
	Id                         int       `json:"id" gorm:"id"`                                                       // id
	RepairBillId               int       `json:"repair_bill_id" gorm:"repair_bill_id"`                               // 维修订单id
	MachineAccessoryRelationId int       `json:"machine_accessory_relation_id" gorm:"machine_accessory_relation_id"` // 配件id
	Amount                     int       `json:"amount" gorm:"amount"`                                               // 数量
	CreatedAt                  time.Time `json:"-" gorm:"created_at"`                                                // 创建时间
	CreateTime                 string    `json:"create_time" gorm:"-"`                                               // 创建时间
	UpdatedAt                  time.Time `json:"-" gorm:"updated_at"`                                                // 修改时间
	UpdateTime                 string    `json:"update_time" gorm:"-"`                                               // 修改时间
}

func (RepairBillMachineAccessoryRelation) TableName() string {
	return "repair_bill_machine_accessory_relation"
}

type RepairBillMachineMalfunctionInfo struct {
	BillId              int    `json:"bill_id" gorm:"bill_id"`
	MalfunctionLocId    int    `json:"malfunction_loc_id" gorm:"malfunction_loc_id"`       // 一级故障标签id
	MalfunctionLocName  string `json:"malfunction_loc_name" gorm:"malfunction_loc_name"`   // 一级故障标签名称
	MalfunctionTypeId   int    `json:"malfunction_type_id" gorm:"malfunction_type_id"`     // 二级故障标签id
	MalfunctionTypeName string `json:"malfunction_type_name" gorm:"malfunction_type_name"` // 二级故障标签名称
}

type RepairBillMachineAccessoryInfo struct {
	RepairBillId int     `json:"repair_bill_id" gorm:"repair_bill_id"` // 维修订单id
	AccessoryId  int     `json:"accessory_id" gorm:"accessory_id"`     // 配件id
	Title        string  `json:"title" gorm:"title"`                   // 配件名称
	Price        float64 `json:"price" gorm:"price"`                   // 配件价格
	Amount       int     `json:"amount" gorm:"amount"`                 // 数量
}

type QueryResultForWarranty struct {
	RepairBill
	EndpointName   string `gorm:"column:endpoint_name"`
	EndpointPhone  string `gorm:"column:endpoint_phone"`
	RepairUserName string `gorm:"column:repair_user_name"`
}
