package rbcare_data

import (
	"marketing/internal/model"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type AcDevicesUniqDao interface {
	GetMapByIDs(c *gin.Context, ids []int) (map[int]*model.AcDevicesUniq, error)
}

type acDevicesUniqDao struct {
	db *gorm.DB
}

func NewAcDevicesUniqDao(db *gorm.DB) AcDevicesUniqDao {
	return &acDevicesUniqDao{
		db: db,
	}
}

func (d *acDevicesUniqDao) GetMapByIDs(c *gin.Context, ids []int) (map[int]*model.AcDevicesUniq, error) {
	var list []*model.AcDevicesUniq
	err := d.db.Where("id IN ?", ids).Find(&list).Error
	if err != nil {
		return nil, err
	}
	result := make(map[int]*model.AcDevicesUniq)
	for _, v := range list {
		result[v.ID] = v
	}
	return result, nil
}
