package dao

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"marketing/internal/model"
	"marketing/internal/pkg/db"
)

type MaterialCategoryDao interface {
	CreateMaterialCategory(c *gin.Context, category *model.MaterialCategory) error
	DeleteMaterialCategory(c *gin.Context, id int) error
	UpdateMaterialCategory(c *gin.Context, id int, uMap map[string]interface{}) error
	GetAllMaterialCategory(c *gin.Context) (list []*model.MaterialCategory)
	GetMaterialCategoryById(c *gin.Context, id int) *model.MaterialCategory
	GetMaterialCategoryListByPid(c *gin.Context, pid int) (list []*model.MaterialCategory)
}

// MaterialCategoryDaoImpl 实现 MaterialCategoryDao 接口
type MaterialCategoryDaoImpl struct {
	db *gorm.DB
}

// NewMaterialCategoryDao 创建 MaterialDao 实例
func NewMaterialCategoryDao() MaterialCategoryDao {
	return &MaterialCategoryDaoImpl{
		db: db.GetDB(""),
	}
}

func (d *MaterialCategoryDaoImpl) CreateMaterialCategory(c *gin.Context, category *model.MaterialCategory) error {
	return d.db.WithContext(c).Create(category).Error
}

func (d *MaterialCategoryDaoImpl) DeleteMaterialCategory(c *gin.Context, id int) error {
	return d.db.WithContext(c).Delete(&model.MaterialCategory{}, "id = ?", id).Error
}

func (d *MaterialCategoryDaoImpl) UpdateMaterialCategory(c *gin.Context, id int, uMap map[string]interface{}) error {
	return d.db.WithContext(c).Model(&model.MaterialCategory{}).Where("id = ?", id).Updates(uMap).Error
}

func (d *MaterialCategoryDaoImpl) GetAllMaterialCategory(c *gin.Context) (list []*model.MaterialCategory) {
	d.db.WithContext(c).Model(&model.MaterialCategory{}).Find(&list)
	return
}

func (d *MaterialCategoryDaoImpl) GetMaterialCategoryById(c *gin.Context, id int) *model.MaterialCategory {
	var category model.MaterialCategory
	err := d.db.WithContext(c).Model(&model.MaterialCategory{}).Where("id = ?", id).First(&category).Error
	if err != nil {
		return nil
	}
	return &category
}

func (d *MaterialCategoryDaoImpl) GetMaterialCategoryListByPid(c *gin.Context, pid int) (list []*model.MaterialCategory) {
	d.db.WithContext(c).Model(&model.MaterialCategory{}).Where("pid = ?", pid).Find(&list)
	return
}
