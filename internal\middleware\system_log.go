package middleware

import (
	"bytes"
	"marketing/internal/model"
	logger "marketing/internal/pkg/log"
	"marketing/internal/pkg/utils"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// 需要记录日志的角色相关路由配置
var roleLogRoutes = map[string]struct {
	Module          string
	OpType          string
	Action          string
	NeedRequestData bool
	NeedAfterData   bool
	NeedBeforeData  bool
}{
	"POST:/admin/system/roles": {
		Module:          "角色管理",
		OpType:          "create",
		Action:          "创建角色",
		NeedRequestData: true,
		NeedAfterData:   false,
		NeedBeforeData:  false,
	},
	"PUT:/admin/system/roles/:id": {
		Module:          "角色管理",
		OpType:          "edit",
		Action:          "更新角色",
		NeedRequestData: false,
		NeedAfterData:   true,
		NeedBeforeData:  true,
	},
	"POST:/admin/system/roles/:id/permission": {
		Module:          "角色管理",
		OpType:          "edit",
		Action:          "更新角色权限",
		NeedRequestData: false,
		NeedAfterData:   true,
		NeedBeforeData:  true,
	},
	"POST:/admin/system/roles/:id/menu": {
		Module:          "角色管理",
		OpType:          "edit",
		Action:          "更新角色菜单",
		NeedRequestData: false,
		NeedAfterData:   true,
		NeedBeforeData:  true,
	},
	"DELETE:/admin/system/roles/:id": {
		Module:          "角色管理",
		OpType:          "delete",
		Action:          "删除角色",
		NeedRequestData: false,
		NeedAfterData:   true,
		NeedBeforeData:  true,
	},
	"POST:/admin/system/roles/:id/apps": {
		Module:          "角色管理",
		OpType:          "edit",
		Action:          "更新角色可访问app",
		NeedRequestData: false,
		NeedAfterData:   true,
		NeedBeforeData:  true,
	},
}

type roleLog struct {
	ID                uint   `json:"id"`
	Name              string `json:"name"`
	Slug              string `json:"slug"`
	Status            uint   `json:"status"`
	Remark            string `json:"remark"`
	SystemType        string `json:"system_type"`
	ResourceGroupID   uint   `json:"resource_group_id" form:"resource_group_id"`
	ResourceGroupName string `json:"resource_group_name"`
	Permissions       []struct {
		ID   uint   `json:"id"`
		Name string `json:"name"`
	} `json:"permissions" gorm:"-"`
	Menus []struct {
		ID    uint   `json:"id"`
		Title string `json:"title"`
	} `json:"menus" gorm:"-"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

func AdminRoleLogMiddleware(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取路由配置
		routeKey := c.Request.Method + ":" + c.FullPath()
		logConfig, needLog := roleLogRoutes[routeKey]
		if !needLog {
			c.Next()
			return
		}

		startTime := time.Now()
		var beforeData string
		var requestData string
		var responseBody string
		var roleID int
		var roleName string
		var beforeDataAny any

		roleID = cast.ToInt(c.Param("id"))

		// 获取请求数据
		if logConfig.NeedRequestData {
			requestData = c.GetString("reqBody")
		}

		// 获取修改前数据
		if logConfig.NeedBeforeData {
			beforeDataAny, beforeData = getRoleBeforeData(db, roleID)
			if beforeDataAny != nil {
				roleName = beforeDataAny.(roleLog).Name
			}
		}

		// 捕获响应数据
		blw := &bodyLogWriter{
			ResponseWriter: c.Writer,
			body:           bytes.NewBufferString(""),
		}
		c.Writer = blw
		c.Next()
		responseBody = blw.body.String()

		// 异步记录日志
		if c.Writer.Status() >= 200 && c.Writer.Status() < 300 && responseBodyContainsOK(responseBody) {
			go func() {
				var roleSlug string
				if logConfig.OpType == "create" {
					// 获取新创建的角色信息
					roleSlugAny, _ := getFieldValueFromReqBody(requestData, "slug")
					roleSlug = cast.ToString(roleSlugAny)
					role, err := getRoleByName(db, roleSlug)
					if err != nil {
						logger.Error("根据角色名查询角色信息失败", zap.Error(err))
						return
					}
					roleName = role.Name
					roleID = int(role.ID)
				}

				afterData := ""
				if logConfig.NeedAfterData {
					_, afterData = getRoleBeforeData(db, roleID)
				}
				remark := logConfig.Action
				log := model.AdminRoleLog{
					RoleID:     roleID,
					RoleName:   roleName,
					OpType:     logConfig.OpType,
					Module:     logConfig.Module,
					Remark:     remark,
					Method:     c.Request.Method,
					Path:       c.FullPath(),
					Operator:   int(c.GetUint("uid")),
					OperatorIP: c.ClientIP(),
					Request:    requestData,
					Before:     beforeData,
					After:      afterData,
					CreatedAt:  startTime,
				}

				if err := db.Create(&log).Error; err != nil {
					logger.Error("记录角色操作日志失败", zap.Error(err))
				}
			}()
		}
	}
}

// getRoleBeforeData 获取角色修改前的数据
func getRoleBeforeData(db *gorm.DB, id int) (any, string) {
	if id == 0 {
		return nil, ""
	}

	var role roleLog
	// 查询角色基本信息
	if err := db.Table("admin_roles as r").
		Select("r.id, r.name, r.slug, r.status, r.remark, r.system_type, admin_role_resources.group_id as resource_group_id,"+
			"resource_groups.name as resource_group_name ,r.created_at, r.updated_at").
		Joins("LEFT JOIN admin_role_resources ON admin_role_resources.role_id = r.id AND admin_role_resources.deleted_at is null").
		Joins("LEFT JOIN resource_groups ON resource_groups.id = admin_role_resources.group_id AND resource_groups.deleted_at is null").
		Where("r.id = ?", id).
		First(&role).Error; err != nil {
		return nil, ""
	}

	// 查询角色权限
	if err := db.Table("admin_permissions_v2").
		Select("admin_permissions_v2.id, admin_permissions_v2.name").
		Joins("JOIN admin_role_permissions_v2 ON admin_permissions_v2.id = admin_role_permissions_v2.permission_id").
		Where("admin_role_permissions_v2.role_id = ?", id).
		Where("admin_role_permissions_v2.deleted_at IS NULL").
		Where("admin_permissions_v2.deleted_at IS NULL").
		Scan(&role.Permissions).Error; err != nil {
		logger.Error("获取角色权限失败", zap.Error(err))
	}

	// 查询角色菜单
	if err := db.Table("admin_menus_v2").
		Select("admin_menus_v2.id, admin_menus_v2.title").
		Joins("JOIN admin_role_menus_v2 ON admin_menus_v2.id = admin_role_menus_v2.menu_id").
		Where("admin_role_menus_v2.role_id = ?", id).
		Where("admin_role_menus_v2.deleted_at IS NULL").
		Where("admin_menus_v2.deleted_at IS NULL").
		Scan(&role.Menus).Error; err != nil {
		logger.Error("获取角色菜单失败", zap.Error(err))
	}

	return role, utils.ToJSON(role)
}

// getRoleByName 根据角色名查询角色信息
func getRoleByName(db *gorm.DB, slug string) (*model.AdminRoles, error) {
	var role model.AdminRoles
	if err := db.Where("slug = ?", slug).First(&role).Error; err != nil {
		return nil, err
	}
	return &role, nil
}
