package model

import (
	"time"
)

type EndpointPermission struct {
	EndpointID   uint      `gorm:"column:endpoint_id;primaryKey"`   // 用作复合主键的一部分
	PermissionID uint      `gorm:"column:permission_id;primaryKey"` // 用作复合主键的一部分
	CreatedAt    time.Time `gorm:"column:created_at;autoCreateTime"`
	UpdatedAt    time.Time `gorm:"column:updated_at;autoUpdateTime"`
}

func (EndpointPermission) TableName() string {
	return "endpoint_permissions"
}
