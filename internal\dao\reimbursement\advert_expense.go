package reimbursement

import (
	"errors"
	"fmt"
	api "marketing/internal/api/reimbursement"
	"marketing/internal/model"
	"marketing/internal/pkg/utils"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// GetAdvertExpenseQuickPickStat 获取广告费用快捷键统计
func (r *repo) GetAdvertExpenseQuickPickStat(c *gin.Context, req *api.AdvertExpenseQuickPickStatReq) (*api.AdvertExpenseQuickPickStatResp, error) {
	var result api.AdvertExpenseQuickPickStatResp

	// Build the base query
	var query *gorm.DB

	if req.ReimbursementStatus != 0 {
		// Query with reimbursement_apply_order_summary join
		query = r.db.WithContext(c).Table("reimbursement_advert_expense_list rael").
			Select(`count(*) as total_count,
				SUM(CASE WHEN rael.status = 0 AND rael.rollback = 0 THEN 1 ELSE 0 END) AS pending_audit_count,
				SUM(CASE WHEN rael.status = 1 THEN 1 ELSE 0 END) AS first_approved_count,
				SUM(CASE WHEN rael.status = 2 THEN 1 ELSE 0 END) AS final_approved_count,
				SUM(CASE WHEN rael.status = -1 THEN 1 ELSE 0 END) AS rejected_count,
				SUM(CASE WHEN rael.status = -100 THEN 1 ELSE 0 END) AS cancelled_count`).
			Joins("LEFT JOIN reimbursement_apply_order_summary raos ON rael.id = raos.apply_order_id").
			Where("raos.status = ? AND raos.apply_order_type = ? AND rael.policy_id = ?",
				req.ReimbursementStatus, "advert_expense", req.PolicyID)
	} else {
		// Query without join
		query = r.db.WithContext(c).WithContext(c).Table("reimbursement_advert_expense_list rael").
			Select(`count(*) as total_count,
				SUM(CASE WHEN rael.status = 0 AND rael.rollback = 0 THEN 1 ELSE 0 END) AS pending_audit_count,
				SUM(CASE WHEN rael.status = 1 THEN 1 ELSE 0 END) AS first_approved_count,
				SUM(CASE WHEN rael.status = 2 THEN 1 ELSE 0 END) AS final_approved_count,
				SUM(CASE WHEN rael.status = -1 THEN 1 ELSE 0 END) AS rejected_count,
				SUM(CASE WHEN rael.status = -100 THEN 1 ELSE 0 END) AS cancelled_count`).
			Where("rael.policy_id = ?", req.PolicyID)
	}

	// Add optional filters
	if req.OrderID != 0 {
		query = query.Where("rael.id = ?", req.OrderID)
	}

	if req.TopAgency != 0 {
		query = query.Where("rael.top_agency = ?", req.TopAgency)
	}

	if req.StartTime != "" && req.EndTime != "" {
		query = query.Where("rael.created_at BETWEEN ? AND ?", req.StartTime, req.EndTime)
	}

	if req.Status != 0 {
		query = query.Where("rael.status = ?", req.Status)
		if req.Status == 0 {
			query = query.Where("rael.rollback = 0")
		}
	}

	if req.CompanyID != 0 {
		query = query.Where("rael.company_id = ?", req.CompanyID)
	}

	if req.MaterialReturnStatus != 0 {
		query = query.Where("rael.material_return_status = ?", req.MaterialReturnStatus)
	}

	if req.ExpressComeSn != "" {
		query = query.Where("rael.express_come_sn = ?", req.ExpressComeSn)
	}

	if req.CompletionStatus != 0 {
		query = query.Where("rael.completion_status = ?", req.CompletionStatus)
	}

	// Execute the query
	var queryResult struct {
		TotalCount         int `json:"total_count"`
		PendingAuditCount  int `json:"pending_audit_count"`
		FirstApprovedCount int `json:"first_approved_count"`
		FinalApprovedCount int `json:"final_approved_count"`
		RejectedCount      int `json:"rejected_count"`
		CancelledCount     int `json:"cancelled_count"`
	}

	err := query.Scan(&queryResult).Error
	if err != nil {
		return nil, err
	}

	// Map the result to response struct
	result.TotalCount = queryResult.TotalCount
	result.PendingAuditCount = queryResult.PendingAuditCount
	result.FirstApprovedCount = queryResult.FirstApprovedCount
	result.FinalApprovedCount = queryResult.FinalApprovedCount
	result.RejectedCount = queryResult.RejectedCount
	result.CancelledCount = queryResult.CancelledCount

	return &result, nil
}

// GetAdvertExpenseList 获取广告费用列表,对应python的/reimbursement/advert_expense/list/page
func (r *repo) GetAdvertExpenseList(c *gin.Context, req *api.AdvertExpenseListReq) (*api.AdvertExpenseListResp, error) {
	// 设置默认分页参数
	req.SetDefaults()

	// 1. 获取统计信息
	stat, err := r.getAdvertExpenseListStat(req)
	if err != nil {
		return nil, err
	}

	// 2. 获取分页列表
	list, err := r.getAdvertExpenseListPage(req)
	if err != nil {
		return nil, err
	}

	// 3. 获取总金额
	total, err := r.getAdvertExpenseTotal(req)
	if err != nil {
		return nil, err
	}

	return &api.AdvertExpenseListResp{
		ReimbursementListStat: stat,
		ReimbursementList:     list,
		ApplyOrderTotal:       total,
	}, nil
}

// getAdvertExpenseListStat 获取广告费用列表统计
func (r *repo) getAdvertExpenseListStat(req *api.AdvertExpenseListReq) (int64, error) {
	var count int64
	var query *gorm.DB

	if req.ReimbursementStatus != nil && *req.ReimbursementStatus != 0 {
		// 有报销状态时，需要关联 reimbursement_apply_order_summary 表
		query = r.db.Table("reimbursement_advert_expense_list ral").
			Joins("LEFT JOIN reimbursement_apply_order_summary raos ON ral.id = raos.apply_order_id").
			Where("raos.status = ? AND raos.apply_order_type = ? AND ral.policy_id = ?",
				*req.ReimbursementStatus, "advert_expense", req.PolicyID)
	} else {
		// 没有报销状态时，直接查询主表
		query = r.db.Table("reimbursement_advert_expense_list ral").
			Where("ral.policy_id = ?", req.PolicyID)
	}

	// 添加其他过滤条件
	query = r.addAdvertExpenseFilters(query, req)

	// 使用 Count 方法获取行数
	err := query.Count(&count).Error
	if err != nil {
		return 0, err
	}

	return count, nil
}

// addAdvertExpenseFilters 添加广告费用查询过滤条件
func (r *repo) addAdvertExpenseFilters(query *gorm.DB, req *api.AdvertExpenseListReq) *gorm.DB {
	// 订单ID过滤
	if req.ID != nil && *req.ID != 0 {
		query = query.Where("ral.id = ?", *req.ID)
	}

	// 顶级代理过滤
	if req.TopAgency != nil && *req.TopAgency != 0 {
		query = query.Where("ral.top_agency = ?", *req.TopAgency)
	}

	// 时间范围过滤
	if req.StartTime != "" && req.EndTime != "" {
		query = query.Where("ral.created_at BETWEEN ? AND ?", req.StartTime, req.EndTime)
	}

	// 状态过滤
	if req.Status != nil {
		query = query.Where("ral.status = ?", *req.Status)
		if *req.Status == 0 {
			query = query.Where("ral.rollback = 0")
		}
	}

	// 公司ID过滤
	if req.CompanyID != 0 {
		query = query.Where("ral.company_id = ?", req.CompanyID)
	}

	// 物料退回状态过滤
	if req.MaterialReturnStatus != nil && *req.MaterialReturnStatus != 0 {
		query = query.Where("ral.material_return_status = ?", *req.MaterialReturnStatus)
	}

	// 快递单号过滤
	if req.ExpressComeSn != "" {
		query = query.Where("ral.express_come_sn = ?", req.ExpressComeSn)
	}

	// 完成状态过滤
	if req.CompletionStatus != nil && *req.CompletionStatus != 0 {
		query = query.Where("ral.completion_status = ?", *req.CompletionStatus)
	}

	return query
}

// getAdvertExpenseListPage 获取广告费用分页列表
func (r *repo) getAdvertExpenseListPage(req *api.AdvertExpenseListReq) ([]api.AdvertExpenseListItem, error) {
	// 计算分页偏移量
	offset := (req.Page - 1) * req.PageSize
	if offset < 0 {
		offset = 0
	}

	// 获取当前年月，用于查询预估金额
	now := time.Now().Format("2006-01")

	var query *gorm.DB

	if req.ReimbursementStatus != nil && *req.ReimbursementStatus != 0 {
		// 有报销状态时，需要关联 reimbursement_apply_order_summary 表
		query = r.db.Table("reimbursement_advert_expense_list ral").
			Select(`DISTINCT ral.id, ral.code, rp.name, ral.company_id, ral.company, ral.amount,
					ral.actual_amount, ral.created_at, ral.status, a.name AS agency_name,
					ral.material_return_status, ral.express_come_sn, ral.completion_status, ral.rollback,
					(SELECT rbis.norm_quantity FROM reimbursement_balance_import_standard rbis
					 WHERE rbis.company_id = ral.company_id AND DATE_FORMAT(rbis.month,'%Y-%m') = ?
					 AND standard_type = ? LIMIT 1) AS estimate_amount,
					(SELECT GROUP_CONCAT(raos.status) FROM reimbursement_apply_order_summary raos
					 WHERE raos.apply_order_id = ral.id AND raos.apply_order_type = ?) AS reimbursement_status`,
				now, "estimate_amount", "advert_expense").
			Joins("LEFT JOIN reimbursement_policy rp ON ral.policy_id = rp.id").
			Joins("LEFT JOIN agency a ON ral.top_agency = a.id").
			Joins("LEFT JOIN reimbursement_apply_order_summary raos ON ral.id = raos.apply_order_id").
			Where("raos.status = ? AND raos.apply_order_type = ? AND ral.policy_id = ?",
				*req.ReimbursementStatus, "advert_expense", req.PolicyID)
	} else {
		// 没有报销状态时，直接查询主表
		query = r.db.Table("reimbursement_advert_expense_list ral").
			Select(`DISTINCT ral.id, ral.code, rp.name, ral.company_id, ral.company, ral.amount,
					ral.actual_amount, ral.created_at, ral.status, a.name AS agency_name,
					ral.material_return_status, ral.express_come_sn, ral.completion_status, ral.rollback,
					(SELECT rbis.norm_quantity FROM reimbursement_balance_import_standard rbis
					 WHERE rbis.company_id = ral.company_id AND DATE_FORMAT(rbis.month,'%Y-%m') = ?
					 AND standard_type = ? LIMIT 1) AS estimate_amount,
					(SELECT GROUP_CONCAT(raos.status) FROM reimbursement_apply_order_summary raos
					 WHERE raos.apply_order_id = ral.id AND raos.apply_order_type = ?) AS reimbursement_status`,
				now, "estimate_amount", "advert_expense").
			Joins("LEFT JOIN reimbursement_policy rp ON ral.policy_id = rp.id").
			Joins("LEFT JOIN agency a ON ral.top_agency = a.id").
			Where("ral.policy_id = ?", req.PolicyID)
	}

	// 添加过滤条件
	query = r.addAdvertExpenseFilters(query, req)

	// 添加排序和分页
	query = query.Order("ral.created_at DESC").Limit(req.PageSize).Offset(offset)

	var results []struct {
		ID                   int       `db:"id"`
		Code                 string    `db:"code"`
		Name                 string    `db:"name"`
		CompanyID            int       `db:"company_id"`
		Company              string    `db:"company"`
		Amount               float64   `db:"amount"`
		ActualAmount         float64   `db:"actual_amount"`
		CreatedAt            time.Time `db:"created_at"`
		Status               int       `db:"status"`
		AgencyName           string    `db:"agency_name"`
		MaterialReturnStatus int       `db:"material_return_status"`
		ExpressComeSn        string    `db:"express_come_sn"`
		CompletionStatus     int       `db:"completion_status"`
		Rollback             int       `db:"rollback"`
		EstimateAmount       *int      `db:"estimate_amount"`
		ReimbursementStatus  *string   `db:"reimbursement_status"`
	}

	err := query.Scan(&results).Error
	if err != nil {
		return nil, err
	}

	// 转换为响应格式
	var items []api.AdvertExpenseListItem
	for _, result := range results {
		item := api.AdvertExpenseListItem{
			ID:                   result.ID,
			Code:                 result.Code,
			Name:                 result.Name,
			CompanyID:            result.CompanyID,
			Company:              result.Company,
			Amount:               result.Amount,
			ActualAmount:         result.ActualAmount,
			CreatedAt:            result.CreatedAt.Format("2006-01-02 15:04:05"),
			Status:               result.Status,
			AgencyName:           result.AgencyName,
			MaterialReturnStatus: result.MaterialReturnStatus,
			ExpressComeSn:        result.ExpressComeSn,
			CompletionStatus:     result.CompletionStatus,
			Rollback:             result.Rollback,
			EstimateAmount:       result.EstimateAmount,
			ReimbursementStatus:  result.ReimbursementStatus,
		}
		items = append(items, item)
	}

	return items, nil
}

// getAdvertExpenseTotal 获取广告费用总金额
func (r *repo) getAdvertExpenseTotal(req *api.AdvertExpenseListReq) (*api.AdvertExpenseTotal, error) {
	var query *gorm.DB

	if req.ReimbursementStatus != nil && *req.ReimbursementStatus != 0 {
		// 有报销状态时，需要关联 reimbursement_apply_order_summary 表
		query = r.db.Table("reimbursement_advert_expense_list ral").
			Select("SUM(ral.amount) as amount_total").
			Joins("LEFT JOIN reimbursement_apply_order_summary raos ON ral.id = raos.apply_order_id").
			Where("raos.status = ? AND raos.apply_order_type = ? AND ral.policy_id = ?",
				*req.ReimbursementStatus, "advert_expense", req.PolicyID)
	} else {
		// 没有报销状态时，直接查询主表
		query = r.db.Table("reimbursement_advert_expense_list ral").
			Select("SUM(ral.amount) as amount_total").
			Where("ral.policy_id = ?", req.PolicyID)
	}

	// 添加过滤条件
	query = r.addAdvertExpenseFilters(query, req)

	// 排除状态为-100的记录（除非明确指定）
	if req.Status == nil || *req.Status != -100 {
		query = query.Where("ral.status != -100")
	}

	var result struct {
		AmountTotal *float64 `db:"amount_total"`
	}

	err := query.Scan(&result).Error
	if err != nil {
		return nil, err
	}

	return &api.AdvertExpenseTotal{
		AmountTotal: result.AmountTotal,
	}, nil
}

// GetAdvertExpenseDetail 获取核销详情的函数
func (r *repo) GetAdvertExpenseDetail(c *gin.Context, req *api.OrderReq) (*api.AdvertExpenseDetailResp, error) {
	var detail api.AdvertExpenseDetailResp

	query := r.db.WithContext(c).Table("reimbursement_advert_expense_list ral").
		Select("ral.id, ral.sn, ral.uid, ral.top_agency, ral.second_agency, ral.company_id, ral.company,"+
			"ral.status, ral.amount, ral.actual_amount, ral.materiel_source,"+
			"ral.created_at, ral.audit_time, ral.rollback,"+
			"ral.remark, ral.policy_id, rp.name AS policy_name,"+
			"rp.start_time AS policy_start_time, rp.end_time AS policy_end_time,"+
			"au.phone, a.name AS agency_name").
		Joins("LEFT JOIN reimbursement_policy rp ON ral.policy_id = rp.id").
		Joins("LEFT JOIN admin_users au ON ral.uid = au.id").
		Joins("LEFT JOIN agency a ON ral.top_agency = a.id").
		Where("ral.id = ?", req.ID)

	err := query.First(&detail).Error
	if err != nil {
		return nil, err
	}
	if req.Agency != 0 {
		query = query.Where("ral.top_agency = ? or ral.second_agency = ?", req.Agency, req.Agency)
	}

	return &detail, nil
}

// GetAdvertExpenseOrder 获取申请单详情
func (r *repo) GetAdvertExpenseOrder(c *gin.Context, orderID int, agency map[string]int) (*model.ReimbursementAdvertExpenseList, error) {
	var order model.ReimbursementAdvertExpenseList
	var query = r.db.WithContext(c)
	if agency != nil {
		query = query.Where("top_agency = ? AND second_agency = ?", agency["top_agency"], agency["second_agency"])
	}
	err := query.Where("id = ?", orderID).First(&order).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return &order, err
}

// InvalidAdvertExpense 广告费用申请单作废
func (r *repo) InvalidAdvertExpense(c *gin.Context, orderID int) (bool, error) {
	// 更新状态为-100（作废）
	err := r.db.WithContext(c).Model(&model.ReimbursementAdvertExpenseList{}).
		Where("id = ?", orderID).
		Update("status", -100).Error

	if err != nil {
		return false, err
	}

	return true, nil
}

// AuditAdvertExpense 广告费用申请单审核
func (r *repo) AuditAdvertExpense(c *gin.Context, req *api.AuditAdvertExpenseReq, userID uint) (bool, error) {
	var err error
	now := time.Now()

	// Start the transaction
	err = r.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		if req.Status == -1 { // 审核拒绝
			err := tx.Model(&model.ReimbursementAdvertExpenseList{}).
				Where("id = ?", req.ID).
				Updates(map[string]interface{}{
					"audit_man":  userID,
					"status":     0,
					"audit_time": now,
					"rollback":   1,
					"remark":     req.Remark,
					"updated_at": now,
				}).Error
			if err != nil {
				return err
			}
		} else { // 审核通过
			err = tx.Model(&model.ReimbursementAdvertExpenseList{}).
				Where("id = ?", req.ID).
				Updates(map[string]interface{}{
					"status":     1,
					"audit_time": now,
					"updated_at": now,
				}).Error
			if err != nil {
				return err
			}

			order, err := r.GetAdvertExpenseOrder(c, req.ID, nil)
			if err != nil {
				return err
			}

			err = tx.Create(&model.ReimbursementApplyOrderSummary{
				ApplyOrderID:      req.ID,
				SN:                order.SN,
				UID:               order.UID,
				TopAgency:         order.TopAgency,
				SecondAgency:      0,
				CompanyID:         order.CompanyID,
				Code:              order.Code,
				Company:           order.Company,
				PolicyID:          order.PolicyID,
				Amount:            order.Amount,
				Status:            0,
				ApplyOrderType:    "advert_expense",
				ReimbursementType: order.ReimbursementType,
				CreatedAt:         order.CreatedAt,
				UpdatedAt:         &now,
			}).Error
			if err != nil {
				return err
			}
		}
		return nil
	})

	if err != nil {
		return false, err
	}
	return true, nil
}
func (r *repo) AdvertExpenseOrderSplit(c *gin.Context, req *api.AdvertExpenseOrderSplitReq, order *model.ReimbursementApplyOrderSummary) error {
	// 开始事务
	return r.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		now := time.Now()

		// 1. 更新原订单状态为-3(已拆分)
		err := tx.Model(&model.ReimbursementApplyOrderSummary{}).
			Where("id = ?", req.ID).
			Updates(map[string]interface{}{
				"status":     -3,
				"updated_at": now,
			}).Error
		if err != nil {
			return err
		}

		// 2. 为每个拆分金额创建新申请单
		for _, amount := range req.SplitAmount {
			// 生成唯一编号
			sn := utils.GenerateUniqueSN()

			// 创建新申请单记录
			newSummary := model.ReimbursementApplyOrderSummary{
				ApplyOrderID:         order.ApplyOrderID,
				UID:                  order.UID,
				SN:                   sn,
				TopAgency:            order.TopAgency,
				SecondAgency:         order.SecondAgency,
				CompanyID:            order.CompanyID,
				Code:                 order.Code,
				Company:              order.Company,
				PolicyID:             order.PolicyID,
				Amount:               amount,
				ApplyOrderType:       order.ApplyOrderType,
				ReimbursementType:    order.ReimbursementType,
				SplitType:            1, // 标记为拆分单
				TurnType:             order.TurnType,
				OrderID:              &req.ID, // 关联原单ID
				MaterialReturnStatus: order.MaterialReturnStatus,
				CreatedAt:            now,
				UpdatedAt:            &now,
			}

			// 插入新记录
			if err = tx.Create(&newSummary).Error; err != nil {
				return err
			}
		}

		return nil
	})
}

// ApplyOrderSummaryInvalid 广告费用申请单作废
func (r *repo) ApplyOrderSummaryInvalid(c *gin.Context, orderID int) error {
	// 直接更新申请单状态为作废(-100)
	err := r.db.WithContext(c).Model(&model.ReimbursementApplyOrderSummary{}).
		Where("id = ?", orderID).
		Updates(map[string]interface{}{
			"status":         -100,
			"invalid_amount": gorm.Expr("amount"), // 记录作废金额
			"updated_at":     time.Now(),
		}).Error
	return err
}

func (r *repo) GetPendingAdvertExpense(c *gin.Context, companyID int, topAgency uint) ([]api.PendingTask, error) {
	var results []api.PendingTask

	err := r.db.WithContext(c).
		Table("reimbursement_advert_expense_list rael").
		Select(`rp.id AS policy_id, 
                rp.name AS policy_name, 
                rp.policy_type,
                rael.id, 
                rael.sn, 
                rael.status, 
                rael.created_at, 
                rael.amount,
                rael.company, 
                rael.rollback, 
                ? as type`, 1).
		Joins("LEFT JOIN reimbursement_policy rp ON rael.policy_id = rp.id").
		Where("rael.status = ? AND rael.top_agency = ? AND rael.company_id = ?",
			0, topAgency, companyID).
		Scan(&results).Error

	if err != nil {
		return nil, err
	}

	return results, nil
}

func (r *repo) GetAdvertExpenseOrders(c *gin.Context, req *api.ClientOrdersReq) ([]*api.ClientOrdersResp, error) {
	if req.Status != 1 {
		return []*api.ClientOrdersResp{}, nil
	}

	offset := (req.Page - 1) * req.PageSize

	var results []*api.ClientOrdersResp
	query := r.db.WithContext(c).Table("reimbursement_advert_expense_list rael").
		Select(`
            rp.id as policy_id,
            rp.name as policy_name,
            rp.policy_type,
            rael.id,
            rael.sn,
            rael.status,
            rael.created_at,
            rael.amount,
            rael.company,
            rael.rollback,
            ? as type`, 1).
		Joins("LEFT JOIN reimbursement_policy rp ON rael.policy_id = rp.id").
		Where("(rael.status = 0 OR rael.status = -1) AND rael.top_agency = ? AND rael.company_id = ? AND rael.policy_id = ?",
			req.TopAgency, req.CompanyID, req.PolicyID)

	if req.StartTime != "" && req.EndTime != "" {
		query = query.Where("rael.created_at BETWEEN ? AND ?", req.StartTime, req.EndTime)
	}

	err := query.Order("rael.created_at desc").
		Offset(offset).
		Limit(req.PageSize).
		Scan(&results).Error
	if err != nil {
		return nil, err
	}

	return results, nil
}

func (r *repo) GetAdvertExpenseApplySummaryOrders(c *gin.Context, req *api.ClientOrdersReq) ([]*api.ClientOrdersResp, error) {

	offset := (req.Page - 1) * req.PageSize

	var results []*api.ClientOrdersResp
	query := r.db.WithContext(c).Table("reimbursement_apply_order_summary raos").
		Select(`
            rp.id as policy_id,
            rp.name as policy_name,
            rp.policy_type,
            raos.id,
            raos.apply_order_id,
            raos.sn,
            raos.status,
            raos.created_at,
            raos.amount,
            raos.company,
            raos.turn_type,
            raos.split_type,
            ? as type`, 3).
		Joins("LEFT JOIN reimbursement_policy rp ON raos.policy_id = rp.id").
		Where("raos.top_agency = ? AND raos.company_id = ? AND (raos.policy_id = ? OR raos.turn_type = 1)",
			req.TopAgency, req.CompanyID, req.PolicyID).
		Where("(raos.status = 0 OR raos.status = -3 OR raos.status = -100)")

	if req.StartTime != "" && req.EndTime != "" {
		query = query.Where("rael.created_at BETWEEN ? AND ?", req.StartTime, req.EndTime)
	}

	err := query.Order("raos.created_at desc").
		Offset(offset).
		Limit(req.PageSize).
		Scan(&results).Error
	if err != nil {
		return nil, err
	}

	return results, nil
}

func (r *repo) ApplyAdvertExpense(c *gin.Context, req *api.AdvertExpenseApplyReq) error {
	// 生成唯一编号
	sn := utils.GenerateUniqueSN()
	now := time.Now()

	// 开启事务
	return r.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		// 1. 创建广告费报销申请记录
		advertExpense := &model.ReimbursementAdvertExpenseList{
			UID:               req.Uid,
			SN:                sn,
			TopAgency:         req.Agency,
			PolicyID:          req.PolicyID,
			CompanyID:         req.CompanyID,
			Code:              req.Code,
			Company:           req.CompanyName,
			MaterielSource:    req.MaterielSource,
			Amount:            req.Amount,
			Status:            0,
			CreatedAt:         now,
			ReimbursementType: req.ReimbursementType,
		}

		if err := tx.Create(advertExpense).Error; err != nil {
			return fmt.Errorf("create advert expense record failed: %w", err)
		}

		// 2. 批量创建申请信息记录
		var applyInfos []model.ReimbursementApplyInfo
		for _, info := range req.ApplicationInfo {

			applyInfos = append(applyInfos, model.ReimbursementApplyInfo{
				ApplyOrderID:   advertExpense.ID,
				Explain:        info.Explain,
				URL:            info.URL,
				ApplyOrderType: "advert_expense",
				Type:           "1",
			})
		}

		if len(applyInfos) > 0 {
			if err := tx.CreateInBatches(applyInfos, 100).Error; err != nil {
				return fmt.Errorf("create apply info records failed: %w", err)
			}
		}

		return nil
	})
}

// ChangeAdvertExpense 广告费用申请单修改
//
//goland:noinspection ALL
func (r *repo) ChangeAdvertExpense(c *gin.Context, req *api.AdvertExpenseApplyReq) (bool, error) {
	now := time.Now()

	// 开始事务
	err := r.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		// 1. 更新广告费用申请单
		err := tx.Model(&model.ReimbursementAdvertExpenseList{}).
			Where("id = ? AND top_agency = ?", req.ID, req.Agency).
			Updates(map[string]interface{}{
				"status":          0,
				"uid":             req.Uid,
				"materiel_source": req.MaterielSource,
				"amount":          req.Amount,
				"rollback":        0,
				"updated_at":      now,
			}).Error
		if err != nil {
			return err
		}

		// 2. 获取现有的申请信息
		var existingApplyInfos []model.ReimbursementApplyInfo
		err = tx.Where("apply_order_id = ? AND apply_order_type = ? AND deleted_at IS NULL AND type = 1",
			req.ID, "advert_expense").Find(&existingApplyInfos).Error
		if err != nil {
			return err
		}

		// 创建现有ID的映射
		existingIDs := make(map[uint]bool)
		for _, info := range existingApplyInfos {
			existingIDs[info.ID] = true
		}

		// 3. 处理新的申请信息
		var newApplyInfos []model.ReimbursementApplyInfo
		var updateIDs []uint

		for _, info := range req.ApplicationInfo {

			if info.ID > 0 && existingIDs[uint(info.ID)] {
				// 更新现有记录
				err = tx.Model(&model.ReimbursementApplyInfo{}).
					Where("id = ? AND type = 1 AND deleted_at IS NULL", info.ID).
					Update("explain", info.Explain).Error
				if err != nil {
					return err
				}
				updateIDs = append(updateIDs, uint(info.ID))
			} else {
				// 创建新记录
				newApplyInfos = append(newApplyInfos, model.ReimbursementApplyInfo{
					ApplyOrderID:   req.ID,
					Explain:        info.Explain,
					URL:            info.URL,
					ApplyOrderType: "advert_expense",
					Type:           "1",
				})
			}
		}

		// 4. 批量创建新的申请信息
		if len(newApplyInfos) > 0 {
			err = tx.CreateInBatches(newApplyInfos, 100).Error
			if err != nil {
				return err
			}
		}

		// 5. 删除不再需要的申请信息
		for _, existingInfo := range existingApplyInfos {
			found := false
			for _, id := range updateIDs {
				if existingInfo.ID == id {
					found = true
					break
				}
			}
			if !found {
				err = tx.Model(&model.ReimbursementApplyInfo{}).
					Where("id = ? AND type = 1", existingInfo.ID).
					Update("deleted_at", now).Error
				if err != nil {
					return err
				}
			}
		}

		return nil
	})

	if err != nil {
		return false, err
	}

	return true, nil
}
