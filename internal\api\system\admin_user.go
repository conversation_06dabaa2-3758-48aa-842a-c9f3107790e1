package system

import (
	"marketing/internal/api"
	"marketing/internal/pkg/types"
)

// AdminUserReq 用户列表请求
type AdminUserReq struct {
	api.PaginationParams
	ID           uint   `form:"id" json:"id"`
	Username     string `form:"username" json:"username"`
	Name         string `form:"name" json:"name"`
	Phone        string `form:"phone" json:"phone"`
	Role         int    `form:"role" json:"role"`
	Roles        string `form:"roles" json:"roles"`
	GroupID      uint   `form:"group_id" json:"group_id"`
	GroupSlug    string `form:"group_slug" json:"group_slug"`
	AgencyID     uint   `form:"agency_id" json:"agency_id"`
	TopAgency    uint   `form:"top_agency" json:"top_agency"`
	SecondAgency uint   `form:"second_agency" json:"second_agency"`
	TagID        uint   `form:"tag_id" json:"tag_id"`
	Status       *int8  `form:"status" json:"status"`
	IsAdmin      string `form:"is_admin" json:"is_admin"` //是否是公司管理人员
	Type         string `form:"type" json:"type"`
	Channel      string `form:"channel" json:"channel"`
	IsWecom      int    `form:"is_wecom" json:"is_wecom"` //是否是企微用户
	EndpointID   uint   `form:"endpoint_id" json:"endpoint_id"`
	IDs          string `form:"ids" json:"ids"` //批量操作的用户ID
	ActivedAt    string `form:"actived_at"`     // 接收单个时间参数，如 "2023-05-01 00:00:00"
}

// AdminUserResp 用户信息响应
type AdminUserResp struct {
	ID        string           `json:"id"`
	Username  string           `json:"username"`
	Name      string           `json:"name"`
	Phone     string           `json:"phone"`
	QwUserid  string           `json:"qw_userid"`
	IRoles    string           `json:"-"` //内部数据处理用不返回给前端
	Roles     []any            `json:"roles" gorm:"-"`
	Status    int              `json:"status"`
	Avatar    string           `json:"avatar"`
	GroupID   uint             `form:"group_id" json:"group_id"`
	GroupName string           `json:"group_name"`
	TagIDs    []int            `json:"tag_ids" form:"tag_ids" gorm:"-"` //企微标签
	ITags     string           `json:"-"`
	TagNames  []string         `json:"tag_names" gorm:"-"`
	ITagNames string           `json:"-" gorm:"i_tag_names"`
	CreatedAt types.CustomTime `json:"created_at"`           // 创建时间
	ActivedAt types.CustomTime `json:"actived_at,omitempty"` // 激活时间
	UpdatedAt types.CustomTime `json:"updated_at,omitempty"` // 更新时间
	DeletedAt types.CustomTime `json:"deleted_at,omitempty"` // 删除时间

	//总代相关字段
	TopAgency        uint   `json:"top_agency"`
	TopAgencyName    string `json:"top_agency_name,omitempty"`
	SecondAgency     uint   `json:"second_agency"`
	SecondAgencyName string `json:"second_agency_name,omitempty"`
	EndpointName     string `json:"endpoint_name,omitempty"`
}

// AddUserReq 代表用户模型
type AddUserReq struct {
	ID            uint    `form:"id" json:"id"`
	Username      string  `json:"username" form:"username" binding:"required"`
	Name          string  `json:"name" form:"name" binding:"required"`
	Password      string  `json:"password" form:"password"` // 移除 required 标签，允许密码为空
	Phone         string  `json:"phone" form:"phone" binding:"phone"`
	Avatar        string  `json:"avatar" form:"avatar"`
	RoleIDs       []uint  `json:"role_ids" form:"role_ids"` // 角色字符串
	GroupID       *uint   `form:"group_id" json:"group_id"`
	TagIDs        *[]uint `json:"tag_ids" form:"tag_ids"` //企微标签
	Status        *int8   `json:"status" form:"status"`
	TopAgency     uint    `json:"top_agency" form:"top_agency"`
	SecondAgency  uint    `json:"second_agency" form:"second_agency"`
	SyncWecomUser bool    `json:"sync_wecom_user" form:"sync_wecom_user"`
	Partition     uint    `json:"partition" form:"partition"`
	OperateRemark string  `json:"-" form:"-"`
	EndpointID    uint    `json:"endpoint_id" form:"endpoint_id"`
	EndpointRole  string  `json:"endpoint_role" form:"endpoint_role"`
	UpdateType    string  `json:"update_type" form:"update_type"` //更新用户的时候用到，用来区分是更新用户还是更新代理用户还是更新终端用户
}

// ResetPasswordReq 重置密码请求
type ResetPasswordReq struct {
	ID            uint   `json:"id" form:"id"`
	Password      string `json:"password" form:"password" binding:"required"`
	OperateRemark string `json:"operate_remark" form:"operate_remark"`
}

// UpdateStatusReq 更新状态请求
type UpdateStatusReq struct {
	ID            uint   `json:"id" form:"id"`
	Status        int8   `json:"status" form:"status" binding:"oneof=0 1"`
	OperateRemark string `json:"operate_remark" form:"operate_remark"`
}

// GetUserLogsReq 获取用户操作日志请求
type GetUserLogsReq struct {
	api.PaginationParams
}

type UpdateUserWecomTagReq struct {
	TagIDs []uint `json:"tag_ids" form:"tag_ids" binding:"required"`
}

type BindWecomReq struct {
	Code string `json:"code" form:"code" binding:"required"`
}

type SearchUserReq struct {
	api.PaginationParams
	Keyword string `form:"keyword" json:"keyword" binding:"required"` // 搜索关键词
}

type UserSearchResult struct {
	ID       uint   `gorm:"column:id" json:"id"`
	Name     string `gorm:"column:name" json:"name"`
	Username string `gorm:"column:username" json:"username"`
}
