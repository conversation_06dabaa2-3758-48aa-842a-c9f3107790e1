package model

import (
	"marketing/internal/pkg/types"
)

type ReimbursementPolicy struct {
	ID                int              `json:"id" gorm:"primaryKey"`
	Name              string           `json:"name" gorm:"column:name"`                             // 政策名称
	StartTime         types.DateOnly   `json:"start_time" gorm:"column:start_time"`                 // 开始时间
	EndTime           types.DateOnly   `json:"end_time" gorm:"column:end_time"`                     // 结束时间
	CreatedAt         types.CustomTime `json:"created_at" gorm:"column:created_at"`                 // 创建时间
	UpdatedAt         types.CustomTime `json:"updated_at" gorm:"column:updated_at"`                 // 更新时间
	PolicyType        string           `json:"policy_type" gorm:"column:policy_type"`               // 政策类型
	ReimbursementType int              `json:"reimbursement_type" gorm:"column:reimbursement_type"` // 报销类型
	UserName          string           `json:"user_name" gorm:"column:user_name"`                   // 用户名称
	Phone             string           `json:"phone" gorm:"column:phone"`                           // 电话
	Province          string           `json:"province" gorm:"column:province"`                     // 省份
	City              string           `json:"city" gorm:"column:city"`                             // 城市
	District          string           `json:"district" gorm:"column:district"`                     // 区域
	Address           string           `json:"address" gorm:"column:address"`                       // 地址
	Explain           string           `json:"explain" gorm:"column:explain"`                       // 说明
	StandardType      string           `json:"standard_type" gorm:"column:standard_type"`           // 标准类型
	Archive           int              `json:"archive" gorm:"column:archive"`                       // 是否归档
	ArchiveTime       types.CustomTime `json:"archive_time" gorm:"column:archive_time"`             // 归档时间
	Type              int              `json:"type" gorm:"column:type"`                             // 类型
	Declare           int              `json:"declare" gorm:"column:declare"`                       // 申报
	Content           string           `json:"content" gorm:"column:content"`                       // 内容
	Remark            string           `json:"remark" gorm:"column:remark"`                         // 备注
	LookOver          string           `json:"look_over" gorm:"column:look_over"`                   // 是否查看
	ApproveUids       string           `json:"approve_uids" gorm:"column:approve_uids"`             // 审批人
	ApproveApplyUids  string           `json:"approve_apply_uids" gorm:"column:approve_apply_uids"` // 审批申请人
	GiftLimit         int              `json:"gift_limit" gorm:"column:gift_limit"`                 // 礼品限制
}

func (*ReimbursementPolicy) TableName() string {
	return "reimbursement_policy"
}
