package dao

import (
	"gorm.io/gorm"
	"marketing/internal/model"
	"marketing/internal/pkg/db"
)

const (
	CvArticleCategory    = "article_category"
	CvMachineMalfunction = "machine_malfunction"
	CvModelCategory      = "model_category"
	CvMaterialCategory   = "material_category"
	CvMachineAccessory   = "machine_accessory"
)

func GetCacheVersion(key string) int {
	var c model.CacheVersion
	err := db.GetDB("").Model(&model.CacheVersion{}).Where("cache_key = ?", key).First(&c).Error
	if err != nil {
		db.GetDB("").Save(&model.CacheVersion{
			CacheKey: key,
			Version:  0,
		})
		return 0
	}
	return c.Version
}

func AddCacheVersion(key string) error {
	return db.GetDB("").Model(&model.CacheVersion{}).Where("cache_key = ?", key).
		UpdateColumn("version", gorm.Expr("version + ?", 1)).Error
}
