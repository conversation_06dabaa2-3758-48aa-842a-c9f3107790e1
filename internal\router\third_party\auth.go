package third_party

import (
	"marketing/internal/dao/app_auth"
	"marketing/internal/dao/auth"
	"marketing/internal/handler/third_party"
	thirdPartyService "marketing/internal/service/third_party"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type AuthRouter struct {
	Db             *gorm.DB
	appAuthDao     app_auth.AppAuthDao
	appAuthCache   app_auth.AppAuthCacheInterface
	authTokenCache auth.ThirdPartyAuthCacheInterface
}

func NewAuthRouter(Db *gorm.DB, authDao app_auth.AppAuthDao, appAuthCache app_auth.AppAuthCacheInterface, authTokenCache auth.ThirdPartyAuthCacheInterface) *AuthRouter {
	return &AuthRouter{
		Db:             Db,
		appAuthDao:     authDao,
		appAuthCache:   appAuthCache,
		authTokenCache: authTokenCache,
	}
}

func (a *AuthRouter) Register(r *gin.RouterGroup) {
	// 创建第三方认证相关组件
	authService := thirdPartyService.NewThirdPartyAuthService(a.Db, a.appAuthDao, a.appAuthCache, a.authTokenCache)
	authHandler := third_party.NewThirdPartyAuthHandler(authService)

	// 第三方认证路由组
	authRouterGroup := r.Group("/auth")
	{
		// 第三方认证接口（无需认证）
		authRouterGroup.GET("", authHandler.Authenticate)
		authRouterGroup.POST("", authHandler.Authenticate)
	}
	authRouterGroupV2 := r.Group("/auth/v2")
	{
		authRouterGroupV2.POST("", authHandler.AuthenticateV2)
	}
}
