package auth

import (
	"errors"
	"marketing/internal/api/auth"
	api "marketing/internal/api/system"
	"marketing/internal/consts"
	"marketing/internal/dao/admin_user"
	app "marketing/internal/dao/app_system"
	authCache "marketing/internal/dao/auth"
	"marketing/internal/model"
	appError "marketing/internal/pkg/errors"
	"marketing/internal/pkg/log"
	"marketing/internal/pkg/utils"
	"marketing/internal/service/system"
	"net/url"
	"regexp"
	"slices"
	"sort"
	"strings"
	"time"

	"github.com/spf13/cast"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type CoreAuthService struct {
	db             *gorm.DB
	userRepo       admin_user.UserDao
	authCache      authCache.AuthCacheInterface
	userCache      admin_user.UserCacheInterface
	adminMenuSvc   system.AdminMenuInterface
	appSystemCache app.AppSystemCacheInterface
	appSystemSvc   system.AppSystemSvc
}

func NewCoreAuthService(
	db *gorm.DB,
	userRepo admin_user.UserDao,
	authCache authCache.AuthCacheInterface,
	userCache admin_user.UserCacheInterface,
	adminMenuSvc system.AdminMenuInterface,
	appSystemCache app.AppSystemCacheInterface,
	appSystemSvc system.AppSystemSvc,
) *CoreAuthService {
	return &CoreAuthService{
		db:             db,
		userRepo:       userRepo,
		authCache:      authCache,
		userCache:      userCache,
		adminMenuSvc:   adminMenuSvc,
		appSystemCache: appSystemCache,
		appSystemSvc:   appSystemSvc,
	}
}

func (s *CoreAuthService) getUID(c *gin.Context) uint {
	return c.GetUint("uid")
}

// CheckPermissionByUrlMethod 判断是否有权限访问
func (s *CoreAuthService) checkPermissionByUrlMethod(c *gin.Context, u *model.AdminUsers) bool {

	//判断是否是超管，超管都放行
	if u.IsSuperAdmin() {
		return true
	}

	path := c.FullPath()

	if path == "" {
		return false
	}

	if path != "/" && path[len(path)-1] == '/' {
		path = strings.TrimRight(path, "/")
	}

	// 获取查询参数并确保唯一性
	formParams := c.Request.URL.Query()
	params := make(url.Values)
	for key, values := range formParams {
		if len(values) > 0 {
			params.Set(key, values[0]) // 使用 Set 确保每个键只有一个值
		}
	}

	method := c.Request.Method

	for _, v := range u.Permissions {
		httpMethod := strings.Split(v.HTTPMethod, ",")
		httpPath := strings.Split(v.HTTPPath, ",")
		if httpMethod[0] == "" || s.inMethodArr(httpMethod, method) {
			if httpPath[0] == "*" {
				return true
			}

			for i := 0; i < len(httpPath); i++ {
				matchPath := strings.TrimSpace(httpPath[i])
				matchPath, matchParam := s.getParam(matchPath)

				// 精确匹配
				if matchPath == path && s.checkParam(params, matchParam) {
					log.Debug("精确匹配", zap.String("path", path), zap.String("matchPath", matchPath))
					return true
				}

				// 模糊匹配
				if strings.Contains(matchPath, "*") {
					if s.wildcardMatch(matchPath, path) && s.checkParam(params, matchParam) {
						log.Debug("模糊匹配", zap.String("path", path), zap.String("matchPath", matchPath))
						return true
					}
				}
			}
		}
	}

	return false
}

func (s *CoreAuthService) validateUser(c *gin.Context, req auth.LoginReq) (*model.AdminUsers, error) {
	// 1. 验证用户
	user, err := s.userRepo.GetByUsername(c, req.Username)
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, appError.NewErr("用户不存在")
	}
	if err != nil {
		return nil, err
	}

	if *user.Status != 1 {
		return nil, appError.NewErr("用户已被禁用")
	}

	// 2. 验证密码
	if !utils.PasswordVerify(req.Password, user.Password) {
		return nil, appError.NewErr("密码不正确")
	}

	return user, nil
}

func (s *CoreAuthService) validatePhone(c *gin.Context, req auth.PhoneLoginReq) (*model.AdminUsers, error) {
	//根据用户名判断用户是否可用
	log.Debug("管理端登录Login")
	// 1. 验证用户
	user, err := s.userRepo.GetByPhone(c, req.Phone)
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, appError.NewErr("用户不存在")
	}
	if err != nil {
		return nil, err
	}

	if *user.Status != 1 {
		return nil, appError.NewErr("用户已被禁用")
	}

	// 2. 验证验证码是否有效
	var phoneCode model.PhoneCode
	err = s.db.WithContext(c).Model(&model.PhoneCode{}).Where("phone = ? AND code = ?", req.Phone, req.Code).First(&phoneCode).Error
	if err != nil {
		return nil, appError.NewErr("验证码无效")
	}
	//判断 验证码是否过期
	expiresAt := phoneCode.UpdatedAt.Add(consts.PhoneCodeExpiresIn)
	if time.Now().After(expiresAt) {
		return nil, appError.NewErr("验证码已过期")
	}
	// 3.验证码已经失效
	if phoneCode.Consume == 1 {
		return nil, appError.NewErr("验证码已失效")
	}
	// 4. 验证码使用次数+1
	err = s.db.WithContext(c).Model(&model.PhoneCode{}).Where("phone =? AND code =?", req.Phone, req.Code).Update("consume", 1).Error
	return user, nil
}

func (s *CoreAuthService) validateAccessibleSystem(c *gin.Context, xGateType string, user *model.AdminUsers) (bool, error) {
	// 3. 验证用户是否有系统访问权限
	userGroup, err := s.userRepo.GetUserGroups(c, user)

	if err != nil || userGroup == nil {
		return false, appError.NewErr("用户没有绑定用户组")
	}
	userGroupID := userGroup.ID
	//获取应用系统
	appSystem, err := s.getAppSystemByKey(c, xGateType)
	if err != nil {
		return false, appError.NewErr("系统不存在")
	}
	//判断是否是子应用
	if appSystem.Parent != "" {
		appSystem, err = s.getAppSystemByKey(c, appSystem.Parent)
		if err != nil {
			return false, appError.NewErr("系统不存在")
		}
	}

	accessibleSystem, err := appSystem.GetUserGroup()
	if err != nil {
		return false, appError.NewErr("用户没有系统访问权限")
	}
	if !slices.Contains(accessibleSystem, int(userGroupID)) {
		return false, appError.NewErr("用户没有系统访问权限")
	}
	return true, nil
}

func (s *CoreAuthService) generateToken(c *gin.Context, user *model.AdminUsers, systemType string) (*auth.TokenUserInfoResp, error) {

	//判断是否已经登录，有token取出
	log.Debug("生成token Login", zap.String("systemType", systemType))
	//获取应用系统
	appSystem, err := s.getAppSystemByKey(c, systemType)
	if err != nil {
		return nil, appError.NewErr("系统不存在")
	}
	//判断是否是子应用（是的话用父应用登录）
	if appSystem.Parent != "" {
		appSystem, err = s.getAppSystemByKey(c, appSystem.Parent)
		if err != nil {
			return nil, appError.NewErr("系统不存在")
		}
	}
	token, err := utils.GenerateAccessToken(user.ID, user.Username, appSystem.AppKey, consts.AccessTokenExpiresIn, appSystem.JwtKey)
	if err != nil {
		return nil, err
	}
	refreshToken, err := utils.GenerateRefreshToken(user.ID, user.Username, appSystem.AppKey, consts.RefreshTokenExpiresIn, appSystem.JwtKey)
	if err != nil {
		return nil, err
	}
	//存 refreshToken 到 redis
	if err := s.authCache.SetToken(c, refreshToken, int64(user.ID), consts.RefreshTokenExpiresIn); err != nil {
		return nil, err
	}
	// 更新活跃时间
	if err := s.updateUserActiveTime(c, user.ID); err != nil {
		log.Error("更新活跃时间失败", zap.Error(err))
	}

	return s.buildUserInfo(c, user, token, refreshToken, appSystem.AppKey)
}

func (s *CoreAuthService) refreshToken(c *gin.Context, refreshToken, systemType string) (*auth.TokenUserInfoResp, error) {
	//检查refresh 是否还有效
	uid, err := s.authCache.GetToken(c, refreshToken)
	if err != nil {
		return nil, err
	}
	appSystem, err := s.getAppSystemByKey(c, systemType)
	if err != nil {
		return nil, appError.NewErr("系统不存在")
	}
	claims, err := utils.ValidateToken(refreshToken, appSystem.JwtKey)
	if err != nil {
		return nil, err
	}
	if uid == 0 || uid != cast.ToInt64(claims.UserID) {
		return nil, appError.NewErr("refresh token 已经失效")
	}
	//重新生成token 返回给前端
	token, err := utils.GenerateAccessToken(cast.ToUint(uid), claims.UserName, claims.SystemType, consts.AccessTokenExpiresIn, appSystem.JwtKey)
	if err != nil {
		return nil, err
	}
	user, err := s.userRepo.GetByID(c, claims.UserID)
	if err != nil {
		return nil, err
	}
	// 更新活跃时间
	if err := s.updateUserActiveTime(c, user.ID); err != nil {
		log.Error("更新活跃时间失败", zap.Error(err))
	}
	return s.buildUserInfo(c, user, token, refreshToken, claims.SystemType)
}

func (s *CoreAuthService) buildUserInfo(c *gin.Context, user *model.AdminUsers, token, refreshToken, systemType string) (*auth.TokenUserInfoResp, error) {
	var userInfo auth.TokenUserInfoResp

	userData, err := s.userRepo.GetUserWithAll(c, user.ID, systemType)
	if err != nil {
		return nil, err
	}
	timestamp := time.Now().Add(consts.AccessTokenExpiresIn).Unix() - 1
	//数据组装
	userInfo.ID = cast.ToInt64(user.ID)
	userInfo.Token = token
	userInfo.Expires = timestamp
	userInfo.RefreshToken = refreshToken
	userInfo.Name = userData.Name
	userInfo.Username = userData.Username
	userInfo.JobNumber = userData.JobNumber
	if userData.Avatar == nil {
		userInfo.Avatar = ""
	} else {
		userInfo.Avatar = *userData.Avatar
	}

	if userData.Phone != nil {
		userInfo.Phone = *userData.Phone
	} else {
		userInfo.Phone = "" // 或者设置为默认值
	}
	userInfo.Roles = userData.ToRoleIdentifiers()
	userInfo.Permissions = userData.ToPermissionIdentifiers()
	userInfo.Menus = s.adminMenuSvc.BuildMenuTree(userData.Menus)
	userInfo.Apps = make([]*api.AppSystemMenuResp, 0)

	for _, v := range userData.UserApps {
		appSystemMenu := &api.AppSystemMenuResp{
			AppKey: v.AppKey,
			Name:   v.Name,
			Icon:   v.Icon,
			Index:  v.Index,
			Type:   v.Type,
		}
		userInfo.Apps = append(userInfo.Apps, appSystemMenu)
	}
	return &userInfo, nil
}

func (s *CoreAuthService) inMethodArr(arr []string, str string) bool {
	sort.Strings(arr)
	_, found := slices.BinarySearch(arr, str)
	return found
}

func (s *CoreAuthService) getParam(u string) (string, url.Values) {
	m := make(url.Values)
	urr := strings.Split(u, "?")
	if len(urr) > 1 {
		m, _ = url.ParseQuery(urr[1])
	}
	return urr[0], m
}

func (s *CoreAuthService) checkParam(src, comp url.Values) bool {
	if len(comp) == 0 {
		return true
	}
	if len(src) == 0 {
		return false
	}
	for key, value := range comp {
		v, find := src[key]
		if !find {
			return false
		}
		if len(value) == 0 {
			continue
		}
		if len(v) == 0 {
			return false
		}
		for i := 0; i < len(v); i++ {
			if v[i] == value[i] {
				continue
			} else {
				return false
			}
		}
	}
	return true
}

// wildcardMatch 用于检查路径与包含 '*' 的通配符模式是否匹配
func (s *CoreAuthService) wildcardMatch(pattern, str string) bool {
	pattern = "^" + strings.ReplaceAll(pattern, "*", ".*") + "$"
	//pattern = "^" + strings.ReplaceAll(pattern, "*", "[^/]*") + "$"
	reg, err := regexp.Compile(pattern)
	if err != nil {
		log.Error("Wildcard match error: ", zap.Error(err))
		return false
	}
	return reg.MatchString(str)
}

// 使用AppSystemService
func (s *CoreAuthService) getAppSystemByKey(c *gin.Context, xGateType string) (*model.AppSystemV2, error) {
	return s.appSystemSvc.GetAppSystemByKey(c, xGateType)
}

func (s *CoreAuthService) updateUserActiveTime(c *gin.Context, uid uint) error {
	//更新用户最后登录时间
	err := s.userRepo.UpdateUserActiveTime(c, uid)
	if err != nil {
		return err
	}
	return nil
}
