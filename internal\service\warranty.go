package service

import (
	"bytes"
	"crypto/md5"
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
	"github.com/xuri/excelize/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"io"
	"marketing/internal/api/endpoint"
	api "marketing/internal/api/warranty"
	"marketing/internal/api/warranty/convertor"
	"marketing/internal/consts"
	"marketing/internal/dao/strategy"
	"marketing/internal/pkg/db"
	zaplog "marketing/internal/pkg/log"
	"marketing/internal/pkg/mes"
	"marketing/internal/pkg/types"
	"marketing/internal/service/contact"
	"marketing/internal/service/prototype"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"sync"

	"regexp"
	"time"

	repo "marketing/internal/dao"
	userRepo "marketing/internal/dao/admin_user"
	endpointRepo "marketing/internal/dao/endpoint"
	prototypeRepo "marketing/internal/dao/prototype"
	"marketing/internal/model"

	"marketing/internal/pkg/errors"
)

type InterfaceWarranty interface {
	// Create 新增保卡
	Create(c *gin.Context, salesmanID uint, parms *api.CreateWarrantyReq) (map[string]interface{}, error)
	GetEndpoints(c *gin.Context, name string) []*api.GetEndpointResp
	// GetInfo 获取保卡
	GetInfo(c *gin.Context, info *api.WarrantyInfoReq) (gin.H, error)
	GetOneWarranty(c *gin.Context, id int) (gin.H, error)
	Update(c *gin.Context, id int, params *api.EditReq) (int64, error)
	// UpdateAssessment 更新保卡是否纳入考核
	UpdateAssessment(c *gin.Context, id int, action uint) error
	ReturnWarranty(c *gin.Context, req *api.ReturnReq) error
	// GetReturnList 获取退货列表
	GetReturnList(c *gin.Context, info *api.ReturnListReq) (gin.H, error)
	// ReturnEdit 更新退货记录
	ReturnEdit(c *gin.Context, id int, reason string) error
	// Exchange 换货
	Exchange(c *gin.Context, a *api.ExchangeReq) error
	// GetExchangeList 获取换货列表
	GetExchangeList(c *gin.Context, info *api.ExchangeListReq) (gin.H, error)
	ExchangeGetOneWarranty(c *gin.Context, id int) (gin.H, error)
	ExchangeEdit(c *gin.Context, id int, reason string) error
	SNGetWarranties(c *gin.Context, dbGetStrategy strategy.GetWarrantiesDBStrategy) (*api.WarrantiesResp, error)
	GroupImportWarranty(c *gin.Context, rows [][]string, f *excelize.File, sheetName string) *api.ImportResp
	ExportWarranties(c *gin.Context, page int, pageSize int, code string) ([]*api.WarrantiesExportResp, int64, error)
	BatchGetWarranties(c *gin.Context, barcodes []string) *api.BatchWarrantiesResp
	CheckHasScreenInsurance(c *gin.Context, barcode string) (interface{}, error)
	ScreenInsuranceRefund(c *gin.Context, sn string) (interface{}, error)

	TrackMachineLife(c *gin.Context, barcode string) (*api.MachineLifeResp, error)
	MachinesHistoriesList(c *gin.Context, params *api.MachinesHistoriesReq) (*api.MachinesHistoriesResp, error)
	MachineHistory(c *gin.Context, barcode string) ([]*api.MachineHistoryResp, error)
	MachineHistoryCreate(c *gin.Context, params *api.MachineHistoryUpdReq) (string, error)

	DevicesLimitAccountList(c *gin.Context, req *api.DevicesLimitAccountReq) (list []*api.DevicesLimitAccount, total int64, err error)
	DevicesLimitAccountEdit(c *gin.Context, params *api.DevicesLimitAccountUpdReq) (string, error)
	DevicesLimitAccountImport(c *gin.Context, f *excelize.File, fileName string, sheetName string) (string, error)
}
type warranty struct {
	repo           repo.InterfaceWarranty
	userDao        userRepo.UserDao
	endpointDao    endpointRepo.EndpointDao
	prototypeDao   prototypeRepo.PrototypeInterface
	machineTypeDao repo.MachineTypeDao
	commonDao      repo.CommonDao
	prototypeCache prototypeRepo.PrototypeCache
	contactSvc     contact.InterfaceContact
	prototypeSvc   prototype.PrototypeService
	repairBillSvc  RepairBillSvc
}

type machineLifeData struct {
	packInfo             []*model.MesPackDevices
	inStockInfos         []*model.V3Instock
	outStockInfos        []*model.V3Outstock
	returnStockInfos     []*model.V3Returnstock
	allotStockInfos      []*model.V3Allotstock
	warrantiesInfo       *api.WarrantiesResp
	activatedDevicesInfo []*model.AcDevicesUniq
	prototypeInfo        []*api.PrototypeForMachineLife
}

// NewWarrantyService 创建一个新的保单数据访问对象
func NewWarrantyService(
	repo repo.InterfaceWarranty,
	userRepo userRepo.UserDao,
	endpointDao endpointRepo.EndpointDao,
	prototypeDao prototypeRepo.PrototypeInterface,
	machineTypeDao repo.MachineTypeDao,
	commonDao repo.CommonDao,
	prototypeCache prototypeRepo.PrototypeCache,
	contactSvc contact.InterfaceContact,
	prototypeSvc prototype.PrototypeService,
	repairBillSvc RepairBillSvc,
) InterfaceWarranty {
	return &warranty{
		repo:           repo,
		userDao:        userRepo,
		endpointDao:    endpointDao,
		prototypeDao:   prototypeDao,
		machineTypeDao: machineTypeDao,
		commonDao:      commonDao,
		prototypeCache: prototypeCache,
		contactSvc:     contactSvc,    // 联系人服务
		prototypeSvc:   prototypeSvc,  // 样机服务
		repairBillSvc:  repairBillSvc, // 维修服务
	}
}

func (w *warranty) Create(c *gin.Context, salesmanID uint, parms *api.CreateWarrantyReq) (map[string]interface{}, error) {
	var pending *api.Warranty
	var err error
	cnt := parms.Contact
	var buyDate, studentBirthday time.Time
	if bd := parms.Date; bd != "" {
		buyDate, err = time.Parse(time.DateOnly, bd)
		if err != nil {
			return nil, errors.NewErr("非法日期格式")
		}
	}

	if sb := parms.StudentBirthday; sb != "" {
		studentBirthday, err = time.Parse("2006-01-02", sb)
		if err != nil {
			return nil, errors.NewErr("非法日期格式")
		}
	}

	pending = &api.Warranty{
		Barcode:         parms.Barcode,
		Endpoint:        int(parms.EndpointID),
		BuyDate:         buyDate,
		CustomerPhone:   parms.Phone,
		CustomerName:    parms.Name,
		CustomerSex:     parms.Sex,
		PurchaseWay:     parms.PurchaseWay,
		StudentName:     parms.StudentName,
		StudentSchool:   parms.StudentSchool,
		StudentGrade:    parms.StudentGrade,
		StudentBirthday: studentBirthday,
		StudentSex:      parms.StudentSex,
		SalesmanID:      int(salesmanID),
	}
	if err := validParamsIn(pending); err != nil {
		return nil, err
	}
	if err := w.hasSalesman(c, pending); err != nil {
		return nil, err
	}
	endpointInfo, err := w.hasPermissionToAddWarranty(c, pending.SalesmanID)
	if err != nil {
		return nil, err
	}
	err = w.allowPrototypeEntryCheck(c, pending)
	if err != nil {
		return nil, err
	}

	// 处理保卡录入
	resp, err := w.processWarrantyEntry(c, pending, endpointInfo, cnt)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (w *warranty) GetEndpoints(c *gin.Context, name string) []*api.GetEndpointResp {
	resp := w.endpointDao.GetEndpointByName(c, name)
	if len(resp) == 0 {
		return nil
	}
	return resp
}

func (w *warranty) GetInfo(c *gin.Context, query *api.WarrantyInfoReq) (gin.H, error) {
	var resp []map[string]interface{}
	list, total, page, pageSize, err := w.repo.GetWarrantiesWithEndpoint(c, query)
	if err != nil {
		return nil, err
	}
	for _, we := range list {
		var buyDate string
		if !we.BuyDate.IsZero() {
			buyDate = we.BuyDate.Format("2006-01-02 15:04:05")
		}
		resp = append(resp, gin.H{
			"warranty_id":    we.ID,
			"barcode":        we.Barcode,
			"ext_barcode":    we.ExtBarcode,
			"model":          we.Model,
			"salesman":       we.Salesman,
			"salesman_phone": we.Phone,
			"customer": gin.H{
				"customer_name":    we.CustomerName,
				"customer_phone":   we.CustomerPhone,
				"customer_sex":     we.CustomerSex,
				"customer_address": we.CustomerAddress,
			},
			"endpoint": gin.H{
				"endpoint_name":    we.Name,
				"endpoint_address": we.Address,
				"endpoint_phone":   we.Phone,
				"endpoint_manager": we.Manager,
			},
			"student": gin.H{
				"student_name":     we.StudentName,
				"student_school":   we.StudentSchool,
				"student_grade":    we.StudentGrade,
				"student_birthday": we.StudentBirthday,
			},
			"assessment": we.Assessment,
			"buy_date":   buyDate,
			"insurance":  0, // 是否投保 -写死为0
		})
	}

	return gin.H{
		"total":         total,
		"page":          page,
		"size":          pageSize,
		"warranty_list": resp,
	}, nil
}

func (w *warranty) Update(c *gin.Context, id int, params *api.EditReq) (int64, error) {
	assessment := uint(0)
	update := &model.Warranty{Assessment: &assessment}
	if err := fill(update, params); err != nil {
		return 0, err
	}
	update.ID = id
	affected, err := w.repo.UpdateByID(c, update)
	if err != nil {
		return 0, err
	}
	return affected, nil
}

func (w *warranty) UpdateAssessment(c *gin.Context, id int, action uint) error {
	err := w.repo.UpdateAssessment(c, id, action)
	if err != nil {
		return errors.NewErr("更新失败")
	}
	return nil
}

func (w *warranty) ReturnWarranty(c *gin.Context, params *api.ReturnReq) error {
	q := c.Query("uid")
	if q == "" {
		return errors.NewErr("未登录或登录失效")
	}
	id, err := strconv.Atoi(q)
	if err != nil {
		return errors.NewErr("uid错误")
	}
	uid := uint(id)
	var endpointID int
	var returnAt time.Time

	epInfo, err := w.endpointDao.GetUserEndpointInfo(c, int(uid))
	if err != nil || epInfo == nil {
		return errors.NewErr("无权限操作")
	}
	endpointID = epInfo.ID
	if params.ReturnAt == "" {
		returnAt = time.Now()
	} else {
		var perm bool
		if id != 1 {
			perm, err = w.endpointDao.CheckEndpointPermission(c, endpointID, "warranty-tuihuan-time")
			if err != nil {
				return err
			}
		}
		if !perm {
			returnAt = time.Now()
		} else {
			t, err := time.ParseInLocation("2006-01-02", params.ReturnAt, time.Local)
			if err != nil {
				return errors.NewErr("退机时间格式错误")
			}
			returnAt = t.Add(23*time.Hour + 59*time.Minute + 59*time.Second)
		}
	}

	if !isBarcode(params.Barcode) {
		return errors.NewErr("条码格式错误")
	}
	wt, err := w.repo.GetOneWarranty(c, params.Barcode) // 默认为管理人员，查到保单即获取，无视时间限制
	if err != nil || wt == nil {
		return errors.NewErr("旧机器电子保卡不存在")
	}
	if valid := checkOutOfDate(wt, returnAt, 1); !valid {
		return errors.NewErr("已经超出退货有效期")
	}
	if wt.Type == -1 {
		return errors.NewErr("此保卡已冻结，请联系客服部")
	}
	if returnAt.Before(wt.BuyDate) {
		return errors.NewErr("退机时间选择不能早于购买时间")
	}
	if returnAt.After(time.Now()) {
		return errors.NewErr("退机时间选择不能晚于购买时间")
	}

	isOverdue := false
	if wt.BuyDate.Add(7 * 24 * time.Hour).Before(returnAt) {
		isOverdue = true
	}
	if uid != 1 && endpointID != 0 && endpointID != wt.Endpoint {
		return errors.NewErr("此保卡非此终端录入，无权限操作")
	}

	ret, err := w.repo.ReturnByBarcode(c, params.Barcode, params.Reason, wt.ID, uid, endpointID, returnAt, isOverdue)
	if err != nil || !ret {
		return errors.NewErr("退货操作失败")
	}

	numTasks := 2
	errCh := make(chan error, numTasks)
	var wg sync.WaitGroup

	wg.Add(numTasks)
	go w.commonDao.WarrantyReturnLoadContact(wt, errCh, &wg) // 更新保单联系人信息
	go w.repo.AddVirtualCard(c, wt, errCh, &wg)              // 添加虚拟保卡

	go func() {
		wg.Wait()
		close(errCh)
	}()
	// 收集协程中的错误
	var collectedErrs []string
	for err = range errCh {
		if err != nil {
			collectedErrs = append(collectedErrs, err.Error())
		}
	}
	if len(collectedErrs) > 0 {
		return errors.NewErr(strings.Join(collectedErrs, " "))
	}
	// 保卡退换机添加样机
	res, err := w.prototypeDao.AddPrototypeByWarranty(c, &model.Prototype{
		ModelID:  wt.ModelID,
		Model:    wt.Model,
		Barcode:  params.Barcode,
		Number:   wt.Number,
		Imei:     wt.Imei,
		Endpoint: wt.Endpoint,
		UserID:   id,
		Type:     3,
	})
	if err != nil {
		return err
	}
	// 添加样机缓存
	if res == 2 {
		if err := w.prototypeCache.Set(c, wt.Number); err != nil {
			return errors.NewErr(fmt.Sprintf("添加缓存失败: %v", err))
		}
	}
	// 进销存同步
	if res == 1 || res == 2 || res == 3 {
		if err = w.repo.DrpMachine(c, params.Barcode, "", 3, res, wt); err != nil {
			return err
		}
	}
	// 碎屏保退款
	if data, _ := w.CheckHasScreenInsurance(c, params.Barcode); data != nil {
		if parsedData, ok := data.(map[string]interface{}); ok && parsedData["sn"].(string) != "" {
			sn := parsedData["sn"].(string)
			if refund, _ := w.ScreenInsuranceRefund(c, sn); refund == nil {
				zaplog.Warn("退机成功，但碎屏保退款失败，请联系相关人员")
			}
		}
	}
	return nil
}

func (w *warranty) Exchange(c *gin.Context, params *api.ExchangeReq) error {
	id, err := strconv.Atoi(c.Query("uid"))
	// 新版前端暂不传extBarcodeNew extBarcodeNew := params.ExtBarcodeNew
	if err != nil {
		return errors.NewErr("用户id参数错误")
	}
	uid := uint(id)
	endpointID, err := strconv.Atoi(c.Query("endpoint_id"))
	if err != nil {
		return errors.NewErr("终端参数错误")
	}
	if !isBarcode(params.Barcode) || !isBarcode(params.NewBarcode) {
		return errors.NewErr("条码格式错误")
	}
	exchangeAt := time.Now()
	wt, err := w.repo.GetOneWarranty(c, params.Barcode)
	if err != nil || wt == nil {
		return errors.NewErr("旧机器电子保卡不存在")
	}
	if wt.BuyDate.Add(30 * 24 * time.Hour).Before(exchangeAt) {
		return errors.NewErr("已经超出换货有效期")
	}
	if wt.Type == -1 {
		return errors.NewErr("此保卡已冻结，请联系客服部")
	}

	m, err := mes.CheckMachine(mes.CheckMachineParams{
		Barcode: params.NewBarcode,
		Imei:    "",
		Number:  "",
	})
	if err != nil || m == nil {
		return errors.NewErr("无效的新机器条码")
	}
	machine, ok := m.(map[string]interface{})
	if ok {
		extBarcodeNum, _ := strconv.Atoi(machine["ext_barcode_count"].(string))
		if extBarcodeNum < 0 {
			return errors.NewErr("此新机器条码为副机条码，不可进行换机，请输入对应的新机器主机条码")
		}
	}
	sold, err := w.repo.HasWarranty(c, params.NewBarcode, "", "")
	if sold != nil && sold.Status == 1 {
		return errors.NewErr("新机器电子保卡已存在")
	}
	prototypeInfo, _ := w.prototypeDao.GetInfo(c, params.NewBarcode)
	if prototypeInfo != nil && prototypeInfo.Type == 4 && prototypeInfo.Discontinued == 0 {
		return errors.NewErr("演示样机在库不允许录入保卡")
	}

	// 前端页面无更换副机选项，暂不写校验副机逻辑

	machineModelID, _ := strconv.Atoi(machine["model_id"].(string))
	newCustomerPrice, err := w.machineTypeDao.GetPriceByModelID(c, machineModelID)
	if err != nil || newCustomerPrice == 0 {
		return err
	}
	oldWarranty := *wt // deep copy
	modelID, _ := strconv.Atoi(machine["model_id"].(string))
	oldModelName := wt.Model
	productTime, _ := time.Parse(time.DateTime, machine["product_date"].(string))
	wt.CustomerPrice = newCustomerPrice
	wt.ModelID = modelID
	wt.Model = machine["model"].(string)
	wt.ProductDate = productTime
	wt.Barcode = params.NewBarcode
	wt.Imei = machine["imei1"].(string)
	wt.Endpoint = endpointID

	// 碎屏保校验
	insurance, _ := w.CheckHasScreenInsurance(c, params.Barcode)
	var sn string
	if insurance != nil {
		if parsedInsurance, ok := insurance.(map[string]interface{}); ok && parsedInsurance["sn"].(string) != "" {
			sn = parsedInsurance["sn"].(string)
			if oldModelName != machine["model"].(string) {
				return errors.NewErr("该产品已购买碎屏增值服务，无法更换其他型号")
			}
		}
	}

	var updErr error
	if sold != nil { // 更新虚卡为正常状态
		updErr = w.repo.ExchangeByUpdate(c, &api.UpdExchange{
			ID:             sold.ID,
			Barcode:        params.Barcode,
			BarcodeNew:     params.NewBarcode,
			ExtBarcodeNew:  "",
			Reason:         params.Reason,
			Warranty:       wt,
			Uid:            uid,
			EndpointID:     endpointID,
			Number:         machine["number"].(string),
			ExchangedAt:    exchangeAt,
			WarrantyPeriod: exchangeAt,
		})
	} else {
		updErr = w.repo.ExchangeByCreate(c, &api.Exchange{
			Barcode:        params.Barcode,
			BarcodeNew:     params.NewBarcode,
			ExtBarcodeNew:  "",
			Reason:         params.Reason,
			Warranty:       wt,
			Uid:            uid,
			EndpointID:     endpointID,
			Number:         machine["number"].(string),
			ExchangedAt:    exchangeAt,
			WarrantyPeriod: exchangeAt,
		})
	}
	if updErr != nil {
		return errors.NewErr("换货操作失败")
	}

	task := 2
	errCh := make(chan error, task)
	var wg sync.WaitGroup
	wg.Add(task)
	go w.repo.AddVirtualCard(c, &oldWarranty, errCh, &wg) // 添加旧保卡为虚拟保卡
	req := &api.PrototypeCancel{                          // 换货新条码样机离库同时解除绑定
		Barcode: params.NewBarcode,
		Number:  "",
	}
	go func(r *api.PrototypeCancel) {
		defer wg.Done()
		errCh <- w.prototypeSvc.WarrantyCancelPrototype(c, r)
	}(req)

	wg.Wait()
	close(errCh)

	// 收集协程中的错误
	var collectedErrs []string
	for err = range errCh {
		if err != nil {
			collectedErrs = append(collectedErrs, err.Error())
		}
	}
	if len(collectedErrs) > 0 {
		return errors.NewErr(strings.Join(collectedErrs, " "))
	}

	// 换机成功旧条码加入样机
	res, err := w.prototypeDao.AddPrototypeByWarranty(c, &model.Prototype{
		Barcode:  params.Barcode,
		Endpoint: oldWarranty.Endpoint,
		Model:    oldWarranty.Model,
		Number:   oldWarranty.Number,
		ModelID:  oldWarranty.ModelID,
		UserID:   int(uid),
		Imei:     oldWarranty.Imei,
		Type:     2})
	if err != nil {
		return err
	}
	// 同步进销存
	if res == 1 || res == 2 || res == 3 {
		if err = w.repo.DrpMachine(c, params.Barcode, params.NewBarcode, 2, res, &oldWarranty); err != nil {
			return err
		}
	}
	// 更换碎屏保
	if insurance != nil {
		if parsedInsurance, ok := insurance.(map[string]interface{}); ok && parsedInsurance["sn"].(string) != "" {
			exchangeBroken, _ := w.ScreenInsuranceExchange(c, params.Barcode, params.NewBarcode, sn)
			if exchangeBroken == nil {
				zaplog.Warn("换货成功，碎屏保更换失败")
			}
		}
	}
	return nil
}

func (w *warranty) GetExchangeList(c *gin.Context, info *api.ExchangeListReq) (gin.H, error) {
	var resp []map[string]interface{}
	list, total, page, pageSize, err := w.repo.GetWarrantyExchangeList(c, info)
	if err != nil {
		return nil, err
	}
	for _, one := range list {
		resp = append(resp, gin.H{
			"id":          one.ID,
			"barcode_old": one.Barcode,
			"barcode_new": one.BarcodeNew,
			"model_old":   one.ModelOld,
			"model_new":   one.ModelNew,
			"detail": gin.H{
				"endpoint_name":    one.EndpointName,
				"endpoint_address": one.EndpointAddress,
				"endpoint_phone":   one.EndpointPhone,
				"endpoint_manager": one.EndpointManager,
				"customer_name":    one.CustomerName,
			},
			"reason":      one.Reason,
			"exchange_at": one.CreatedAt.Format(time.DateTime),
		})
	}
	return gin.H{
		"total":       total,
		"page":        page,
		"page_size":   pageSize,
		"return_list": resp,
	}, nil
}

func (w *warranty) ExchangeGetOneWarranty(c *gin.Context, id int) (gin.H, error) {
	ex, err := w.repo.GetOneExchangeByID(c, id)
	if err != nil {
		return nil, err
	}
	return gin.H{
		"barcode_old": ex.Barcode,
		"barcode_new": ex.BarcodeNew,
		"reason":      ex.Reason,
		"created_at":  ex.CreatedAt.Format(time.DateTime),
	}, nil
}

func (w *warranty) GetReturnList(c *gin.Context, info *api.ReturnListReq) (gin.H, error) {
	var resp []map[string]interface{}
	list, total, page, pageSize, err := w.repo.GetWarrantyReturnList(c, info)
	if err != nil {
		return nil, err
	}
	for _, one := range list {
		resp = append(resp, gin.H{
			"id":      one.ID,
			"barcode": one.Barcode,
			"model":   one.Model,
			"detail": gin.H{
				"endpoint_name":    one.Name,
				"endpoint_address": one.Address,
				"endpoint_phone":   one.Phone,
				"endpoint_manager": one.Manager,
				"customer_name":    one.CustomerName,
			},
			"reason":    one.Reason,
			"manager":   one.Manager,
			"return_at": one.ReturnAt.Format(time.DateTime),
		})
	}
	return gin.H{
		"total":       total,
		"page":        page,
		"page_size":   pageSize,
		"return_list": resp,
	}, nil
}

func (w *warranty) ReturnEdit(c *gin.Context, id int, reason string) error {
	if err := w.repo.EditReturnByReason(c, id, reason); err != nil {
		return errors.NewErr("更新失败")
	}
	return nil
}

func (w *warranty) GetOneWarranty(c *gin.Context, id int) (gin.H, error) {
	one, err := w.repo.GetOneWarrantyByID(c, id)
	if err != nil {
		return nil, err
	}
	return gin.H{
		"barcode":          one.Barcode,
		"ext_barcode":      one.ExtBarcode,
		"salesman":         one.Salesman,
		"customer_name":    one.CustomerName,
		"customer_phone":   one.CustomerPhone,
		"customer_sex":     one.CustomerSex,
		"purchase_way":     one.PurchaseWay,
		"student_name":     one.StudentName,
		"student_grade":    one.StudentGrade,
		"student_school":   one.StudentSchool,
		"student_birthday": one.StudentBirthday,
		"student_sex":      one.StudentSex,
		"assessment":       one.Assessment,
		"buy_date":         one.BuyDate.Format(time.DateTime),
		"status":           one.Status,
		"model":            one.Model,
	}, nil
}

func (w *warranty) ExchangeEdit(c *gin.Context, id int, reason string) error {
	affected, err := w.repo.ExchangeEditByReason(c, id, reason)
	if err != nil || affected == 0 {
		return errors.NewErr("编辑失败")
	}
	return nil
}

func (w *warranty) SNGetWarranties(c *gin.Context, dbGetStrategy strategy.GetWarrantiesDBStrategy) (*api.WarrantiesResp, error) {
	var warrantiesResp *api.WarrantiesResp
	var wg sync.WaitGroup
	var mu sync.Mutex

	var warrantiesInfo []*api.WarrantiesInfo
	warranties, err := dbGetStrategy.GetWarranties(c)
	if err != nil {
		return nil, err
	}
	if len(warranties) == 0 {
		return nil, errors.NewErr("无相关保卡信息")
	}
	barcodeRecord := make(map[string]struct{}, len(warranties))
	for _, warrantyDetail := range warranties {
		if _, ok := barcodeRecord[warrantyDetail.Barcode]; !ok {
			barcodeRecord[warrantyDetail.Barcode] = struct{}{}
		}
		wg.Add(1)
		go func(detail *api.WarrantyDetail) {
			defer wg.Done()
			readyWarranty, err := w.getBarcodeLifecycleInfo(c, detail)
			if err != nil {
				return
			}
			mu.Lock()
			warrantiesInfo = append(warrantiesInfo, readyWarranty)
			mu.Unlock()
		}(warrantyDetail)
	}
	// 寄修记录查询
	var repairInfo []*api.RepairInfo
	for barcode := range barcodeRecord {
		var repair api.RepairInfo
		repairBill, err := w.repairBillSvc.GetRepairBillForWarranty(c, barcode) // 获取外部寄修的维修信息
		if err != nil {
			zaplog.Info("获取外部寄修记录失败", zap.Error(err))
		}
		repairOrderInfo, err := w.repairBillSvc.GetPostRepairOrderForWarranty(c, barcode) // 获取寄修系统的维修信息
		if err != nil {
			zaplog.Info("获取寄修系统记录失败", zap.Error(err))
		}
		repair.FillRepairBillInfo(barcode, repairBill)
		repair.FillRepairOrderInfo(barcode, repairOrderInfo)
		repairInfo = append(repairInfo, &repair)
	}

	wg.Wait()
	warrantiesResp = &api.WarrantiesResp{
		WarrantiesInfo: warrantiesInfo,
		RepairInfo:     repairInfo,
	}
	return warrantiesResp, nil
}

func (w *warranty) GroupImportWarranty(c *gin.Context, rows [][]string, f *excelize.File, sheetName string) *api.ImportResp {
	var validWarranties []*api.CreateWarrantyReq
	var validationErrors []string
	var resp api.ImportResp
	phoneRegex := regexp.MustCompile(`^1\d{10}$`)
	barcodeRegex := regexp.MustCompile(`^[A-Za-z0-9]{6,14}$`)
	sexMap := map[string]string{"男": "m", "女": "f", "m": "m", "f": "f"}
	uidMap := make(map[*api.CreateWarrantyReq]uint)

	for i := 1; i < len(rows); i++ {
		row := rows[i]
		rowIndex := i + 1 // Excel中的实际行号

		// 补齐空单元格，防止索引越界
		for len(row) < len(consts.ValidHeader) {
			row = append(row, "")
		}

		terminalAccount := strings.TrimSpace(row[2])
		if terminalAccount == "" {
			continue
		}
		buyDate, err := parseExcelDate(f, sheetName, fmt.Sprintf("D%d", rowIndex), row[3])
		zaplog.Info("buydate_is:", zap.Any("buy_date", buyDate))
		if err != nil {
			validationErrors = append(validationErrors, fmt.Sprintf("第 %d 行: 购买日期格式不正确", rowIndex))
		}
		studentBirthday, err := parseExcelDate(f, sheetName, fmt.Sprintf("K%d", rowIndex), row[10])

		if err != nil {
			studentBirthday = ""
		}
		barcode := strings.TrimSpace(row[0])
		if !barcodeRegex.MatchString(barcode) {
			validationErrors = append(validationErrors, fmt.Sprintf("第 %d 行: SN码格式不正确 (应为6-14位字母或数字)", rowIndex))
		}
		phone := strings.TrimSpace(row[4])
		if !phoneRegex.MatchString(phone) {
			validationErrors = append(validationErrors, fmt.Sprintf("第 %d 行: 顾客电话格式不正确", rowIndex))
		}
		customerName := strings.TrimSpace(row[5])
		if customerName == "" {
			validationErrors = append(validationErrors, fmt.Sprintf("第 %d 行: 顾客姓名不能为空", rowIndex))
		} else if len(customerName) > 32*3 { // 考虑UTF-8字符
			validationErrors = append(validationErrors, fmt.Sprintf("第 %d 行: 顾客姓名过长", rowIndex))
		}
		// 关联用户校验
		userInfo, err := w.userDao.GetByUsername(c, terminalAccount)
		if err != nil {
			validationErrors = append(validationErrors, fmt.Sprintf("第 %d 行: 用户账号 '%s' 不存在", rowIndex, terminalAccount))
			continue
		}
		salesmanID := userInfo.ID

		if len(validationErrors) > 0 {
			continue
		}
		var readyWarranty *api.CreateWarrantyReq
		readyWarranty = &api.CreateWarrantyReq{
			Barcode:         barcode,
			Date:            buyDate,
			Phone:           phone,
			Name:            customerName,
			Sex:             sexMap[strings.TrimSpace(row[6])],
			StudentName:     strings.TrimSpace(row[7]),
			StudentSchool:   strings.TrimSpace(row[8]),
			StudentGrade:    strings.TrimSpace(row[9]),
			StudentBirthday: studentBirthday,
			StudentSex:      sexMap[strings.TrimSpace(row[11])],
		}
		uidMap[readyWarranty] = salesmanID
		validWarranties = append(validWarranties, readyWarranty)
	}
	if len(validationErrors) > 0 {
		resp.FailedMsg = errors.NewErr(fmt.Sprintf("数据校验失败，具体数据：\n%s", strings.Join(validationErrors, "\n"))).Error()
		return &resp
	}

	var importErrors []string
	var importSuccess []string
	successCount := 0
	for _, wt := range validWarranties {
		_, err := w.Create(c, uidMap[wt], wt)
		if err != nil {
			importErrors = append(importErrors, fmt.Sprintf("保卡[%s]导入失败：%s", wt.Barcode, err.Error()))
		} else {
			importSuccess = append(importSuccess, fmt.Sprintf("保卡[%s]导入成功", wt.Barcode))
			successCount++
		}
	}

	resp.Msg = fmt.Sprintf("所有数据导入成功")
	if len(importErrors) > 0 {
		resp.Msg = fmt.Sprintf("%d条数据导入成功，%d条数据导入失败", successCount, len(importErrors))
		resp.SuccessMsg = strings.Join(importSuccess, "\n")
		resp.FailedMsg = strings.Join(importErrors, "\n")
	}

	return &resp
}

func (w *warranty) ExportWarranties(c *gin.Context, page int, pageSize int, code string) ([]*api.WarrantiesExportResp, int64, error) {
	modelIds, err := w.machineTypeDao.GetModelIDsByCategoryIDs(c, []int{1, 2, 3, 8})
	if err != nil {
		return nil, 0, err
	}
	warrantiesExport, total, err := w.repo.GetExportWarranties(c, page, pageSize, code, modelIds)
	if err != nil {
		return nil, 0, err
	}
	return warrantiesExport, total, nil
}

func (w *warranty) BatchGetWarranties(c *gin.Context, barcodes []string) *api.BatchWarrantiesResp {
	successWarranty := make([]*api.WarrantiesResp, 0)
	batchGetMsg := fmt.Sprintf("成功查询所有数据")
	var failedMsg []string
	for _, barcode := range barcodes {
		snStrategy := strategy.NewStrategyForBarcodeGetWarranty(db.GetDB(), barcode)
		oneBarcodeRecord, err := w.SNGetWarranties(c, snStrategy)
		if err != nil {
			failedMsg = append(failedMsg, fmt.Sprintf("获取保卡[%s]信息失败: %s", barcode, err.Error()))
			continue
		}
		successWarranty = append(successWarranty, oneBarcodeRecord)
	}
	if len(failedMsg) > 0 {
		batchGetMsg = strings.Join(failedMsg, "\n")
	}
	return &api.BatchWarrantiesResp{
		BatchGetMsg: batchGetMsg,
		List:        successWarranty,
	}
}

func (w *warranty) TrackMachineLife(c *gin.Context, barcode string) (*api.MachineLifeResp, error) {
	var wg sync.WaitGroup

	data := &machineLifeData{}
	tasks := w.buildGetMachineLifeTasks(c, barcode, data)

	wg.Add(len(tasks))
	for _, task := range tasks {
		t := task
		go func() {
			defer wg.Done()
			if err := t.run(); err != nil {
				zaplog.Info("查找"+t.name+"失败", zap.Error(err))
			}
		}()
	}
	wg.Wait()

	machineLife := &api.MachineLifeResp{
		WarrantiesInfo:      data.warrantiesInfo,
		PrototypeInfo:       data.prototypeInfo,
		ActivatedDeviceInfo: convertor.NewActivatedDeviceInfoConvertor().ModelsToResponse(data.activatedDevicesInfo),
		PackInfo:            convertor.NewPackInfoConvertor().ModelsToResponse(data.packInfo),
		K3Info: &api.K3Info{
			InStock:     convertor.NewK3InStockConvertor().ModelsToResponse(data.inStockInfos),
			OutStock:    convertor.NewK3OutStockConvertor().ModelsToResponse(data.outStockInfos),
			ReturnStock: convertor.NewK3ReturnStockConvertor().ModelsToResponse(data.returnStockInfos),
			AllotStock:  convertor.NewK3AllotStockConvertor().ModelsToResponse(data.allotStockInfos),
		},
	}

	return machineLife, nil
}

func (w *warranty) MachinesHistoriesList(c *gin.Context, params *api.MachinesHistoriesReq) (*api.MachinesHistoriesResp, error) {
	list, total, err := w.repo.GetMachinesHistoriesList(c, params)
	if err != nil {
		return nil, errors.NewErr("查找机器信息变更历史失败")
	}
	return &api.MachinesHistoriesResp{
		Page:     params.Page,
		PageSize: params.PageSize,
		Total:    total,
		List:     list,
	}, nil
}

func (w *warranty) MachineHistory(c *gin.Context, barcode string) ([]*api.MachineHistoryResp, error) {
	machineHistory, err := w.repo.GetDevicesHistory(c, barcode)
	if err != nil {
		return nil, errors.NewErr("查找条码机器历史信息失败")
	}
	var machineHistoryResp []*api.MachineHistoryResp
	for _, history := range machineHistory {
		var machineHistoryDto *api.MachineHistoryResp
		machineHistoryDto = &api.MachineHistoryResp{
			Id:         history.ID,
			ModelId:    history.ModelID,
			Model:      history.Model,
			Barcode:    history.Barcode,
			Number:     history.Number,
			NumberNew:  history.NumberNew,
			Remark:     history.Remark,
			From:       int(history.From),
			Status:     int(history.Status),
			AddTime:    history.AddTime,
			UpdateTime: history.UpdateTime,
		}
		machineHistoryResp = append(machineHistoryResp, machineHistoryDto)
	}
	return machineHistoryResp, nil
}

func (w *warranty) MachineHistoryCreate(c *gin.Context, params *api.MachineHistoryUpdReq) (string, error) {
	var msg string
	devicesInMesOrHistory, err := w.repo.GetDevicesHistory(c, params.Barcode)
	if len(devicesInMesOrHistory) == 0 {
		return "", errors.NewErr("无SN码对应设备信息")
	}
	devicesInHistory, err := w.repo.GetDevicesHistoryByNumberNew(c, params.NumberNew)
	if err != nil {
		msg = fmt.Sprintf("查找设备历史信息错误")
		return msg, err
	}
	if devicesInHistory != nil {
		msg = fmt.Sprintf("新序列号设备已存在")
		return msg, nil
	}
	for _, oneHistory := range devicesInMesOrHistory {
		err = w.repo.UpsertDevicesHistory(c, oneHistory, params)
		if err != nil {
			msg = fmt.Sprintf("机器历史变更信息创建/更新失败")
			return msg, err
		}
	}
	msg = fmt.Sprintf("变更成功")
	return msg, nil
}

func (w *warranty) DevicesLimitAccountList(c *gin.Context, req *api.DevicesLimitAccountReq) (list []*api.DevicesLimitAccount, total int64, err error) {
	accountList, total, err := w.repo.GetDevicesLimitAccountList(c, req)
	if err != nil {
		return
	}
	for _, account := range accountList {
		var accountDto *api.DevicesLimitAccount
		accountDto = &api.DevicesLimitAccount{
			ID:        account.ID,
			Barcode:   account.Barcode,
			Number:    account.Number,
			Phone:     account.Phone,
			Model:     account.Model,
			Remark:    account.Remark,
			Status:    *account.Status,
			CreatedAt: account.CreatedAt,
			UpdatedAt: account.UpdatedAt,
		}
		list = append(list, accountDto)
	}
	return
}

func (w *warranty) DevicesLimitAccountEdit(c *gin.Context, params *api.DevicesLimitAccountUpdReq) (msg string, err error) {
	msg = fmt.Sprintf("更新成功")
	phone := params.Phone
	status := params.Status
	query := gorm.Expr("id = ?", params.ID)
	err = w.repo.EditDevicesLimitAccount(c, query, map[string]interface{}{
		"phone":  phone,
		"status": *status,
	})
	if err != nil {
		msg = fmt.Sprintf("更新失败")
	}
	return
}

func (w *warranty) DevicesLimitAccountImport(c *gin.Context, f *excelize.File, fileName string, sheetName string) (string, error) {
	phoneRegex := regexp.MustCompile(`^1\d{10}$`)
	barcodeRegex := regexp.MustCompile(`^[A-Za-z0-9]{6,14}$`)
	rows, err := f.Rows(sheetName)
	if err != nil {
		return "导入数据失败", errors.NewErr(fmt.Sprintf("文件不标准:%v", err))
	}
	defer func(rows *excelize.Rows) {
		_ = rows.Close()
	}(rows)

	var (
		excelDataErrors []string
		insertData      []*model.DeviceLimitAccount
		barcodeToData   = make(map[string]*model.DeviceLimitAccount)
	)
	rows.Next()
	rowNum := 0
	for rows.Next() {
		rowNum++
		columns, err := rows.Columns(excelize.Options{RawCellValue: true})
		if err != nil {
			return "导入数据失败", errors.NewErr(fmt.Sprintf("读取行数据失败:%v", err))
		}
		if len(columns) < 5 || (len(columns) > 6 && strings.Join(columns[:6], "") == "") {
			continue
		}
		barcode := strings.TrimSpace(getExcelData(columns, 4))
		if !barcodeRegex.MatchString(barcode) {
			excelDataErrors = append(excelDataErrors, fmt.Sprintf("第 %d 行: SN码[%s]格式错误", rowNum, barcode))
			continue
		}
		phone := strings.TrimSpace(getExcelData(columns, 5)) // 清理空格
		if !phoneRegex.MatchString(phone) {
			excelDataErrors = append(excelDataErrors, fmt.Sprintf("第 %d 行: 电话格式不正确", rowNum))
			continue
		}
		cond := gorm.Expr("barcode = ?", barcode)
		account, err := w.repo.GetDevicesLimitAccountByCond(c, cond)
		if account != nil {
			excelDataErrors = append(excelDataErrors, fmt.Sprintf("第 %d 行: SN码[%s]已存在", rowNum, barcode))
			continue
		}
		barcodeToData[barcode] = &model.DeviceLimitAccount{
			Model:   strings.TrimSpace(getExcelData(columns, 3)),
			Barcode: barcode,
			Phone:   phone,
			Remark:  fileName,
			Status:  intPtr(1),
		}
	}
	if len(barcodeToData) == 0 {
		return "导入数据失败", errors.NewErr(strings.Join(excelDataErrors, "\n"))
	}
	barcodes := getKeys(barcodeToData)
	inMesDevices, err := w.repo.GetDevicesLimitNumber(c, barcodes)
	if err != nil {
		return "导入数据失败", errors.NewErr(fmt.Sprintf("查询条码记录失败:%v", err))
	}
	var successImportMsg []string
	for barcode, number := range inMesDevices {
		if data, ok := barcodeToData[barcode]; ok {
			data.Number = number
			insertData = append(insertData, data)
			successImportMsg = append(successImportMsg, fmt.Sprintf("保卡[%s]导入成功", barcode))
		} else {
			excelDataErrors = append(excelDataErrors, fmt.Sprintf("查询不到条形码[%s]记录", barcode))
		}
	}
	if len(insertData) == 0 {
		return "导入数据失败", errors.NewErr(fmt.Sprintf("没有匹配的条码记录:%v", err))
	}
	err = w.repo.BatchInsertDevicesLimitAccount(c, insertData)
	if err != nil {
		return "导入数据失败", errors.NewErr(fmt.Sprintf("批量插入数据失败:%v", err))
	}
	if len(excelDataErrors) > 0 {
		return strings.Join(successImportMsg, "\n"), errors.NewErr(strings.Join(excelDataErrors, "\n"))
	}
	return "所有数据导入成功", nil
}

func (w *warranty) processWarrantyEntry(
	c *gin.Context,
	pending *api.Warranty,
	endpointInfo *endpoint.EndpointInfoWithC,
	cnt int,
) (map[string]interface{}, error) {
	// 查询现有保卡（包括副卡）并进行初步校验
	existingWarranty, err := w.repo.HasWarranty(c, pending.Barcode, pending.Imei, pending.Number)
	if err != nil {
		return nil, fmt.Errorf("查询已有保卡失败: %w", err)
	}
	// 处理已存在主卡的情况
	if existingWarranty != nil && !existingWarranty.CreatedAt.IsZero() {
		return nil, errors.NewErr("电子保卡已存在")
	}
	// 校验副机条码（1、pending 中提供的副机条码  2、existingWarranty中的副机条码）
	if pending.ExtBarcode != "" {
		if err := w.validateSecondaryCard(c, pending, existingWarranty); err != nil {
			return nil, err
		}
	}

	var resp map[string]interface{}
	var finalWarranty *model.Warranty

	if existingWarranty != nil {
		finalWarranty, resp, err = w.processExistingWarranty(c, pending, endpointInfo, existingWarranty)
		if err != nil {
			return nil, err
		}
	} else {
		finalWarranty, resp, err = w.processNewWarranty(c, pending, endpointInfo)
		if err != nil {
			return nil, err
		}
	}

	var affected int64
	if existingWarranty != nil {
		affected, err = w.repo.UpdateWarranty(c, finalWarranty)
	} else {
		affected, err = w.repo.AddWarranty(c, finalWarranty)
	}
	if err != nil || affected == 0 {
		return nil, errors.NewErr(fmt.Sprintf("保存保卡信息失败: %v", err))
	}

	// 激活终端
	if err := w.endpointDao.ActivateEndpoint(c, endpointInfo.ID); err != nil {
		// 激活失败可能不直接阻塞保卡录入，但需要记录或返回错误
		return nil, fmt.Errorf("激活终端失败: %w", err)
	}

	// 补充响应信息
	resp["endpoint_name"] = endpointInfo.Name
	resp["endpoint_address"] = endpointInfo.Address
	resp["end_phone"] = endpointInfo.Phone
	resp["endpoint_manager"] = endpointInfo.Manager

	// TODO: 通知成功添加 notifyAddSuccess

	// 同步进销存
	if err = w.repo.DrpMachine(c, finalWarranty.Barcode, "", 1, 0, nil); err != nil {
		return nil, err
	}
	// 更新联系人
	if cnt == 1 {
		if ok := w.contactSvc.LoadContactByWarranty(c, finalWarranty); !ok {
			return nil, errors.NewErr("导入联系人失败")
		} // 同步联系人信息
	}
	// 取消样机
	err = w.prototypeSvc.WarrantyCancelPrototype(c, &api.PrototypeCancel{
		Barcode: finalWarranty.Barcode,
		Number:  finalWarranty.Number,
	})
	if err != nil {
		zaplog.Info(err.Error())
		return resp, nil
	}
	// 录保卡更新老带新订单的warranty_id字段
	go func() {
		err = w.repo.UpdateOldWithNewOrder(c, &api.UpdOldWithNewReq{
			Model:      finalWarranty.Model,
			EndpointID: endpointInfo.ID,
			Phone:      finalWarranty.CustomerPhone,
			Barcode:    finalWarranty.Barcode,
		})
	}()
	// TODO: 补录保卡(需要前端传来is_supply字段,新版暂时没有)
	return resp, nil
}

func (w *warranty) processExistingWarranty(
	c *gin.Context,
	pending *api.Warranty,
	endpointInfo *endpoint.EndpointInfoWithC,
	existingWarranty *api.WarrantyWithMT,
) (*model.Warranty, map[string]interface{}, error) {
	// 1. 校验特定品类（Category ID 8，agency 渠道，Model 以 "D" 开头）的学生信息
	if existingWarranty.CategoryId == 8 && endpointInfo.Channel == "agency" && strings.HasPrefix(existingWarranty.Model, "D") {
		if pending.StudentName == "" {
			return nil, nil, errors.NewErr("学生姓名不能为空")
		}
		if pending.StudentSchool == "" {
			return nil, nil, errors.NewErr("学生学校不能为空")
		}
		if pending.StudentGrade == "" {
			return nil, nil, errors.NewErr("学生年级不能为空")
		}
	}

	// 2. 查询品类和价格
	modelInfo := w.machineTypeDao.GetMachineTypeByModelId(c, existingWarranty.ModelID)
	if modelInfo == nil || modelInfo.CategoryId == 0 {
		// TODO: 微信通知失败
		return nil, nil, errors.NewErr("未找到机型信息")
	}
	customerPrice := modelInfo.CustomerPrice

	// 3. 计算 assessment
	assessment := uint(0)
	if existingWarranty.CategoryId == 1 {
		// 检查是否在样机库
		count, err := w.prototypeDao.PrototypeCheck(c, pending.Barcode, "") // 假设PrototypeCheck需要imei
		if err != nil {
			return nil, nil, errors.NewErr(fmt.Sprintf("校验样机库失败: %v", err))
		}

		// 判断激活时间是否合法
		if count == 0 && !existingWarranty.ActivatedAtOld.IsZero() && pending.BuyDate.After(existingWarranty.ActivatedAtOld) {
			isDateRangeValid := existingWarranty.ActivatedAtOld.After(time.Date(2021, 1, 31, 0, 0, 0, 0, time.UTC)) ||
				existingWarranty.ActivatedAtOld.Before(time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC))
			if isDateRangeValid {
				msg := fmt.Sprintf("录入失败，购买日期不得晚于机器的激活日期。 \n本机的激活时间为%s，请修改购买日期后重新提交。",
					existingWarranty.ActivatedAtOld.Format("2006-01-02"))
				return nil, nil, errors.NewErr(msg)
			}
		}

		// 确定 assessment 值
		if existingWarranty.ActivatedAtOld.IsZero() {
			assessment = 0 // 没有激活时间按正常流程
		} else {
			nowTime := time.Now()
			// 录入时间和购买时间同年同月并且纳入考核
			if pending.BuyDate.Format("2006-01") == nowTime.Format("2006-01") &&
				existingWarranty.ActivatedAtOld.After(time.Date(2021, 1, 31, 0, 0, 0, 0, time.UTC)) {
				assessment = 1
			} else if pending.BuyDate.AddDate(0, 1, 0).Format("2006-01") == nowTime.Format("2006-01") &&
				nowTime.Day() <= 3 &&
				existingWarranty.ActivatedAtOld.After(time.Date(2021, 1, 31, 0, 0, 0, 0, time.UTC)) {
				assessment = 1
			}
		}
	} else {
		// 默认纳入考核
		assessment = 1
	}

	updateWarranty := &model.Warranty{
		Barcode:             pending.Barcode,
		Imei:                existingWarranty.Imei,
		ExtBarcode:          pending.ExtBarcode,
		Endpoint:            endpointInfo.ID,
		BuyDate:             pending.BuyDate,
		Assessment:          &assessment,
		SalesmanID:          pending.SalesmanID,
		Salesman:            pending.Salesman,
		CustomerPrice:       customerPrice,
		CustomerName:        pending.CustomerName,
		CustomerSex:         pending.CustomerSex,
		CustomerPhone:       pending.CustomerPhone,
		CustomerAddr:        pending.CustomerAddr,
		StudentUID:          pending.StudentUID,
		CreatedAt:           pending.CreatedAt,
		StudentName:         pending.StudentName,
		StudentSex:          pending.StudentSex,
		StudentSchoolID:     pending.StudentSchoolID,
		StudentSchool:       pending.StudentSchool,
		StudentSchoolAdcode: pending.StudentSchoolAdcode,
		StudentGrade:        pending.StudentGrade,
		StudentBirthday:     types.CustomTime(pending.StudentBirthday),
		PurchaseWay:         pending.PurchaseWay,
		UpdatedAt:           types.CustomTime{},
		Recommender:         pending.Recommender,
		RecommenderPhone:    pending.RecommenderPhone,
		Type:                pending.Type,
		Lng:                 pending.Lng,
		Lat:                 pending.Lat,
	}

	resp := map[string]interface{}{
		"barcode":          pending.Barcode,
		"ext_barcode":      pending.ExtBarcode,
		"salesman":         pending.Salesman,
		"customer_name":    pending.CustomerName,
		"customer_phone":   pending.CustomerPhone,
		"customer_addr":    pending.CustomerAddr,
		"model":            existingWarranty.Model,
		"endpoint":         endpointInfo.ID,
		"buy_date":         pending.BuyDate.Format("2006-01-02"),
		"student_name":     pending.StudentName,
		"student_school":   pending.StudentSchool,
		"student_grade":    pending.StudentGrade,
		"student_birthday": pending.StudentBirthday.Format("2006-01-02"),
		"created_at":       pending.CreatedAt.Format("2006-01-02 15:04:05"),
		"customer_price":   pending.CustomerPrice,
		"customer_sex":     pending.CustomerSex,
		"student_sex":      pending.StudentSex,
		"salesman_id":      pending.SalesmanID,
	}

	return updateWarranty, resp, nil
}

func (w *warranty) processNewWarranty(
	c *gin.Context,
	pending *api.Warranty,
	endpointInfo *endpoint.EndpointInfoWithC,
) (*model.Warranty, map[string]interface{}, error) {
	// 查询MES设备信息
	mesDevice, err := w.commonDao.GetMesDevice(c, pending.Barcode, pending.Imei, pending.Number)
	if err != nil || mesDevice == nil {
		return nil, nil, errors.NewErr("无效的条形码或IMEI或序列号")
	}

	// 原逻辑包含副机条码限制，但数据库中没有med_devices_type表
	//if mesDevice.ExtBarcodeCount < 0 {
	//	return nil, nil, errors.NewErr("此条码为副机条码，不可进行保卡录入，请输入对应的主机条码")
	//}
	//if pending.ExtBarcode != "" && mesDevice.ExtBarcodeCount <= 0 {
	//	return nil, nil, errors.NewErr("该机型无需副机条码")
	//}

	// 查询机型信息
	modelInfo := w.machineTypeDao.GetMachineTypeByModelId(c, mesDevice.ModelID)
	if modelInfo == nil || modelInfo.CategoryId == 0 {
		// TODO: 发送微信通知失败
		return nil, nil, errors.NewErr("未找到机型信息")
	}

	// 校验特定品类的学生信息
	if modelInfo.CategoryId == 8 && endpointInfo.Channel == "agency" && strings.HasPrefix(modelInfo.Name, "D") {
		if pending.StudentName == "" {
			return nil, nil, errors.NewErr("学生姓名不能为空")
		}
		if pending.StudentSchool == "" {
			return nil, nil, errors.NewErr("学生学校不能为空")
		}
		if pending.StudentGrade == "" {
			return nil, nil, errors.NewErr("学生年级不能为空")
		}
	}

	// 确定 assessment 值
	var assessment uint
	if modelInfo.CategoryId == 1 {
		assessment = 0 // 品类 1，默认 assessment 为 0
	} else {
		assessment = 1 // 其他品类，默认 assessment 为 1
	}

	state := int8(mesDevice.Status)
	addWarranty := &model.Warranty{
		Imei:                mesDevice.Imei1,
		Barcode:             mesDevice.Barcode, // 使用 MES 设备中的 Barcode
		ExtBarcode:          pending.ExtBarcode,
		Salesman:            pending.Salesman,
		CustomerName:        pending.CustomerName,
		CustomerPhone:       pending.CustomerPhone,
		CustomerAddr:        pending.CustomerAddr,
		ModelID:             mesDevice.ModelID,
		Model:               mesDevice.Model,
		Endpoint:            endpointInfo.ID,
		BuyDate:             pending.BuyDate,
		ProductDate:         mesDevice.ProductDate,
		Lng:                 pending.Lng,
		Lat:                 pending.Lat,
		StudentName:         pending.StudentName,
		StudentSchoolAdcode: pending.StudentSchoolAdcode,
		StudentSchoolID:     pending.StudentSchoolID,
		StudentSex:          pending.StudentSex,
		StudentSchool:       pending.StudentSchool,
		StudentGrade:        pending.StudentGrade,
		StudentBirthday:     types.CustomTime(pending.StudentBirthday),
		CustomerPrice:       modelInfo.CustomerPrice,
		SalesmanID:          pending.SalesmanID,
		State:               &state,
		Number:              mesDevice.Number,
		CustomerSex:         pending.CustomerSex,
		PurchaseWay:         pending.PurchaseWay,
		CreatedAt:           pending.CreatedAt,
		Recommender:         pending.Recommender,
		RecommenderPhone:    pending.RecommenderPhone,
		Assessment:          &assessment,
		StudentUID:          pending.StudentUID,
		Type:                pending.Type,
	}

	resp := map[string]interface{}{
		"barcode":          mesDevice.Barcode,
		"ext_barcode":      pending.ExtBarcode,
		"number":           mesDevice.Number,
		"salesman":         pending.Salesman,
		"customer_name":    pending.CustomerName,
		"customer_phone":   pending.CustomerPhone,
		"customer_addr":    pending.CustomerAddr,
		"model_id":         mesDevice.ModelID,
		"model":            mesDevice.Model,
		"endpoint":         endpointInfo.ID,
		"buy_date":         pending.BuyDate.Format("2006-01-02"),
		"product_date":     mesDevice.ProductDate.Format("2006-01-02"),
		"student_name":     pending.StudentName,
		"student_school":   pending.StudentSchool,
		"student_grade":    pending.StudentGrade,
		"student_birthday": pending.StudentBirthday.Format("2006-01-02"),
		"created_at":       pending.CreatedAt.Format("2006-01-02 15:04:05"),
		"customer_price":   modelInfo.CustomerPrice,
		"customer_sex":     pending.CustomerSex,
		"student_sex":      pending.StudentSex,
		"salesman_id":      pending.SalesmanID,
	}

	return addWarranty, resp, nil

}

func (w *warranty) allowPrototypeEntryCheck(c *gin.Context, pending *api.Warranty) error {
	prototypeInfo, err := w.prototypeDao.GetInfo(c, pending.Barcode) // 判断是否是演示样机
	if err != nil {
		return err
	}
	if prototypeInfo != nil && prototypeInfo.Type == consts.PrototypeTypeDemo && prototypeInfo.Discontinued == consts.PrototypeListingOn {
		return errors.NewErr("演示样机在库不允许录入保卡")
	}
	return nil
}

func (w *warranty) hasPermissionToAddWarranty(c *gin.Context, salesmanID int) (*endpoint.EndpointInfoWithC, error) {
	endpointInfo, err := w.endpointDao.GetUserEndpointInfo(c, salesmanID)
	if err != nil || endpointInfo.ID == 0 {
		return nil, errors.NewErr("账号无权限添加电子保卡")
	}
	return endpointInfo, nil
}

func (w *warranty) hasSalesman(c *gin.Context, pending *api.Warranty) error {
	// 查询user_endpoint获取UID
	var err error
	var salesmanID uint
	salesmanID = uint(pending.SalesmanID)
	if salesmanID == 0 { // 无salesmanID对应终端录入
		salesmanID, err = w.userDao.GetUserID(c, pending.Endpoint)
		pending.SalesmanID = int(salesmanID)
		if err != nil || salesmanID == 0 {
			return errors.NewErr("店长信息不存在")
		}
	}
	// 查询admin_users获取销售人员
	salesman, err := w.userDao.GetSalesmanByUserID(c, salesmanID)
	if err != nil {
		return errors.NewErr("店长信息不存在")
	}
	pending.Salesman = salesman
	return nil
}

func (w *warranty) validateSecondaryCard(c *gin.Context, pending *api.Warranty, existingWarranty *api.WarrantyWithMT) error {
	// 验证提供的副卡
	extSold, err := w.repo.HasWarranty(c, pending.ExtBarcode, "", "") // 验证副机条码的机器是否存在
	if err != nil {
		return errors.NewErr("查询副机保卡失败")
	}
	if extSold != nil {
		if !extSold.CreatedAt.IsZero() {
			return errors.NewErr("该副机条码电子保卡已存在")
		} else {
			extBarcodeNum := extSold.ExtBarcodeNum
			if extBarcodeNum >= 0 {
				return errors.NewErr("无效的副机条码")
			}
		}
	} else { // 验证副机条码的机器是否有效
		extMachine, err := mes.CheckMachine(mes.CheckMachineParams{
			Barcode: pending.ExtBarcode,
			Imei:    "",
			Number:  "",
		})
		if err != nil || extMachine == nil {
			return errors.NewErr("无效的副机条码")
		}
		if m, ok := extMachine.(map[string]interface{}); ok {
			extBarcodeNum := m["ext_barcode_count"].(int)
			if extBarcodeNum >= 0 {
				return errors.NewErr("无效的副机条码")
			}
		}
	}

	if existingWarranty != nil {
		// 如果已有保卡信息，则使用其 ExtBarcodeNum
		if existingWarranty.ExtBarcodeNum < 0 {
			return errors.NewErr("此条码为副机条码，不可进行保卡录入，请输入对应的主机条码")
		}
		if pending.ExtBarcode != "" && existingWarranty.ExtBarcodeNum <= 0 {
			return errors.NewErr("该机型无需副机条码")
		}
	}

	return nil
}

func (w *warranty) getBarcodeLifecycleInfo(c *gin.Context, warrantyDetail *api.WarrantyDetail) (*api.WarrantiesInfo, error) {
	var ready *api.WarrantiesInfo
	switch warrantyDetail.Status {
	case consts.WarrantyStatusActive:
		ready = &api.WarrantiesInfo{
			Detail: *warrantyDetail,
		}
	case consts.WarrantyStatusExchanged:
		e, err := w.repo.GetOneExchangeByWarrantyID(c, warrantyDetail.ID)
		if err != nil {
			return nil, err
		}
		ready = &api.WarrantiesInfo{
			Detail:       *warrantyDetail,
			ExchangeInfo: e,
		}
	case consts.WarrantyStatusReturned:
		r, err := w.repo.GetOneReturnByWarrantyID(c, warrantyDetail.ID)
		if err != nil {
			return nil, err
		}
		ready = &api.WarrantiesInfo{
			Detail:     *warrantyDetail,
			ReturnInfo: r,
		}
	case consts.WarrantyStatusRenewed:
	case consts.WarrantyStatusOther:
	}

	// TODO:碎屏险
	return ready, nil
}

// CheckHasScreenInsurance 调用repair api获取条码碎屏险信息
func (w *warranty) CheckHasScreenInsurance(c *gin.Context, barcode string) (interface{}, error) {
	params := getRepairParam()
	params["barcode"] = barcode

	values := url.Values{}
	for k, v := range params {
		values.Set(k, v)
	}
	repairHost := viper.GetString("repair.baseURL")
	urlStr := fmt.Sprintf("%s/broken_screen_insurance/check?%s", repairHost, values.Encode())
	client := &http.Client{Timeout: consts.HttpTimeout}
	req, err := http.NewRequestWithContext(c, "GET", urlStr, nil)
	if err != nil {
		return nil, err
	}
	resp, err := client.Do(req)
	if err != nil {
		zaplog.Info(fmt.Sprintf("请求 %s 失败：%v", urlStr, zap.Error(err)))
		return nil, err
	}
	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(resp.Body)

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		zaplog.Info(fmt.Sprintf("read from repair_host failed, error:%v", err))
		return nil, err
	}
	var data map[string]interface{}
	if err = json.Unmarshal(body, &data); err != nil {
		zaplog.Info(fmt.Sprintf("unmarshal repair_host failed, error:%v", err))
		return nil, err
	}
	if ok, exists := data["ok"]; exists && ok == float64(1) {
		return data["data"], nil
	}
	zaplog.Info(fmt.Sprintf("查询条码[%s]碎屏险失败: %v", barcode, data["msg"].(string)))
	return nil, nil
}

// ScreenInsuranceRefund 调用repair api退保碎屏保订单
func (w *warranty) ScreenInsuranceRefund(c *gin.Context, sn string) (interface{}, error) {
	repairHost := viper.GetString("repair.baseURL")
	urlStr := fmt.Sprintf("%s/broken_screen_insurance/refund", repairHost)
	param := getRepairParam()
	param["order_sn"] = sn

	values := url.Values{}
	for k, v := range param {
		values.Set(k, v)
	}
	body := values.Encode()

	req, err := http.NewRequestWithContext(c, "POST", urlStr, bytes.NewBufferString(body))
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	client := &http.Client{Timeout: consts.HttpTimeout}
	resp, err := client.Do(req)
	if err != nil {
		zaplog.Info(fmt.Sprintf("请求 %s 失败：%v", urlStr, zap.Error(err)))
		// TODO: sendWXWorkMsg()
		return nil, err
	}
	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(resp.Body)

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	var data map[string]interface{}
	if err := json.Unmarshal(respBody, &data); err != nil {
		return nil, err
	}
	var ret interface{}
	if ok, exists := data["ok"]; exists && ok == float64(1) {
		ret = data["data"]
		zaplog.Info(fmt.Sprintf("碎屏保订单[%s]退保成功:", data["msg"].(string)))
	} else {
		zaplog.Info(fmt.Sprintf("碎屏保订单[%s]退保失败: %v", urlStr, data["msg"].(string)))
		// TODO: sendWXWorkMsg()
	}
	return ret, nil
}

func (w *warranty) ScreenInsuranceExchange(c *gin.Context, barcode, barcodeNew, orderSn string) (interface{}, error) {
	repairHost := viper.GetString("repair.baseURL")
	urlStr := fmt.Sprintf("%s/broken_screen_insurance/exchange", repairHost)
	param := getRepairParam()
	param["order_sn"] = orderSn
	param["old_barcode"] = barcode
	param["new_barcode"] = barcodeNew

	form := url.Values{}
	for k, v := range param {
		form.Set(k, v)
	}
	req, err := http.NewRequestWithContext(c, "POST", urlStr, bytes.NewBufferString(form.Encode()))
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	client := &http.Client{Timeout: time.Second * 5}
	resp, err := client.Do(req)
	if err != nil {
		zaplog.Info(fmt.Sprintf("请求 %s 失败：%v", urlStr, zap.Error(err)))
		// TODO: sendWXWorkMsg()
		return nil, err
	}
	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(resp.Body)

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	var data map[string]interface{}
	if err = json.Unmarshal(body, &data); err != nil {
		return nil, err
	}
	if ok, exists := data["ok"]; exists && ok == float64(1) {
		zaplog.Info(fmt.Sprintf("碎屏保换保成功: %+v", data))
		return data["data"], nil
	}
	zaplog.Info(fmt.Sprintf("碎屏保换保失败: %s", data["msg"].(string)))
	// TODO: sendWXWorkMsg(碎屏保换保失败)
	return nil, nil
}

func validParamsIn(in *api.Warranty) error {
	if in.Barcode == "" {
		return errors.NewErr("序列号不能为空")
	}
	if in.Endpoint == 0 && in.SalesmanID == 0 {
		return errors.NewErr("终端地址或销售ID不能为空")
	}
	if in.CustomerPhone == "" {
		return errors.NewErr("手机不能为空")
	}
	if in.CustomerName == "" {
		return errors.NewErr("姓名不能为空")
	}

	if in.Type == 0 {
		in.Type = 1
	}
	now := time.Now()
	buy := in.BuyDate
	in.CreatedAt = now
	if buy.IsZero() {
		now := time.Now()
		in.BuyDate = now
	} else {
		if buy.After(time.Now()) {
			return errors.NewErr("购机时间不能超过当前时间")
		}
		if buy.AddDate(2, 0, 0).Before(time.Now()) {
			return errors.NewErr("购机时间不能超过两年")
		}
	}

	if in.Barcode != "" && !isBarcode(in.Barcode) {
		return errors.NewErr("条码格式错误")
	}
	if in.ExtBarcode != "" && !isBarcode(in.ExtBarcode) {
		return errors.NewErr("副机条码错误")
	}
	if in.Imei != "" && !isImei(in.Imei) {
		return errors.NewErr("IMEI格式错误")
	}
	if in.Number != "" && !isSerialNumber(in.Number) {
		return errors.NewErr("序列号错误")
	}
	if !isPhone(in.CustomerPhone) {
		return errors.NewErr("电话格式错误")
	}
	if in.StudentGrade != "" && !consts.IsGradeValid(in.StudentGrade) {
		return errors.NewErr("年级错误")
	}
	if !in.StudentBirthday.IsZero() && in.StudentBirthday.After(now) {
		return errors.NewErr("学生生日不能超过当前时间")
	}

	return nil
}

// 判断条码格式，5-14位字母或数字
func isBarcode(barcode string) bool {
	matched, _ := regexp.MatchString(`^[A-Za-z-0-9]{6,14}$`, barcode)
	return matched
}

// 判断IMEI格式，14位数字
func isImei(imei string) bool {
	matched, _ := regexp.MatchString(`^\d{14}$`, imei)
	return matched
}

// 判断序列号格式，7-64位字母或数字
func isSerialNumber(number string) bool {
	matched, _ := regexp.MatchString(`^[A-Za-z-0-9]{8,64}$`, number)
	return matched
}

// 判断是否为性别
func isSex(sex string) bool {
	if sex != consts.Male && sex != consts.Female {
		return false
	}
	return true
}

// 判断手机号格式，1开头的11位数字
func isPhone(phone string) bool {
	matched, _ := regexp.MatchString(`^1[0-9]{10}$`, phone)
	return matched
}

func fill(update *model.Warranty, params *api.EditReq) error {
	if params.ExtBarcode != "" {
		update.ExtBarcode = params.ExtBarcode
	}
	if params.CustomerName != "" {
		update.CustomerName = params.CustomerName
	}
	if params.CustomerPhone != "" {
		update.CustomerPhone = params.CustomerPhone
	}
	if params.CustomerSex != "" {
		if !isSex(params.CustomerSex) {
			return errors.NewErr("性别错误")
		}
		update.CustomerSex = params.CustomerSex
	}
	if params.PurchaseWay != "" {
		update.PurchaseWay = params.PurchaseWay
	}
	if params.StudentName != "" {
		update.StudentName = params.StudentName
	}
	if params.StudentSchool != "" {
		update.StudentSchool = params.StudentSchool
	}
	if params.StudentGrade != "" {
		update.StudentGrade = params.StudentGrade
	}
	if params.StudentBirthday != "" {
		date, err := time.Parse("2006-01-02", params.StudentBirthday)
		if err != nil {
			return err
		}
		update.StudentBirthday = types.CustomTime(date)
	}
	if params.StudentSex != "" {
		if !isSex(params.CustomerSex) {
			return errors.NewErr("性别错误")
		}
		update.StudentSex = params.StudentSex
	}

	if params.Assessment == consts.WarrantyAssessment {
		*(update.Assessment) = 1
	} else {
		*(update.Assessment) = 0
	}
	return nil
}

func checkOutOfDate(wt *model.Warranty, returnAt time.Time, tp int) bool {
	cond := map[int]int{
		1: 7,
		2: 30,
		3: 365,
	}

	return !wt.BuyDate.Add(time.Duration(cond[tp]) * 24 * time.Hour).Before(returnAt)
}

// parseExcelDate 辅助处理Excel中的日期（数字格式）
func parseExcelDate(f *excelize.File, sheet, axis, cellValue string) (string, error) {
	if cellValue == "" {
		return "", nil
	}
	val, _ := f.GetCellValue(sheet, axis)
	return val, nil
}

func getExcelData(list []string, i int) string {
	if len(list) > i {
		return list[i]
	}
	return ""
}

func intPtr(i int) *int {
	return &i
}

func getKeys(m map[string]*model.DeviceLimitAccount) []string {
	keys := make([]string, 0, len(m))
	for k := range m {
		keys = append(keys, k)
	}
	return keys
}

func getRepairParam() map[string]string {
	appid := viper.GetString("repair.appID")
	appKey := viper.GetString("repair.appKey")
	deviceItem := []string{"1", "", "", appid, "", ""}
	deviceID := ""
	for i, v := range deviceItem {
		if i > 0 {
			deviceID += "/"
		}
		deviceID += v
	}
	t := time.Now().Unix()
	sn := fmt.Sprintf("%x", md5Sum(deviceID+appKey+fmt.Sprintf("%d", t)))
	return map[string]string{
		"t":  fmt.Sprintf("%d", t),
		"ua": deviceID,
		"sn": sn,
	}
}

func md5Sum(s string) [16]byte {
	return md5.Sum([]byte(s))
}

func (w *warranty) buildGetMachineLifeTasks(c *gin.Context, barcode string, data *machineLifeData) []struct {
	name string
	run  func() error
} {
	return []struct {
		name string
		run  func() error
	}{
		{
			name: "包装记录",
			run: func() (err error) {
				data.packInfo, err = w.repo.GetPackRecord(c, barcode)
				return
			},
		},
		{
			name: "K3入库记录",
			run: func() (err error) {
				data.inStockInfos, err = w.repo.GetK3RecordInStock(c, barcode)
				return
			},
		},
		{
			name: "K3出库记录",
			run: func() (err error) {
				data.outStockInfos, err = w.repo.GetK3RecordOutStock(c, barcode)
				return
			},
		},
		{
			name: "K3退货记录",
			run: func() (err error) {
				data.returnStockInfos, err = w.repo.GetK3RecordReturnStock(c, barcode)
				return
			},
		},
		{
			name: "K3调拨记录",
			run: func() (err error) {
				data.allotStockInfos, err = w.repo.GetK3RecordAllotStock(c, barcode)
				return
			},
		},
		{
			name: "保卡数据",
			run: func() (err error) {
				machineStrategy := strategy.NewStrategyForMachineLife(db.GetDB(), barcode)
				data.warrantiesInfo, err = w.SNGetWarranties(c, machineStrategy)
				return
			},
		},
		{
			name: "激活记录",
			run: func() (err error) {
				data.activatedDevicesInfo, err = w.repo.GetActivatedDevices(c, barcode)
				return
			},
		},
		{
			name: "样机记录",
			run: func() (err error) {
				data.prototypeInfo, err = w.prototypeDao.GetPrototypeForMachineLife(c, barcode)
				return
			},
		},
	}
}
