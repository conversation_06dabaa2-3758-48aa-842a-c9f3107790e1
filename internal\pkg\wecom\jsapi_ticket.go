package wecom

import (
	"encoding/json"
	"fmt"
	"marketing/internal/pkg/log"
	"sync"
	"time"

	"go.uber.org/zap"
)

// JsapiTicketResp 用于解析获取到的jsapi_ticket响应
type JsapiTicketResp struct {
	ErrCode   int    `json:"errcode"`
	ErrMsg    string `json:"errmsg"`
	Ticket    string `json:"ticket"`
	ExpiresIn int    `json:"expires_in"`
}

// jsapiTicketInfo 用于存储ticket信息
type jsapiTicketInfo struct {
	ticket     string
	expiration time.Time
}

// ticketCache 用于缓存ticket
type ticketCache struct {
	sync.RWMutex
	jsapiTicket *jsapiTicketInfo
}

// 初始化ticket缓存
func (client *Client) initTicketCache() {
	client.ticketCache = &ticketCache{}
}

// GetJsapiTicket 获取企业jsapi_ticket
func (client *Client) GetJsapiTicket() (string, error) {
	client.ticketCache.Lock()
	defer client.ticketCache.Unlock()

	now := time.Now()

	// 如果缓存的ticket仍然有效，直接返回
	if client.ticketCache.jsapiTicket != nil && now.Before(client.ticketCache.jsapiTicket.expiration) {
		return client.ticketCache.jsapiTicket.ticket, nil
	}

	// 获取新的ticket
	accessToken, err := client.GetAccessToken()
	if err != nil {
		return "", err
	}

	url := fmt.Sprintf("https://qyapi.weixin.qq.com/cgi-bin/get_jsapi_ticket?access_token=%s", accessToken)
	resp, err := client.httpClient.R().Get(url)
	if err != nil {
		return "", err
	}

	log.Debug("企微获取jsapi_ticket：", zap.Any("企微获取jsapi_ticket响应", resp))

	var ticketResp JsapiTicketResp
	if err := json.Unmarshal(resp.Body(), &ticketResp); err != nil {
		log.Error("解析jsapi_ticket响应失败：", zap.Error(err))
		return "", err
	}

	if ticketResp.ErrCode != 0 {
		log.Error("获取jsapi_ticket失败：", zap.Int("errcode", ticketResp.ErrCode), zap.String("errmsg", ticketResp.ErrMsg))
		return "", &WeChatAPIError{ErrCode: ticketResp.ErrCode, ErrMsg: ticketResp.ErrMsg}
	}

	// 更新缓存
	client.ticketCache.jsapiTicket = &jsapiTicketInfo{
		ticket:     ticketResp.Ticket,
		expiration: now.Add(time.Duration(ticketResp.ExpiresIn) * time.Second),
	}

	return client.ticketCache.jsapiTicket.ticket, nil
}
