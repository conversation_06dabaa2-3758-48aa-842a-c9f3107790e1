package base

import (
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"marketing/internal/handler"
	"marketing/internal/service/base"
)

type RegionHandler interface {
	RegionList(c *gin.Context)
}

type regionHandler struct {
	service base.RegionService
}

func NewRegionHandler(service base.RegionService) RegionHandler {
	return &regionHandler{service: service}
}

func (r *regionHandler) RegionList(c *gin.Context) {
	regionType := c.<PERSON><PERSON>("region_type", "0")
	parentId := cast.ToInt(c.Query("parent_id"))

	list, err := r.service.GetRegionList(c, regionType, parentId)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, list)
}
