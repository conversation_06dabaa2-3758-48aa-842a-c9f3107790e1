package dao

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"marketing/internal/model"
	"marketing/internal/pkg/db"
)

type V3CustomerDao interface {
	EditCustomer(c *gin.Context, customerId int, uMap map[string]interface{}) error
	GetCustomerList(c *gin.Context, channel, groupName string, customerStatus int, name string, pageNum, pageSize int) (list []*model.CustomerInfo, total int64)
	GetCustomerById(c *gin.Context, customerId int) *model.CustomerInfo
	GetCustomerGroupByGIds(c *gin.Context, groupIds []int) (list []*model.V3CustomerGroup)
	GetCustomerGroupByGId(c *gin.Context, groupId int) *model.V3CustomerGroup
}

// V3CustomerDaoImpl 实现 V3CustomerDao 接口
type V3CustomerDaoImpl struct {
	db *gorm.DB
}

// NewV3CustomerDao 创建 V3CustomerDao 实例
func NewV3CustomerDao() V3CustomerDao {
	return &V3CustomerDaoImpl{
		db: db.GetDB(""),
	}
}

func (d *V3CustomerDaoImpl) EditCustomer(c *gin.Context, customerId int, uMap map[string]interface{}) error {
	return d.db.WithContext(c).Model(&model.V3Customer{}).Where("cust_id = ?", customerId).Updates(uMap).Error
}

func (d *V3CustomerDaoImpl) GetCustomerList(c *gin.Context, channel, groupCode string, customerStatus int, name string, pageNum, pageSize int) (
	list []*model.CustomerInfo, total int64) {

	query := d.db.WithContext(c).Model(&model.V3Customer{}).
		Select("v3_customer_group.group_code,v3_customer_group.group_name,v3_customer_group.group_status,v3_customer.cust_id," +
			"v3_customer.cust_code,v3_customer.cust_name,v3_customer.cust_short_name,v3_customer.cust_status," +
			"v3_customer.channel,v3_customer_group.group_id,v3_customer_group.group_parentid").
		Joins("left join v3_customer_group on v3_customer.group_code = v3_customer_group.group_code")

	if len(channel) > 0 {
		query = query.Where("v3_customer.channel = '" + channel + "'")
	}

	if len(groupCode) > 0 {
		query = query.Where("v3_customer.group_code = '" + groupCode + "'")
	}

	if customerStatus == 0 || customerStatus == 1 {
		query = query.Where("v3_customer.cust_status = ?", customerStatus)
	}

	if len(name) > 0 {
		query = query.Where("v3_customer.cust_name = '" + name + "'")
	}

	if pageNum < 1 {
		pageNum = 1
	}

	if pageSize < 1 {
		pageSize = 20
	}

	query.Count(&total)
	query.Offset((pageNum - 1) * pageSize).Limit(pageSize).Find(&list)

	return
}

func (d *V3CustomerDaoImpl) GetCustomerById(c *gin.Context, customerId int) *model.CustomerInfo {
	var info model.CustomerInfo
	err := d.db.WithContext(c).Model(&model.V3Customer{}).
		Select("v3_customer_group.group_code,v3_customer_group.group_name,v3_customer_group.group_status,v3_customer.cust_id,"+
			"v3_customer.cust_code,v3_customer.cust_name,v3_customer.cust_short_name,v3_customer.cust_status,"+
			"v3_customer.channel,v3_customer_group.group_id,v3_customer_group.group_parentid").
		Joins("left join v3_customer_group on v3_customer.group_code = v3_customer_group.group_code").
		Where("v3_customer.cust_id = ?", customerId).First(&info).Error
	if err != nil {
		return nil
	}

	return &info
}

func (d *V3CustomerDaoImpl) GetCustomerGroupByGIds(c *gin.Context, groupIds []int) (list []*model.V3CustomerGroup) {
	d.db.WithContext(c).Model(&model.V3CustomerGroup{}).Where("group_id in (?)", groupIds).Find(&list)
	return
}

func (d *V3CustomerDaoImpl) GetCustomerGroupByGId(c *gin.Context, groupId int) *model.V3CustomerGroup {
	var g model.V3CustomerGroup
	err := d.db.WithContext(c).Model(&model.V3CustomerGroup{}).Where("group_id = ?", groupId).First(&g).Error
	if err != nil {
		return nil
	}
	return &g
}
