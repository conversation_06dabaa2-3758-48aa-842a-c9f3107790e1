package model

import "time"

// ReimbursementApplyInfo 报销申请信息表
type ReimbursementApplyInfo struct {
	ID                     uint       `gorm:"primaryKey;column:id;type:int(10) unsigned;not null;auto_increment"`
	ApplyOrderID           int        `gorm:"column:apply_order_id;type:int(11);default:0;comment:申请单id"`
	Explain                string     `gorm:"column:explain;type:varchar(255);not null;default:'';comment:解释说明"`
	URL                    string     `gorm:"column:url;type:varchar(255);not null;default:'';comment:图片信息"`
	CreatedAt              time.Time  `gorm:"column:created_at;type:timestamp;not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt              *time.Time `gorm:"column:updated_at;type:timestamp;null"`
	ApplyOrderType         string     `gorm:"column:apply_order_type;type:enum('promotional_products','advert_expense');not null;comment:核销类型 'promotional_products'--促销品,'advert_expense'--广告费"`
	DeletedAt              *time.Time `gorm:"column:deleted_at;type:timestamp;null;comment:软删除;index"`
	ReimbursementListID    int        `gorm:"column:reimbursement_list_id;type:int(11);default:0;comment:核销单id"`
	ReimbursementSummaryID int        `gorm:"column:reimbursement_summary_id;type:int(11);default:0;comment:核销小单id"`
	Type                   string     `gorm:"column:type;type:varchar(255);not null;default:'1';comment:1--凭证 2--收款单"`
}

// TableName 指定表名
func (ReimbursementApplyInfo) TableName() string {
	return "reimbursement_apply_info"
}
