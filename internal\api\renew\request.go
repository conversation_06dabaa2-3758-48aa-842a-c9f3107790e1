package renew

import (
	"marketing/internal/api"
	"marketing/internal/model"
	"marketing/internal/pkg/types"
)

type AddApplicationReq struct {
	ID                uint     `json:"id" form:"id"`
	Barcode           string   `json:"barcode" form:"barcode" binding:"required"`
	Model             string   `json:"model" form:"model" `
	ModelID           uint     `json:"model_id" form:"model_id" `
	TopAgency         uint     `json:"top_agency" form:"top_agency"`
	SecondAgency      uint     `json:"second_agency" form:"second_agency"`
	EndpointID        uint     `json:"endpoint_id" form:"endpoint_id"`
	Issues            []string `json:"issues" form:"issues" binding:"required"`
	DamageDescription string   `json:"damage_description" form:"damage_description"`
	DamageImages      []string `json:"damage_images" form:"damage_images"`
	Status            string   `json:"status" form:"status"`
	Applicant         uint     `json:"applicant" form:"applicant"`
	ApplicantType     string   `json:"applicant_type" form:"applicant_type"` // endpoint: 终端, agency: 代理
}

type AuditApplicationReq struct {
	ID        uint   `json:"id" form:"id"`
	Comment   string `json:"comment" form:"comment"`
	HandlerID uint   `json:"handler_id" form:"handler_id"`
	Status    string `json:"status" form:"status" binding:"required"`
	AuditType string `json:"audit_type" form:"audit_type"`
}

type ListApplicationReq struct {
	api.PaginationParams
	ID            uint     `json:"id" form:"id"`
	Barcode       string   `json:"barcode" form:"barcode"`
	Barcodes      []string `json:"barcodes" form:"barcodes"`
	Model         string   `json:"model" form:"model"`
	Status        string   `json:"status" form:"status"`
	AgencyID      uint     `json:"agency_id" form:"agency_id"`
	EndpointID    uint     `json:"endpoint_id" form:"endpoint_id"`
	StatusSlices  []string `json:"status_slices" form:"status_slices"`
	Applicant     uint     `json:"applicant" form:"applicant"`
	DataType      int      `json:"data_type" form:"data_type"` // 1: 审核列表, 2: 申请列表
	StartTime     string   `json:"start_time" form:"start_time"`
	EndTime       string   `json:"end_time" form:"end_time"`
	ApplicantType string   `json:"applicant_type" form:"applicant_type"` // endpoint: 终端, agency: 代理
}

type CheckMachineReq struct {
	ID          uint     `json:"id" form:"id"`
	Barcode     string   `json:"barcode" form:"barcode"`
	CheckResult string   `json:"check_result" form:"check_result"`
	CheckFacade string   `json:"check_facade" form:"check_facade"`
	CheckImages []string `json:"check_images" form:"check_images"`
	Status      string   `json:"status" form:"status"`
	HandlerID   uint     `json:"handler_id" form:"handler_id"`
	Comment     string   `json:"comment" form:"comment"`
}

type ListApplicationResp struct {
	model.RenewApplication
	TopAgencyName    string      `json:"top_agency_name"`
	SecondAgencyName string      `json:"second_agency_name"`
	EndpointName     string      `json:"endpoint_name"`
	StatusName       string      `json:"status_name"`
	NextStatusName   string      `json:"next_status_name"`
	ApplicantName    string      `json:"applicant_name"`
	IssuesSlices     []string    `json:"issues"`
	Audits           []AuditResp `json:"audits"`
}

type ApplicationInfoResp struct {
	model.RenewApplication
	ApplicantName    string      `json:"applicant_name"`
	TopAgencyName    string      `json:"top_agency_name"`
	SecondAgencyName string      `json:"second_agency_name"`
	EndpointName     string      `json:"endpoint_name"`
	StatusName       string      `json:"status_name"`
	NextStatusName   string      `json:"next_status_name"`
	IssuesSlices     []string    `json:"issues"`
	Audits           []AuditResp `json:"audits"`
}

type AuditResp struct {
	model.RenewApplicationStatus
	StatusName  string `json:"status_name"`
	HandlerName string `json:"handler_name"`
}

type WarrantyResp struct {
	Barcode        string           `json:"barcode"`
	Status         int8             `json:"status"` // -1-虚卡翻新，0-虚卡，1-正常，2-换机，3-退机，4-其他，5-翻新
	WarrantyID     int              `json:"warranty_id"`
	ModelID        int              `json:"model_id"`
	Model          string           `json:"model"`
	Endpoint       string           `json:"endpoint"`
	BuyDate        types.CustomTime `json:"buy_date"`
	ActivatedAtOld types.CustomTime `json:"activated_at_old"` //个人中心激活
	CustomerName   string           `json:"customer_name"`
	CustomerPhone  string           `json:"customer_phone"`
}
