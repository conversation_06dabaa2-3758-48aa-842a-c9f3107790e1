package model

import (
	"marketing/internal/consts"
	"time"

	"github.com/shopspring/decimal"
)

type Order struct {
	ID                      uint               `gorm:"primaryKey;autoIncrement;column:id"`
	Uid                     uint               `gorm:"column:uid;not null;default:0;comment:用户id"`
	Endpoint                uint               `gorm:"column:endpoint;not null;default:0;comment:终端id"`
	Sn                      string             `gorm:"column:sn;type:varchar(100);not null;default:'';uniqueIndex;comment:sn码"`
	Status                  consts.OrderStatus `gorm:"column:status;not null;default:0;comment:订单状态"`
	Barcode                 string             `gorm:"column:barcode;type:varchar(100);not null;default:'';comment:机器条码"`
	Serial                  string             `gorm:"column:serial;type:varchar(100);not null;default:'';comment:机器序列号"`
	Imei                    string             `gorm:"column:imei;type:varchar(100);not null;default:'';comment:机器imei"`
	ModelName               string             `gorm:"column:model_name;type:varchar(100);not null;default:'';comment:机型名称"`
	ModelID                 uint               `gorm:"column:model_id;not null;default:0;comment:机型id"`
	Color                   *string            `gorm:"column:color;type:varchar(50);comment:颜色"`
	InSiPeriod              int8               `gorm:"column:in_si_period;not null;default:0;comment:碎屏保保内保外，保内1，保外2，0没有保修信息"`
	HasScreenInsurance      int8               `gorm:"column:has_screen_insurance;not null;default:0;comment:有没有碎屏保，1有，0没有"`
	UsedScreenInsurance     int8               `gorm:"column:used_screen_insurance;not null;default:0;comment:是否使用碎屏保，1是，0否"`
	InPeriod                int8               `gorm:"column:in_period;not null;default:0;comment:保内保外，保内1，保外2，0没有保修信息"`
	HasWarranty             int8               `gorm:"column:has_warranty;not null;default:0;comment:有没有保卡，1有，0没有"`
	Reason                  int                `gorm:"column:reason;not null;default:0;comment:损坏原因，人为1，元件损坏2"`
	Damage                  string             `gorm:"column:damage;type:varchar(500);not null;default:'';comment:受损类型，多个类型用英文逗号分隔"`
	RepairImage             string             `gorm:"column:repair_image;type:varchar(2000);not null;default:'';comment:维修图片"`
	PeriodFile              string             `gorm:"column:period_file;type:varchar(2000);not null;default:'';comment:保内证明文件，json数组字符串"`
	UploadFile              string             `gorm:"column:upload_file;type:varchar(2000);not null;default:'';comment:损坏文件，json数组字符串"`
	VideoFile               string             `gorm:"column:video_file;type:varchar(2000);not null;default:'';comment:视频文件，json数组字符串"`
	Description             string             `gorm:"column:description;type:varchar(500);not null;default:'';comment:损坏描述，最多200字"`
	Name                    string             `gorm:"column:name;type:varchar(50);not null;default:'';comment:联系人，最多20字"`
	Phone                   string             `gorm:"column:phone;type:varchar(50);not null;default:'';comment:电话号码"`
	Province                string             `gorm:"column:province;type:varchar(50);not null;default:'';comment:省"`
	City                    string             `gorm:"column:city;type:varchar(50);not null;default:'';comment:市"`
	District                string             `gorm:"column:district;type:varchar(50);not null;default:'';comment:区"`
	Address                 string             `gorm:"column:address;type:varchar(500);not null;default:'';comment:详细地址，最多200字"`
	ComeExpType             int8               `gorm:"column:come_exp_type;not null;default:0;comment:寄来快递类型，1上门服务，2自主寄件"`
	RbComeExpSn             string             `gorm:"column:rb_come_exp_sn;type:varchar(100);not null;default:'';comment:公司内部寄来快递单号"`
	ComeExpSn               string             `gorm:"column:come_exp_sn;type:varchar(100);not null;default:'';comment:寄来快递单号"`
	ComeExpCom              string             `gorm:"column:come_exp_com;type:varchar(100);not null;default:'';comment:寄来快递公司"`
	AuditStatus             int8               `gorm:"column:audit_status;not null;default:0;comment:审核状态，1同意寄修，2不需要寄修"`
	AuditOpinion            string             `gorm:"column:audit_opinion;type:varchar(500);not null;default:'';comment:审核意见，最多200字"`
	ComeSure                int8               `gorm:"column:come_sure;not null;default:0;comment:寄来收货确认，1已收货"`
	RbPaySn                 string             `gorm:"column:rb_pay_sn;type:varchar(100);not null;default:'';comment:公司内部支付单号"`
	PayCom                  int8               `gorm:"column:pay_com;not null;default:0;comment:支付公司，1微信，2支付宝"`
	PaySn                   string             `gorm:"column:pay_sn;type:varchar(100);not null;default:'';comment:支付单号"`
	IsPaid                  int8               `gorm:"column:is_paid;not null;default:0;comment:是否已支付， 1为已支付"`
	PayTime                 *time.Time         `gorm:"column:pay_time;comment:支付时间"`
	CheckMan                int                `gorm:"column:check_man;not null;default:0;comment:检查人，维修工id"`
	RepairMan               int                `gorm:"column:repair_man;not null;default:0;comment:维修人，维修工id"`
	Deal                    string             `gorm:"column:deal;type:varchar(200);not null;default:'';comment:维修处理，最多100字"`
	OptionalAccessoryStatus int8               `gorm:"column:optional_accessory_status;not null;default:0;comment:自选配件状态 0正常 -1部分取消 -2全部取消"`
	OptionalAccessoryAmount decimal.Decimal    `gorm:"column:optional_accessory_amount;type:decimal(12,2);not null;default:0.00;comment:总自选配件费用"`
	OptionalAccessoryCast   decimal.Decimal    `gorm:"column:optional_accessory_cast;type:decimal(12,2);not null;default:0.00;comment:自选配件实际费用"`
	AccessoryAmount         decimal.Decimal    `gorm:"column:accessory_amount;type:decimal(12,2);not null;default:0.00;comment:总配件费用"`
	AccessoryCast           decimal.Decimal    `gorm:"column:accessory_cast;type:decimal(12,2);not null;default:0.00;comment:配件费用"`
	AccessoryInAr           decimal.Decimal    `gorm:"column:accessory_in_ar;type:decimal(12,2);not null;default:0.00;comment:弃修时应付配件费用"`
	StaffCast               decimal.Decimal    `gorm:"column:staff_cast;type:decimal(12,2);not null;default:0.00;comment:检测费用--快递费用"`
	AmountInAr              decimal.Decimal    `gorm:"column:amount_in_ar;type:decimal(12,2);not null;default:0.00;comment:弃修时应付费用"`
	OtherAmountInNr         decimal.Decimal    `gorm:"column:other_amount_in_nr;type:decimal(12,2);not null;default:0.00;comment:其它费用-正常维修时"`
	OtherAmountInAr         decimal.Decimal    `gorm:"column:other_amount_in_ar;type:decimal(12,2);not null;default:0.00;comment:其它费用-弃修时"`
	Amount                  decimal.Decimal    `gorm:"column:amount;type:decimal(12,2);not null;default:0.00;comment:总金额"`
	PayAmount               decimal.Decimal    `gorm:"column:pay_amount;type:decimal(12,2);not null;default:0.00;comment:待付金额"`
	HiddenAmount            decimal.Decimal    `gorm:"column:hidden_amount;type:decimal(12,2);not null;default:0.00;comment:隐藏配件费用"`
	DealRemark              string             `gorm:"column:deal_remark;type:varchar(200);not null;default:'';comment:维修备注，最多100字"`
	ReceiveCase             string             `gorm:"column:receive_case;type:varchar(200);not null;default:'';comment:收到的配件，最多100字"`
	Attachment              *string            `gorm:"column:attachment;type:varchar(200);comment:附带的配件，最多100字"`
	RepairCheckStatus       int8               `gorm:"column:repair_check_status;not null;default:0;comment:检测状态说明 1--配件不足、2--老化检测中、3--正在等待检测"`
	RepairStatus            int8               `gorm:"column:repair_status;not null;default:0;comment:维修状态，0未维修，1维修完，2弃修"`
	RbGoExpSn               string             `gorm:"column:rb_go_exp_sn;type:varchar(100);not null;default:'';comment:寄去公司内部单号"`
	GoExpSn                 string             `gorm:"column:go_exp_sn;type:varchar(100);not null;default:'';comment:寄去快递单号"`
	GoExpCom                string             `gorm:"column:go_exp_com;type:varchar(100);not null;default:'';comment:寄去快递公司"`
	GoExpType               int8               `gorm:"column:go_exp_type;not null;default:0;comment:寄去快递支付类型，1为月结，2为到付"`
	GoSure                  int8               `gorm:"column:go_sure;not null;default:0;comment:寄去快递确认发货，1为确认发货"`
	GoReceived              int8               `gorm:"column:go_received;not null;default:0;comment:寄去快递确认收货，1为确认收货"`
	GoConfirm               int8               `gorm:"column:go_confirm;not null;default:0;comment:回寄确认，1确认进入回寄列表，0未确定"`
	RepairEndpoint          uint               `gorm:"column:repair_endpoint;not null;default:0;comment:选择的寄修中心id"`
	RepeatOrder             int8               `gorm:"column:repeat_order;not null;default:0;comment:是否重复寄修，1重复 二次维修"`
	RepeatRemark            string             `gorm:"column:repeat_remark;type:varchar(200);not null;default:'';comment:二次维修备注，最多100字 返修现象分析备注"`
	ArRepeat                int8               `gorm:"column:ar_repeat;not null;default:0;comment:该机型之前弃修次数"`
	IsAgency                int8               `gorm:"column:is_agency;not null;default:0;comment:是否终端寄修，1是，0否"`
	Quality                 int8               `gorm:"column:quality;not null;default:0;comment:品检状态，1通过，2打回"`
	Connect                 int8               `gorm:"column:connect;not null;default:0;comment:联系状态，1联系成功，2联系失败，3用户弃修，4专柜和用户确认中，5用户考虑中，6已弃修，联系成功"`
	Freeze                  int8               `gorm:"column:freeze;not null;default:0;comment:冻结状态，1冻结"`
	NeedInvoice             *int8              `gorm:"column:need_invoice;default:0;comment:是否需要发票"`
	InvoiceType             *int8              `gorm:"column:invoice_type;default:0;comment:发票类型，1个人，2企业"`
	InvoiceTitle            *string            `gorm:"column:invoice_title;type:varchar(500);comment:发票抬头"`
	InvoiceTaxID            *string            `gorm:"column:invoice_tax_id;type:varchar(500);comment:发票税号"`
	InvoiceEmail            *string            `gorm:"column:invoice_email;type:varchar(500);comment:发票接收邮箱"`
	Appraise                int8               `gorm:"column:appraise;not null;default:0;comment:是否已评价"`
	Auditor                 int                `gorm:"column:auditor;not null;default:0;comment:审核人"`
	ExtendWarranty          int8               `gorm:"column:extend_warranty;not null;default:0;comment:是否允许延保，1是，0否"`
	RefundCom               int8               `gorm:"column:refund_com;not null;default:0;comment:退款公司，1微信，2支付宝"`
	IsRefund                int8               `gorm:"column:is_refund;not null;default:0;comment:是否已退款，0为未退， 1为已全部退款，2为部分退款"`
	RefundAmount            decimal.Decimal    `gorm:"column:refund_amount;type:decimal(12,2);not null;default:0.00;comment:已退款金额"`
	RbRefundSn              string             `gorm:"column:rb_refund_sn;type:varchar(100);not null;default:'';comment:最新一次内部退款单号"`
	Type                    *int8              `gorm:"column:type;default:1;comment:订单类型 1--正常寄修 2--代理商寄修 3--终端代寄"`
	Agency                  *string            `gorm:"column:agency;type:varchar(100);comment:代理地区"`
	CreatedAt               time.Time          `gorm:"column:created_at;not null"`
	UpdatedAt               time.Time          `gorm:"column:updated_at;not null"`
	PickupTime              *time.Time         `gorm:"column:pickup_time;comment:取件时间"`
	ReceiveTime             *time.Time         `gorm:"column:receive_time;comment:签收时间"`
	UpdatedAtLast           time.Time          `gorm:"column:updated_at_last;not null;comment:最后下单时间 -- 回寄产品"`
	PrMaterials             []PrMaterial       `gorm:"foreignKey:PrSn;references:Sn"`
}

func (o *Order) TableName() string {
	return "order"
}
