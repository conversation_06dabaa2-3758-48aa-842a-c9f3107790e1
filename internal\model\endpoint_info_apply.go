package model

import (
	"marketing/internal/pkg/types"
)

type EndpointInfoApply struct {
	ID           uint                                    `json:"id" gorm:"primaryKey;autoIncrement"`
	EndpointID   uint                                    `json:"endpoint_id" gorm:"not null"`
	ProvinceCode uint                                    `json:"province_code" gorm:"not null;default:0"`
	CityCode     uint                                    `json:"city_code" gorm:"not null;default:0"`
	DistrictCode uint                                    `json:"district_code" gorm:"not null;default:0"`
	Address      string                                  `json:"address" gorm:"type:varchar(100);not null;default:''"`
	Latitude     float64                                 `json:"latitude" gorm:"type:decimal(9,6);not null;default:0.000000"`
	Longitude    float64                                 `json:"longitude" gorm:"type:decimal(9,6);not null;default:0.000000"`
	Manager      string                                  `json:"manager" gorm:"type:varchar(10);not null;default:''"`
	Phone        string                                  `json:"phone" gorm:"type:varchar(50);not null;default:''"`
	EndpointName string                                  `json:"endpoint_name" gorm:"type:varchar(100);not null;default:''"`
	Synchronized uint8                                   `json:"synchronized" gorm:"type:tinyint(3);not null;default:0"`
	Status       string                                  `json:"status" gorm:"type:enum('to_audit','approved','rejected');not null"`
	AuditOpinion string                                  `json:"audit_opinion" gorm:"type:varchar(100);not null;default:''"`
	AuditUserID  uint                                    `json:"audit_user_id" gorm:"not null"`
	AuditTime    types.CustomTime                        `json:"audit_time" gorm:"type:datetime;not null;default:0000-00-00 00:00:00"`
	CreatedAt    types.CustomTime                        `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt    types.CustomTime                        `json:"updated_at" gorm:"autoUpdateTime"`
	Area         uint16                                  `json:"area" gorm:"type:smallint(5);not null"`
	ManualReview uint8                                   `json:"manual_review" gorm:"type:tinyint(3);not null;default:0"`
	Snapshot     types.JSONField[map[string]interface{}] `json:"snapshot" gorm:"type:text;not null"`
	ChannelLevel uint8                                   `json:"channel_level" gorm:"type:tinyint(3);not null;default:0"`
}

func (EndpointInfoApply) TableName() string {
	return "endpoint_info_apply"
}
