package prototype

import (
	"marketing/internal/api/prototype"
	"marketing/internal/model"

	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// UpCounterStats 上柜机型统计数据
type UpCounterStats struct {
	PassedAudit int64 `json:"passed_audit"` // 审核通过数量
	FailedAudit int64 `json:"failed_audit"` // 审核不通过数量
	NotAudit    int64 `json:"not_audit"`    // 未审核数量
	NotUpload   int64 `json:"not_upload"`   // 未上传数量
}

// UpCounter 上柜机型DAO
type UpCounter interface {
	GetList(c *gin.Context, req *prototype.UpCounterSearch) ([]*prototype.UpCounterListVo, int64, error)
	GetCounterStats(c *gin.Context, modelNames []string) (map[string]*UpCounterStats, error)
	UpdateStatus(c *gin.Context, id int, isUpCounter int) error
	GetMachineTypeRelationByName(c *gin.Context, name string) (*model.MachineTypeRelation, error)
}

type upCounter struct {
	db *gorm.DB
}

// NewUpCounterDao 创建DAO实例
func NewUpCounterDao(db *gorm.DB) UpCounter {
	return &upCounter{
		db: db,
	}
}

func (s *upCounter) GetList(c *gin.Context, req *prototype.UpCounterSearch) ([]*prototype.UpCounterListVo, int64, error) {
	var list []*prototype.UpCounterListVo
	var count int64
	query := s.db.WithContext(c).Model(&model.MachineType{})
	query = query.Joins("left join  machine_type_relation as r on machine_type.name = r.name")
	// 过滤条件
	if req.Model != "" {
		query = query.Where("machine_type.name LIKE ?", "%"+req.Model+"%")
	}
	if req.IsUpCounter != nil {
		if *req.IsUpCounter == 0 {
			query = query.Where("r.is_up_counter = 0 or r.is_up_counter is null")
		} else {
			query = query.Where("r.is_up_counter = 1")
		}
	}
	if req.MarketDateStart != "" {
		query = query.Where("r.market_date >= ?", req.MarketDateStart)
	}
	if req.MarketDateEnd != "" {
		query = query.Where("r.market_date <= ?", req.MarketDateEnd)
	}
	query = query.Select([]string{
		"machine_type.model_id",
		"machine_type.name",
		"machine_type.name as model_name",
		"machine_type.category_id",
		"machine_type.customer_price as price",
		"r.*",
	})

	query = query.Where("machine_type.created_at >= ?", "2023-01-01 00:00:00")
	query = query.Group("machine_type.name")

	err := query.Count(&count).Error
	if err != nil {
		return nil, 0, err
	}
	err = query.Offset((req.Page - 1) * req.PageSize).Limit(req.PageSize).Order("machine_type.model_id desc").Find(&list).Error
	if err != nil {
		return nil, 0, err
	}
	return list, count, nil
}

// GetCounterStats 获取上柜机型统计数据
func (s *upCounter) GetCounterStats(c *gin.Context, modelNames []string) (map[string]*UpCounterStats, error) {
	var stats []struct {
		Model  string `gorm:"column:model"`
		Status int    `gorm:"column:status"`
		Count  int64  `gorm:"column:count"`
	}

	// 获取所有终端的数量
	var totalEndpoints int64
	err := s.db.WithContext(c).Model(&model.Endpoint{}).Where("status = ?", 1).Count(&totalEndpoints).Error
	if err != nil {
		return nil, err
	}

	// 获取每个机型的统计数据
	err = s.db.WithContext(c).Table("prototype_up_counter as c").
		Joins("join endpoint as e on c.endpoint_id = e.id").
		Where("e.status = ?", 1).
		Where("c.model in (?)", modelNames).
		Select("c.model, c.status, count(*) as count").
		Group("c.model, c.status").
		Scan(&stats).Error
	if err != nil {
		return nil, err
	}

	// 获取每个机型已上传的终端数量
	var uploadedEndpoints []struct {
		Model string `gorm:"column:model"`
		Count int64  `gorm:"column:count"`
	}
	err = s.db.WithContext(c).Table("prototype_up_counter as c").
		Joins("join endpoint as e on c.endpoint_id = e.id").
		Where("e.status = ?", 1).
		Where("c.model in (?)", modelNames).
		Select("c.model, count(DISTINCT c.endpoint_id) as count").
		Group("c.model").
		Scan(&uploadedEndpoints).Error
	if err != nil {
		return nil, err
	}

	// 组装数据
	result := make(map[string]*UpCounterStats)
	for _, name := range modelNames {
		result[name] = &UpCounterStats{
			PassedAudit: 0,
			FailedAudit: 0,
			NotAudit:    0,
			NotUpload:   totalEndpoints,
		}
	}

	// 填充统计数据
	for _, stat := range stats {
		if _, ok := result[stat.Model]; !ok {
			continue
		}
		switch stat.Status {
		case 2: // 审核通过
			result[stat.Model].PassedAudit = stat.Count
		case -2: // 审核不通过
			result[stat.Model].FailedAudit = stat.Count
		case 1: // 未审核
			result[stat.Model].NotAudit = stat.Count
		}
	}

	// 填充未上传数量
	for _, uploaded := range uploadedEndpoints {
		if _, ok := result[uploaded.Model]; !ok {
			continue
		}
		result[uploaded.Model].NotUpload = totalEndpoints - uploaded.Count
	}

	return result, nil
}

// GetMachineTypeRelationByName 通过名称获取机型关系信息
func (s *upCounter) GetMachineTypeRelationByName(c *gin.Context, name string) (*model.MachineTypeRelation, error) {
	var relation model.MachineTypeRelation
	if err := s.db.WithContext(c).Where("name = ?", name).First(&relation).Error; err != nil {
		return nil, err
	}
	return &relation, nil
}

// UpdateStatus 更新样机上柜状态
func (s *upCounter) UpdateStatus(c *gin.Context, id int, isUpCounter int) error {
	// 更新数据
	nowTime := time.Now()
	data := map[string]interface{}{
		"is_up_counter": isUpCounter,
		"updated_at":    nowTime,
	}

	if isUpCounter == 1 {
		data["up_counter_time"] = nowTime
	} else {
		data["down_counter_time"] = nowTime
	}

	// 更新数据库
	if err := s.db.WithContext(c).Model(&model.MachineTypeRelation{}).
		Where("id = ?", id).
		Updates(data).Error; err != nil {
		return err
	}

	return nil
}
