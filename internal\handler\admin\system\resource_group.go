package system

import (
	"github.com/spf13/cast"
	api "marketing/internal/api/system"
	"marketing/internal/consts"
	"marketing/internal/handler"
	appError "marketing/internal/pkg/errors"
	"marketing/internal/service/system"
	"strconv"

	"github.com/gin-gonic/gin"
)

type ResourceGroupHandler interface {
	Create(c *gin.Context)
	Update(c *gin.Context)
	Delete(c *gin.Context)
	List(c *gin.Context)
	GetResourceGroupsDropdown(c *gin.Context)
	Sync(c *gin.Context)
}

type resourceGroupHandler struct {
	svc system.ResourceGroupService
}

func NewResourceGroupHandler(svc system.ResourceGroupService) ResourceGroupHandler {
	return &resourceGroupHandler{svc: svc}
}

func (h *resourceGroupHandler) Create(c *gin.Context) {
	var group api.ResourceGroupReq
	if err := c.ShouldBindJSON(&group); err != nil {
		handler.Error(c, err)
		return
	}

	if err := h.svc.Create(c, &group); err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, group)
}

func (h *resourceGroupHandler) Update(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		handler.Error(c, appError.NewErr("id不能为空"))
		return
	}

	var group api.ResourceGroupReq
	if err := c.ShouldBindJSON(&group); err != nil {
		handler.Error(c, err)
		return
	}

	group.ID = uint(id)

	if err := h.svc.Update(c, &group); err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, group)
}

func (h *resourceGroupHandler) Delete(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		handler.Error(c, appError.NewErr("id不能为空"))
		return
	}

	if err := h.svc.Delete(c, uint(id)); err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c)
}

func (h *resourceGroupHandler) List(c *gin.Context) {
	var params api.ListResourceGroupReq

	params.Name = c.Query("name")
	page, _ := strconv.Atoi(c.DefaultQuery("page", strconv.Itoa(consts.DefaultPage)))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", strconv.Itoa(consts.DefaultPageSize)))
	params.Page = page
	params.PageSize = pageSize

	groups, total, err := h.svc.List(c, params)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, gin.H{"list": groups, "total": total, "page": page, "page_size": pageSize})
}

func (h *resourceGroupHandler) GetResourceGroupsDropdown(c *gin.Context) {
	name := c.Query("name")

	groups, err := h.svc.ListResourceGroupsNoPage(c, name)
	if err != nil {
		handler.Error(c, err)
		return
	}

	var data []map[string]interface{}
	for _, group := range groups {
		data = append(data, map[string]interface{}{
			"id":   group.ID,
			"name": group.Name,
		})
	}

	handler.Success(c, data)
}

func (h *resourceGroupHandler) Sync(c *gin.Context) {
	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		handler.Error(c, appError.NewErr("id不能为空"))
		return
	}
	err := h.svc.Sync(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c)
}
