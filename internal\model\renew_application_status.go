package model

import (
	"marketing/internal/pkg/types"
)

type RenewApplicationStatus struct {
	ID            uint             `json:"-" gorm:"primaryKey;autoIncrement"`
	ApplicationID uint             `json:"-" gorm:"not null;comment:'申请ID'"`
	Status        string           `json:"-" gorm:"type:enum('Pending','FirstLevelReview','HeadOfficeReview','Repair','Completed','Rejected');not null;comment:'修改后的状态'"`
	HandlerID     uint             `json:"-" gorm:"not null;comment:'处理人的User ID'"`
	Comment       string           `json:"comment" gorm:"type:text;comment:'备注'"` // 如果允许 NULL，Go 的字符串类型即可
	CreatedAt     types.CustomTime `json:"created_at" gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP"`
}

func (RenewApplicationStatus) TableName() string {
	return "renew_application_status"
}
