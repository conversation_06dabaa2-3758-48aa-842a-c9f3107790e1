package system

import (
	"marketing/internal/api"
	"marketing/internal/api/system"
	"marketing/internal/handler"
	"marketing/internal/pkg/errors"
	system2 "marketing/internal/service/system"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

type AdminUserInterface interface {
	Lists(c *gin.Context)
	Add(c *gin.Context)
	Update(c *gin.Context)
	UpdateStatus(c *gin.Context)
	Get(c *gin.Context)
	ResetPassword(c *gin.Context)
	GetRoleDropdown(c *gin.Context)
	GetGroupDropdown(c *gin.Context)
	GetWecomTagDropdown(c *gin.Context)
	GetUserLogs(c *gin.Context)
	UpdateWecomTag(c *gin.Context)
	UserAddToGroup(c *gin.Context)
	SyncUser(c *gin.Context)
	Delete(c *gin.Context)
}

type adminUser struct {
	adminUserSvc      system2.AdminUserInterface
	adminRoleSvc      system2.AdminRoleInterface
	adminUserGroupSvc system2.AdminUserGroupInterface
	wecomTagSvc       system2.WecomTagInterface
}

// NewAdminUser 创建 adminUser 实例
func NewAdminUser(
	adminUserSvc system2.AdminUserInterface,
	adminRoleSvc system2.AdminRoleInterface,
	groupSvc system2.AdminUserGroupInterface,
	tagSvc system2.WecomTagInterface) AdminUserInterface {
	return &adminUser{
		adminUserSvc:      adminUserSvc,
		adminRoleSvc:      adminRoleSvc,
		adminUserGroupSvc: groupSvc,
		wecomTagSvc:       tagSvc,
	}
}

// Lists 获取用户列表
func (a *adminUser) Lists(c *gin.Context) {
	var req system.AdminUserReq
	// 你可以使用显式绑定声明绑定 multipart form：绑定不了json格式
	// c.ShouldBindWith(&form, binding.Form)
	// 或者简单地使用 ShouldBind 方法自动绑定
	// 如果是 `GET` 请求，只使用 `Form` 绑定引擎（`query`）。
	// 如果是 `POST` 请求，首先检查 `content-type` 是否为 `JSON` 或 `XML`，然后再使用 `Form`（`form-data`）。
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	//分页处理
	req.PaginationParams.SetDefaults()
	data, err := a.adminUserSvc.Lists(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, data)
}

// Add 新增用户
func (a *adminUser) Add(c *gin.Context) {
	var req system.AddUserReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	_, err := a.adminUserSvc.Add(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

// Update 更新用户信息
func (a *adminUser) Update(c *gin.Context) {
	var req system.AddUserReq
	if err := c.ShouldBind(&req); err != nil {
		err = errors.NewErr("参数错误" + err.Error())
		handler.Error(c, err)
		return
	}
	req.ID = cast.ToUint(c.Param("id"))
	if req.ID == 0 {
		handler.Error(c, errors.NewErr("id不能为空"))
		return
	}
	err := a.adminUserSvc.Update(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

// UpdateStatus 更新用户状态
func (a *adminUser) UpdateStatus(c *gin.Context) {
	var req system.UpdateStatusReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	req.ID = cast.ToUint(c.Param("id"))
	if req.ID == 0 {
		handler.Error(c, errors.NewErr("id不能为空"))
		return
	}

	err := a.adminUserSvc.UpdateStatus(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

// Get 获取用户列表
func (a *adminUser) Get(c *gin.Context) {
	id := cast.ToUint(c.Param("id"))

	data, err := a.adminUserSvc.Get(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, data)
}

// ResetPassword 重置密码
func (a *adminUser) ResetPassword(c *gin.Context) {
	var req system.ResetPasswordReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	req.ID = cast.ToUint(c.Param("id"))
	if req.ID == 0 {
		handler.Error(c, errors.NewErr("id不能为空"))
		return
	}
	err := a.adminUserSvc.ResetPassword(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

// GetRoleDropdown 获取用户角色列表
func (a *adminUser) GetRoleDropdown(c *gin.Context) {
	roleName := c.Query("roleName")
	roles, err := a.adminRoleSvc.ListRolesNoPage(c, roleName, "")
	if err != nil {
		handler.Error(c, err)
		return
	}
	var data []map[string]interface{}
	for _, role := range roles {
		data = append(data, map[string]interface{}{
			"id":   role.ID,
			"slug": role.Slug,
			"name": role.Name,
		})
	}

	handler.Success(c, data)
}

// GetGroupDropdown 获取用户角色列表
func (a *adminUser) GetGroupDropdown(c *gin.Context) {
	groupName := c.Query("groupName")
	groups, err := a.adminUserGroupSvc.ListGroupsNoPage(c, groupName)
	if err != nil {
		handler.Error(c, err)
		return
	}
	var data []map[string]interface{}
	for _, group := range groups {
		data = append(data, map[string]interface{}{
			"id":   group.ID,
			"slug": group.Slug,
			"name": group.Name,
		})
	}

	handler.Success(c, data)
}

// GetWecomTagDropdown 获取 WeCom 标签列表
func (a *adminUser) GetWecomTagDropdown(c *gin.Context) {
	tagName := c.Query("tagName")
	tags, err := a.wecomTagSvc.ListNoPage(c, tagName)
	if err != nil {
		handler.Error(c, err)
		return
	}
	var data []map[string]interface{}
	for _, tag := range tags {
		data = append(data, map[string]interface{}{
			"id":   tag.ID,
			"name": tag.TagName,
		})
	}

	handler.Success(c, data)
}

// GetUserLogs 获取用户操作日志
func (a *adminUser) GetUserLogs(c *gin.Context) {
	var req system.GetUserLogsReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	req.PaginationParams.SetDefaults()
	UID := cast.ToUint(c.Param("id"))
	data, total, err := a.adminUserSvc.GetUserLogs(c, UID, req.Page, req.PageSize)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, &api.PaginationListResp{Data: data, Total: total, Page: req.Page, PageSize: req.PageSize})
}

// UpdateWecomTag 更新企微标签
func (a *adminUser) UpdateWecomTag(c *gin.Context) {
	var req system.UpdateUserWecomTagReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	UID := cast.ToUint(c.Param("id"))
	err := a.adminUserSvc.UpdateTags(c, UID, req.TagIDs)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c)
}

// UserAddToGroup adds a user to a user group
func (a *adminUser) UserAddToGroup(c *gin.Context) {
	var req system.AddUsersToGroupReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}

	if err := a.adminUserGroupSvc.AddUsersToGroup(c, req); err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

// SyncUser 同步用户到微信
func (a *adminUser) SyncUser(c *gin.Context) {
	type Request struct {
		IDs    []int  `json:"ids"`
		TagIDs []uint `json:"tags"`
	}
	var req Request
	// 解析 JSON 数据
	if err := c.BindJSON(&req); err != nil {
		handler.Error(c, errors.NewErr("请求数据格式错误，不是有效的 JSON 格式"))
		return
	}
	ids := req.IDs
	if len(ids) == 0 {
		handler.Error(c, errors.NewErr("ids不能为空"))
		return
	}
	err := a.adminUserSvc.SyncUser(c, ids, req.TagIDs)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

// Delete 删除用户
func (a *adminUser) Delete(c *gin.Context) {
	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		handler.Error(c, errors.NewErr("id不能为空"))
		return
	}
	err := a.adminUserSvc.Delete(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}
