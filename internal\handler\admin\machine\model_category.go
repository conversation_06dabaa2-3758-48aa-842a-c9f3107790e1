package machine

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/handler"
	"marketing/internal/pkg/e"
	"marketing/internal/pkg/errors"
	"marketing/internal/service/machine"
)

type ModelCategory struct {
	svc machine.ModelCategorySvcInterface
}

func NewModelCategory(svc machine.ModelCategorySvcInterface) *ModelCategory {
	return &ModelCategory{
		svc: svc,
	}
}

func (m *ModelCategory) ModelCategoryList(c *gin.Context) {
	list := m.svc.GetAllModelCategory(c)
	handler.Success(c, gin.H{
		"list": list,
	})
}

func (m *ModelCategory) GetAllRepairModelCategory(c *gin.Context) {
	list := m.svc.GetAllRepairModelCategory(c)
	handler.Success(c, gin.H{
		"list": list,
	})
}

func (m *ModelCategory) RefreshModelCategory(c *gin.Context) {
	list := m.svc.RefreshModelCategory(c)
	handler.Success(c, gin.H{
		"list": list,
	})
}

func (m *ModelCategory) EditModelCategory(c *gin.Context) {
	id := e.ReqParamInt(c, "id", -1)
	pid := e.ReqParamInt(c, "pid")
	name := e.ReqParamStr(c, "name")

	if id != -1 {
		err := m.svc.EditModelCategory(c, id, pid, name)
		if err != nil {
			handler.Error(c, errors.NewErr(err.Error()))
			return
		}
	} else {
		err := m.svc.AddModelCategory(c, pid, name)
		if err != nil {
			handler.Error(c, errors.NewErr(err.Error()))
			return
		}
	}

	handler.Success(c, gin.H{})
}

func (m *ModelCategory) DeleteModelCategory(c *gin.Context) {
	id := e.ReqParamInt(c, "id")
	err := m.svc.DelModelCategory(c, id)
	if err != nil {
		handler.Error(c, errors.NewErr("删除物料标签失败"))
		return
	}

	handler.Success(c, gin.H{})
}
