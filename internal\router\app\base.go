package app

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/config"
	"marketing/internal/handler/base"
	"marketing/internal/pkg/oss"
	"marketing/internal/pkg/upload"
	"marketing/internal/service"
	baseSvc "marketing/internal/service/base"
	"marketing/internal/service/system"
)

type BaseRouter struct {
	cfg          *config.Config
	appSystemSvc system.AppSystemSvc
}

func NewBaseRouter(cfg *config.Config, appSystemSvc system.AppSystemSvc) *BaseRouter {
	return &BaseRouter{
		cfg:          cfg,
		appSystemSvc: appSystemSvc,
	}
}

func (b *BaseRouter) Register(r *gin.RouterGroup) {
	//公共模块
	baseRouter := r.Group("/base")
	{
		client := oss.NewClient()
		uploadService := service.NewOssUploadService(upload.NewOssUploadServiceV2(client))
		uploadController := base.NewHandlerUploadFileV2(uploadService)
		baseRouter.POST("/upload_file/:path", uploadController.UploadFile)
		baseRouter.DELETE("/delete_file", uploadController.DeleteFile)

		//oss 临时凭证
		ossService := baseSvc.NewOssService(b.cfg)
		ossHandler := base.NewOssHandler(ossService)
		baseRouter.GET("/oss/sts", ossHandler.GetStsToken)

		// 企业微信相关接口
		wecomService := baseSvc.NewWeComService(b.appSystemSvc)
		wecomHandler := base.NewWeComHandler(wecomService)
		baseRouter.GET("/wecom/jsapi_ticket", wecomHandler.GetJsapiTicket)
	}
}
