package endpoint

import (
	"errors"
	api "marketing/internal/api/endpoint"
	"marketing/internal/dao"
	userDao "marketing/internal/dao/admin_user"
	endpoint2 "marketing/internal/dao/endpoint"
	"marketing/internal/model"
	appError "marketing/internal/pkg/errors"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/qichengzx/coordtransform"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

// Endpoint 处理与 Endpoint 相关的业务逻辑
type Endpoint interface {
	// GetAllEndpoints 获取所有 Endpoint 数据
	GetAllEndpoints(c *gin.Context, param api.GetEndpointReq) ([]*api.ListEndpointRes, int64, error)
	// GetEndpointByID 根据 ID 获取指定的 Endpoint 数据
	GetEndpointByID(c *gin.Context, id uint) (*model.Endpoint, error)
	// CreateEndpoint 创建新的 Endpoint 数据
	CreateEndpoint(c *gin.Context, param *api.AddEndpointReq) (*model.Endpoint, error)
	// UpdateEndpoint 更新 Endpoint 数据
	UpdateEndpoint(c *gin.Context, param *api.AddEndpointReq) (*model.Endpoint, error)
	// UpdateStatus 更新状态
	UpdateStatus(c *gin.Context, req api.UpdateStatusReq) error
	// GetTopAgencies 获取所有代理信息
	GetTopAgencies(c *gin.Context) ([]map[string]any, error)
	// GetSecondAgencies 获取所有二代信息
	GetSecondAgencies(c *gin.Context, topAgency uint) ([]map[string]any, error)
	// GetEndpointType 获取终端设置
	GetEndpointType(c *gin.Context) ([]map[string]any, error)
	//GetTree 获取终端树
	GetTree(c *gin.Context, endpoint bool) ([]*endpoint2.DepartmentNode, error)
	// GetAgencyUserEndpoint 获取代理用户所有终端信息
	GetAgencyUserEndpoint(c *gin.Context, secondAgency uint) ([]map[string]any, error)
	// GetUserSecondAgency 获取代理用户所有二代信息
	GetUserSecondAgency(c *gin.Context) ([]map[string]any, error)
	//GetEndpointByAgencyID 获取代理终端
	GetEndpointByAgencyID(c *gin.Context, agencyId uint) ([]map[string]any, error)
}

type endpoint struct {
	db          *gorm.DB
	endpointDao endpoint2.EndpointDao
	userDao     userDao.UserDao
}

// NewEndpoint 创建 Endpoint 实例
func NewEndpoint(db *gorm.DB, endpointDao endpoint2.EndpointDao, userDao userDao.UserDao) Endpoint {
	return &endpoint{
		db:          db,
		endpointDao: endpointDao,
		userDao:     userDao,
	}
}

// GetAllEndpoints 获取所有 Endpoint 数据
func (s *endpoint) GetAllEndpoints(c *gin.Context, param api.GetEndpointReq) ([]*api.ListEndpointRes, int64, error) {
	// 调用 DAO 层，获取所有 Endpoint 数据
	endpoints, total, err := s.endpointDao.GetAllEndpoints(c, param)
	if err != nil {
		return nil, 0, err
	}
	var endpointIds []uint
	// 使用 map 来去重
	agencyIdsMap := make(map[int]struct{})
	for _, v := range endpoints {
		endpointIds = append(endpointIds, cast.ToUint(v.ID))
		agencyIdsMap[v.TopAgency] = struct{}{} // 添加 TopAgency
		if v.SecondAgency > 0 {
			agencyIdsMap[v.SecondAgency] = struct{}{} // 添加 SecondAgency
		}
	}

	// 将 map 的键转换为切片
	var agencyIds []int
	for id := range agencyIdsMap {
		agencyIds = append(agencyIds, id)
	}
	// 获取所有代理信息
	agencies, err := s.endpointDao.GetAgencies(c, agencyIds)
	// 获取最新的形象更新信息
	images, err := s.endpointDao.GetImageUpdatedAtMap(c, endpointIds)

	infos, err := s.endpointDao.GetInfoUpdatedMap(c, endpointIds)
	// 将 Endpoint 数据转换为 API 响应数据
	var data []*api.ListEndpointRes
	for _, v := range endpoints {
		agencyName := agencies[uint(v.TopAgency)] + "[一级]"
		if v.SecondAgency > 0 {
			agencyName = agencies[uint(v.SecondAgency)] + "[二级]"
		}
		//更新时间处理
		var imageUpdatedTime, infoUpdatedTime, dataUpdatedTime, createdTime, activeTime string
		if image, ok := images[cast.ToUint(v.ID)]; ok {
			imageUpdatedTime = image.Format(time.DateTime)
		}

		if info, ok := infos[cast.ToUint(v.ID)]; ok {
			infoUpdatedTime = info.Format(time.DateTime)
		}

		if v.UpdatedAt != nil {
			dataUpdatedTime = v.UpdatedAt.Format(time.DateTime)
		}
		if v.CreatedAt.IsZero() {
			createdTime = v.CreatedAt.Format(time.DateTime)
		}
		if v.ActiveAt != nil && v.ActiveAt.IsZero() {
			activeTime = v.ActiveAt.Format(time.DateTime)
		}
		data = append(data, &api.ListEndpointRes{
			Endpoint:     *v,
			AgencyName:   agencyName,
			CreatedAtStr: createdTime,
			ActiveAtStr:  activeTime,
			UpdatedTime: api.UpdateTimeData{
				Image: imageUpdatedTime,
				Info:  infoUpdatedTime,
				Data:  dataUpdatedTime,
			},
		})
	}

	return data, total, err
}

// GetEndpointByID 根据 ID 获取指定的 Endpoint 数据
func (s *endpoint) GetEndpointByID(c *gin.Context, id uint) (*model.Endpoint, error) {
	return s.endpointDao.GetEndpointByID(c, id)
}

// CreateEndpoint 创建新的 Endpoint 数据
func (s *endpoint) CreateEndpoint(c *gin.Context, param *api.AddEndpointReq) (*model.Endpoint, error) {
	// 将字符串转换为 float64，以便传递给 BD09toGCJ02 函数
	blng, err := strconv.ParseFloat(param.Blng, 64)
	if err != nil {
		return nil, appError.NewErr("Error parsing longitude")
	}

	blat, err := strconv.ParseFloat(param.Blat, 64)
	if err != nil {
		return nil, appError.NewErr("Error parsing latitude")
	}
	lng, lat := coordtransform.BD09toGCJ02(blng, blat)

	address := s.BuildFullAddress(param.Province, param.City, param.District, param.Address)
	e := model.Endpoint{
		// 必填字段
		Name:      param.Name,
		Type:      param.Type,
		Province:  param.Province,
		City:      param.City,
		Address:   address,
		TopAgency: param.TopAgency,
		Phone:     &param.Phone,
		Manager:   &param.Manager,
		Blng:      &param.Blng,
		Blat:      &param.Blat,

		// 可选字段 - 仅当请求中包含值时才设置
		District:     param.District,
		SecondAgency: param.SecondAgency,
		Images:       &param.Images,
		License:      &param.License,

		// 可选字段 - 有默认值，仅当请求中明确指定时才覆盖
		ChannelLevel: param.ChannelLevel, // 默认 0
		Status:       param.Status,       // 默认 1
		OpenStatus:   param.OpenStatus,   // 默认 0
		IsPreSale:    param.IsPreSale,    // 默认 1
		IsAfterSale:  param.IsAfterSale,  // 默认 0

		// 坐标字段 - 有默认值 "0.000000"
		Lng: strconv.FormatFloat(lng, 'f', -1, 64),
		Lat: strconv.FormatFloat(lat, 'f', -1, 64),
	}

	return s.endpointDao.CreateEndpoint(c, &e)
}

// UpdateEndpoint 更新 Endpoint 数据
func (s *endpoint) UpdateEndpoint(c *gin.Context, param *api.AddEndpointReq) (*model.Endpoint, error) {
	//判断终端是否存在
	existEndpoint, err := s.GetEndpointByID(c, param.ID)
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, appError.NewErr("Endpoint does not exist")
	}
	if err != nil {
		return nil, err
	}
	// 将字符串转换为 float64，以便传递给 BD09toGCJ02 函数
	blng, err := strconv.ParseFloat(param.Blng, 64)
	if err != nil {
		return nil, appError.NewErr("Error parsing longitude")
	}

	blat, err := strconv.ParseFloat(param.Blat, 64)
	if err != nil {
		return nil, appError.NewErr("Error parsing latitude")
	}
	lng, lat := coordtransform.BD09toGCJ02(blng, blat)
	address := s.BuildFullAddress(param.Province, param.City, param.District, param.Address)
	existEndpoint.Name = param.Name
	existEndpoint.Province = param.Province
	existEndpoint.City = param.City
	existEndpoint.Address = address
	existEndpoint.TopAgency = param.TopAgency
	existEndpoint.Phone = &param.Phone
	existEndpoint.Manager = &param.Manager
	existEndpoint.Blng = &param.Blng
	existEndpoint.Blat = &param.Blat
	existEndpoint.District = param.District
	existEndpoint.SecondAgency = param.SecondAgency
	existEndpoint.Images = &param.Images
	existEndpoint.License = &param.License
	existEndpoint.ChannelLevel = param.ChannelLevel
	existEndpoint.Status = param.Status
	existEndpoint.IsPreSale = param.IsPreSale
	existEndpoint.IsAfterSale = param.IsAfterSale
	existEndpoint.Lng = strconv.FormatFloat(lng, 'f', -1, 64)
	existEndpoint.Lat = strconv.FormatFloat(lat, 'f', -1, 64)
	//营业状态
	if param.Status == 1 {
		existEndpoint.OpenStatus = 0
	} else if param.Status == 0 {
		existEndpoint.OpenStatus = 3
	}

	return s.endpointDao.UpdateEndpoint(c, existEndpoint)
}

// UpdateStatus 更新状态
func (s *endpoint) UpdateStatus(c *gin.Context, req api.UpdateStatusReq) error {
	// 判断用户是否存在
	existEndpoint, err := s.GetEndpointByID(c, req.ID)
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return appError.NewErr("Endpoint does not exist")
	}
	if err != nil {
		return err
	}

	// 更新状态
	existEndpoint.Status = req.Status
	//营业状态
	if req.Status == 1 {
		existEndpoint.OpenStatus = 0
	} else if req.Status == 0 {
		existEndpoint.OpenStatus = 3
	}
	_, err = s.endpointDao.UpdateEndpoint(c, existEndpoint)
	return err
}

// GetTopAgencies 获取所有代理信息
func (s *endpoint) GetTopAgencies(c *gin.Context) ([]map[string]any, error) {
	type Agency struct {
		ID   uint   `json:"id"`
		Name string `json:"name"`
	}

	var agencies []Agency
	err := s.db.WithContext(c).Table("agency").Select("id, name").
		Where("pid = ?", 0).Where("deleted_at is null").Find(&agencies).Error
	if err != nil {
		return nil, err
	}

	// Convert the slice of structs to a slice of maps
	var result []map[string]any
	for _, agency := range agencies {
		result = append(result, map[string]any{
			"id":   agency.ID,
			"name": agency.Name,
		})
	}

	return result, nil
}

// GetSecondAgencies 获取所有二代信息
func (s *endpoint) GetSecondAgencies(c *gin.Context, agencyID uint) ([]map[string]any, error) {
	type Agency struct {
		ID   uint   `json:"id"`
		Name string `json:"name"`
	}

	var agencies []Agency
	err := s.db.WithContext(c).Table("agency").Select("id, name").
		Where("level = 2").
		Where("pid = ? or id = ?", agencyID, agencyID).Where("deleted_at is null").Find(&agencies).Error
	if err != nil {
		return nil, err
	}

	// Convert the slice of structs to a slice of maps
	var result []map[string]any
	for _, agency := range agencies {
		result = append(result, map[string]any{
			"id":   agency.ID,
			"name": agency.Name,
		})
	}

	return result, nil
}

func (s *endpoint) GetEndpointType(c *gin.Context) ([]map[string]any, error) {
	// 获取终端类型
	var types []model.EndpointType
	err := s.db.WithContext(c).Find(&types).Error
	if err != nil {
		return nil, err
	}
	var result []map[string]any
	for _, t := range types {
		result = append(result, map[string]any{
			"id":   t.ID,
			"name": t.Name,
		})
	}
	return result, nil
}

func (s *endpoint) GetAllEndpointWithParent(c *gin.Context) ([]map[string]any, error) {
	// 获取所有终端
	return s.endpointDao.GetAllEndpoint(c)
}

func (s *endpoint) GetTree(c *gin.Context, endpoint bool) ([]*endpoint2.DepartmentNode, error) {
	return s.endpointDao.GetTree(c, endpoint)
}

func (s *endpoint) GetAgencyUserEndpoint(c *gin.Context, secondAgency uint) ([]map[string]any, error) {
	uid := c.GetUint("uid")
	agency, err := s.userDao.UserAgency(c, uid)
	if err != nil {
		return nil, err
	}
	if agency == nil {
		return nil, appError.NewErr("用户未绑定代理商")
	}
	agencyId := agency.ID
	if agency.Level == 1 && secondAgency > 0 {
		agencyId = secondAgency
	}
	endpoints, err := s.endpointDao.GetEndpointByAgency(c, agencyId, 1)
	if err != nil {
		return nil, err
	}
	if endpoints == nil {
		return nil, appError.NewErr("没有可用的终端")
	}
	var data []map[string]any
	for _, endpoint := range *endpoints {
		data = append(data, map[string]any{
			"id":   endpoint.ID,
			"name": endpoint.Name,
		})
	}
	return data, nil
}

func (s *endpoint) GetEndpointByAgencyID(c *gin.Context, agencyId uint) ([]map[string]any, error) {
	endpoints, err := s.endpointDao.GetEndpointByAgency(c, agencyId, 1)
	if err != nil {
		return nil, err
	}
	if endpoints == nil {
		return nil, appError.NewErr("没有可用的终端")
	}
	var data []map[string]any
	for _, endpoint := range *endpoints {
		data = append(data, map[string]any{
			"id":   endpoint.ID,
			"name": endpoint.Name,
		})
	}
	return data, nil
}

func (s *endpoint) GetUserSecondAgency(c *gin.Context) ([]map[string]any, error) {
	uid := c.GetUint("uid")
	agency, err := s.userDao.UserAgency(c, uid)
	if err != nil {
		return nil, err
	}
	if agency == nil {
		return nil, appError.NewErr("用户未绑定代理商")
	}
	data, err := s.GetSecondAgencies(c, agency.ID)
	if err != nil {
		return nil, err
	}
	return data, nil
}

// BuildFullAddress 根据省市区ID和详细地址构建完整地址
func (s *endpoint) BuildFullAddress(provinceID, cityID, districtID int, detailAddress string) string {
	// 收集非零的地区ID
	regionIDs := make([]int, 0, 3)
	if provinceID != 0 {
		regionIDs = append(regionIDs, provinceID)
	}
	if cityID != 0 {
		regionIDs = append(regionIDs, cityID)
	}
	if districtID != 0 {
		regionIDs = append(regionIDs, districtID)
	}

	// 如果没有地区ID，直接返回详细地址
	if len(regionIDs) == 0 {
		return detailAddress
	}

	// 查询地区信息
	regions := dao.GetRegionsByIds(regionIDs)

	// 创建地区ID到名称的映射
	regionMap := make(map[int]string, len(regions))
	for _, r := range regions {
		regionMap[r.RegionId] = r.RegionName
	}

	// 构建地址
	var sb strings.Builder

	// 按顺序添加省市区
	if name, ok := regionMap[provinceID]; ok {
		sb.WriteString(name)
	}
	if name, ok := regionMap[cityID]; ok {
		sb.WriteString(name)
	}
	if name, ok := regionMap[districtID]; ok {
		sb.WriteString(name)
	}

	// 添加详细地址
	sb.WriteString(detailAddress)

	return sb.String()
}
