package dao

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"marketing/internal/api/action"
	"marketing/internal/api/machine"
	"marketing/internal/model"
	myErrors "marketing/internal/pkg/errors"
	"strings"
	"sync"
)

type CommonDao interface {
	Models(c *gin.Context, ids []string) ([]map[string]any, error)
	RangeModel(c *gin.Context, name string) ([]map[string]any, error)
	DetailBarcode(c *gin.Context, id string) (action.DetailBarcode, error)
	SiftWarranty(c *gin.Context, detail model.SalesPromotion) ([]action.SiftWarranty, error)
	GetAcDevices(c *gin.Context, ids []int) ([]Res, error)
	WarrantyInfo(c *gin.Context, ids []int) ([]result, error)
	GetAgencyList(c *gin.Context, params AgencyListParams) ([]*model.Agency, int64, error)
	GetEndpointList(c *gin.Context, params EndpointListParams) ([]*model.Endpoint, int64, error)
	GetPartitionTips(c *gin.Context) ([]map[string]any, error)
	GetMesDevice(c *gin.Context, barcode string, imei string, number string) (*machine.MesDeviceResp, error)
	WarrantyReturnLoadContact(warranty *model.Warranty, ch chan<- error, wg *sync.WaitGroup)
}

// AgencyListParams 代理商列表查询参数
type AgencyListParams struct {
	Name      string `json:"name"` // 代理商名称
	Pid       int    `json:"pid"`
	Partition int    `json:"partition"`
}

// EndpointListParams 终端列表查询参数
type EndpointListParams struct {
	Name         string `json:"name"`
	TopAgency    int    `json:"top_agency"`
	SecondAgency int    `json:"second_agency"`
}

type CommonGorm struct {
	db *gorm.DB
}

func NewCommonGorm(db *gorm.DB) *CommonGorm {
	return &CommonGorm{db: db}
}

type Res struct {
	ID     int    `gorm:"column:id"`
	Origin string `gorm:"column:origin"`
}

type result struct {
	ID             int
	Number         string
	ActivatedAtOld string
	StudentName    string
}

func (g *CommonGorm) WarrantyInfo(c *gin.Context, ids []int) ([]result, error) {
	var res []result
	err := g.db.WithContext(c).
		Table("warranty").
		Select("id,activated_at_old,student_name,number").
		Where("id in (?)", ids).
		Find(&res).Error
	return res, err
}
func (g *CommonGorm) GetAcDevices(c *gin.Context, ids []int) ([]Res, error) {
	if len(ids) == 0 {
		return []Res{}, myErrors.NewErr("ids 为空")
	}
	res := make([]Res, 0)
	err := g.db.WithContext(c).Table("ac_devices_uniq").Where("id IN (?)", ids).Select("id,origin").Find(&res).Error
	return res, err
}

func (g *CommonGorm) SiftWarranty(c *gin.Context, detail model.SalesPromotion) ([]action.SiftWarranty, error) {
	curDB := g.db.WithContext(c).Table("warranty as w").
		Joins("LEFT JOIN endpoint as e ON w.endpoint=e.id").
		Joins("LEFT JOIN agency as a ON e.top_agency=a.id").
		Joins("LEFT JOIN region as r ON e.province=r.region_id").
		Joins("LEFT JOIN region as r2 ON e.city=r2.region_id").
		Select("w.endpoint, w.model ,w.model_id, w.barcode,w.id as warranty_id , w.buy_date, w.customer_name , w.customer_phone,w.activated_id")
	//1.查询条件
	curDB = curDB.Where("w.status = ?", 1).Where("a.channel", "agency")
	//2.活动条件
	curDB = curDB.Where("buy_date>?", detail.StartTime).Where("buy_date<?", detail.EndTime)
	models := strings.Split(detail.Model, ",")
	if len(models) > 0 {
		curDB = curDB.Where("w.model_id in (?)", models)
	}
	curDB = curDB.Where("TIMESTAMPDIFF(SECOND, w.buy_date, w.activated_at_old) > ?", detail.HourInterval*3600).
		Where("TIMESTAMPDIFF(SECOND, w.buy_date, w.activated_at_old) < ?", detail.HourInterval*-3600)
	var warranties []action.SiftWarranty
	err := curDB.Find(&warranties).Error
	return warranties, err
}

// Models 查找机型
func (g *CommonGorm) Models(c *gin.Context, ids []string) ([]map[string]any, error) {
	var models []map[string]any
	g.db.WithContext(c).Table("machine_type").Select("model_id,name").Where("model_id in (?)", ids).Find(&models)
	return models, nil

}

// RangeModel 机型遍历
func (g *CommonGorm) RangeModel(c *gin.Context, name string) ([]map[string]any, error) {
	var models []map[string]any
	curDB := g.db.WithContext(c).Table("machine_type").Select("model_id as id, name")
	if name != "" {
		curDB = curDB.Where("name like ?", "%"+name+"%")
	}
	curDB = curDB.Where("visibility = ?", 1)
	curDB = curDB.Where("created_at > ?", "2024-01-01 00:00:00")
	err := curDB.Order("model_id desc").Find(&models).Error
	return models, err

}

// DetailBarcode 查找保修单
func (g *CommonGorm) DetailBarcode(c *gin.Context, id string) (action.DetailBarcode, error) {
	var detail action.DetailBarcode
	row := g.db.WithContext(c).Table("warranty").
		Select("model_id,model,endpoint,buy_date,customer_name,customer_phone,barcode,activated_id").
		Where("warranty_id = ?", id).Find(&detail)
	if row.RowsAffected == 0 {
		return action.DetailBarcode{}, myErrors.NewErr("未找到该条记录")
	}
	if row.Error != nil {
		return action.DetailBarcode{}, row.Error
	}
	var phone string
	g.db.WithContext(c).Table("ac_devices_uniq").Where("id=?", detail.ActivatedID).Select("origin").Find(&phone)
	detail.ActivatedPhone = phone
	if detail.ActivatedPhone == detail.CustomerPhone {
		detail.IsSamePhone = 1
	}
	return detail, nil
}

// GetAgencyList 获取代理商列表
func (g *CommonGorm) GetAgencyList(c *gin.Context, params AgencyListParams) ([]*model.Agency, int64, error) {
	var agencies []*model.Agency
	curDB := g.db.WithContext(c).Table("agency").Where("deleted_at IS NULL")

	// 查询条件
	if params.Name != "" {
		curDB = curDB.Where("name like ?", "%"+params.Name+"%")
	}
	if params.Partition != 0 {
		curDB = curDB.Where("`partition` = ?", params.Partition) // Escaped partition with backticks
	}
	if params.Pid != 0 {
		curDB = curDB.Where("pid = ?", params.Pid)
	}

	// 计算总数
	var total int64
	curDB.Count(&total)

	// 排序
	curDB = curDB.Order("id ASC")

	err := curDB.Find(&agencies).Error
	return agencies, total, err
}

// GetEndpointList 获取终端列表
func (g *CommonGorm) GetEndpointList(c *gin.Context, params EndpointListParams) ([]*model.Endpoint, int64, error) {
	var endpoints []*model.Endpoint
	curDB := g.db.WithContext(c).Table("endpoint").Where("status = ?", 1)

	// 查询条件
	if params.Name != "" {
		curDB = curDB.Where("name like ?", "%"+params.Name+"%")
	}
	if params.TopAgency != 0 {
		curDB = curDB.Where("top_agency = ?", params.TopAgency)
	}
	if params.SecondAgency != 0 {
		curDB = curDB.Where("second_agency = ?", params.SecondAgency)
	}

	// 计算总数
	var total int64
	curDB.Count(&total)

	// 排序
	curDB = curDB.Order("id DESC")

	err := curDB.Find(&endpoints).Error
	return endpoints, total, err
}
func (g *CommonGorm) GetPartitionTips(c *gin.Context) ([]map[string]any, error) {
	var res []map[string]any
	err := g.db.WithContext(c).Table("partition").Select("name,id").Find(&res).Error
	return res, err
}

func (g *CommonGorm) GetMesDevice(c *gin.Context, barcode, imei, number string) (*machine.MesDeviceResp, error) {
	var result machine.MesDeviceResp
	query := g.db.WithContext(c).Table("mes_devices AS d").
		// Select("d.barcode, d.number, d.color, d.meid, d.imei1, d.imei2, d.model, d.model_id, dt.ext_barcode_count, d.state, IF(d.procedure = 4, 1, 0) AS status, d.update_time").
		Select("d.barcode, d.number, d.color, d.meid, d.imei1, d.imei2, d.model, d.model_id, d.state, IF(d.procedure = 4, 1, 0) AS status, d.update_time AS product_date").
		// 数据库中无mes_device_type表 Joins ("LEFT JOIN mes_device_type dt ON d.model_id = dt.id").
		Where("d.state IN (0,1,2,3)")

	if barcode != "" {
		query = query.Where("d.barcode = ?", barcode)
	} else if imei != "" {
		query = query.Where("d.imei = ?", imei)
	} else if number != "" {
		query = query.Where("d.number = ?", number)
	} else {
		return nil, nil
	}

	query = query.Order("d.update_time DESC").Limit(1)

	err := query.Scan(&result).Error
	if err != nil {
		return nil, err
	}
	if result.Barcode == "" {
		return nil, nil
	}
	return &result, nil
}

func (g *CommonGorm) WarrantyReturnLoadContact(warranty *model.Warranty, ch chan<- error, wg *sync.WaitGroup) {
	defer wg.Done()

	var count int64
	customerPhone := warranty.CustomerPhone
	endpointID := warranty.Endpoint
	// 统计激活该顾客电话下的保单数量
	if err := g.db.Table("warranty").
		Where("customer_phone = ? AND status = 1", customerPhone).
		Count(&count).Error; err != nil {
		ch <- err
		return
	}
	// 如果数量为0则更新其状态
	if count == 0 {
		if err := g.db.Table("contact").
			Where("endpoint_id = ? AND customer_phone = ?", endpointID, customerPhone).
			Updates(map[string]interface{}{
				"type":     "lost",
				"sub_type": "returned",
			}).Error; err != nil {
			ch <- err
			return
		}
	}
	ch <- nil
}
