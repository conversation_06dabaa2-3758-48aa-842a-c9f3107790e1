package operation

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/handler"
	"marketing/internal/pkg/e"
	"marketing/internal/pkg/errors"
	"marketing/internal/pkg/utils"
	"marketing/internal/service/operation"
)

type OpArticleComment struct {
	svc operation.OpArticleCommentSvcInterface
}

func NewOpArticleComment(svc operation.OpArticleCommentSvcInterface) *OpArticleComment {
	return &OpArticleComment{
		svc: svc,
	}
}

func (o *OpArticleComment) GetOpArticleCommentStatistics(c *gin.Context) {
	commentType := e.ReqParamInt(c, "comment_type")
	pageNum := e.ReqParamInt(c, "page_num")
	pageSize := e.ReqParamInt(c, "page_size")
	list, total := o.svc.GetOpArticleCommentStatistics(c, commentType, pageNum, pageSize)
	handler.Success(c, gin.H{
		"list":  list,
		"total": total,
	})
}

func (o *OpArticleComment) GetOpArticleCommentList(c *gin.Context) {
	list := o.svc.GetOpArticleList(c, e.ReqParamInt(c, "article_Id"))
	handler.Success(c, gin.H{
		"list": list,
	})
}

func (o *OpArticleComment) UpdateOpArticleCommentStatus(c *gin.Context) {
	id := e.ReqParamInt(c, "id")
	visible := e.ReqParamInt(c, "visible", -1)
	selected := e.ReqParamInt(c, "selected", -1)
	top := e.ReqParamInt(c, "top", -1)
	discarded := e.ReqParamInt(c, "discarded", -1)
	err := o.svc.UpdateOpArticleCommentStatus(c, id, visible, selected, top, discarded)
	if err != nil {
		handler.Error(c, errors.NewErr("编辑内容评论失败:"+err.Error()))
		return
	}

	handler.Success(c, gin.H{})
}

func (o *OpArticleComment) OpArticleCommentReply(c *gin.Context) {
	id := e.ReqParamInt(c, "id")
	articleId := e.ReqParamInt(c, "article_Id")
	context := e.ReqParamStr(c, "context")
	uid := c.GetUint("uid")

	files := make([]string, 0)
	utils.JsonStrToObjectList(e.ReqParamStr(c, "files"), &files)

	err := o.svc.ReplyOpArticleComment(c, id, articleId, context, files, int(uid))
	if err != nil {
		handler.Error(c, errors.NewErr("回复内容评论失败:"+err.Error()))
		return
	}

	handler.Success(c, gin.H{})
}
