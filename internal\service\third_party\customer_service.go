package third_party

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"marketing/internal/config"
	appError "marketing/internal/pkg/errors"
	"marketing/internal/pkg/log"
	"marketing/internal/pkg/sms"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-resty/resty/v2"
)

type CustomerServiceInterface interface {
	SendCaptcha(ctx *gin.Context, phone string, template int) error
	SendVideoCaptcha(ctx *gin.Context, phone string, templateParam map[string]any) error
	QueryRepairProgress(ctx *gin.Context, keyword string) any
	RepairUrgent(ctx *gin.Context, keyword string) any // 寄修进度加急
}
type customerSvc struct {
	config *config.Config
}

// NewCustomerService 创建客户服务实例
func NewCustomerService(cfg *config.Config) CustomerServiceInterface {
	return &customerSvc{
		config: cfg,
	}
}

func (c *customerSvc) SendCaptcha(ctx *gin.Context, phone string, template int) error {
	signName := "读书郎"
	templateCode := "" // 短信模板代码
	templateParam := make(map[string]any)
	switch template {
	case 1:
		templateCode = "courseware_download" // 课件下载
	case 2:
		templateCode = "after_sale_endpoint" // 售后网点查询
	case 3:
		templateCode = "after_sale_policy" // 售后政策、配件价格
	case 4:
		templateCode = "repair_service" // 寄修服务
	case 5:
		templateCode = "troubleshooting" // 问题排查
	default:
		return appError.NewErr("无效的短信模板")
	}

	b, msg := sms.SendSMS(phone, signName, templateCode, templateParam)
	if b == false {
		return appError.NewErr(msg)
	}

	return nil
}

// SendVideoCaptcha 发送视频短信
func (c *customerSvc) SendVideoCaptcha(ctx *gin.Context, phone string, templateParam map[string]any) error {
	signName := "读书郎"
	templateCode := "video_link" // 短信模板代码

	b, msg := sms.SendSMS(phone, signName, templateCode, templateParam)
	if b == false {
		return appError.NewErr(msg)
	}

	return nil
}

// QueryRepairProgress 查询寄修进度
func (c *customerSvc) QueryRepairProgress(ctx *gin.Context, keyword string) any {
	// 生成寄修接口参数
	params := c.getRepairParams()

	// 构建请求URL
	url := fmt.Sprintf("%s/order/query", c.config.Repair.BaseURL)

	// 创建HTTP客户端
	client := resty.New()
	log.Info("查询寄修进度", zap.String("keyword", keyword), zap.String("X-Request-ID", ctx.GetHeader("X-Request-ID")))
	// 发送请求
	resp, err := client.R().
		SetQueryParams(map[string]string{
			"t":       strconv.FormatInt(params["t"].(int64), 10),
			"ua":      params["ua"].(string),
			"sn":      params["sn"].(string),
			"keyword": keyword,
		}).
		Get(url)

	if err != nil {
		log.Error("请求失败"+err.Error(), zap.String("X-Request-ID", ctx.GetHeader("X-Request-ID")))
		return gin.H{"msg": fmt.Sprintf("请求失败: %s", err.Error()), "ok": 0}
	}

	// 解析响应JSON
	var response map[string]interface{}
	if err := json.Unmarshal(resp.Body(), &response); err != nil {
		log.Error("响应解析失败"+err.Error(), zap.String("response", string(resp.Body())), zap.String("X-Request-ID", ctx.GetHeader("X-Request-ID")))
		return gin.H{"msg": "查询失败", "ok": 0}
	}

	// 检查响应状态
	ok, exists := response["ok"]
	if !exists || ok != float64(1) {
		// 失败时返回原始消息
		msg, _ := response["msg"].(string)
		if msg == "" {
			msg = "查询失败"
		}
		return gin.H{"msg": msg, "ok": 0}
	}

	// 成功时提取关键信息
	data, exists := response["data"].(map[string]interface{})
	if !exists {
		log.Error("数据格式错误", zap.Any("response", response), zap.String("X-Request-ID", ctx.GetHeader("X-Request-ID")))
		return gin.H{"msg": "查询失败", "ok": 0}
	}

	logData, exists := data["log"].(map[string]interface{})
	if !exists {
		log.Error("没有日志信息", zap.Any("response", response), zap.String("X-Request-ID", ctx.GetHeader("X-Request-ID")))
		return gin.H{"msg": "查询失败,没有日志信息", "ok": 0}
	}

	// 提取需要的字段
	title, _ := logData["title"].(string)
	date, _ := logData["date"].(string)
	orderSn, _ := logData["order_sn"].(string)

	// 返回简化的响应
	result := map[string]interface{}{
		"title":    title,
		"date":     date,
		"order_sn": orderSn,
		"ok":       1,
	}

	resultBytes, _ := json.Marshal(result)
	return string(resultBytes)
}

// RepairUrgent 寄修进度加急
func (c *customerSvc) RepairUrgent(ctx *gin.Context, keyword string) any {
	// 生成寄修接口参数
	params := c.getRepairParams()

	// 构建请求URL
	url := fmt.Sprintf("%s/order/urgent", c.config.Repair.BaseURL)

	// 创建HTTP客户端
	client := resty.New()

	// 发送POST请求
	resp, err := client.R().
		SetHeader("Content-Type", "application/x-www-form-urlencoded").
		SetFormData(map[string]string{
			"t":       strconv.FormatInt(params["t"].(int64), 10),
			"ua":      params["ua"].(string),
			"sn":      params["sn"].(string),
			"keyword": keyword,
		}).
		Post(url)

	if err != nil {
		return gin.H{"msg": fmt.Sprintf("请求失败: %s", err.Error()), "ok": 0}
	}

	// 解析响应JSON
	var response map[string]interface{}
	if err := json.Unmarshal(resp.Body(), &response); err != nil {
		return gin.H{"msg": "响应解析失败", "ok": 0}
	}
	// 检查响应状态
	ok, exists := response["ok"]
	if !exists || ok != float64(1) {
		// 失败时返回原始消息
		msg, _ := response["msg"].(string)
		if msg == "" {
			msg = "加急失败，请联系人工客服"
		}
		return gin.H{"msg": msg, "ok": 0}
	}
	// 成功时提取关键信息
	data, exists := response["data"].(map[string]interface{})
	if !exists {
		return gin.H{"msg": "加急失败，请联系人工客服", "ok": 0}
	}
	urgent, _ := data["message"].(string)

	return gin.H{
		"msg": urgent,
		"ok":  1,
	}
}

// getRepairParams 生成寄修接口参数
func (c *customerSvc) getRepairParams() map[string]interface{} {
	appid := c.config.Repair.AppID
	appkey := c.config.Repair.AppKey

	// 构建device_id
	deviceItems := []string{"1", "", "", appid, "", ""}
	deviceID := strings.Join(deviceItems, "/")

	// 生成时间戳
	t := time.Now().Unix()

	// 生成签名
	signStr := deviceID + appkey + strconv.FormatInt(t, 10)
	hash := md5.Sum([]byte(signStr))
	sn := hex.EncodeToString(hash[:])

	return map[string]interface{}{
		"t":  t,
		"ua": deviceID,
		"sn": sn,
	}
}
