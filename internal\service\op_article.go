package service

import (
	"errors"
	"marketing/internal/api/operation"
	"marketing/internal/dao"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
)

type ArticleService interface {
	// 公共
	CheckUserPublisher(c *gin.Context, uid uint, pid uint) error

	// 内容
	List(c *gin.Context, req operation.ArticleListReq) (operation.ArticleListRsp, error)
	Creators(c *gin.Context) (operation.ArticleCreatorsRsp, error)
	Detail(c *gin.Context, id uint) (operation.ArticleDetailRsp, error)
	Create(c *gin.Context, req operation.ArticleCreateReq) (uint, error)

	// 类别
	CategoryList(c *gin.Context) (operation.ArticleCategoryListRsp, error)

	// 标签
	TagList(c *gin.Context) (operation.ArticleTagListRsp, error)

	// 评论
	CommentList(c *gin.Context, req operation.ArticleCommentListReq) (operation.ArticleCommentListRsp, error)
}

type GormArticleService struct {
	dao dao.OpArticleDao
}

func NewGormArticleService(dao dao.OpArticleDao) ArticleService {
	return &GormArticleService{dao: dao}
}

func (g GormArticleService) List(c *gin.Context, req operation.ArticleListReq) (operation.ArticleListRsp, error) {
	list, total, err := g.dao.List(c, req)
	if err != nil {
		return operation.ArticleListRsp{}, err
	}

	aids := lo.Map(list, func(item operation.ArticleListRspItem, _ int) uint {
		return item.ID
	})
	// 获取标签信息
	tags, err := g.dao.Tags(c, aids)
	if err != nil {
		return operation.ArticleListRsp{}, err
	}

	// 计算分享数
	shareMap, err := g.dao.ShareCount(c, aids)
	if err != nil {
		return operation.ArticleListRsp{}, err
	}

	// 计算点赞数
	LikeMap, err := g.dao.LikeCount(c, aids, operation.LikeTargetArticle)
	if err != nil {
		return operation.ArticleListRsp{}, err
	}

	// 计算浏览数
	viewMap, err := g.dao.ViewCount(c, aids)
	if err != nil {
		return operation.ArticleListRsp{}, err
	}

	// 计算评论数
	commentMap, err := g.dao.CommentCount(c, aids)
	if err != nil {
		return operation.ArticleListRsp{}, err
	}

	for i := range list {
		list[i].Tags = tags[list[i].ID]
		if list[i].Tags == nil {
			list[i].Tags = []operation.ArticleTagsItem{}
		}
		list[i].NumShares = shareMap[list[i].ID]
		list[i].NumLikes = LikeMap[list[i].ID]
		list[i].NumViews = viewMap[list[i].ID]
		list[i].NumComments = commentMap[list[i].ID]
	}

	return operation.ArticleListRsp{
		List:  list,
		Total: total,
	}, err
}

func (g *GormArticleService) Creators(c *gin.Context) (operation.ArticleCreatorsRsp, error) {
	list, err := g.dao.Creators(c)
	if err != nil {
		return operation.ArticleCreatorsRsp{}, err
	}

	return operation.ArticleCreatorsRsp{
		List: list,
	}, nil
}

func (g *GormArticleService) Detail(c *gin.Context, id uint) (operation.ArticleDetailRsp, error) {
	detail, err := g.dao.Detail(c, id)
	if err != nil {
		return operation.ArticleDetailRsp{}, err
	}
	return detail, nil
}

func (g *GormArticleService) CheckUserPublisher(c *gin.Context, uid uint, pid uint) error {
	roles, err := g.dao.GetUserRoles(c, uid)
	if err != nil {
		return err
	}

	publisherIds, err := g.dao.GetUserPublisherIds(c, uid, roles)
	if err != nil {
		return err
	}

	if len(publisherIds) == 0 {
		return errors.New("未关联运营号，不能发表内容")
	}

	if !lo.Contains(publisherIds, pid) {
		return errors.New("非账号关联运营号，不能发表内容")
	}
	return nil
}

func (g *GormArticleService) Create(c *gin.Context, req operation.ArticleCreateReq) (uint, error) {
	return g.dao.CreateTx(c, req)
}

// 类别

func (g *GormArticleService) CategoryList(c *gin.Context) (operation.ArticleCategoryListRsp, error) {
	list, err := g.dao.CategoryList(c)
	if err != nil {
		return operation.ArticleCategoryListRsp{}, err
	}

	buildTree := func(rows []operation.ArticleCategoryListRspItem) []operation.ArticleCategoryListRspItem {
		// 创建一个映射表，用于快速查找节点
		nodeMap := make(map[uint]int)
		for i := range rows {
			nodeMap[rows[i].ID] = i
		}

		// 遍历所有节点，构建树结构
		childrenMap := map[uint][]uint{}
		for _, row := range rows {
			// 将当前节点添加到其父节点的 Children 中
			childrenMap[row.ParentID] = append(childrenMap[row.ParentID], row.ID)
		}

		// 建树
		var traverse func(node operation.ArticleCategoryListRspItem) operation.ArticleCategoryListRspItem
		traverse = func(node operation.ArticleCategoryListRspItem) operation.ArticleCategoryListRspItem {
			for _, childId := range childrenMap[node.ID] {
				child := traverse(rows[nodeMap[childId]])
				node.Children = append(node.Children, child)
			}
			return node
		}

		root := operation.ArticleCategoryListRspItem{
			ID: 0,
		}
		root = traverse(root)

		return root.Children
	}
	tree := buildTree(list)

	// 填充文章数量
	var traverse func(node *operation.ArticleCategoryListRspItem)
	traverse = func(node *operation.ArticleCategoryListRspItem) {
		for i := range node.Children {
			traverse(&node.Children[i])
			node.NumArticles += node.Children[i].NumArticles
		}
	}
	for i := range tree {
		traverse(&tree[i])
	}

	return operation.ArticleCategoryListRsp{
		List: tree,
	}, nil
}

// 标签

func (g *GormArticleService) TagList(c *gin.Context) (operation.ArticleTagListRsp, error) {
	list, err := g.dao.TagList(c)
	if err != nil {
		return operation.ArticleTagListRsp{}, err
	}

	return operation.ArticleTagListRsp{
		List: list,
	}, nil
}

// 评论

func (g *GormArticleService) CommentList(c *gin.Context, req operation.ArticleCommentListReq) (operation.ArticleCommentListRsp, error) {
	// 获取文章的评论设置
	commentSetting, err := g.dao.CommentSetting(c, req.ID)
	if err != nil {
		return operation.ArticleCommentListRsp{}, err
	}

	// 获取评论列表
	list, err := g.dao.CommentList(c, req, commentSetting)
	if err != nil {
		return operation.ArticleCommentListRsp{}, err
	}
	if len(list) == 0 {
		return operation.ArticleCommentListRsp{
			List: []operation.ArticleCommentListRspItem{},
		}, nil
	}

	// 收集评论的ID
	sourceIds := []int{}
	for _, item := range list {
		sourceIds = append(sourceIds, item.ID)
	}

	// 获取评论的回复
	children, err := g.dao.CommentReplies(c, sourceIds, req.Type, commentSetting, req.Creator, 0, req.ReplySize)
	if err != nil {
		return operation.ArticleCommentListRsp{}, err
	}

	// 收集所有用户ID和评论ID
	allUserIds := []uint{}
	allCommentIds := []uint{}
	for _, rows := range children {
		for _, c := range rows.List {
			allUserIds = append(allUserIds, uint(c.CreatedBy))
			allCommentIds = append(allCommentIds, uint(c.ID))
		}
	}
	for _, item := range list {
		allUserIds = append(allUserIds, uint(item.CreatedBy))
		allCommentIds = append(allCommentIds, uint(item.ID))
	}
	allUserIds = lo.Uniq(allUserIds)       // 去重用户ID
	allCommentIds = lo.Uniq(allCommentIds) // 去重评论ID

	// 获取评论的点赞数
	likeMap, err := g.dao.LikeCount(c, allCommentIds, operation.LikeTargetComment)
	if err != nil {
		return operation.ArticleCommentListRsp{}, err
	}

	// 获取用户的评论数
	UserCommentMap, err := g.dao.UserCommentCount(c, allUserIds)
	if err != nil {
		return operation.ArticleCommentListRsp{}, err
	}

	// 获取评论创建者的信息
	creatorMap, err := g.dao.CreatorInfos(c, allUserIds, UserCommentMap)
	if err != nil {
		return operation.ArticleCommentListRsp{}, err
	}

	// 递归函数，用于构建评论的回复树
	var traverse func(pid uint) (node *operation.CommentReplies)
	traverse = func(pid uint) (node *operation.CommentReplies) {
		if children[pid] == nil {
			return nil
		}
		for i := range children[pid].List {
			children[pid].List[i].NumLikes = int(likeMap[uint(children[pid].List[i].ID)])      // 设置点赞数
			children[pid].List[i].Creator = *creatorMap[uint(children[pid].List[i].CreatedBy)] // 设置创建者信息
			children[pid].List[i].Replies = traverse(uint(children[pid].List[i].ID))           // 递归获取回复
		}
		return children[pid]
	}

	// 为每个评论设置点赞数、创建者信息和回复
	for i := range list {
		list[i].NumLikes = int(likeMap[uint(list[i].ID)])
		list[i].Creator = *creatorMap[uint(list[i].CreatedBy)]
		list[i].Replies = traverse(uint(list[i].ID))
	}

	// 返回评论列表响应
	return operation.ArticleCommentListRsp{
		List: list,
	}, nil
}
