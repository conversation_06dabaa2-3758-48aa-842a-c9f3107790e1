package model

import "time"

// EndpointType 结构体映射数据库表 endpoint_types
type EndpointType struct {
	ID          int       `gorm:"primary_key" json:"id"`
	Code        string    `gorm:"type:varchar(10);not null" json:"code"`
	Name        string    `gorm:"type:varchar(100);not null" json:"name"`
	Description string    `gorm:"type:text" json:"description"`
	Status      int       `gorm:"default:1" json:"status"` // 使用 bool 类型来表示 TINYINT(1)
	CreatedAt   time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt   time.Time `gorm:"default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP" json:"updated_at"`
}

// TableName 设置表名
func (EndpointType) TableName() string {
	return "endpoint_types"
}
