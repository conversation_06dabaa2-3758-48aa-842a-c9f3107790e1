package app_auth

import (
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"marketing/internal/model"
	"marketing/internal/pkg/log"
	"time"
)

const (
	AppAuthCacheKey = "app_auth_cache_key:"
)

type AppAuthCacheInterface interface {
	GetAppAuthByKey(c *gin.Context, key string) (*model.AppAuth, error)
	SetAppAuth(c *gin.Context, appAuth *model.AppAuth, key string, expiration time.Duration) error
	DeleteAppAuth(c *gin.Context, key string) error
}

type appAuthCache struct {
	client *redis.Client
}

func NewAppAuthCache(client *redis.Client) AppAuthCacheInterface {
	return &appAuthCache{
		client: client,
	}
}

func (s *appAuthCache) GetAppAuthByKey(c *gin.Context, key string) (*model.AppAuth, error) {
	if key == "" {
		return nil, fmt.Errorf("key is empty")
	}
	key = AppAuthCacheKey + key
	data, err := s.client.Get(c, key).Bytes()
	if err != nil {
		log.Error("GetAppAuthByKey error", zap.Error(err), zap.String("key", key))
		return nil, err
	}
	var appAuth model.AppAuth
	if err := json.Unmarshal(data, &appAuth); err != nil {
		fmt.Println("Unmarshal error:", err)
		return nil, err
	}
	return &appAuth, nil
}

func (s *appAuthCache) SetAppAuth(c *gin.Context, appAuth *model.AppAuth, key string, expiration time.Duration) error {
	key = AppAuthCacheKey + key
	data, err := json.Marshal(appAuth)
	if err != nil {
		return err
	}
	return s.client.Set(c, key, data, expiration).Err()
}

func (s *appAuthCache) DeleteAppAuth(c *gin.Context, key string) error {
	if key == "" {
		return fmt.Errorf("key is empty")
	}
	key = AppAuthCacheKey + key
	return s.client.Del(c, key).Err()
}
