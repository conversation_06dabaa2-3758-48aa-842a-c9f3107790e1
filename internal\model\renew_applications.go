package model

import (
	"marketing/internal/pkg/types"

	"gorm.io/gorm"
)

type RenewApplication struct {
	ID                int                   `json:"id" gorm:"primaryKey;autoIncrement"`
	Barcode           string                `json:"barcode" gorm:"type:varchar(64);not null;comment:'Barcode S/N码'"`
	Number            string                `json:"number" gorm:"type:varchar(64);not null;comment:'Number'"`
	Model             string                `json:"model" gorm:"type:varchar(64);not null;comment:'Model'"`
	ModelID           uint                  `json:"model_id" gorm:"not null;comment:'Model ID'"`
	Applicant         uint                  `json:"-" gorm:"not null;comment:'User ID'"`
	TopAgency         uint                  `json:"-" gorm:"not null;comment:'Top Agency ID'"`
	SecondAgency      uint                  `json:"-" gorm:"not null;comment:'Second Agency ID'"`
	EndpointID        uint                  `json:"endpoint_id" gorm:"not null;comment:'Endpoint ID'"`
	Status            string                `json:"status" gorm:"type:enum('Pending','FirstLevelReview','HeadOfficeReview','Repair','Completed','Rejected');not null;default:'Pending';comment:'Status'"`
	Issues            string                `json:"-" gorm:"type:set('花屏','线条','触摸异常','不开机','自动重启','摄像头异常','摄像头翻转异常','耗电快','不充电','其他');not null;comment:'Issues'"`
	DamageDescription string                `json:"damage_description" gorm:"type:text;comment:'Damage Description'"`
	DamageImages      types.JSONStringArray `json:"damage_images" gorm:"type:text;comment:'Damage Images or video JSON列表格式化存储'"`
	CheckResult       string                `json:"check_result" gorm:"type:varchar(255);comment:检测结果;collate:utf8mb4_general_ci"`
	CheckFacade       string                `json:"check_facade" gorm:"type:enum('整机好','整机坏','外观好性能坏','外观坏性能好');collate:utf8mb4_general_ci"`
	CheckImages       types.JSONStringArray `json:"check_images" gorm:"type:text;comment:检测视频或者图片;collate:utf8mb4_general_ci"`
	CreatedAt         types.CustomTime      `json:"created_at" gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt         types.CustomTime      `json:"-" gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP;autoUpdateTime"`
	DeletedAt         gorm.DeletedAt        `json:"-" gorm:"type:timestamp"`
	ApplicantType     string                `json:"applicant_type" gorm:"column:applicant_type;type:varchar(20);comment:申请人类型"`
}

func (RenewApplication) TableName() string {
	return "renew_applications"
}
