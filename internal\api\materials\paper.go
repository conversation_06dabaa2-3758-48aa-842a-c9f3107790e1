package materials

import (
	"marketing/internal/model"
	"time"
)

type PaperListReq struct {
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
	ID       int    `json:"id"`
	Label    string `json:"label"`
}
type PaperList struct {
	ID          uint      `json:"id"`          // id
	Name        string    `json:"name"`        // 名称
	Description string    `json:"description"` // 描述
	Duration    uint      `json:"duration"`    // 考试时长
	Status      uint8     `json:"status"`      // 是否发布，0为未发布，1为发布
	Start       time.Time `json:"start"`       // 开始时间
	End         time.Time `json:"end"`         // 结束时间
	Number      int       `json:"number"`      // 出题数量
	UpdatedAt   time.Time `json:"updated_at"`  // 更新时间
	//连接字段
	HadNum      int `json:"had_num"`
	HadEndpoint int `json:"had_endpoint"`
	HadSalesman int `json:"had_salesman"`
}

/*type PaperWithQuestions struct {
	PaperDetail
	QuestionDetails []QuestionTips `json:"question_details"`
}*/

type PaperDetail struct {
	ID          int       `json:"id"`
	Name        string    `json:"name"`        // 名称
	Description string    `json:"description"` // 描述
	Duration    uint      `json:"duration"`    // 考试时长
	Status      uint8     `json:"status"`      // 是否发布，0为未发布，1为发布
	Creator     string    `json:"creator"`     // 命题人
	Start       time.Time `json:"start"`       // 开始时间
	End         time.Time `json:"end"`         // 结束时间
	Number      int       `json:"number"`      // 出题数量
	CreatedAt   time.Time `json:"created_at"`  // 创建时间
	UpdatedAt   time.Time `json:"updated_at"`  // 更新时间
}
type QuestionTips struct {
	QuestionID   int    `json:"question_id"`
	QuestionName string `json:"question_name"`
	QuestionType string `json:"question_type"`
}

type PaperUpsertReq struct {
	Paper     Paper `json:"paper"`
	Questions []int `json:"question"`
}
type Paper struct {
	ID          int       `json:"id"`
	Name        string    `json:"name"`        // 名称
	Description string    `json:"description"` // 描述
	Duration    uint      `json:"duration"`    // 考试时长
	Status      uint8     `json:"status"`      // 是否发布，0为未发布，1为发布
	Creator     string    `json:"creator"`     // 命题人
	Start       string    `json:"start"`       // 开始时间
	End         string    `json:"end"`         // 结束时间
	Number      int       `json:"number"`      // 出题数量
	CreatedAt   time.Time `json:"-"`           // 创建时间
	UpdatedAt   time.Time `json:"-"`           // 更新时间
}
type PaperStatusReq struct {
	ID     int   `json:"id"`
	Status uint8 `json:"status"`
}
type QuestionListReq struct {
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
	Label    string `json:"label"`
	ID       int    `json:"id"`
	Type     string `json:"type"`
}
type QuestionListResp struct {
	model.Question
	QuestionType string `json:"question_type"`
	AnswerNum    int    `json:"answer_num"`
}
type QuestionDetailResp struct {
	model.Question
	Papers []PaperView `json:"papers"`
}

// PaperView 	试卷概述
type PaperView struct {
	ID          int    `json:"id"`          // id
	Name        string `json:"name"`        // 名称
	Description string `json:"description"` // 描述
}
type QuestionUpsertReq struct {
	ID          int      `json:"id"`
	Type        int      `json:"type"`
	Detail      string   `json:"detail"`
	Description string   `json:"description"`
	Options     []string `json:"options"`
	Answer      []string `json:"answer"`
}
type QuestionTypeTips struct {
	Name string `json:"name"`
	ID   int    `json:"id"`
}
