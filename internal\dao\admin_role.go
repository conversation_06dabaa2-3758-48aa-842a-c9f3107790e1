package dao

import (
	"marketing/internal/model"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type AdminRoleDao interface {
	// Define methods for AdminRoleDao here
	GetAdminRoleByIDs(c *gin.Context, ids []int) ([]*model.AdminRoles, error)
}

type adminRoleDao struct {
	db *gorm.DB
}

func NewAdminRoleDao(db *gorm.DB) AdminRoleDao {
	return &adminRoleDao{
		db: db,
	}
}

func (d *adminRoleDao) GetAdminRoleByIDs(c *gin.Context, ids []int) ([]*model.AdminRoles, error) {
	// Implement methods for AdminRoleDao here
	var roles []*model.AdminRoles
	if err := d.db.WithContext(c).Where("id IN ?", ids).Find(&roles).Error; err != nil {
		return nil, err
	}
	return roles, nil
}
