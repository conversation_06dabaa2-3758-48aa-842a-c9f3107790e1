package materials

import (
	"time"
)

// TrainSubjectList 主题列表字段
type TrainSubjectList struct {
	ID        int       `json:"id"`
	Title     string    `json:"title"`
	NumPapers int       `json:"num_papers"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	Type      string    `json:"type"`
	Enabled   bool      `json:"enabled"`
	//连接字段
	HadCourse      int `json:"had_course"`      //已关联课程
	HadCertificate int `json:"had_certificate"` //已关联证书
	HadPractice    int `json:"had_practice"`    //已关联大实战
}

// TrainSubjectAdd 新增专题请求
type TrainSubjectAdd struct {
	SubjectID       uint             `json:"subject_id"`
	Title           string           `json:"title"`
	StartTime       string           `json:"start_time" binding:"required"`
	EndTime         string           `json:"end_time" binding:"required"`
	Enabled         uint8            `json:"enabled"`
	Type            string           `json:"type" `
	Lecturer        string           `json:"lecturer"`
	LecturerAvatar  string           `json:"lecturer_avatar"`
	BriefIntro      string           `json:"brief_intro"`
	Tag             string           `json:"tag"`
	CompoundVisible uint8            `json:"compound_visible"`
	Tactic          uint8            `json:"tactic"`
	HasCertificate  uint8            `json:"has_certificate"`
	Agencies        map[string][]int `json:"agencies"`
	Courses         []TrainCourse    `json:"courses"`
	Practices       TrainPractice    `json:"practices"`
}

type AgencyTips struct {
	ID           int            `json:"value"`
	Name         string         `json:"label"`
	SecondAgency []SecondAgency `json:"children"`
}

type SecondAgency struct {
	ID   int    `json:"value"`
	Name string `json:"label"`
	Pid  int    `json:"-"`
}

type TrainCourse struct {
	CourseID uint `json:"course_id"`
	//基础字段
	Name  string `json:"name"`
	IsUse int8   `json:"is_use"`
	Url   string `json:"url"`
	//直播课时
	LiveStartTime string `json:"live_start_time"`
	LiveEndTime   string `json:"live_end_time"`
	//普通课时
	Issuer      string `json:"issuer"`
	Description string `json:"description"`
	SubjectWithPaper
}

type SubjectWithPaper struct {
	SubjectID          uint    `json:"subject_id"`
	PaperID            uint    `json:"paper_id"`
	Credit             float64 `json:"credit"`
	CreditLearningTime int     `json:"credit_learning_time"`
}

// TrainPractice 大实战
type TrainPractice struct {
	ID          uint   `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	MinCredit   uint   `json:"min_credit"`
	MaxCredit   uint   `json:"max_credit"`
	IsUse       int8   `json:"is_use"`
	SubjectID   uint   `json:"subject_id"`
}

type SWCBatchDelete struct {
	SubjectIDs []uint
	CourseIDs  []uint
}

type TrainCourseDetail struct {
	ID            uint   `json:"course_id"`
	Name          string `json:"name"`
	Description   string `json:"description"`
	Issuer        string `json:"issuer"`
	ResourceURL   string `json:"resource_url"`
	CreatedAt     string `json:"created_at"`
	UpdatedAt     string `json:"updated_at"`
	LiveStartTime string `json:"live_start_time"`
	LiveEndTime   string `json:"live_end_time"`
	IsUse         int8   `json:"is_use"`
}

type TrainCourseUpdate struct {
	ID            uint   `json:"course_id"`
	Name          string `json:"name"`
	Description   string `json:"description"`
	Issuer        string `json:"issuer"`
	ResourceURL   string `json:"resource_url"`
	LiveStartTime string `json:"live_start_time"`
	LiveEndTime   string `json:"live_end_time"`
	IsUse         int8   `json:"is_use"`
}
type TrainSubjectListReq struct {
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
	Type     string `json:"type"`
	Enabled  string `json:"enabled"`
	Name     string `json:"name"`
}
