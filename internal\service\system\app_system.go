package system

import (
	"errors"
	api "marketing/internal/api/system"
	"marketing/internal/consts"
	dao "marketing/internal/dao/app_system"
	"marketing/internal/model"
	appError "marketing/internal/pkg/errors"
	"marketing/internal/pkg/log"
	"marketing/internal/pkg/utils"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type AppSystemSvc interface {
	GetAppSystemByKey(c *gin.Context, key string) (*model.AppSystemV2, error)
	Add(c *gin.Context, req *api.AddAppSystemReq) error
	Update(c *gin.Context, req *api.AddAppSystemReq) error
	Delete(c *gin.Context, id uint) error
	GetAppSystemList(c *gin.Context, req api.AppSystemReq) ([]*api.AppSystemResp, int64, error)
	GetParentAppSystemKey(c *gin.Context, req api.AppSystemReq) ([]map[string]string, error)
}

type appSystemSvc struct {
	appSystemDao   dao.AppSystem
	appSystemCache dao.AppSystemCacheInterface
}

func NewAppSystemSvc(appSystemDao dao.AppSystem, appSystemCache dao.AppSystemCacheInterface) AppSystemSvc {
	return &appSystemSvc{
		appSystemDao:   appSystemDao,
		appSystemCache: appSystemCache,
	}
}

// GetAppSystemByKey 获取系统信息（带缓存）
func (s *appSystemSvc) GetAppSystemByKey(c *gin.Context, xGateType string) (*model.AppSystemV2, error) {
	if xGateType == "" {
		return nil, appError.NewErr("系统不存在")
	}

	// 先从缓存获取
	appSystem, err := s.appSystemCache.GetAppSystemByKey(c, xGateType)
	if err == nil && appSystem != nil {
		return appSystem, nil
	}

	// 缓存不存在，从数据库获取
	appSystem, err = s.appSystemDao.GetAppSystemByKey(c, xGateType)
	if err != nil {
		return nil, err
	}

	// 设置缓存
	err = s.appSystemCache.SetAppSystem(c, appSystem, xGateType, consts.AppSystemExpiresIn)
	if err != nil {
		log.Error("set app system cache error: ", zap.Error(err))
	}

	return appSystem, err
}

func (s *appSystemSvc) Add(c *gin.Context, req *api.AddAppSystemReq) error {
	exist, err := s.appSystemDao.GetAppSystemByKey(c, req.AppKey)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}
	if exist != nil {
		return appError.NewErr("app_key 已经存在")
	}
	var appSystem model.AppSystemV2
	appSystem.AppKey = req.AppKey
	appSystem.CorpID = req.CorpID
	appSystem.AgentID = req.AgentID
	appSystem.CorpSecret = req.CorpSecret
	appSystem.Name = req.Name
	appSystem.Desc = req.Desc
	appSystem.Type = req.Type
	appSystem.Index = req.Index
	appSystem.Icon = req.Icon
	appSystem.Visibility = req.Visibility
	appSystem.UserGroup = utils.IntSliceToString(req.UserGroup)
	appSystem.JwtKey, _ = utils.GenerateRandomString32()
	appSystem.Parent = req.Parent
	appSystem.Rank = req.Rank
	err = s.appSystemDao.Add(c, &appSystem)
	if err != nil {
		return err
	}
	return nil
}

func (s *appSystemSvc) Update(c *gin.Context, req *api.AddAppSystemReq) error {
	appSystem, err := s.appSystemDao.GetAppSystemByID(c, req.ID)
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return appError.NewErr("数据不存在")
	}
	if err != nil {
		return err
	}

	appSystem.AppKey = req.AppKey
	appSystem.CorpID = req.CorpID
	appSystem.AgentID = req.AgentID
	appSystem.CorpSecret = req.CorpSecret
	appSystem.Name = req.Name
	appSystem.Desc = req.Desc
	appSystem.Type = req.Type
	appSystem.Index = req.Index
	appSystem.Icon = req.Icon
	appSystem.Visibility = req.Visibility
	appSystem.UserGroup = utils.IntSliceToString(req.UserGroup)
	appSystem.Parent = req.Parent
	appSystem.Rank = req.Rank
	err = s.appSystemDao.Update(c, appSystem)
	if err != nil {
		return err
	}
	err = s.appSystemCache.DeleteAppSystem(c, appSystem.AppKey)
	return nil
}

func (s *appSystemSvc) Delete(c *gin.Context, id uint) error {
	appSystem, err := s.appSystemDao.GetAppSystemByID(c, id)
	if appSystem == nil || errors.Is(err, gorm.ErrRecordNotFound) {
		return appError.NewErr("数据不存在")
	}
	if err != nil {
		return err
	}

	err = s.appSystemDao.Delete(c, id)
	if err != nil {
		return err
	}
	err = s.appSystemCache.DeleteAppSystem(c, appSystem.AppKey)
	return nil
}
func (s *appSystemSvc) GetAppSystemList(c *gin.Context, req api.AppSystemReq) ([]*api.AppSystemResp, int64, error) {
	var res []*api.AppSystemResp
	isChild := 0
	req.IsChild = &isChild
	data, total, err := s.appSystemDao.GetAppSystemList(c, req)
	if err != nil {
		return nil, 0, err
	}
	var parents []string
	unique := make(map[string]bool)
	for _, v := range data {
		if !unique[v.AppKey] {
			parents = append(parents, v.AppKey)
			unique[v.AppKey] = true
		}
	}
	var childReq api.AppSystemReq
	childReq.Page = 1
	childReq.PageSize = 1000
	childReq.Parents = parents
	childrenData, _, err := s.appSystemDao.GetAppSystemList(c, childReq)
	if err != nil {
		return nil, 0, err
	}
	userGroupMap, err := s.appSystemDao.GetUserGroupMap(c)
	if err != nil {
		return nil, 0, err
	}
	newData := append(data, childrenData...)
	for _, v := range newData {
		userGroup, _ := v.GetUserGroup()
		var userGroupNames []string
		for _, groupID := range userGroup {
			groupName, ok := userGroupMap[groupID]
			if ok {
				userGroupNames = append(userGroupNames, groupName)
			}
		}
		res = append(res, &api.AppSystemResp{
			ID:            v.ID,
			AppKey:        v.AppKey,
			JwtKey:        v.JwtKey,
			Parent:        v.Parent,
			CorpID:        v.CorpID,
			AgentID:       v.AgentID,
			CorpSecret:    v.CorpSecret,
			Name:          v.Name,
			Desc:          v.Desc,
			Type:          v.Type,
			Index:         v.Index,
			Icon:          v.Icon,
			Visibility:    v.Visibility,
			UserGroup:     userGroup,
			UserGroupName: userGroupNames,
			CreatedAt:     v.CreatedAt.String(),
			UpdatedAt:     v.UpdatedAt.String(),
		})
	}

	tree := s.BuildAppSystemTree(c, res)
	return tree, total, nil
}

// BuildAppSystemTree 构建应用系统树形结构
func (s *appSystemSvc) BuildAppSystemTree(c *gin.Context, appSystems []*api.AppSystemResp) []*api.AppSystemResp {
	// 创建一个ID到应用系统的映射
	appMap := make(map[string]*api.AppSystemResp)
	// 根节点列表
	var rootNodes []*api.AppSystemResp

	// 第一次遍历，创建映射
	for _, app := range appSystems {
		appMap[app.AppKey] = app
		app.Children = make([]*api.AppSystemResp, 0)
	}

	// 第二次遍历，构建树形结构
	for _, app := range appSystems {
		// 如果是根节点
		if app.Parent == "" {
			rootNodes = append(rootNodes, app)
		} else {
			// 如果是子节点，添加到父节点的子节点列表中
			if parent, exists := appMap[app.Parent]; exists {
				parent.Children = append(parent.Children, app)
			} else {
				// 如果找不到父节点，则作为根节点处理
				rootNodes = append(rootNodes, app)
			}
		}
	}

	return rootNodes
}

func (s *appSystemSvc) GetParentAppSystemKey(c *gin.Context, req api.AppSystemReq) ([]map[string]string, error) {
	res := make([]map[string]string, 0)
	isChild := 0
	req.IsChild = &isChild
	req.PageSize = 1000
	data, total, err := s.appSystemDao.GetAppSystemList(c, req)
	if err != nil {
		return nil, err
	}
	if total == 0 {
		return res, nil
	}
	for _, v := range data {
		res = append(res, map[string]string{
			"value": v.AppKey,
			"label": v.Name,
		})
	}
	return res, nil
}
