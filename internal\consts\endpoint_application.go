package consts

type EndpointApplicationState int

const (
	//ApplicationWaitingReview 待审核
	ApplicationWaitingReview EndpointApplicationState = 0
	//ApplicationApproved 设点审核通过，新店生成编码
	ApplicationApproved EndpointApplicationState = 100
	//ApplicationRejected 审核失败
	ApplicationRejected EndpointApplicationState = -100

	//ApplicationMaterialSupport 物料支持已经提交
	ApplicationMaterialSupport EndpointApplicationState = 200

	//ApplicationPostback 资料已经回传
	ApplicationPostback EndpointApplicationState = 300

	//核销初审（对应24年的writeOff，status=5）
	ApplicationInitialWriteOff         EndpointApplicationState = 400
	ApplicationInitialWriteOffRejected EndpointApplicationState = -400

	// 渠道确认（对应24年的channelAudit，status=6）
	ApplicationChannelConfirmation         EndpointApplicationState = 500
	ApplicationChannelConfirmationRejected EndpointApplicationState = -500

	//ApplicationRecordConfirmation入账确认（对应24年的RecordAudit，status=7）
	ApplicationRecordConfirmation         EndpointApplicationState = 600
	ApplicationRecordConfirmationRejected EndpointApplicationState = -600

	//ApplicationCompleted 已完成
	ApplicationCompleted EndpointApplicationState = 900
	//ApplicationCancelled 已终止
	ApplicationCancelled EndpointApplicationState = -900
)

var endpointApplicationStatusMap = map[EndpointApplicationState]string{
	ApplicationWaitingReview:               "待审核",
	ApplicationApproved:                    "审核通过",
	ApplicationRejected:                    "审核失败",
	ApplicationMaterialSupport:             "物料支持已经提交",
	ApplicationPostback:                    "已回传资料",
	ApplicationInitialWriteOff:             "核销初审",
	ApplicationInitialWriteOffRejected:     "核销初审失败",
	ApplicationChannelConfirmation:         "渠道确认",
	ApplicationChannelConfirmationRejected: "渠道确认失败",
	ApplicationRecordConfirmation:          "入账确认",
	ApplicationRecordConfirmationRejected:  "入账确认失败",
	ApplicationCompleted:                   "已完成",
	ApplicationCancelled:                   "已终止",
}

func (state EndpointApplicationState) String() string {
	return endpointApplicationStatusMap[state]
}
