package dao

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"marketing/internal/model"
	"marketing/internal/pkg/db"
	"marketing/internal/pkg/utils"
)

type ChannelDao interface {
	CreateChannel(c *gin.Context, channel *model.Channels) error
	DeleteChannel(c *gin.Context, id int) error
	UpdateChannel(c *gin.Context, id int, uMap map[string]interface{}) error
	GetAllChannel(c *gin.Context) (list []*model.Channels)
	GetChannelList(c *gin.Context, code string, pageNum, pageSize int) (list []*model.Channels, total int64)
	GetChannelById(c *gin.Context, id int) (channel *model.Channels)
	GetChannelByCode(c *gin.Context, code string) (channel *model.Channels)
	GetChannelByCodes(c *gin.Context, codes []string) (list []*model.Channels)
}

// ChannelsDaoImpl 实现 ChannelDao 接口
type ChannelsDaoImpl struct {
	db *gorm.DB
}

// NewChannelDao 创建 ChannelDao 实例
func NewChannelDao() ChannelDao {
	return &ChannelsDaoImpl{
		db: db.GetDB(""),
	}
}

func (d *ChannelsDaoImpl) CreateChannel(c *gin.Context, channel *model.Channels) error {
	return d.db.WithContext(c).Create(channel).Error
}

func (d *ChannelsDaoImpl) DeleteChannel(c *gin.Context, id int) error {
	return d.db.WithContext(c).Delete(&model.Channels{}, "id = ?", id).Error
}

func (d *ChannelsDaoImpl) UpdateChannel(c *gin.Context, id int, uMap map[string]interface{}) error {
	return d.db.WithContext(c).Model(&model.Channels{}).Where("id = ?", id).Updates(uMap).Error
}

func (d *ChannelsDaoImpl) GetAllChannel(c *gin.Context) (list []*model.Channels) {
	d.db.WithContext(c).Model(&model.Channels{}).Find(&list)

	for _, l := range list {
		l.CreateTime = utils.GetTimeStr(l.CreatedAt)
		l.UpdateTime = utils.GetTimeStr(l.UpdatedAt)
	}

	return
}

func (d *ChannelsDaoImpl) GetChannelList(c *gin.Context, code string, pageNum, pageSize int) ([]*model.Channels, int64) {
	query := d.db.WithContext(c).Model(&model.Channels{})

	if len(code) > 0 {
		query = query.Where("code like '%" + code + "%'")
	}

	data, total := utils.PaginateQueryV1(query, pageNum, pageSize, new([]*model.Channels))
	for i, p := range *data {
		(*data)[i].CreateTime = utils.GetTimeStr(p.CreatedAt)
		(*data)[i].UpdateTime = utils.GetTimeStr(p.UpdatedAt)
	}

	return *data, total
}

func (d *ChannelsDaoImpl) GetChannelById(c *gin.Context, id int) *model.Channels {
	var channel model.Channels
	err := d.db.WithContext(c).Model(&model.Channels{}).Where("id = ?", id).First(&channel).Error
	if err != nil {
		return nil
	}

	channel.CreateTime = utils.GetTimeStr(channel.CreatedAt)
	channel.UpdateTime = utils.GetTimeStr(channel.UpdatedAt)

	return &channel
}

func (d *ChannelsDaoImpl) GetChannelByCode(c *gin.Context, code string) *model.Channels {
	var channel model.Channels
	err := d.db.WithContext(c).Model(&model.Channels{}).Where("code = ?", code).First(&channel).Error
	if err != nil {
		return nil
	}

	channel.CreateTime = utils.GetTimeStr(channel.CreatedAt)
	channel.UpdateTime = utils.GetTimeStr(channel.UpdatedAt)

	return &channel
}

func (d *ChannelsDaoImpl) GetChannelByCodes(c *gin.Context, codes []string) (list []*model.Channels) {
	d.db.WithContext(c).Model(&model.Channels{}).Where("code in (?)", codes).Find(&list)
	return
}
