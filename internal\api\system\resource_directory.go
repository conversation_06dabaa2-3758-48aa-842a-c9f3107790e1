package system

type ResourceDirectoryReq struct {
	ID        uint   `json:"id"`
	Name      string `json:"name" binding:"required"`
	Code      string `json:"code" binding:"required"`
	ParentID  uint   `json:"parent_id"`
	CreatedBy uint   `json:"created_by"`
	UpdatedBy uint   `json:"updated_by"`
}

type ListResourceDirectoryReq struct {
	Name     string `json:"name"`
	Code     string ` json:"code"`
	ParentID *int64 `json:"parent_id"`
}

type ResourceDirectoryResp struct {
	ID        uint   `json:"id"`
	Name      string `json:"name"`
	Code      string `json:"code"`
	ParentID  uint   `json:"parent_id"`
	CreatedBy int    `json:"created_by"`
	UpdatedBy int    `json:"updated_by"`
	Level     int    `json:"level"`
	Rank      uint   `json:"rank"`
	Path      string `json:"path"`
	UpdatedAt string `json:"updated_at"`
	CreatedAt string `json:"created_at"`
}
