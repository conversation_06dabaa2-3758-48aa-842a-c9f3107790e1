package model

import (
	"gorm.io/gorm"
	"strings"
)

type AdminUserGroupV2 struct {
	gorm.Model
	Name       string `gorm:"unique;not null"`
	Slug       string `gorm:"unique;not null"`
	ResourceId uint   `gorm:"column:resource_id"`
	System     string `gorm:"column:system"`
}

func (AdminUserGroupV2) TableName() string {
	return "admin_user_group_v2"
}

func (a AdminUserGroupV2) GetSystem() []string {
	if a.System == "" {
		return []string{}
	}
	system := strings.Split(a.System, ",")
	return system
}
