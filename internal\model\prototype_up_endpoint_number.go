package model

import "time"

type PrototypeUpEndpointNumber struct {
	ID             int       `json:"id" gorm:"column:id"`
	Model          string    `json:"model" gorm:"column:model"`
	TopAgency      uint      `json:"top_agency" gorm:"column:top_agency"`
	EndpointNumber int64     `json:"endpoint_number" gorm:"column:endpoint_number"`
	CreatedAt      time.Time `json:"created_at" gorm:"column:created_at"`
	UpdatedAt      time.Time `json:"updated_at" gorm:"column:updated_at"`
}

func (PrototypeUpEndpointNumber) TableName() string {
	return "prototype_up_endpoint_number"
}
