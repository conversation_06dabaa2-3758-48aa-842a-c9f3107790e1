package dao

import (
	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
	"gorm.io/gorm"
	"marketing/internal/model"
	"marketing/internal/pkg/db"
	"marketing/internal/pkg/utils"
)

type UserAfterSalesDao interface {
	CreateUserAfterSales(c *gin.Context, user *model.UserAfterSalesDetails) error
	DeleteUserAfterSales(c *gin.Context, id int) error
	EditUserAfterSales(c *gin.Context, id int, userMap, adminUserMap, areaMap map[string]interface{}) error
	GetUserAfterSalesList(c *gin.Context, name string, pageNum, pageSize int) ([]*model.UserAfterSalesDetails, int64)
	GetUserAfterSalesById(c *gin.Context, id int) *model.UserAfterSalesDetails
	DataEncrypt(c *gin.Context)
}

// UserAfterSalesDaoImpl 实现 UserAfterSalesDao 接口
type UserAfterSalesDaoImpl struct {
	db *gorm.DB
}

// NewUserAfterSalesDao 创建 UserAfterSalesDao 实例
func NewUserAfterSalesDao() UserAfterSalesDao {
	return &UserAfterSalesDaoImpl{
		db: db.GetDB(""),
	}
}

func (d *UserAfterSalesDaoImpl) CreateUserAfterSales(c *gin.Context, param *model.UserAfterSalesDetails) error {
	tx := d.db.WithContext(c).Begin()

	user := &model.UserAfterSales{
		UserId:          param.UserId,
		EndpointId:      param.EndpointId,
		Status:          param.Status,
		AgencyType:      param.AgencyType,
		CompanyType:     param.CompanyType,
		ThroughTraining: param.ThroughTraining,
		Education:       param.Education,
		EntryTime:       param.EntryTime,
		Remark:          param.Remark,
		Deliverable:     param.Deliverable,
		Corporation:     param.Corporation,
	}

	// 加密的密钥
	key := viper.GetString("encrypt.encrypt_key")

	// 加密手机号
	if len(param.Phone) > 0 {
		encrypt, _ := utils.Base64AESCBCEncrypt([]byte(param.Phone), []byte(key))
		user.PhoneCode = encrypt
	}

	// 加密身份证号码
	if len(param.IdentityCard) > 0 {
		encrypt, _ := utils.Base64AESCBCEncrypt([]byte(param.IdentityCard), []byte(key))
		user.IdentityCardCode = encrypt
	}

	if dbErr := tx.Create(&user).Error; dbErr != nil {
		tx.Rollback()
		return dbErr
	}

	if dbErr := tx.Create(&model.AfterSalesDeliveryAddress{
		AfterSalesId: user.Id,
		Province:     param.Province,
		City:         param.City,
		District:     param.District,
		Address:      param.Address,
	}).Error; dbErr != nil {
		tx.Rollback()
		return dbErr
	}

	adminUserMap := make(map[string]interface{}, 0)
	adminUserMap["name"] = param.Name
	adminUserMap["phone"] = param.Phone

	if dbErr := tx.Model(&model.AdminUsers{}).Where("id = ?", param.UserId).Updates(adminUserMap).Error; dbErr != nil {
		tx.Rollback()
		return dbErr
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}

	return nil
}

func (d *UserAfterSalesDaoImpl) DeleteUserAfterSales(c *gin.Context, id int) error {
	tx := d.db.WithContext(c).Begin()

	if dbErr := tx.Delete(&model.UserAfterSales{}, "id = ?", id).Error; dbErr != nil {
		tx.Rollback()
		return dbErr
	}

	if dbErr := tx.Delete(&model.AfterSalesDeliveryAddress{}, "after_sales_id = ?", id).Error; dbErr != nil {
		tx.Rollback()
		return dbErr
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}

	return nil
}

func (d *UserAfterSalesDaoImpl) EditUserAfterSales(c *gin.Context, id int, userMap, adminUserMap, areaMap map[string]interface{}) error {
	tx := d.db.WithContext(c).Begin()

	if dbErr := tx.Model(&model.UserAfterSales{}).Where("id = ?", id).Updates(userMap).Error; dbErr != nil {
		tx.Rollback()
		return dbErr
	}

	if v, ok := userMap["user_id"]; ok {
		if dbErr := tx.Model(&model.AdminUsers{}).Where("id = ?", v).Updates(adminUserMap).Error; dbErr != nil {
			tx.Rollback()
			return dbErr
		}
	}

	if dbErr := tx.Model(&model.AfterSalesDeliveryAddress{}).Where("after_sales_id = ?", id).Updates(areaMap).Error; dbErr != nil {
		tx.Rollback()
		return dbErr
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}

	return nil
}

func (d *UserAfterSalesDaoImpl) GetUserAfterSalesList(c *gin.Context, name string, pageNum, pageSize int) ([]*model.UserAfterSalesDetails, int64) {
	query := d.db.WithContext(c).Model(&model.UserAfterSales{}).
		Joins("left join admin_users on user_id = admin_users.id").
		Joins("left join after_sales_delivery_address on user_after_sales.id = after_sales_delivery_address.after_sales_id").
		Select("user_after_sales.id,user_id,endpoint_id,user_after_sales.status,agency_type,company_type,phone_code,password," +
			"admin_users.name,identity_card_code,through_training,education,entry_time,remark,deliverable,corporation," +
			"province,city,district,after_sales_delivery_address.address,user_after_sales.created_at,user_after_sales.updated_at")

	if len(name) > 0 {
		query = query.Where("admin_users.name = ?", name)
	}

	if pageNum < 1 {
		pageNum = 1
	}

	if pageSize < 1 {
		pageSize = 20
	}

	var (
		total int64
		list  []*model.UserAfterSalesDetails
	)

	query.Count(&total)
	query.Offset((pageNum - 1) * pageSize).Limit(pageSize).Order("id").Find(&list)

	// 加密的密钥
	key := viper.GetString("encrypt.encrypt_key")

	for _, l := range list {
		l.EntryTimeStr = utils.GetTimeStrDay(l.EntryTime)
		l.CreateTime = utils.GetTimeStr(l.CreatedAt)
		l.UpdateTime = utils.GetTimeStr(l.UpdatedAt)
		if len(l.PhoneCode) > 0 {
			decrypt, _ := utils.Base64AESCBCDecrypt(l.PhoneCode, []byte(key))
			l.Phone = string(decrypt)
		}

		if len(l.IdentityCardCode) > 0 {
			decrypt, _ := utils.Base64AESCBCDecrypt(l.IdentityCardCode, []byte(key))
			l.IdentityCard = string(decrypt)
		}
	}

	return list, total
}

func (d *UserAfterSalesDaoImpl) GetUserAfterSalesById(c *gin.Context, id int) *model.UserAfterSalesDetails {
	var user model.UserAfterSalesDetails
	err := d.db.WithContext(c).Model(&model.UserAfterSales{}).
		Joins("left join admin_users on user_id = admin_users.id").
		Joins("left join after_sales_delivery_address on user_after_sales.id = after_sales_delivery_address.after_sales_id").
		Select("user_after_sales.id,user_id,endpoint_id,user_after_sales.status,agency_type,company_type,user_after_sales.phone,password,"+
			"admin_users.name,identity_card,through_training,education,entry_time,remark,deliverable,corporation,"+
			"province,city,district,after_sales_delivery_address.address,user_after_sales.created_at,user_after_sales.updated_at").
		Where("user_after_sales.id = ?", id).First(&user).Error
	if err != nil {
		return nil
	}

	user.EntryTimeStr = utils.GetTimeStrDay(user.EntryTime)
	user.CreateTime = utils.GetTimeStr(user.CreatedAt)
	user.UpdateTime = utils.GetTimeStr(user.UpdatedAt)

	// 加密的密钥
	key := viper.GetString("encrypt.encrypt_key")

	if len(user.PhoneCode) > 0 {
		decrypt, _ := utils.Base64AESCBCDecrypt(user.PhoneCode, []byte(key))
		user.Phone = string(decrypt)
	}

	if len(user.IdentityCardCode) > 0 {
		decrypt, _ := utils.Base64AESCBCDecrypt(user.IdentityCardCode, []byte(key))
		user.IdentityCard = string(decrypt)
	}

	return &user
}

func (d *UserAfterSalesDaoImpl) DataEncrypt(c *gin.Context) {
	key := viper.GetString("encrypt.encrypt_key")
	list := make([]*model.UserAfterSales, 0)
	d.db.WithContext(c).Select("id,phone,identity_card").Model(&model.UserAfterSales{}).Find(&list)
	for _, l := range list {
		uMap := make(map[string]interface{}, 0)

		if len(l.Phone) > 0 {
			phoneEncrypt, err := utils.Base64AESCBCEncrypt([]byte(l.Phone), []byte(key))
			if err == nil {
				uMap["phone_code"] = phoneEncrypt
			}
		}

		if len(l.IdentityCard) > 0 {
			identityCardEncrypt, err := utils.Base64AESCBCEncrypt([]byte(l.IdentityCard), []byte(key))
			if err == nil {
				uMap["identity_card_code"] = identityCardEncrypt
			}
		}

		d.db.WithContext(c).Model(&model.UserAfterSales{}).Where("id = ?", l.Id).Updates(uMap)
	}
}
