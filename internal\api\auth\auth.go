package auth

import (
	"marketing/internal/api/system"
)

// LoginReq 登录请求
type LoginReq struct {
	Username string `form:"username" binding:"required"`
	Password string `form:"password" binding:"required"`
}

// WcLoginReq 登录请求
type WcLoginReq struct {
	Code string `json:"code" form:"code" binding:"required"`
}

// TokenUserInfoResp 登录响应用户信息
type TokenUserInfoResp struct {
	ID           int64                       `json:"id"`
	Avatar       string                      `json:"avatar"`
	Username     string                      `json:"username"`
	Name         string                      `json:"name"`
	JobNumber    string                      `json:"jobNumber"`
	Phone        string                      `json:"phone"`
	Token        string                      `json:"token"`
	Expires      int64                       `json:"expires"`
	RefreshToken string                      `json:"refreshToken"`
	Roles        []string                    `json:"roles"`
	Permissions  []string                    `json:"permissions"`
	Menus        []*system.MenuItem          `json:"menus"`
	Apps         []*system.AppSystemMenuResp `json:"apps"`
}

type RefreshTokenReq struct {
	RefreshToken string `json:"refreshToken" binding:"required"`
}

type PhoneLoginReq struct {
	Phone string `json:"phone" form:"phone" binding:"required"`
	Code  string `json:"code" form:"code" binding:"required"`
}
