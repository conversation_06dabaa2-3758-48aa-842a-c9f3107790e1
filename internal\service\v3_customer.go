package service

import (
	"errors"
	"github.com/gin-gonic/gin"
	"marketing/internal/dao"
	"marketing/internal/model"
)

type V3CustomerSvc interface {
	GetCustomerList(c *gin.Context, channel, groupCode string, customerStatus int, name string, pageNum, pageSize int) ([]*model.CustomerInfo, int64)
	GetCustomerById(c *gin.Context, customerId int) *model.CustomerInfo
	EditCustomer(c *gin.Context, customerId int, channel string, groupId int, customerName, customerShortName string) error
}

type V3CustomerSvcImpl struct {
	customerRepo dao.V3CustomerDao
	channelRepo  dao.ChannelDao
}

func NewV3CustomerService(customerRepo dao.V3CustomerDao, channelRepo dao.ChannelDao) V3CustomerSvc {
	return &V3CustomerSvcImpl{
		customerRepo: customerRepo,
		channelRepo:  channelRepo,
	}
}

func (s *V3CustomerSvcImpl) GetCustomerList(c *gin.Context, channel, groupCode string, customerStatus int, name string, pageNum, pageSize int) ([]*model.CustomerInfo, int64) {
	list, total := s.customerRepo.GetCustomerList(c, channel, groupCode, customerStatus, name, pageNum, pageSize)

	channelCodes := make([]string, 0)
	parentGroupIds := make([]int, 0)

	for _, l := range list {
		channelCodes = append(channelCodes, l.Channel)
		parentGroupIds = append(parentGroupIds, l.GroupParentid)
	}

	channelList := s.channelRepo.GetChannelByCodes(c, channelCodes)
	for _, l := range list {
		for _, channelCode := range channelList {
			if l.Channel == channelCode.Code {
				l.ChannelName = channelCode.Name
				break
			}
		}
	}

	parentGroupList := s.customerRepo.GetCustomerGroupByGIds(c, parentGroupIds)
	for _, l := range list {
		for _, group := range parentGroupList {
			if l.GroupParentid == group.GroupId {
				l.GroupParentName = group.GroupName
				break
			}
		}
	}

	return list, total
}

func (s *V3CustomerSvcImpl) GetCustomerById(c *gin.Context, customerId int) *model.CustomerInfo {
	info := s.customerRepo.GetCustomerById(c, customerId)
	if info == nil {
		return nil
	}

	channel := s.channelRepo.GetChannelByCode(c, info.Channel)
	if channel != nil {
		info.ChannelName = channel.Name
	}

	g := s.customerRepo.GetCustomerGroupByGId(c, info.GroupParentid)
	if g != nil {
		info.GroupParentName = g.GroupName
	}

	return info
}

func (s *V3CustomerSvcImpl) EditCustomer(c *gin.Context, customerId int, channel string, groupId int, customerName, customerShortName string) error {
	if s.customerRepo.GetCustomerById(c, customerId) == nil {
		return errors.New("编辑金蝶用户:用户不存在")
	}

	if s.channelRepo.GetChannelByCode(c, channel) == nil {
		return errors.New("编辑金蝶用户:渠道类型不存在")
	}

	g := s.customerRepo.GetCustomerGroupByGId(c, groupId)
	if g == nil {
		return errors.New("编辑金蝶用户:分组不存在")
	}

	uMap := make(map[string]interface{})
	uMap["channel"] = channel
	uMap["group_code"] = g.GroupCode
	uMap["cust_name"] = customerName
	uMap["cust_short_name"] = customerShortName

	return s.customerRepo.EditCustomer(c, customerId, uMap)
}
