package model

import (
	"time"
)

// Channels 渠道
type Channels struct {
	ID         uint      `json:"id" gorm:"primaryKey;autoIncrement"` // ID
	Code       string    `json:"code" gorm:"code"`                   // 渠道码
	Name       string    `json:"name" gorm:"name"`                   // 渠道名称
	CreatedAt  time.Time `json:"-" gorm:"created_at"`                // CreatedAt 是分区的创建时间
	CreateTime string    `json:"create_time" gorm:"-"`               // 创建时间
	UpdatedAt  time.Time `json:"-" gorm:"updated_at"`                // UpdatedAt 是分区的修改时间
	UpdateTime string    `json:"update_time" gorm:"-"`               // 修改时间
}

func (Channels) TableName() string {
	return "agency_channels"
}
