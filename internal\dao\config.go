package dao

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type ConfigRepository interface {
	GetConfig(ctx *gin.Context, key string) (string, error)
}
type config struct {
	db *gorm.DB
}

func NewConfigRepository(db *gorm.DB) ConfigRepository {
	return &config{db: db}
}

func (c *config) GetConfig(ctx *gin.Context, key string) (string, error) {
	var result string
	err := c.db.WithContext(ctx).Table("config").Select("value").Where("`key` = ?", key).Scan(&result).Error
	if err != nil {
		return "", err
	}
	return result, nil
}
