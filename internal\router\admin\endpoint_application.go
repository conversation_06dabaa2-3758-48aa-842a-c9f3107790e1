package admin

import (
	"marketing/internal/dao"
	"marketing/internal/dao/endpoint"
	endpointApplicationDao "marketing/internal/dao/endpoint_application"
	repo "marketing/internal/dao/endpoint_application"
	"marketing/internal/dao/warranty"
	endpointApplicationHandler "marketing/internal/handler/admin/endpoint_application"
	"marketing/internal/pkg/db"
	"marketing/internal/service/endpoint_application"
	service "marketing/internal/service/endpoint_application"

	"github.com/gin-gonic/gin"
)

// EndpointApplicationRouter 终端政策路由
type EndpointApplicationRouter struct{}

// NewEndpointApplicationRouter 创建终端政策路由实例
func NewEndpointApplicationRouter() *EndpointApplicationRouter {
	return &EndpointApplicationRouter{}
}

// Register 注册终端政策路由
func (e *EndpointApplicationRouter) Register(r *gin.RouterGroup) {
	// 终端政策管理模块
	policyRouter := r.Group("/endpoint-policy")
	{
		var Db = db.GetDB()
		// 初始化 DAO, Service, and Controller
		policyDao := endpointApplicationDao.NewEndpointPolicyDao(Db)
		policyService := endpoint_application.NewEndpointPolicyService(policyDao)
		policyController := endpointApplicationHandler.NewEndpointPolicyHandler(policyService)

		// 终端政策的增删改查方法
		policyRouter.POST("", policyController.CreateEndpointPolicy)                   // 创建终端政策
		policyRouter.GET("", policyController.GetEndpointPolicyList)                   // 获取终端政策列表
		policyRouter.GET("/:id", policyController.GetEndpointPolicyByID)               // 根据ID获取终端政策
		policyRouter.PUT("/:id", policyController.UpdateEndpointPolicy)                // 更新终端政策
		policyRouter.PUT("/:id/enabled", policyController.UpdateEndpointPolicyEnabled) // 更新终端政策
		policyRouter.DELETE("/:id", policyController.DeleteEndpointPolicy)             // 删除终端政策

		//终端申请管理
		endpointApplyDao := repo.NewEndpointApplyDao(Db)
		endpointDao := endpoint.NewEndpointDao(Db)
		endpointTypeRepo := dao.NewEndpointTypeRepository(Db)
		configRepo := dao.NewConfigRepository(Db)
		subjectRepo := dao.NewGormTrainSubjectDao(Db)
		materialRepo := dao.NewMaterialDao()
		endpointImageDao := endpoint.NewEndpointImageDao(Db)
		warrantyRepo := warranty.NewWarrantyDao(Db)

		endpointApplyService := service.NewEndpointApplyService(endpointApplyDao, policyDao, endpointDao, endpointTypeRepo, configRepo, subjectRepo, materialRepo, endpointImageDao, warrantyRepo)
		applyHandler := endpointApplicationHandler.NewApplyHandler(endpointApplyService)
		applyRouter := r.Group("/endpoint-application")
		{
			applyRouter.PUT("/:id/audit", applyHandler.AuditEndpointApply)
			applyRouter.GET("/materials", applyHandler.GetMaterials)
			applyRouter.POST("/:id/material-support", applyHandler.MaterialSupport)
			applyRouter.POST("/:id/write-off", applyHandler.WriteOff) //核销初审
			applyRouter.POST("/:id/channel-confirmation", applyHandler.ChannelConfirmation)
			applyRouter.POST("/:id/record-confirmation", applyHandler.RecordConfirmation)
			applyRouter.GET("/latest-endpoint-image", applyHandler.LatestEndpointImage)
			applyRouter.GET("/sales-amount", applyHandler.SalesAmount)
		}
	}
}
