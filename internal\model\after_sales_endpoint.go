package model

import (
	"time"
)

type AfterSalesEndpoint struct {
	Id                int       `json:"id" gorm:"id"`                                     // id
	AgencyId          int       `json:"agency_id" gorm:"agency_id"`                       // 代理id
	Name              string    `json:"name" gorm:"name"`                                 // 名字
	Phone             string    `json:"phone" gorm:"phone"`                               // 手机号
	Address           string    `json:"address" gorm:"address"`                           // 地址
	BelongToTopAgency int       `json:"belong_to_top_agency" gorm:"belong_to_top_agency"` // 是否属于一级代理 0:不属于 1:属于
	Status            int       `json:"status" gorm:"status"`                             // 是否启用 0:未启用 1:启用
	TopAgencyId       int       `json:"top_agency_id" gorm:"top_agency_id"`               // 一级代理id
	TopAgencyName     string    `json:"top_agency_name" gorm:"-"`                         // 一级代理名称
	SecondAgencyId    int       `json:"second_agency_id" gorm:"second_agency_id"`         // 二级代理id
	SecondAgencyName  string    `json:"second_agency_name" gorm:"-"`                      // 二级代理名称
	CreatedAt         time.Time `json:"-" gorm:"created_at"`                              // 创建时间
	CreateTime        string    `json:"create_time" gorm:"-"`                             // 创建时间
	UpdatedAt         time.Time `json:"-" gorm:"updated_at"`                              // 修改时间
	UpdateTime        string    `json:"update_time" gorm:"-"`                             // 修改时间
}

func (AfterSalesEndpoint) TableName() string {
	return "after_sales_endpoint"
}
