package warranty

import (
	"fmt"
	"marketing/internal/model"
	"time"
)

type WarrantiesResp struct {
	WarrantiesInfo []*WarrantiesInfo `json:"warranties_info"`
	RepairInfo     []*RepairInfo     `json:"repair_info,omitempty"`
}

type WarrantiesInfo struct {
	Detail       WarrantyDetail        `json:"warranty_detail"`
	ReturnInfo   *WarrantyReturnInfo   `json:"warranty_return_info,omitempty"`
	ExchangeInfo *WarrantyExchangeInfo `json:"warranty_exchange_info,omitempty"`
}

type RepairInfo struct {
	Barcode            string                    `json:"barcode"`
	RepairBillMessage  string                    `json:"repair_bill_message,omitempty"`
	RepairOrderMessage string                    `json:"repair_order_message,omitempty"`
	RepairBill         []model.RepairBillDetails `json:"repair_bill,omitempty"`
	RepairOrderInfo    []RepairOrderInfo         `json:"repair_order_info,omitempty"`
}

type WarrantyDetail struct {
	// warranty
	ID             int        `json:"id"`
	Status         int8       `json:"status"`
	Salesman       string     `json:"salesman"`
	CustomerName   string     `json:"customer_name"`
	CustomerPhone  string     `json:"customer_phone"`
	ModelID        int        `json:"model_id"`
	ExtBarcode     string     `json:"ext_barcode"`
	Imei           string     `json:"imei"`
	Model          string     `json:"model"`
	BuyDate        *time.Time `json:"buy_date"`
	CreatedAt      *time.Time `json:"created_at"`
	UpdatedAt      *time.Time `json:"updated_at"`
	ActivatedAtOld time.Time  `json:"activated_at_old"`
	ActivatedAt    string     `json:"activated_at"`
	ProductDate    *time.Time `json:"product_date"`
	Barcode        string     `json:"barcode"`
	Number         string     `json:"number"`
	// endpoint
	Name    string `json:"name"`
	Address string `json:"address"`
	Manager string `json:"manager"`
	Phone   string `json:"phone"`
	// agency
	TopAgency    string `json:"top_agency"`
	SecondAgency string `json:"second_agency"`
	// order
	OrderTime *time.Time `json:"order_time"`
}

type WarrantyReturnInfo struct {
	Reason    string     `json:"reason"`
	CreatedAt *time.Time `json:"created_at"`
	// endpoint
	Name    string `json:"name"`
	Address string `json:"address"`
	Manager string `json:"manager"`
	Phone   string `json:"phone"`
}

type WarrantyExchangeInfo struct {
	BarcodeNew string     `json:"barcode_new"`
	Reason     string     `json:"reason"`
	CreatedAt  *time.Time `json:"created_at"`
	// endpoint
	Name    string `json:"name"`
	Address string `json:"address"`
	Manager string `json:"manager"`
	Phone   string `json:"phone"`
}

func (r *RepairInfo) FillRepairBillInfo(barcode string, bill []model.RepairBillDetails) {
	r.Barcode = barcode
	if len(bill) == 0 {
		r.RepairBillMessage = fmt.Sprintf("无相关条码外部寄修维修信息")
	} else {
		r.RepairBillMessage = fmt.Sprintf("查询到相关条码外部寄修维修信息")
		r.RepairBill = bill
	}
}

func (r *RepairInfo) FillRepairOrderInfo(barcode string, repairOrder []RepairOrderInfo) {
	r.Barcode = barcode
	if len(repairOrder) == 0 {
		r.RepairOrderMessage = fmt.Sprintf("无相关条码寄修系统维修信息")
	} else {
		r.RepairOrderMessage = fmt.Sprintf("查询到相关条码寄修系统维修信息")
		r.RepairOrderInfo = repairOrder
	}
}
