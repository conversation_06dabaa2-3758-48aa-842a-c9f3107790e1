// service/member_service.go
package service

import (
	"errors"
	"github.com/gin-gonic/gin"
	"log"
	"marketing/internal/dao"
	"time"
)

// service/member_service.go

// AdminUsersService 提供业务逻辑操作
type AdminUsersService struct {
	DAO *dao.EndpointAdminUsersDao
}

// GetMembers 根据条件查询成员信息
func (service *AdminUsersService) GetMembersbyCondition(params dao.AdminUserQueryParams, c *gin.Context) ([]dao.AdminUsers, int64, int, int, error) {

	// 可以在这里加入业务验证或其他处理
	members, total, page, pageSize, err := service.DAO.GetMembersByConditions(params, c)
	if err != nil {
		return nil, 0, 0, 0, err
	}

	return members, total, page, pageSize, nil

}

// 定义 AdminUsersService

// 创建成员
func (service *AdminUsersService) CreateMember(member *dao.AdminUsers) error {
	// 业务验证可以在此添加，比如检查是否已有相同的登录账号等
	if member.QwUserid == "" {
		return errors.New("Qw_UserId cannot be empty")
	}
	return service.DAO.CreateMember(member)
}

// 获取成员信息
func (service *AdminUsersService) GetMemberByID(id uint) (*dao.AdminUsers, error) {
	return service.DAO.GetMemberByID(id)
}

// 获取所有成员信息
func (service *AdminUsersService) GetAllMembers(c *gin.Context) ([]dao.AdminUsers, int64, int, int, error) {
	data, all, page, pagesize, err := service.DAO.GetAllMembers(c)
	if err != nil {
		return nil, 0, 0, 0, err
	}
	return data, all, page, pagesize, nil
}

// 更新成员信息
func (service *AdminUsersService) UpdateMember(member *dao.AdminUsers) error {
	// 检查成员是否存在
	existingMember, err := service.DAO.GetMemberByID(member.Id)
	if err != nil {
		return errors.New("member not found")
	}

	// 只有在status为0时才能恢复为1
	if member.Status == "0" && existingMember.Status == "0" {
		return errors.New("member is deleted, cannot be updated")
	}

	// 更新状态逻辑：如果status为0则是删除，恢复时status为1
	if member.Status == "1" && existingMember.Status == "0" {
		// 恢复时需要设置状态为1，并清空删除时间
		member.DeletedAt = time.Time{}
	}

	return service.DAO.UpdateMember(member)
}

// 软删除成员（延时操作）
func (service *AdminUsersService) SoftDeleteMember(id uint) error {
	// 延时删除，10秒后执行软删除
	go func() {
		time.Sleep(10 * time.Second)
		if err := service.DAO.SoftDeleteMember(id); err != nil {
			log.Printf("Error soft deleting member with ID %d: %v", id, err)
		}
	}()
	return nil
}

// 恢复成员
func (service *AdminUsersService) RestoreMember(id uint) error {
	return service.DAO.RestoreMember(id)
}

// 定时任务删除：物理删除成员
func (service *AdminUsersService) PhysicalDeleteMember(id uint) error {
	return service.DAO.PhysicalDeleteMember(id)
}

// MemberInfoService 提供业务逻辑操作

func (service *AdminUsersService) GetAllAgencyTrees() (res []dao.ResponseTree, err error) {
	data, err := dao.EndpointAdminusersDao.GetAllAgencyTrees()
	if err != nil {
		return nil, err
	}
	return data, nil
}
