package router

import (
	"github.com/gin-gonic/gin"
)

// RouterBuilder 路由构建器
// 提供流式API，简化路由构建过程
type RouterBuilder struct {
	routers []IRouter
	prefix  string
}

// NewRouterBuilder 创建路由构建器
func NewRouterBuilder(prefix string) *RouterBuilder {
	return &RouterBuilder{
		prefix:  prefix,
		routers: make([]IRouter, 0),
	}
}

// AddRouter 添加路由
func (b *RouterBuilder) AddRouter(router IRouter) *RouterBuilder {
	b.routers = append(b.routers, router)
	return b
}

// AddRouterWithMiddleware 添加带中间件的路由
func (b *RouterBuilder) AddRouterWithMiddleware(router IRouter, middlewares ...gin.HandlerFunc) *RouterBuilder {
	wrapper := NewMiddlewareWrapper(router, middlewares...)
	b.routers = append(b.routers, wrapper)
	return b
}

// AddRouterWithPath 添加带路径前缀的路由
func (b *RouterBuilder) AddRouterWithPath(router IRouter, path string) *RouterBuilder {
	wrapper := NewMiddlewareWrapper(router).WithPathPrefix(path)
	b.routers = append(b.routers, wrapper)
	return b
}

// AddRouterWithPathAndMiddleware 添加带路径前缀和中间件的路由
func (b *RouterBuilder) AddRouterWithPathAndMiddleware(router IRouter, path string, middlewares ...gin.HandlerFunc) *RouterBuilder {
	wrapper := NewMiddlewareWrapper(router, middlewares...).WithPathPrefix(path)
	b.routers = append(b.routers, wrapper)
	return b
}

// Build 构建路由组
func (b *RouterBuilder) Build() *RouterGroup {
	return &RouterGroup{
		BaseRouterGroup: NewBaseRouterGroup(b.prefix),
		routers:         b.routers,
	}
}

// RouterGroup 通用路由组
// 用于替代各种特定的路由组实现
type RouterGroup struct {
	*BaseRouterGroup
	routers []IRouter
}

// Register 注册所有路由
func (g *RouterGroup) Register(r *gin.RouterGroup) {
	for _, router := range g.routers {
		router.Register(r)
	}
}
