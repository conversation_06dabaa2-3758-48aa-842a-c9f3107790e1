package endpoint_application

import (
	api "marketing/internal/api/endpoint_application"
	"marketing/internal/handler"
	"marketing/internal/pkg/errors"
	service "marketing/internal/service/endpoint_application"
	userSvc "marketing/internal/service/system"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

type EndpointApplyHandler interface {
	CreateEndpointApply(c *gin.Context)
	UpdateEndpointApply(c *gin.Context)
	GetEndpointApplyList(c *gin.Context)
	PostbackEndpointApply(c *gin.Context)
	DownloadWriteOffTable(c *gin.Context)
}

type endpointApply struct {
	applyService service.EndpointApplyService
	userService  userSvc.AdminUserInterface
}

func NewEndpointApplyHandler(applyService service.EndpointApplyService, userService userSvc.AdminUserInterface) EndpointApplyHandler {
	return &endpointApply{
		applyService: applyService,
		userService:  userService,
	}
}

func (e endpointApply) CreateEndpointApply(c *gin.Context) {
	var req api.CreatedEndpointApplyReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	err := e.applyService.CreateEndpointApply(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c)
}

func (e endpointApply) UpdateEndpointApply(c *gin.Context) {
	id := cast.ToInt(c.Param("id"))
	if id <= 0 {
		handler.Error(c, errors.NewErr("id参数错误"))
		return
	}
	var req api.CreatedEndpointApplyReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	err := e.applyService.UpdateEndpointApply(c, id, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c)
}

func (e endpointApply) GetEndpointApplyList(c *gin.Context) {
	var req api.EndpointApplicationListReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	req.SetDefaults()
	//获取用户代理
	agency, err := e.userService.GetUserAgency(c, c.GetUint("uid"))
	if err != nil || agency == nil || agency.ID == 0 {
		handler.Error(c, errors.NewErr("用户代理不存在"))
		return
	}
	if agency.Level == 1 {
		req.TopAgency = int(agency.ID)
	} else {
		req.SecondAgency = int(agency.ID)
	}
	data, total, err := e.applyService.GetEndpointApplyList(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, data, total)
}

func (e endpointApply) PostbackEndpointApply(c *gin.Context) {
	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		handler.Error(c, errors.NewErr("id参数错误"))
		return
	}
	var req api.PostbackEndpointApplyReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	req.ID = id
	err := e.applyService.PostbackEndpointApply(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c)
}

func (e endpointApply) DownloadWriteOffTable(c *gin.Context) {
	id := cast.ToInt(c.Param("id"))
	if id <= 0 {
		handler.Error(c, errors.NewErr("id参数错误"))
		return
	}
	// 下载
	err := e.applyService.DownloadWriteOffTable(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}
}
