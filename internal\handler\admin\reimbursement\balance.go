package reimbursement

import (
	"crypto/rand"
	"encoding/hex"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/xuri/excelize/v2"
	api "marketing/internal/api/reimbursement"
	"marketing/internal/handler"
	"marketing/internal/pkg/errors"
	service "marketing/internal/service/reimbursement"
	"os"
)

type BalanceHandler interface {
	GetBalanceStandard(c *gin.Context)
	GetBalanceList(c *gin.Context)
	ImportBalanceStandard(c *gin.Context)
	ResetBalanceStandard(c *gin.Context) // 重置结算标准
}

type balanceHandler struct {
	service service.BalanceService
}

func NewBalanceHandler(service service.BalanceService) BalanceHandler {
	return &balanceHandler{service: service}
}

// GetBalanceStandard 获取结算标准
func (h *balanceHandler) GetBalanceStandard(c *gin.Context) {
	var req api.BalanceStandardReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, errors.NewErr("参数错误: "+err.Error()))
		return
	}
	// 调用服务获取结算标准
	standard, err := h.service.GetBalanceStandard(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, standard)
}

// GetBalanceList 获取结算列表
func (h *balanceHandler) GetBalanceList(c *gin.Context) {
	var req api.BalanceListReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, errors.NewErr("参数错误: "+err.Error()))
		return
	}

	// 调用服务层获取结算列表数据
	ret, err := h.service.GetBalanceList(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, ret)
}

// ImportBalanceStandard 导入结算标准
func (h *balanceHandler) ImportBalanceStandard(c *gin.Context) {
	policyID := c.PostForm("policy_id")
	if policyID == "" {
		handler.Error(c, errors.NewErr("请选择政策"))
		return
	}
	file, err := c.FormFile("excelFile")
	if err != nil {
		handler.Error(c, errors.NewErr("获取上传的文件失败"))
		return
	}

	// 检查文件大小是否为 0
	if file.Size == 0 {
		handler.Error(c, errors.NewErr("上传的 Excel 文件不能为空"))
		return
	}

	// 生成随机文件名
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		handler.Error(c, err)
		return
	}
	randomFileName := hex.EncodeToString(bytes) + ".xlsx"
	filePath := "./logs/" + randomFileName

	// 保存文件到本地临时目录
	if err := c.SaveUploadedFile(file, filePath); err != nil {
		handler.Error(c, err)
		return
	}

	// 读取 Excel 文件
	f, err := excelize.OpenFile(filePath)
	if err != nil {
		// 删除临时文件
		if removeErr := os.Remove(filePath); removeErr != nil {
			handler.Error(c, removeErr)
			return
		}
		handler.Error(c, err)
		return
	}
	defer func() {
		if closeErr := f.Close(); closeErr != nil {
			handler.Error(c, closeErr)
		}
		if removeErr := os.Remove(filePath); removeErr != nil {
			handler.Error(c, removeErr)
		}
	}()

	// 这里可以添加读取 Excel 文件内容的逻辑
	sheetName := f.GetSheetName(0)
	rows, err := f.GetRows(sheetName)
	if err != nil {
		handler.Error(c, err)
		return
	}

	var data []api.BalanceImport
	for i, row := range rows {
		if i == 0 { // 跳过标题行
			continue
		}
		if len(row) < 3 { // 检查是否有足够的列数据
			continue
		}

		// 将每一行数据存储到结构体
		importData := api.BalanceImport{
			Code:    row[0],
			Company: row[1],
			Balance: cast.ToFloat64(row[2]),
		}
		data = append(data, importData)
	}

	// 检查是否有数据
	if len(data) == 0 {
		handler.Error(c, errors.NewErr("Excel 文件中没有有效数据"))
		return
	}
	// 调用服务层导入结算标准
	err = h.service.ImportBalanceStandard(c, cast.ToInt(policyID), data)

	handler.Success(c, nil)
}

func (h *balanceHandler) ResetBalanceStandard(c *gin.Context) {
	var req api.BalanceResetRep
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, errors.NewErr("参数错误: "+err.Error()))
		return
	}
	if len(req.CompanyID) == 0 && req.ResetAll != 1 {
		handler.Error(c, errors.NewErr("请选择要重置的公司"))
		return
	}

	err := h.service.ResetBalanceStandard(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, "结算标准已重置")
}
