# 材料签收接口文档

## 接口描述
用于处理报销申请单的材料签收操作，支持促销产品和广告费用两种申请单类型。

## 请求信息

**请求URL:** `POST /api/admin/reimbursement/apply/material-sign-in`

**请求方法:** `POST`

## 请求参数

| 参数名 | 必传 | 参数类型 | 描述 |
|--------|------|----------|------|
| id | Y | string | 申请单ID |
| apply_order_type | Y | string | 申请单类型，可选值：promotional_products（促销产品）、advert_expense（广告费用） |

## 请求示例

### Form表单请求
```bash
curl -X POST "http://localhost:8080/api/admin/reimbursement/apply/material-sign-in" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "id=123&apply_order_type=promotional_products"
```

### JSON请求
```bash
curl -X POST "http://localhost:8080/api/admin/reimbursement/apply/material-sign-in" \
  -H "Content-Type: application/json" \
  -d '{
    "id": "123",
    "apply_order_type": "promotional_products"
  }'
```

## 返回示例

### 成功响应
```json
{
  "code": 200,
  "message": "签收成功",
  "data": null
}
```

### 失败响应

#### 参数错误
```json
{
  "code": 400,
  "message": "参数错误"
}
```

#### 申请单类型错误
```json
{
  "code": 400,
  "message": "申请单类型有误"
}
```

#### 申请单不存在
```json
{
  "code": 400,
  "message": "未找到此申请单，请确认"
}
```

#### 材料已签收
```json
{
  "code": 400,
  "message": "材料已签收，无需二次操作"
}
```

#### 系统错误
```json
{
  "code": 500,
  "message": "系统出错"
}
```

## 返回参数说明

| 参数名 | 类型 | 描述 |
|--------|------|------|
| code | int | 状态码，200表示成功 |
| message | string | 响应消息 |
| data | object | 响应数据，签收成功时为null |

## 业务逻辑

### 1. 参数验证
- 验证申请单ID是否存在
- 验证申请单类型是否为有效值（promotional_products 或 advert_expense）

### 2. 申请单检查
- 根据申请单类型查询对应的申请单详情
- 检查申请单是否存在
- 检查材料回寄状态，如果已签收（status=2）则不允许重复操作

### 3. 签收处理
- 获取申请单关联的汇总列表
- 根据汇总列表中的状态判断完成状态：
  - 如果没有有效订单：设置完成状态为1（已完成）
  - 如果存在未核销或核销中的订单（status=0或1）：设置完成状态为0（未完成）
  - 如果所有订单都已核销：设置完成状态为1（已完成）

### 4. 数据库更新
使用事务确保数据一致性：

#### 更新主表
- **促销产品表**：`reimbursement_promotional_products_list`
- **广告费用表**：`reimbursement_advert_expense_list`
- 更新字段：
  - `completion_status`：完成状态
  - `completion_time`：完成时间
  - `material_return_status`：材料回寄状态（设置为2-已签收）

#### 更新汇总表
- **汇总表**：`reimbursement_apply_order_summary`
- 根据订单状态分别处理：
  - 存在未完成订单：更新 `material_return_status` 为2
  - 所有订单已完成：同时更新 `material_return_status`、`completion_status`、`completion_time`

## 状态说明

### 材料回寄状态（material_return_status）
- `0`：未回寄
- `1`：已回寄
- `2`：已签收

### 完成状态（completion_status）
- `0`：未完成
- `1`：已完成

### 订单状态（status）
- `0`：未核销
- `1`：核销中
- `2`：已核销
- 负数：已作废

## 注意事项

1. **幂等性**：如果材料已经签收，接口会返回错误，不会重复处理
2. **事务性**：所有数据库操作在同一个事务中执行，确保数据一致性
3. **状态依赖**：签收操作会根据关联订单的核销状态自动判断完成状态
4. **权限控制**：需要管理员权限才能执行签收操作

## 错误码说明

| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| 400 | 参数错误 | 检查请求参数格式和必填项 |
| 400 | 申请单类型有误 | 确保apply_order_type为promotional_products或advert_expense |
| 400 | 未找到申请单 | 检查申请单ID是否正确 |
| 400 | 材料已签收 | 该申请单材料已经签收，无需重复操作 |
| 500 | 系统错误 | 联系技术支持检查系统状态 |

## 相关接口

- 获取申请单详情：用于查看申请单当前状态
- 申请单列表：用于查看所有待签收的申请单
- 核销状态查询：用于了解关联订单的核销进度
