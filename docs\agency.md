# 渠道管理 API 文档

## 1. 获取大区列表

##### 请求URL
- `/admin/agency/partitions`  # 改为 partitions

## 2. 新增大区

##### 请求URL
- `/admin/agency/partition-create`  # 改为 partition-create

## 3. 更新大区

##### 请求URL
- `/admin/agency/partition-update/:id`  # 改为 partition-update/:id

## 4. 删除大区

##### 请求URL
- `/admin/agency/partition-delete/:id`  # 改为 partition-delete/:id

## 5. 获取代理数据

##### 请求URL
- `/admin/agency/agency-data`  # 改为 agency-data

## 6. 获取代理用户

##### 请求URL
- `/admin/agency/agency-user-get`  # 改为 agency-user-get

## 7. 新增代理用户

##### 请求URL
- `/admin/agency/agency-user-create`  # 改为 agency-user-create

## 8. 获取公司用户

##### 请求URL
- `/admin/agency/employee-user-get`  # 改为 employee-user-get

## 9. 新增公司用户

##### 请求URL
- `/admin/agency/employee-user-create`  # 改为 employee-user-create

## 10. 更新公司用户

##### 请求URL
- `/admin/agency/employee-user-update/:uid`  # 改为 employee-user-update/:uid

## 11. 获取金蝶用户

##### 请��URL
- `/admin/agency/customer-list`  # 改为 customer-list

## 12. 更新金蝶用户

##### 请求URL
- `/admin/agency/customer-update`  # 改为 customer-update 