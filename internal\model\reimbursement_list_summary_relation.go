package model

type ReimbursementListSummaryRelation struct {
	ReimbursementListID    int `gorm:"type:int(11);not null;comment:'大单id';primaryKey"`
	ReimbursementSummaryID int `gorm:"type:int(11);not null;default:0;comment:'小单id';primaryKey"`
}

// TableName sets the insert table name for this struct type
func (ReimbursementListSummaryRelation) TableName() string {
	return "reimbursement_list_summary_relation"
}
