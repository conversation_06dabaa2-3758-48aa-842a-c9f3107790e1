package service

import (
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
	"github.com/xuri/excelize/v2"
	"marketing/internal/dao"
	"marketing/internal/model"
	"marketing/internal/pkg/utils"
)

type UserAfterSalesSvc interface {
	GetUserAfterSalesList(c *gin.Context, name string, pageNum, pageSize int) ([]*model.UserAfterSalesDetails, int64)
	GetUserAfterSalesById(c *gin.Context, id int) *model.UserAfterSalesDetails
	EditUserAfterSales(c *gin.Context, user *model.UserAfterSalesDetails) error
	DeleteUserAfterSales(c *gin.Context, id int) error
	ExportUserAfterSalesList(c *gin.Context, name string, pageNum, pageSize int) *excelize.File
	DataEncrypt(c *gin.Context)
}

type UserAfterSalesSvcImpl struct {
	agencyRepo         dao.AgencyDao
	endpointRepo       dao.AfterSalesEndpointDao
	userAfterSalesRepo dao.UserAfterSalesDao
}

func NewUserAfterSalesService(agencyRepo dao.AgencyDao, endpointRepo dao.AfterSalesEndpointDao, userAfterSalesRepo dao.UserAfterSalesDao) UserAfterSalesSvc {
	return &UserAfterSalesSvcImpl{
		agencyRepo:         agencyRepo,
		endpointRepo:       endpointRepo,
		userAfterSalesRepo: userAfterSalesRepo,
	}
}

func (s *UserAfterSalesSvcImpl) GetUserAfterSalesList(c *gin.Context, name string, pageNum, pageSize int) ([]*model.UserAfterSalesDetails, int64) {
	list, total := s.userAfterSalesRepo.GetUserAfterSalesList(c, name, pageNum, pageSize)

	endpointIds := make([]int, 0)
	rIds := make([]int, 0)
	for _, l := range list {
		endpointIds = utils.AddIntArrayValue(endpointIds, l.EndpointId)
		rIds = utils.AddIntArrayValue(rIds, l.Province)
		rIds = utils.AddIntArrayValue(rIds, l.City)
		rIds = utils.AddIntArrayValue(rIds, l.District)
	}

	endpointList := s.endpointRepo.GetAfterSalesEndpointByIds(c, endpointIds)
	regionList := dao.GetRegionsByIds(rIds)

	agencyIds := make([]int, 0)
	for _, endpoint := range endpointList {
		agencyIds = utils.AddIntArrayValue(agencyIds, endpoint.TopAgencyId)
		agencyIds = utils.AddIntArrayValue(agencyIds, endpoint.SecondAgencyId)
	}

	agencyList := s.agencyRepo.GetAgencyByIds(c, agencyIds)

	for _, l := range list {
		for _, endpoint := range endpointList {
			if l.EndpointId == endpoint.Id {
				for _, agency := range agencyList {
					if len(l.TopAgencyName) > 0 && len(l.SecondAgencyName) > 0 {
						break
					}

					if endpoint.TopAgencyId == int(agency.ID) {
						l.TopAgencyName = agency.Name
					}

					if endpoint.SecondAgencyId == int(agency.ID) {
						l.SecondAgencyName = agency.Name
					}
				}
				l.EndpointName = endpoint.Name
				break
			}
		}

		pName, cName, dName := "", "", ""
		for _, r := range regionList {
			if l.Province == r.RegionId {
				pName = r.RegionName
			}

			if l.City == r.RegionId {
				cName = r.RegionName
			}

			if l.District == r.RegionId {
				dName = r.RegionName
			}

			if len(pName) > 0 && len(cName) > 0 && len(dName) > 0 {
				break
			}
		}

		l.Address = pName + cName + dName + l.Address
	}

	return list, total
}

func (s *UserAfterSalesSvcImpl) GetUserAfterSalesById(c *gin.Context, id int) *model.UserAfterSalesDetails {
	user := s.userAfterSalesRepo.GetUserAfterSalesById(c, id)
	if user == nil {
		return nil
	}

	endpoint := s.endpointRepo.GetAfterSalesEndpointById(c, user.EndpointId)
	if endpoint != nil {
		user.EndpointName = endpoint.Name
		agencyList := s.agencyRepo.GetAgencyByIds(c, []int{endpoint.TopAgencyId, endpoint.SecondAgencyId})
		for _, agency := range agencyList {
			if agency.ID == uint(endpoint.TopAgencyId) {
				user.TopAgencyName = agency.Name
			}

			if agency.ID == uint(endpoint.SecondAgencyId) {
				user.SecondAgencyName = agency.Name
			}
		}
	}

	return user
}

func (s *UserAfterSalesSvcImpl) EditUserAfterSales(c *gin.Context, param *model.UserAfterSalesDetails) error {
	endpoint := s.endpointRepo.GetAfterSalesEndpointById(c, param.EndpointId)
	if endpoint == nil {
		return errors.New("编辑售后用户:终端不存在")
	}

	if param.Id == 0 {
		return s.AddUserAfterSales(c, param)
	}

	user := s.userAfterSalesRepo.GetUserAfterSalesById(c, param.Id)
	if user == nil {
		return errors.New("编辑售后用户:用户不存在")
	}

	// TODO 需要对区域参数继续校验

	userMap := make(map[string]interface{}, 0)
	adminUserMap := make(map[string]interface{}, 0)
	areaMap := make(map[string]interface{}, 0)

	userMap["endpoint_id"] = param.EndpointId
	userMap["status"] = param.Status
	userMap["agency_type"] = param.AgencyType
	userMap["company_type"] = param.CompanyType
	// 加密的密钥
	key := viper.GetString("encrypt.encrypt_key")
	// 加密手机号
	if len(param.Phone) > 0 {
		encrypt, _ := utils.Base64AESCBCEncrypt([]byte(param.Phone), []byte(key))
		userMap["phone_code"] = encrypt
	}
	// 加密身份证号码
	if len(param.IdentityCard) > 0 {
		encrypt, _ := utils.Base64AESCBCEncrypt([]byte(param.IdentityCard), []byte(key))
		userMap["identity_card_code"] = encrypt
	}
	userMap["through_training"] = param.ThroughTraining
	userMap["education"] = param.Education
	userMap["entry_time"] = param.EntryTime
	userMap["remark"] = param.Remark
	userMap["deliverable"] = param.Deliverable
	userMap["corporation"] = param.Corporation

	userMap["user_id"] = user.UserId
	adminUserMap["name"] = param.Name
	// adminUserMap["password"] = param.Password
	adminUserMap["phone"] = param.Phone

	areaMap["province"] = param.Province
	areaMap["city"] = param.City
	areaMap["district"] = param.District
	areaMap["address"] = param.Address

	return s.userAfterSalesRepo.EditUserAfterSales(c, user.Id, userMap, adminUserMap, areaMap)
}

func (s *UserAfterSalesSvcImpl) AddUserAfterSales(c *gin.Context, user *model.UserAfterSalesDetails) error {
	// TODO 需要对用户id进行校验
	return s.userAfterSalesRepo.CreateUserAfterSales(c, user)
}

func (s *UserAfterSalesSvcImpl) DeleteUserAfterSales(c *gin.Context, id int) error {
	return s.userAfterSalesRepo.DeleteUserAfterSales(c, id)
}

func (s *UserAfterSalesSvcImpl) ExportUserAfterSalesList(c *gin.Context, name string, pageNum, pageSize int) *excelize.File {
	list, _ := s.GetUserAfterSalesList(c, name, pageNum, pageSize)

	sheetName := "售后用户"

	// 新建一个excel文件,并添加表
	file := excelize.NewFile()
	_, _ = file.NewSheet(sheetName)

	// 设置所有列居中
	style, _ := file.NewStyle(&excelize.Style{
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
		Font: &excelize.Font{
			Size: 12,
		},
	})

	_ = file.SetCellValue(sheetName, "A1", "姓名")
	_ = file.SetCellStyle(sheetName, "A1", "A1", style)
	_ = file.SetColWidth(sheetName, "A", "A", 15.0)
	_ = file.SetCellValue(sheetName, "B1", "类别")
	_ = file.SetCellStyle(sheetName, "B1", "B1", style)
	_ = file.SetColWidth(sheetName, "B", "B", 20.0)
	_ = file.SetCellValue(sheetName, "C1", "终端名称")
	_ = file.SetCellStyle(sheetName, "C1", "C1", style)
	_ = file.SetColWidth(sheetName, "C", "C", 50.0)
	_ = file.SetCellValue(sheetName, "D1", "公司名称")
	_ = file.SetCellStyle(sheetName, "D1", "D1", style)
	_ = file.SetColWidth(sheetName, "D", "D", 30.0)
	_ = file.SetCellValue(sheetName, "E1", "支持寄修")
	_ = file.SetCellStyle(sheetName, "E1", "E1", style)
	_ = file.SetColWidth(sheetName, "E", "E", 12.0)
	_ = file.SetCellValue(sheetName, "F1", "寄修地址")
	_ = file.SetCellStyle(sheetName, "F1", "F1", style)
	_ = file.SetColWidth(sheetName, "F", "F", 50.0)
	_ = file.SetCellValue(sheetName, "G1", "联系电话")
	_ = file.SetCellStyle(sheetName, "G1", "G1", style)
	_ = file.SetColWidth(sheetName, "G", "G", 20.0)
	_ = file.SetCellValue(sheetName, "H1", "状态")
	_ = file.SetCellStyle(sheetName, "H1", "H1", style)
	_ = file.SetColWidth(sheetName, "H", "H", 15.0)

	for i, l := range list {
		index := i + 2

		deliverable, status := "否", "禁用"
		if l.Deliverable == 1 {
			deliverable = "是"
		}

		if l.Status == 1 {
			status = "启用"
		}

		_ = file.SetCellValue(sheetName, fmt.Sprintf("A%d", index), l.Name)
		_ = file.SetCellStyle(sheetName, fmt.Sprintf("A%d", index), fmt.Sprintf("A%d", index), style)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("B%d", index), l.CompanyType)
		_ = file.SetCellStyle(sheetName, fmt.Sprintf("B%d", index), fmt.Sprintf("B%d", index), style)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("C%d", index), l.EndpointName)
		_ = file.SetCellStyle(sheetName, fmt.Sprintf("C%d", index), fmt.Sprintf("C%d", index), style)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("D%d", index), l.Corporation)
		_ = file.SetCellStyle(sheetName, fmt.Sprintf("D%d", index), fmt.Sprintf("D%d", index), style)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("E%d", index), deliverable)
		_ = file.SetCellStyle(sheetName, fmt.Sprintf("E%d", index), fmt.Sprintf("E%d", index), style)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("F%d", index), l.Address)
		_ = file.SetCellStyle(sheetName, fmt.Sprintf("F%d", index), fmt.Sprintf("F%d", index), style)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("G%d", index), l.Phone)
		_ = file.SetCellStyle(sheetName, fmt.Sprintf("G%d", index), fmt.Sprintf("G%d", index), style)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("H%d", index), status)
		_ = file.SetCellStyle(sheetName, fmt.Sprintf("H%d", index), fmt.Sprintf("H%d", index), style)
	}

	// 这一步是删除默认创建的Sheet1表
	_ = file.DeleteSheet("Sheet1")

	return file
}

func (s *UserAfterSalesSvcImpl) DataEncrypt(c *gin.Context) {
	s.userAfterSalesRepo.DataEncrypt(c)
}
