package admin

import (
	handler "marketing/internal/handler/admin/report"
	service "marketing/internal/service/report"

	"github.com/gin-gonic/gin"
)

type ReportRouter struct {
	service service.LearningRoomService
}

func NewReportRouter(service service.LearningRoomService) *ReportRouter {
	return &ReportRouter{
		service: service,
	}
}

func (a *ReportRouter) Register(r *gin.RouterGroup) {
	learningRoomHandler := handler.NewLearningRoomHandler(a.service)
	learningRoomRouter := r.Group("/learning-room")
	{
		learningRoomRouter.GET("", learningRoomHandler.GetLearningRoomReport)
	}
}
