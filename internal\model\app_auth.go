package model

import (
	"time"
)

// AppAuth API应用授权表
type AppAuth struct {
	ID          int       `gorm:"primaryKey;autoIncrement;column:id" json:"id"`
	AppID       string    `gorm:"uniqueIndex;size:255;not null;column:appid" json:"appid"`
	AppKey      string    `gorm:"size:255;not null;column:appkey" json:"appkey"`
	Platform    string    `gorm:"size:255;default:'';column:platform" json:"platform"`
	Name        string    `gorm:"size:255;not null;default:'';column:name" json:"name"`
	Description string    `gorm:"size:255;default:'';column:description" json:"description"`
	Status      int       `gorm:"not null;default:0;column:status" json:"status"`
	CreatedAt   time.Time `gorm:"not null;default:CURRENT_TIMESTAMP;column:created_at" json:"created_at"`
	UpdatedAt   time.Time `gorm:"not null;default:'0000-00-00 00:00:00';column:updated_at" json:"updated_at"`
}

// TableName 指定表名
func (AppAuth) TableName() string {
	return "app_auth"
}
