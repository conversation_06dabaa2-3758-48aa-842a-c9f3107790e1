package endpoint

import (
	"errors"
	"fmt"
	"github.com/qichengzx/coordtransform"
	"github.com/spf13/cast"
	api "marketing/internal/api/endpoint"
	adminUserdao "marketing/internal/dao/admin_user"
	dao "marketing/internal/dao/endpoint"
	appError "marketing/internal/pkg/errors"

	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type InfoApplyService interface {
	Lists(c *gin.Context, param *api.GetEndpointInfoApplyReq) ([]*api.ListEndpointInfoApplyRes, int64, error)
	Audit(c *gin.Context, req api.AuditInfoApplyReq) error
}

type infoApply struct {
	db                *gorm.DB
	endpointDao       dao.EndpointDao
	endpointInfoApply dao.InfoApplyDao
	adminUserDao      adminUserdao.UserDao
}

func NewEndpointInfoApplyService(db *gorm.DB, endpointDao dao.EndpointDao, infoApplyDao dao.InfoApplyDao, adminUserDao adminUserdao.UserDao) InfoApplyService {
	return &infoApply{
		db:                db,
		endpointDao:       endpointDao,
		endpointInfoApply: infoApplyDao,
		adminUserDao:      adminUserDao,
	}
}

func (s *infoApply) Lists(c *gin.Context, param *api.GetEndpointInfoApplyReq) ([]*api.ListEndpointInfoApplyRes, int64, error) {
	var list []*api.ListEndpointInfoApplyRes
	var total int64

	// 如果 param.Month 为空，设置为当前年月
	if param.Month == "" {
		currentTime := time.Now()
		param.Month = currentTime.Format("2006-01")
	}

	selectFields := []string{
		"a.id",
		"ta.name as top_agency_name",
		"COALESCE(sa.name, '直营') as second_agency_name",
		"e.name",
		"e.address as endpoint_address",
		"e.code",
		"e.created_at as endpoint_time",
		"e.id as endpoint_id",
		"a.updated_at",
		"a.status",
		"a.manual_review",
		"a.snapshot",
		"a.manager",
		"a.phone",
		"a.address",
		"a.audit_user_id",
		"a.audit_time",
		"a.endpoint_name",
		"a.longitude",
		"a.latitude",
		"a.channel_level",
		"a.audit_opinion",
		"a.province_code",
		"a.city_code",
		"a.district_code",
		"a.address",
	}

	// 构建基础查询
	query := s.db.WithContext(c).Table("endpoint as e").
		Select(selectFields).
		Joins("LEFT JOIN agency as ta ON e.top_agency = ta.id").
		Joins("LEFT JOIN agency as sa ON e.second_agency = sa.id")

	// 处理日期条件的连接
	joinClause := "a.endpoint_id = e.id"
	if param.Month != "" {
		startDate := param.Month + "-01"
		endDate := param.Month + "-31" // 简化处理，实际应该计算月末
		joinClause += fmt.Sprintf(" AND a.created_at BETWEEN '%s' AND '%s'", startDate, endDate)
	}
	query = query.Joins("LEFT JOIN endpoint_info_apply as a ON " + joinClause)

	// 添加基础条件
	query = query.Where("e.status = 1 OR a.id IS NOT NULL")

	// 添加筛选条件
	if param.Name != "" {
		query = query.Where("e.name LIKE ?", "%"+param.Name+"%")
	}
	if param.Code != "" {
		query = query.Where("e.code LIKE ?", "%"+param.Code+"%")
	}
	if param.TopAgency != 0 {
		query = query.Where("e.top_agency = ?", param.TopAgency)
	}
	if param.SecondAgency != 0 {
		if param.SecondAgency == -1 {
			query = query.Where("e.second_agency = 0")
		} else {
			query = query.Where("e.second_agency = ?", param.SecondAgency)
		}
	}

	// 处理状态筛选
	if param.AuditStatus != "" {
		switch param.AuditStatus {
		case "to_audit":
			query = query.Where("a.status = ?", "to_audit")
		case "manual_review_rejected":
			query = query.Where("a.status = ? AND a.manual_review = ?", "rejected", 1)
		case "manual_review_approved":
			query = query.Where("a.status = ? AND a.manual_review = ?", "approved", 1)
		case "auto_approved":
			query = query.Where("a.status = ? AND a.manual_review = ?", "approved", 0)
		}
	}

	// 处理更新状态
	if param.Status != "" {
		switch param.Status {
		case "updated":
			query = query.Where("a.id IS NOT NULL")
		case "not_update":
			query = query.Where("a.id IS NULL")
		}
	}

	// 处理创建时间范围
	if param.StartTime != "" {
		query = query.Where("e.created_at >= ?", param.StartTime)
	}
	if param.EndTime != "" {
		query = query.Where("e.created_at <= ?", param.EndTime)
	}

	// Get the total count
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 排序
	query = query.Order("a.updated_at DESC, e.id")

	// 分页
	offset := (param.Page - 1) * param.PageSize
	query = query.Offset(offset).Limit(param.PageSize).Order("id ASC")

	// Execute the query
	err = query.Scan(&list).Error
	if err != nil {
		return nil, 0, err
	}

	auditUserIdsMap := make(map[uint]struct{})
	for _, v := range list {
		if v.AuditUserID > 0 {
			auditUserIdsMap[v.AuditUserID] = struct{}{}
		}
	}
	// 取所有用户信息
	var auditUserIds []uint
	for id := range auditUserIdsMap {
		auditUserIds = append(auditUserIds, id)
	}
	users, _ := s.adminUserDao.GetByIDs(c, auditUserIds)

	for _, item := range list {
		item.AgencyName = item.TopAgencyName + "-" + item.SecondAgencyName
		//处理审核人
		if item.AuditUserID > 0 {
			for _, v := range users {
				if item.AuditUserID == v.ID {
					item.AuditUser = v.Name
				}
			}

		}
	}

	return list, total, nil
}

func (s *infoApply) Audit(c *gin.Context, req api.AuditInfoApplyReq) error {
	apply, err := s.endpointInfoApply.GetByID(c, req.ID)
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return appError.NewErr("申请不存在")
	}
	if err != nil {
		return err
	}

	if apply.Status != "to_audit" {
		return appError.NewErr("该申请已审核")
	}

	updateData := map[string]interface{}{
		"status":        req.Status,
		"audit_user_id": c.GetUint("uid"),
		"audit_time":    time.Now(),
		"audit_opinion": req.AuditOpinion,
		"manual_review": 1,
		"synchronized":  0,
	}

	return s.db.Transaction(func(tx *gorm.DB) error {
		if err := s.endpointInfoApply.Update(c, req.ID, updateData); err != nil {
			return err
		}

		if req.Status == "approved" {
			// 更新终端信息
			endpoint, err := s.endpointDao.GetEndpointByID(c, apply.EndpointID)
			if err != nil {
				return err
			}

			// 转换经纬度
			lat, lng := coordtransform.GCJ02toBD09(apply.Latitude, apply.Longitude)
			latStr := cast.ToString(lat)
			lngStr := cast.ToString(lng)
			endpoint.Province = int(apply.ProvinceCode)
			endpoint.City = int(apply.CityCode)
			endpoint.District = int(apply.DistrictCode)
			endpoint.Address = apply.Address
			endpoint.Lat = cast.ToString(apply.Latitude)
			endpoint.Lng = cast.ToString(apply.Longitude)
			endpoint.Blat = &latStr
			endpoint.Blng = &lngStr
			endpoint.Phone = &apply.Phone
			endpoint.Manager = &apply.Manager

			if _, err = s.endpointDao.UpdateEndpoint(c, endpoint); err != nil {
				return err
			}
		}

		return nil
	})
}
