package third_party

import "marketing/internal/pkg/types"

// WarrantyThirdPartyResponse 第三方接口返回的保修信息结构
type WarrantyThirdPartyResponse struct {
	Barcode          string           `json:"barcode"`
	Number           string           `json:"number"`
	Imei             string           `json:"imei"`
	Salesman         string           `json:"salesman"`
	CustomerSex      string           `json:"customer_sex"`
	CustomerName     string           `json:"customer_name"`
	CustomerPhone    string           `json:"customer_phone"`
	CustomerAddr     string           `json:"customer_addr"`
	StudentName      string           `json:"student_name"`
	StudentSchool    string           `json:"student_school"`
	StudentSex       string           `json:"student_sex"`
	StudentGrade     string           `json:"student_grade"`
	StudentBirthday  string           `json:"student_birthday"`
	BuyDate          types.CustomTime `json:"buy_date"`
	ProductDate      types.CustomTime `json:"product_date"`
	Model            string           `json:"model"`
	PurchaseWay      string           `json:"purchase_way"`
	Recommender      string           `json:"recommender"`
	RecommenderPhone string           `json:"recommender_phone"`
}

type SendCaptchaRequest struct {
	Phone    string `form:"sendNum" json:"sendNum" binding:"required"`
	Template string `form:"sendContent" json:"sendContent" binding:"required"`
}

type QueryRepairProgressRequest struct {
	Phone   string `form:"phone" json:"phone"`
	Keyword string `form:"keyword" json:"keyword" binding:"required"` // 关键字可以是手机号、寄修单号后六位
}
