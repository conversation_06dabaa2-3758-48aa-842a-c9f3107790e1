package base

import (
	"marketing/internal/handler"
	"marketing/internal/service/base"

	"github.com/gin-gonic/gin"
)

type WeComHandler interface {
	GetJsapiTicket(c *gin.Context)
}

type weComHandler struct {
	wecomSvc base.WeComService
}

func NewWeComHandler(wecomSvc base.WeComService) WeComHandler {
	return &weComHandler{
		wecomSvc: wecomSvc,
	}
}

// GetJsapiTicket 获取企业微信jsapi_ticket
func (h *weComHandler) GetJsapiTicket(c *gin.Context) {
	ticket, err := h.wecomSvc.GetJsapiTicket(c)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, ticket)
}
