package base

import (
	"github.com/gin-gonic/gin"
	api "marketing/internal/api/machine"
	"marketing/internal/dao"
	"marketing/internal/handler"
	"marketing/internal/pkg/db"
	appError "marketing/internal/pkg/errors"
	"marketing/internal/service/machine"
)

type MachineTypeHandler interface {
	MachineTypeList(c *gin.Context)
}

type machineTypeHandler struct {
	machineTypeSvc machine.TMachineTypeSvc
}

func NewMachineHandler() MachineTypeHandler {
	modelCategoryDao := dao.NewModelCategoryDao()
	machineTypeDao := dao.NewMachineTypeDao()
	machineTypeRelationDao := dao.NewMachineTypeRelationDao(db.GetDB())
	machineTypeService := machine.NewMaterialService(machineTypeDao, modelCategoryDao, machineTypeRelationDao)
	return &machineTypeHandler{machineTypeSvc: machineTypeService}
}

func (r *machineTypeHandler) MachineTypeList(c *gin.Context) {
	var req api.MachineTypeReq
	// 解析 JSON 数据
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, appError.NewErr("请求数据格式错误，不是有效的 JSON 格式"))
		return
	}
	data, err := r.machineTypeSvc.GetListNoPage(c, req)
	if err != nil {
		return
	}

	handler.Success(c, data)
}
