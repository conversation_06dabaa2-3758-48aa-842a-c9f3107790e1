# 题库管理 API 文档

## 简介
题库管理模块提供了试卷、作业本和App更新历史的管理功能，包括Excel文件上传、数据查询等操作。

## API 接口列表

### 1. 上传试卷和作业本Excel文件

**请求URL:** `/admin/notice/tiku/upload`

**请求方法:** `POST`

**参数:**

| 参数名 | 是否必需 | 类型 | 描述 |
|--------|----------|------|------|
| file | 必需 | file | Excel文件，需要包含两个工作表：试卷和作业本 |

**Excel格式要求:**
- 第一个工作表（试卷）表头：序号、试卷名称、版本、年级、课程、省、市、区/县、学校、类型、上线日期
- 第二个工作表（作业本）表头：序号、教辅书名、课程、版本、年级、系列、出版社、出版次数、印刷次数、条形码、地区、上线日期

**返回示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "affected_rows": 150,
    "message": "更新成功"
  }
}
```

**返回参数说明:**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| affected_rows | int | 成功插入的记录数 |
| message | string | 操作结果消息 |

**注意事项:**
- Excel文件必须包含两个工作表
- 表头必须严格按照要求格式
- 日期格式必须为Excel标准日期格式

---

### 2. 获取教材类型选项

**请求URL:** `/admin/notice/tiku/textbook/options`

**请求方法:** `GET`

**参数:** 无

**返回示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "name": "双师直播课",
      "value": "sszb_course"
    },
    {
      "name": "名师辅导班",
      "value": "msfd_course"
    },
    {
      "name": "真题试卷",
      "value": "paper"
    },
    {
      "name": "作业本教辅",
      "value": "homework"
    }
  ]
}
```

**返回参数说明:**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| name | string | 显示名称 |
| value | string | 选项值 |

---

### 3. 获取教材历史记录

**请求URL:** `/admin/notice/tiku/textbook/histories`

**请求方法:** `GET`

**参数:**

| 参数名 | 是否必需 | 类型 | 描述 |
|--------|----------|------|------|
| type | 必需 | string | 教材类型，可选值：sszb_course, msfd_course, paper, homework |
| page | 可选 | int | 页码，默认为1 |
| page_size | 可选 | int | 每页数量，默认为20 |

**返回示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "updated_at": "2023-12-01",
      "items": [
        {
          "subject": "数学",
          "grades": ["一年级", "二年级"],
          "name": "小学数学练习册"
        }
      ]
    }
  ]
}
```

**返回参数说明:**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| updated_at | string | 更新日期 |
| items | array | 该日期的记录列表 |
| subject | string | 科目 |
| grades | array/string | 年级信息 |
| name | string | 名称 |

---

### 4. 获取App选项

**请求URL:** `/admin/notice/tiku/app/options`

**请求方法:** `GET`

**参数:** 无

**返回示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "value": "com.example.app",
      "text": "示例应用"
    }
  ]
}
```

**返回参数说明:**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| value | string | 包名 |
| text | string | 应用名称 |

---

### 5. 获取App更新历史

**请求URL:** `/admin/notice/tiku/app/histories`

**请求方法:** `GET`

**参数:**

| 参数名 | 是否必需 | 类型 | 描述 |
|--------|----------|------|------|
| pkg_name | 必需 | string | 应用包名 |
| page | 可选 | int | 页码，默认为1 |
| page_size | 可选 | int | 每页数量，默认为20 |

**返回示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "data": [
      {
        "app_name": "示例应用",
        "version_name": "1.0.0",
        "update_content": "修复了一些问题",
        "devices": "Android",
        "updated_at": "2023-12-01T10:00:00Z"
      }
    ],
    "page": 1,
    "page_size": 20
  }
}
```

**返回参数说明:**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| app_name | string | 应用名称 |
| version_name | string | 版本名称 |
| update_content | string | 更新内容 |
| devices | string | 设备信息 |
| updated_at | string | 更新时间 |

---

## 错误码说明

| 错误码 | 描述 |
|--------|------|
| 400 | 请求参数错误 |
| 500 | 服务器内部错误 |

## 注意事项

1. 所有接口都需要管理员权限
2. Excel上传接口支持的最大文件大小请参考服务器配置
3. 日期格式统一使用ISO 8601标准
4. 分页查询的页码从1开始
