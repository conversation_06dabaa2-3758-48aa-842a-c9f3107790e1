package operation

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"marketing/internal/model"
	"marketing/internal/pkg/db"
)

type OpViewDao interface {
	GetOpArticleViewInfos(c *gin.Context, aids []uint, target int) (list []*model.OpArticleViewCountInfo)
}

// OpViewDaoImpl 实现 OpViewDao 接口
type OpViewDaoImpl struct {
	db *gorm.DB
}

// NewOpViewDao 创建 OpViewDao 实例
func NewOpViewDao() OpViewDao {
	return &OpViewDaoImpl{
		db: db.GetDB(""),
	}
}

func (d *OpViewDaoImpl) GetOpArticleViewInfos(c *gin.Context, aids []uint, target int) (list []*model.OpArticleViewCountInfo) {
	d.db.WithContext(c).Model(&model.OpView{}).
		Where("target = ? and target_id in (?)", target, aids).
		Select("target_id,count(*) num").
		Group("target_id").
		Find(&list)
	return
}
