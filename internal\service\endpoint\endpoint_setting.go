package endpoint

import (
	"errors"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	api "marketing/internal/api/endpoint"
	dao "marketing/internal/dao/endpoint"
	appError "marketing/internal/pkg/errors"
)

type SettingService interface {
	List(c *gin.Context, param api.ListEndpointSettingReq) ([]*api.ListEndpointSettingRes, int64, error)
	Update(c *gin.Context, id uint, param *api.UpdateEndpointSettingReq) error
}

type settingService struct {
	dao dao.SettingDao
}

func NewSettingService(dao dao.SettingDao) SettingService {
	return &settingService{dao: dao}
}

func (s *settingService) List(c *gin.Context, param api.ListEndpointSettingReq) ([]*api.ListEndpointSettingRes, int64, error) {
	return s.dao.List(c, param)
}

// Update 更新终端设置
func (s *settingService) Update(c *gin.Context, id uint, param *api.UpdateEndpointSettingReq) error {
	//判断数据是否存在
	_, err := s.dao.GetByID(c, id)
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return appError.NewErr("数据不存在")
	}
	if err != nil {
		return err
	}
	updateData := map[string]interface{}{
		"prototype_limit":           param.PrototypeLimit,
		"prototype_frequency_limit": param.PrototypeFrequencyLimit,
	}
	return s.dao.Update(c, id, updateData)
}
