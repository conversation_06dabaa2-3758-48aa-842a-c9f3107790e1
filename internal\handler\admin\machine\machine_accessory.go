package machine

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/handler"
	"marketing/internal/pkg/e"
	"marketing/internal/pkg/errors"
	"marketing/internal/service/machine"
)

type TMachineAccessory struct {
	svc machine.TMachineAccessorySvcInterface
}

func NewMachineAccessory(svc machine.TMachineAccessorySvcInterface) *TMachineAccessory {
	return &TMachineAccessory{
		svc: svc,
	}
}

func (m *TMachineAccessory) MachineAccessoryList(c *gin.Context) {
	list := m.svc.GetAllMachineAccessory(c)
	handler.Success(c, gin.H{
		"list": list,
	})
}

func (m *TMachineAccessory) RefreshMachineAccessory(c *gin.Context) {
	list := m.svc.RefreshMachineAccessory(c)
	handler.Success(c, gin.H{
		"list": list,
	})
}

func (m *TMachineAccessory) EditMachineAccessory(c *gin.Context) {
	id := e.ReqParamInt(c, "id", -1)
	pid := e.ReqParamInt(c, "pid")
	title := e.ReqParamStr(c, "title")
	description := e.ReqParamStr(c, "description")

	if id != -1 {
		err := m.svc.EditMachineAccessory(c, id, pid, title, description)
		if err != nil {
			handler.Error(c, errors.NewErr(err.Error()))
			return
		}
	} else {
		err := m.svc.AddMachineAccessory(c, pid, title, description)
		if err != nil {
			handler.Error(c, errors.NewErr(err.Error()))
			return
		}
	}

	handler.Success(c, gin.H{})
}

func (m *TMachineAccessory) DeleteMachineAccessory(c *gin.Context) {
	id := e.ReqParamInt(c, "id")
	err := m.svc.DelMachineAccessory(c, id)
	if err != nil {
		handler.Error(c, errors.NewErr("删除物料标签失败"))
		return
	}

	handler.Success(c, gin.H{})
}
