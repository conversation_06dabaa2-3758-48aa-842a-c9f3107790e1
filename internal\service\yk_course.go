package service

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/api/youke"
	"marketing/internal/dao"
	"marketing/internal/model"
)

type YkCourseService interface {
	LiveList(c *gin.Context, param youke.LiveListReq) ([]youke.LiveList, int64, error)
}

type GormYkCourseService struct {
	dao dao.YkCourseDao
}

func (g *GormYkCourseService) LiveList(c *gin.Context, param youke.LiveListReq) ([]youke.LiveList, int64, error) {
	//查询符合条件的课程
	list, total, err := g.dao.LiveList(c, param)
	if err != nil {
		return nil, 0, err
	}
	//将课程列表转换为domain对象
	lists := make([]youke.LiveList, len(list))
	for k, course := range list {
		lists[k].Course = g.toDomain(course)
	}
	//走缓存查询教师,课程数量,学员数量,课程时长
	return lists, total, nil
}
func (g *GormYkCourseService) toDomain(course model.YkCourse) youke.YKCourse {
	return youke.YKCourse{
		ID:          course.ID,
		Name:        course.Name,
		Description: course.Description,
		Cover:       course.Cover,
		Price:       course.Price,
		Grades:      course.Grades,
		Subject:     course.Subject,
		Enabled:     course.Enabled,
		Sort:        course.Sort,
		CreatedAt:   course.CreatedAt.Format("2006-01-02 15:04:05"), // 把 time.Time 类型转换为字符串
		UpdatedAt:   course.UpdatedAt.Format("2006-01-02 15:04:05"), // 把 time.Time 类型转换为字符串
		Banner:      course.Banner,
	}
}
func NewGormYkCourseService(dao dao.YkCourseDao) *GormYkCourseService {
	return &GormYkCourseService{dao: dao}
}
