package admin

import (
	"marketing/internal/handler/admin/endpoint"
	"marketing/internal/handler/admin/system"
	"marketing/internal/middleware"
	"marketing/internal/provider"
	endpointSvc "marketing/internal/service/endpoint"

	"github.com/gin-gonic/gin"
)

type EndpointRouter struct {
	imageHandler    endpoint.ImageInterface
	infoHandler     endpoint.InfoApplyInterface
	settingHandler  endpoint.SettingHandler
	endpointSvc     endpointSvc.Endpoint
	endpointHandler endpoint.Endpoint
	infra           *provider.InfrastructureProvider
	svc             *provider.ServiceProvider
}

func NewEndpointRouter(endpointSvc endpointSvc.Endpoint,
	endpointHandler endpoint.Endpoint,
	imageHandler endpoint.ImageInterface,
	infoHandler endpoint.InfoApplyInterface,
	infra *provider.InfrastructureProvider,
	svc *provider.ServiceProvider,
	settingHandler endpoint.SettingHandler) *EndpointRouter {
	return &EndpointRouter{
		endpointHandler: endpointHandler,
		endpointSvc:     endpointSvc,
		imageHandler:    imageHandler,
		infoHandler:     infoHandler,
		settingHandler:  settingHandler,
		infra:           infra,
		svc:             svc,
	}
}

func (a *EndpointRouter) Register(r *gin.RouterGroup) {
	// 渠道管理模块
	endpointRouter := r.Group("/endpoint")
	{
		//终端
		endpointsRouter := endpointRouter.Group("/endpoints")
		{
			endpointsRouter.GET("", a.endpointHandler.Lists)                   // 获取终端列表
			endpointsRouter.GET("/:id", a.endpointHandler.Get)                 // 获取指定终端
			endpointsRouter.POST("", a.endpointHandler.Create)                 // 创建终端
			endpointsRouter.PUT("/:id", a.endpointHandler.UpdateEndpoint)      // 更新终端
			endpointsRouter.PUT("/:id/status", a.endpointHandler.UpdateStatus) // 更新终端状态
			endpointsRouter.GET("/top-agencies", a.endpointHandler.GetTopAgency)
			endpointsRouter.GET("/second-agencies", a.endpointHandler.GetSecondAgency)
			endpointsRouter.GET("/types", a.endpointHandler.GetEndpointType)
		}
		//终端形象审核
		endpointImageRouter := endpointRouter.Group("/endpoint-images")
		{
			endpointImageRouter.GET("", a.imageHandler.Lists)
			endpointImageRouter.PUT("/:id/audit", a.imageHandler.Audit)
		}

		//终端档案审核及历史记录
		endpointInfoRouter := endpointRouter.Group("/endpoint-info")
		{
			endpointInfoRouter.GET("", a.infoHandler.Lists)
			endpointInfoRouter.PUT("/:id/audit", a.infoHandler.Audit)
		}
		//终端设置
		endpointSettingRouter := endpointRouter.Group("/endpoint-setting")
		{
			endpointSettingRouter.GET("", a.settingHandler.Lists)
			endpointSettingRouter.PUT("/:id", a.settingHandler.UpdateSetting)
		}
		//终端用户管理
		endpointUserGroup := endpointRouter.Group("/users")
		{
			endpointUserGroup.Use(middleware.AdminUserLogMiddleware(a.infra.DB))

			endpointUserHandler := endpoint.NewEndpointUser(a.svc.AdminUserService, a.svc.AdminRoleService, a.endpointSvc)
			adminUserHandler := system.NewAdminUser(a.svc.AdminUserService,
				a.svc.AdminRoleService,
				a.svc.AdminUserGroupService,
				a.svc.WecomTageService)
			endpointUserGroup.GET("", endpointUserHandler.Lists)
			endpointUserGroup.POST("", endpointUserHandler.Add)
			endpointUserGroup.PUT("", endpointUserHandler.Update)
			endpointUserGroup.DELETE("/:id", endpointUserHandler.Delete)
			endpointUserGroup.GET("/role-dropdown", endpointUserHandler.GetRoleDropdown)
			endpointUserGroup.PUT("/sync", endpointUserHandler.SyncUser)
			endpointUserGroup.GET("/department-tree", a.endpointHandler.GetDepartmentTree)
			endpointUserGroup.PUT("/sync-department", endpointUserHandler.SyncDepartment)

			// 直接用原来的权限不好管控
			endpointUserGroup.PUT("/:id", endpointUserHandler.Update)
			endpointUserGroup.POST("/:id/reset-password", adminUserHandler.ResetPassword)
			endpointUserGroup.POST("/:id/status", adminUserHandler.UpdateStatus)
			endpointUserGroup.PUT("/:id/wecom-tags", adminUserHandler.UpdateWecomTag)
			endpointUserGroup.POST("/add-group", adminUserHandler.UserAddToGroup)
		}
	}
}
