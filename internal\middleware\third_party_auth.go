package middleware

import (
	"bytes"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"io"
	"marketing/internal/dao/app_auth"
	"marketing/internal/pkg/aes"
	"marketing/internal/pkg/log"
	"marketing/internal/service/third_party"
	"net/http"
	"net/url"
	"os"
	"strings"

	"github.com/gin-gonic/gin"
)

const (
	ErrThirdPartyUnauthorized = "第三方认证失败"
	ErrThirdPartyTokenMissing = "缺少认证token"
	ErrThirdPartyTokenInvalid = "认证token无效"
	ErrAppIDMissing           = "缺少AppID"
	ErrAppKeyMissing          = "缺少AppKey"
	ErrAppIDAppKeyInvalid     = "AppID或AppKey无效"
)

// RepairAuthConfig 寄修接口认证配置
type RepairAuthConfig struct {
	AppAuthDao app_auth.AppAuthDao // app_auth DAO
	AppID      string              // 应用ID
}

// ThirdPartyAuthMiddleware 第三方认证中间件(token校验)
func ThirdPartyAuthMiddleware(authService third_party.ThirdPartyAuthServiceInterface) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取token，支持多种方式
		token := getThirdPartyToken(c)

		if token == "" {
			c.JSON(http.StatusUnauthorized, &RespBody{
				OK:      0,
				Message: ErrThirdPartyTokenMissing,
			})
			c.Abort()
			return
		}

		// 验证token
		appAuth, err := authService.ValidateToken(c, token)
		if err != nil || appAuth == false {
			c.JSON(http.StatusUnauthorized, &RespBody{
				OK:      0,
				Message: ErrThirdPartyTokenInvalid,
			})
			c.Abort()
			return
		}

		// 将应用信息存储到上下文中

		c.Next()
	}
}

// getThirdPartyToken 从请求中获取第三方token
// 支持多种方式：
// 1. Header: Authorization: Bearer token
// 2. Header: X-Auth-Token: token
// 3. Form: token=xxx
func getThirdPartyToken(c *gin.Context) string {
	// 1. 从Authorization header获取
	authHeader := c.GetHeader("Authorization")
	if authHeader != "" {
		if strings.HasPrefix(authHeader, "Bearer ") {
			return strings.TrimPrefix(authHeader, "Bearer ")
		}
		return authHeader
	}

	// 2. 从X-Auth-Token header获取
	if token := c.GetHeader("X-Auth-Token"); token != "" {
		return token
	}

	// 3. 从form参数获取
	if token := c.PostForm("token"); token != "" {
		return token
	}

	return ""
}

// AppKeyAuthMiddleware AppID和AppKey验证中间件
// 简单验证appid和appKey是否匹配，验证通过即可访问
func AppKeyAuthMiddleware(authService third_party.ThirdPartyAuthServiceInterface) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取appid和appkey，支持多种方式
		appID, appKey := getAppIDAndAppKey(c)

		if appID == "" {
			c.JSON(http.StatusUnauthorized, &RespBody{
				OK:      0,
				Message: ErrAppIDMissing,
			})
			log.Info(c.GetHeader("X-Request-ID") + ":AppID is missing in request")
			c.Abort()
			return
		}

		if appKey == "" {
			c.JSON(http.StatusUnauthorized, &RespBody{
				OK:      0,
				Message: ErrAppKeyMissing,
			})
			log.Info(c.GetHeader("X-Request-ID") + ":AppKey is missing in request")
			c.Abort()
			return
		}

		// 验证appid和appkey
		appAuth, _ := authService.ValidateAppKey(c, appID, appKey)
		if appAuth == false {
			c.JSON(http.StatusUnauthorized, &RespBody{
				OK:      0,
				Message: ErrAppIDAppKeyInvalid,
			})
			log.Info(c.GetHeader("X-Request-ID") + ":AppID or AppKey is invalid")
			c.Abort()
			return
		}

		c.Next()
	}
}

// getAppIDAndAppKey 从请求中获取AppID和AppKey
// 支持多种方式：
// 1. Header: X-App-ID 和 X-App-Key
// 2. Query: appid 和 appkey
// 3. Form: appid 和 appkey
func getAppIDAndAppKey(c *gin.Context) (string, string) {
	var appID, appKey string

	// 1. 从Header获取
	appID = c.GetHeader("X-App-ID")
	appKey = c.GetHeader("X-App-Key")
	if appID != "" || appKey != "" {
		return appID, appKey
	}

	// 2. 从Query参数获取
	appID = c.Query("appid")
	appKey = c.Query("appkey")
	if appID != "" || appKey != "" {
		return appID, appKey
	}

	// 3. 从Form参数获取
	appID = c.PostForm("appid")
	appKey = c.PostForm("appkey")
	if appID != "" || appKey != "" {
		return appID, appKey
	}

	// 4. 从JSON请求体获取（不消费请求体）
	if c.Request.Method == "POST" || c.Request.Method == "PUT" || c.Request.Method == "PATCH" {
		contentType := c.GetHeader("Content-Type")
		if strings.Contains(contentType, "application/json") {
			// 读取请求体但不消费
			bodyBytes, err := io.ReadAll(c.Request.Body)
			if err == nil && len(bodyBytes) > 0 {
				// 恢复请求体，让后续处理器可以正常读取
				c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

				// 解析JSON获取appid和appkey
				var req struct {
					AppID  string `json:"appid"`
					AppKey string `json:"appkey"`
				}
				if err := json.Unmarshal(bodyBytes, &req); err == nil {
					if req.AppID != "" || req.AppKey != "" {
						return req.AppID, req.AppKey
					}
				}
			}
		}
	}

	return appID, appKey
}

// HollyMD5Middleware 400客服系统合力亿捷访问中间件
func HollyMD5Middleware(authService third_party.ThirdPartyAuthServiceInterface) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取所有查询参数
		queryParams := c.Request.URL.Query()

		// 从app_auth表获取认证信息
		appid, _ := getAppIDAndAppKey(c)
		if appid == "" {
			log.Info(c.GetHeader("X-Request-ID") + ": AppID is missing in request")
			c.JSON(http.StatusUnauthorized, gin.H{
				"msg": "缺少AppID参数",
				"ok":  0,
			})
			c.Abort()
			return
		}
		//appid 解密
		aesKey := os.Getenv("HOLLY_AES_KEY")
		aesData, err := aes.DecryptAESParams(queryParams, aesKey, []string{"appid"})
		if err != nil {
			log.Error(c.GetHeader("X-Request-ID")+": AppID decryption failed", zap.Error(err))
			c.JSON(http.StatusUnauthorized, gin.H{
				"msg": "appid解密失败",
				"ok":  0,
			})
			c.Abort()
			return
		}
		appid = aesData["appid"]
		appAuth, err := authService.GetAppAuth(c, appid)
		if err != nil {
			log.Error(c.GetHeader("X-Request-ID")+": Failed to get app authentication info", zap.Error(err))
			c.JSON(http.StatusUnauthorized, gin.H{
				"msg": "应用认证信息获取失败",
				"ok":  0,
			})
			c.Abort()
			return
		}

		if appAuth == nil {
			log.Info(c.GetHeader("X-Request-ID")+": App not authorized", zap.String("appid", appid))
			c.JSON(http.StatusUnauthorized, gin.H{
				"msg": "应用未授权",
				"ok":  0,
			})
			c.Abort()
			return
		}

		// 提取MD5参数
		md5Param := queryParams.Get("MD5")

		if md5Param == "" {
			log.Info(c.GetHeader("X-Request-ID") + ": MD5 parameter is missing in request")
			c.JSON(http.StatusUnauthorized, gin.H{
				"msg": "缺少MD5签名参数",
				"ok":  0,
			})
			c.Abort()
			return
		}

		// 移除MD5参数，准备验证其他参数
		rawQuery := c.Request.URL.RawQuery

		// 移除 MD5 参数
		newQuery, err := removeAndDecodeParams(rawQuery, "MD5")
		if err != nil {
			log.Error(c.GetHeader("X-Request-ID")+": Failed to remove MD5 parameter", zap.Error(err))
			c.JSON(http.StatusBadRequest, gin.H{
				"msg": "参数处理失败",
				"ok":  0,
			})
			c.Abort()
			return
		}
		// 使用appkey作为MD5密钥验证签名
		if !validateMD5Signature(newQuery, md5Param, appAuth.AppKey) {

			c.JSON(http.StatusUnauthorized, gin.H{
				"msg": "MD5签名验证失败",
				"ok":  0,
			})
			c.Abort()
			return
		}
		c.Set("appkey", appAuth.AppKey)

		c.Next()
	}
}

// validateMD5Signature 验证MD5签名
func validateMD5Signature(params, providedMD5, md5Key string) bool {
	// 拼接密钥
	signStr := params + md5Key

	// 计算MD5
	hash := md5.Sum([]byte(signStr))
	expectedMD5 := hex.EncodeToString(hash[:])

	// 比较签名（不区分大小写）
	return strings.EqualFold(expectedMD5, providedMD5)
}

// buildParamString 构建参数字符串
func buildParamString(params url.Values) string {
	if len(params) == 0 {
		return ""
	}

	// 获取所有参数键并排序（确保签名一致性）
	keys := make([]string, 0, len(params))
	for key := range params {
		keys = append(keys, key)
	}
	//sort.Strings(keys)

	// 构建参数字符串
	var parts []string
	for _, key := range keys {
		value := params.Get(key)
		parts = append(parts, fmt.Sprintf("%s=%s", key, value))
	}

	return strings.Join(parts, "&")
}

func removeAndDecodeParams(rawQuery, paramToRemove string) (string, error) {
	// 分割原始查询字符串
	params := strings.Split(rawQuery, "&")
	var filteredParams []string

	for _, param := range params {
		// 检查参数是否为要移除的
		if !strings.HasPrefix(param, paramToRemove+"=") {
			// 将参数名和值分开
			keyValue := strings.SplitN(param, "=", 2)
			if len(keyValue) == 2 {
				decodedValue, err := url.QueryUnescape(keyValue[1])
				if err != nil {
					return "", err // 返回错误
				}
				filteredParams = append(filteredParams, keyValue[0]+"="+decodedValue)
			} else {
				// 如果没有值，只保留键
				filteredParams = append(filteredParams, keyValue[0])
			}
		}
	}

	// 重新组合过滤后的参数为新查询字符串
	return strings.Join(filteredParams, "&"), nil
}
