package agency

import (
	"errors"
	"fmt"
	"marketing/internal/pkg/db"

	"gorm.io/gorm"
)

type role struct {
	RoleId   int64  `json:"role_id" gorm:"column:id"`
	RoleName string `json:"role_name" gorm:"column:name"`
}

var Role role

func GetRoleByID(roleID int64) (*role, error) {
	// 获取名为 "rbcare" 的数据库连接
	database := db.GetDB("rbcare")
	// 存储查询结果的 adminUser 结构体实例
	var role role

	// 从 "admin_roles" 表中查询 id 等于传入的 roleID 的记录，并将结果存储到 role 结构体中
	result := database.Table("admin_roles").Where("id = ?", roleID).First(&role)
	if result.Error != nil {
		// 如果未找到记录，打印相应信息并返回 nil 和 nil
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			fmt.Printf("role not found By RoleID: %d\n", roleID)
			return nil, nil
		}
		// 打印数据库查询错误信息并返回错误
		fmt.Printf("Database query error: %v\n", result.Error)
		return nil, result.Error
	}

	return &role, nil
}
