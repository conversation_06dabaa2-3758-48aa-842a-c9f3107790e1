package dao

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"marketing/internal/pkg/utils"

	"time"
)

// // userEndpoint 表示用户和终端之间的关系
//
//	type UserEndpoint struct {
//		Uid      string `gorm:"column:uid;type:varchar(255);not null" json:"uid"`             // 用户id，来自 admin_users 表
//		Endpoint string `gorm:"column:endpoint;type:varchar(255);not null" json:"endpoint"`   // 终端id
//		Role     string `gorm:"column:role;type:varchar(255);not null" json:"role"`           // 角色，店长、店员等
//		UserType string `gorm:"column:user_type;type:varchar(255);not null" json:"user_type"` // 用户类型，user 或 visitor
//	}
//type AdminUsers2 struct {
//	Id            uint      `json:"id" gorm:"primaryKey"`                     // 主键ID
//	Username      string    `json:"username" gorm:"unique;type:varchar(255)"` // 用户名，唯一
//	Password      string    `json:"password" gorm:"type:varchar(255)"`        // 密码
//	Name          string    `json:"name" gorm:"type:varchar(255)"`            // 用户名称
//	Avatar        string    `json:"avatar" gorm:"type:varchar(255)"`          // 头像
//	Phone         string    `json:"phone" gorm:"type:varchar(20)"`            // 电话号码
//	Code          uint      `json:"code" gorm:"code"`                         // 手机验证码
//	QwUserid      string    `json:"qw_userid" gorm:"type:varchar(255)"`       // 企业微信userid
//	ExpireTime    time.Time `json:"expire_time" gorm:"type:varchar(20)"`      // 过期时间
//	RememberToken string    `json:"remember_token" gorm:"type:varchar(255)"`  // Remember Token
//	CreatedAt     time.Time `json:"created_at" gorm:"type:varchar(20)"`       // 创建时间
//	UpdatedAt     time.Time `json:"updated_at" gorm:"type:varchar(20)"`       // 更新时间
//	Status        int8      `json:"status" gorm:"type:varchar(10)"`           // 状态: 0-禁用, 1-正常, -1-不活跃
//	Type          int8      `json:"type" gorm:"type:varchar(10)"`             // 类型: 0-终端版, 1-管理版, 2-维修版
//	ActivedAt     time.Time `json:"actived_at" gorm:"type:varchar(20)"`       // 最后活跃时间
//	DeletedAt     time.Time `json:"deleted_at" gorm:"type:varchar(20)"`       // 删除时间
//}

// AdminUsers 结构体用于存储成员信息
type AdminUsers struct {
	Id uint `json:"id" gorm:"primaryKey"`
	//下面4个字段分别来自 userEndpoint，Endpoint，agency
	Role             string `json:"role" gorm:"column:role"` // 角色
	EndpointName     string `json:"endpoint_name" gorm:"column:endpoint_name" `
	TopAgencyName    string `gorm:"column:top_agency_name" json:"top_agency_name"`
	SecondAgencyName string `gorm:"column:second_agency_name" json:"second_agency_name"`
	//下面字段来自admin_users,是否同步还没做
	isSameWithWechat string `json:"is_same_with_wechat" gorm:"column:is_same_with_wechat"`
	// 来自 admin_users 表
	Name      string    `gorm:"column:name;type:varchar(255)" json:"name"`
	Username  string    `gorm:"unique;type:varchar(255)" json:"username"`
	Phone     string    `gorm:"column:phone;type:varchar(20)" json:"phone"`
	Status    string    `gorm:"column:status;type:varchar(10)" json:"status"`
	QwUserid  string    `gorm:"column:qw_userid;type:varchar(255);uniqueIndex" json:"qw_userid"`
	CreatedAt time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
	ActivedAt time.Time `gorm:"column:actived_at;type:datetime" json:"actived_at"`
	DeletedAt time.Time `gorm:"column:deleted_at;type:datetime" json:"deleted_at"`
}

type AdminUserQueryParams struct {
	Role             string `json:"role"` // 角色
	EndpointName     string `json:"endpoint_name"`
	TopAgencyName    string `json:"top_agency_name"`
	SecondAgencyName string `json:"second_agency_name"`
	Name             string `json:"name"`
	Phone            string `json:"phone"`
	Status           string `json:"status"`
	IsSameWithWechat string `json:"is_same_with_wechat"`
}

// 用户信息：实现对用户的增删改查 admin_users和user_endpoint **查**
// 点击终端/直营终端：显示下面用户，用户信息包括下图  admin_users和user_endpoint **查**
// GetMembersByConditions 查询成员，支持多个条件的自由组合
func (dao *EndpointAdminUsersDao) GetMembersByConditions(params AdminUserQueryParams, c *gin.Context) ([]AdminUsers, int64, int, int, error) {
	var members []AdminUsers
	query := GetDB().Table("endpoint").
		Joins("LEFT JOIN user_endpoint ON user_endpoint.endpoint = endpoint.id").
		Joins("LEFT JOIN admin_users ON admin_users.id = user_endpoint.uid").
		Joins("LEFT JOIN agency AS top_agency ON top_agency.id = endpoint.top_agency").
		Joins("LEFT JOIN agency AS second_agency ON second_agency.id = endpoint.second_agency").
		Select("user_endpoint.*, endpoint.name as endpoint_name, top_agency.name as top_agency_name, " +
			"second_agency.name as second_agency_name, " + // 注意这里的逗号
			"admin_users.id as id,admin_users.created_at,admin_users.updated_at,admin_users.deleted_at, " +
			"admin_users.name,admin_users.phone,admin_users.status,admin_users.qw_userid,admin_users.username,admin_users.qw_userid") // 确保字段之间有空格
	// 添加动态条件
	if params.Role != "" {
		query = query.Where("user_endpoint.role = ?", params.Role)
	}

	if params.EndpointName != "" {
		query = query.Where("endpoint.name LIKE ?", "%"+params.EndpointName+"%")
	}

	if params.TopAgencyName != "" {
		query = query.Where("top_agency.name LIKE ?", "%"+params.TopAgencyName+"%")
	}

	if params.SecondAgencyName != "" {
		query = query.Where("second_agency.name LIKE ?", "%"+params.SecondAgencyName+"%")
	}

	if params.Name != "" {
		query = query.Where("admin_users.name LIKE ?", "%"+params.Name+"%")
	}

	if params.Phone != "" {
		query = query.Where("admin_users.phone LIKE ?", "%"+params.Phone+"%")
	}

	if params.Status != "" {
		query = query.Where("admin_users.status = ?", params.Status)
	}

	// 如果有 is_same_with_wechat 字段，可以考虑直接将条件添加进来
	if params.IsSameWithWechat != "" {
		query = query.Where("user_endpoint.is_same_with_wechat = ?", params.IsSameWithWechat)
	}

	query = query.Order("admin_users.created_at").Order("admin_users.updated_at")
	// 执行查询
	err := query.Find(&members).Error

	if err != nil {
		return nil, 0, 0, 0, fmt.Errorf("error querying members: %v", err)
	}
	db := GetDB()
	data, total, page, pageSize, err := utils.PaginateQuery(db, c, &members)
	if err != nil { // 如果出现错误，返回错误信息
		return nil, 0, 0, 0, err
	}
	return *data, total, page, pageSize, nil
}

// 创建成员
func (dao *EndpointAdminUsersDao) CreateMember(member *AdminUsers) error {
	db := GetDB()
	member.Status = "1" // 默认状态是1（激活状态）
	return db.Table("admin_users").Create(member).Error
}

// 获取成员信息，通过ID
func (dao *EndpointAdminUsersDao) GetMemberByID(id uint) (*AdminUsers, error) {
	var member AdminUsers
	db := GetDB()
	if err := db.Table("admin_users").Where("id = ?", id).First(&member, id).Error; err != nil {
		return nil, err
	}
	return &member, nil
}

// 获取所有成员信息
func (dao *EndpointAdminUsersDao) GetAllMembers(c *gin.Context) ([]AdminUsers, int64, int, int, error) {
	var members []AdminUsers
	db := GetDB()
	query := GetDB().Table("endpoint").
		Joins("LEFT JOIN user_endpoint ON user_endpoint.endpoint = endpoint.id").
		Joins("LEFT JOIN admin_users ON admin_users.id = user_endpoint.uid").
		Joins("LEFT JOIN agency AS top_agency ON top_agency.id = endpoint.top_agency").
		Joins("LEFT JOIN agency AS second_agency ON second_agency.id = endpoint.second_agency").
		Select("user_endpoint.*, endpoint.name as endpoint_name, top_agency.name as top_agency_name, " +
			"second_agency.name as second_agency_name, " + // 注意这里的逗号
			"admin_users.id as id,admin_users.created_at,admin_users.updated_at,admin_users.deleted_at, " +
			"admin_users.name,admin_users.phone,admin_users.status,admin_users.qw_userid,admin_users.username,admin_users.qw_userid") // 确保字段之间有空格

	query = query.Order("admin_users.created_at").Order("admin_users.updated_at")
	// 执行查询
	err := query.Find(&members).Error
	if err != nil {
		return nil, 0, 0, 0, fmt.Errorf("error querying members: %v", err)
	}
	data, total, page, pageSize, err := utils.PaginateQuery(db, c, &members)
	if err != nil { // 如果出现错误，返回错误信息
		return nil, 0, 0, 0, err
	}
	return *data, total, page, pageSize, nil
}

// 更新成员信息
func (dao *EndpointAdminUsersDao) UpdateMember(member *AdminUsers) error {
	db := GetDB()
	return db.Table("admin_users").Save(member).Error
}

// 软删除成员（更新status为0）
func (dao *EndpointAdminUsersDao) SoftDeleteMember(id uint) error {
	var member AdminUsers
	db := GetDB()
	if err := db.Table("admin_users").First(&member, id).Error; err != nil {
		return err
	}
	// 设置状态为0表示删除，并记录删除时间
	member.Status = "0"
	member.DeletedAt = time.Now()
	return db.Save(&member).Error
}

// 恢复成员（更新status为1）
func (dao *EndpointAdminUsersDao) RestoreMember(id uint) error {
	var member AdminUsers
	db := GetDB()
	if err := db.Table("admin_users").First(&member, id).Error; err != nil {
		return err
	}
	// 设置状态为1表示恢复
	member.Status = "1"
	return db.Save(&member).Error
}

// 定时任务删除：物理删除成员
func (dao *EndpointAdminUsersDao) PhysicalDeleteMember(id uint) error {
	var member AdminUsers
	db := GetDB()
	if err := db.Table("admin_users").First(&member, id).Error; err != nil {
		return err
	}
	return db.Delete(&member).Error
}

//// 假设我们有一个方法可以从数据库中获取AdminUsers实例
//func GetAdminUsers(userId uint) (*AdminUsers, error) {
//	//DB := GetDB()
//	//var memberInfo AdminUsers
//	////var adminUser AdminUsers
//	//var endpoint Endpoint
//
//	//var userEndpoint UserEndpoint
//	//
//	//if err := DB.Table("admin_users").Where("id = ?", userId).First(&memberInfo).Error; err != nil {
//	//	return nil, err
//	//}
//	//
//	////memberInfo.Id = adminUser.Id
//	////
//	//////memberInfo.Region = adminUser.Region
//	////memberInfo.MemberName = adminUser.ActivityName
//	////
//	////memberInfo.PhoneNumber = adminUser.Phone
//	////memberInfo.Status = strconv.Itoa(int(adminUser.Status))
//	//
//	//if err := DB.Table("user_endpoint").Where("uid = ?", memberInfo.Id).First(&userEndpoint).Error; err != nil {
//	//	return nil, err
//	//}
//	//memberInfo.Role = userEndpoint.Role
//	//
//	//if err := DB.Table("endpoint").Where("id = ?", userEndpoint.Endpoint).First(&endpoint).Error; err != nil {
//	//	return nil, err
//	//}
//	//memberInfo.EndpointName = endpoint.ActivityName
//
//	//return &memberInfo, nil
//}
