package app

import (
	userDao "marketing/internal/dao/admin_user"
	"marketing/internal/dao/endpoint"
	dao "marketing/internal/dao/renew"
	"marketing/internal/handler/app"
	"marketing/internal/pkg/db"
	service "marketing/internal/service/renew"

	"github.com/gin-gonic/gin"
)

type RenewRouter struct {
	renewHandler app.ApplicationHandlerInterface
}

func NewRenewRouter() *RenewRouter {
	var Db = db.GetDB()
	userD := userDao.NewUserDao(Db)
	endpointDao := endpoint.NewEndpointDao(Db)
	renewDao := dao.NewApplicationDao(Db)
	renewService := service.NewApplicationService(userD, renewDao, endpointDao, nil)
	renewHandler := app.NewApplicationHandler(renewService)
	return &RenewRouter{
		renewHandler: renewHandler,
	}
}

func (rr *RenewRouter) Register(r *gin.RouterGroup) {

	renewRouter := r.Group("renew/apply")
	{
		renewRouter.GET("", rr.renewHandler.Lists)
		renewRouter.GET("/:id", rr.renewHandler.GetInfo)
		renewRouter.PUT("/:id/check", rr.renewHandler.Check)
		renewRouter.GET("/status", rr.renewHandler.GetStatus)
		renewRouter.GET("/check-facade", rr.renewHandler.GetCheckFacade)
		renewRouter.GET("/export", rr.renewHandler.Export)
	}
}
