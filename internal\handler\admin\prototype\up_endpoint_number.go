package prototype

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/api/prototype"
	"marketing/internal/handler"
	service "marketing/internal/service/prototype"
)

// UpEndpointNumberHandler 上样终端数量控制器接口
type UpEndpointNumberHandler interface {
	GetEndpointNumber(c *gin.Context)
	Add(c *gin.Context)
	ExportEndpointNumber(c *gin.Context)
}

type upEndpointNumberHandler struct {
	service service.UpEndpointNumberService
}

// NewUpEndpointNumberHandler 创建上样终端数量控制器
func NewUpEndpointNumberHandler(service service.UpEndpointNumberService) UpEndpointNumberHandler {
	return &upEndpointNumberHandler{
		service: service,
	}
}

// GetEndpointNumber 获取上样终端数量列表
func (h *upEndpointNumberHandler) GetEndpointNumber(c *gin.Context) {
	var req prototype.UpEndpointNumberSearch
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	req.SetDefaults()
	list, total, err := h.service.GetList(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, gin.H{
		"list":  list,
		"total": total,
	})
}

// ExportEndpointNumber 导出上样终端数量列表
func (h *upEndpointNumberHandler) ExportEndpointNumber(c *gin.Context) {
	var req prototype.UpEndpointNumberSearch
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}

	err := h.service.ExportList(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}

}

func (h *upEndpointNumberHandler) Add(c *gin.Context) {
	var req prototype.UpEndpointNumberAdd
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}

	err := h.service.Add(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c)
}
