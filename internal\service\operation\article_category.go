package operation

import (
	"errors"
	"github.com/gin-gonic/gin"
	"marketing/internal/api/operation"
	"marketing/internal/dao"
	"marketing/internal/model"
	"marketing/internal/pkg/utils"
	"sort"
	"sync"
)

type ArticleCategoryInfo struct {
	Id        uint                  `json:"id"`         // 内容id
	Pid       uint                  `json:"pid"`        // 父id
	Name      string                `json:"name"`       // 内容名称
	Num       int                   `json:"num"`        // 内容数量
	Enabled   uint8                 `json:"enabled"`    // 上架发布 0:未发布 1:发布
	Order     int                   `json:"order"`      // 排序
	ChildList []ArticleCategoryInfo `json:"child_list"` // 子标签列表
}

var (
	articleCategoryArray      []ArticleCategoryInfo // 内容标签
	articleCategoryArrayMutex sync.RWMutex          // 读写锁
	articleCategoryArrayOnce  sync.Once             // 单例锁
	articleCategoryVersion    int                   // 版本号
)

type OpArticleCategorySvcInterface interface {
	GetAllArticleCategory(c *gin.Context) (infos []ArticleCategoryInfo)
	RefreshArticleCategory(c *gin.Context) (infos []ArticleCategoryInfo)
	EditArticleCategory(c *gin.Context, id, pId int, name string, enabled, order int) error
	AddArticleCategory(c *gin.Context, pId int, name string, enabled, order, createdBy int) error
	DelArticleCategory(c *gin.Context, id int) error
}

type OpArticleCategoryService struct {
	categoryRepo dao.OpArticleCategoryDao
}

func NewMaterialCategoryService(categoryRepo dao.OpArticleCategoryDao) OpArticleCategorySvcInterface {
	s := &OpArticleCategoryService{
		categoryRepo: categoryRepo,
	}

	articleCategoryArrayOnce.Do(func() {
		s.RefreshArticleCategory(new(gin.Context))
	})

	return s
}

func (s *OpArticleCategoryService) GetAllArticleCategory(c *gin.Context) (infos []ArticleCategoryInfo) {
	if articleCategoryVersion != dao.GetCacheVersion(dao.CvArticleCategory) {
		return s.RefreshArticleCategory(c)
	}

	return articleCategoryArray
}

func (s *OpArticleCategoryService) RefreshArticleCategory(c *gin.Context) (infos []ArticleCategoryInfo) {
	articleCategoryArrayMutex.Lock()
	defer articleCategoryArrayMutex.Unlock()

	articleCategoryArray = make([]ArticleCategoryInfo, 0)
	categoryList, _ := s.categoryRepo.GetArticleCategoryList(c)
	for _, category := range categoryList {
		parentIds := make([]uint, 0)

		if category.ParentID != 0 {
			parentIds = s.GetArticleCategoryParentIds(category.ParentID, categoryList)
			if len(parentIds) == 0 {
				continue
			}
		}

		_ = s.addChildCategory(parentIds, &articleCategoryArray, ArticleCategoryInfo{
			Id:        category.ID,
			Pid:       category.ParentID,
			Name:      category.Name,
			Num:       int(category.NumArticles),
			Enabled:   category.Enabled,
			Order:     category.Order,
			ChildList: make([]ArticleCategoryInfo, 0),
		})
	}

	// 修改版本号
	articleCategoryVersion = dao.GetCacheVersion(dao.CvArticleCategory)

	return articleCategoryArray
}

func (s *OpArticleCategoryService) EditArticleCategory(c *gin.Context, id, pId int, name string, enabled, order int) error {
	articleCategoryArrayMutex.Lock()
	defer articleCategoryArrayMutex.Unlock()

	if len(name) == 0 {
		return errors.New("编辑内容分类:名称为空")
	}

	category := s.categoryRepo.GetArticleCategoryById(c, id)
	if category == nil {
		return errors.New("编辑内容分类:分类不存在")
	}

	oldPid := category.ParentID
	childList := make([]ArticleCategoryInfo, 0)
	uMap := make(map[string]interface{})
	uMap["name"] = name
	uMap["enabled"] = uint8(enabled)
	uMap["order"] = order

	if pId != int(oldPid) {
		if pId != 0 && s.categoryRepo.GetArticleCategoryById(c, pId) == nil {
			return errors.New("编辑内容分类:父标签不存在")
		}

		uMap["parent_id"] = pId
	}

	// db中修改内容分类
	dbErr := s.categoryRepo.UpdateArticleCategory(c, int(category.ID), uMap)
	if dbErr != nil {
		return nil
	}

	// 删除旧的节点
	if pId != int(oldPid) {
		// 获取旧的父id
		oldsParentIds, err := s.GetArticleCategoryParentIdsFromDb(oldPid)
		if err != nil {
			return errors.New("编辑内容分类:获取旧的父标签id异常")
		}

		// 获取内容分类的子列表
		childList = s.getChildCategory(oldsParentIds, &articleCategoryArray, category.ID)

		// 缓存中删除内容分类
		s.delChildCategory(oldsParentIds, &articleCategoryArray, category.ID)
	}

	// 获取新的父id
	parentIds, err := s.GetArticleCategoryParentIdsFromDb(uint(pId))
	if err != nil {
		return errors.New("编辑内容分类:获取新的父标签id异常")
	}

	// 缓存中新增内容分类
	s.addChildCategory(parentIds, &articleCategoryArray, ArticleCategoryInfo{
		Id:        category.ID,
		Name:      name,
		Pid:       uint(pId),
		Num:       int(category.NumArticles),
		Enabled:   uint8(enabled),
		Order:     order,
		ChildList: childList,
	})

	// 增加版本号
	s.addVersionNum()

	return nil
}

func (s *OpArticleCategoryService) AddArticleCategory(c *gin.Context, pId int, name string, enabled, order, createdBy int) error {
	articleCategoryArrayMutex.Lock()
	defer articleCategoryArrayMutex.Unlock()

	if len(name) == 0 {
		return errors.New("添加内容分类:名称为空")
	}

	category := &model.OpArticleCategory{
		ParentID:  uint(pId),
		Name:      name,
		Order:     uint(order),
		Enabled:   uint(enabled),
		CreatedBy: uint(createdBy),
	}

	if pId != 0 && s.categoryRepo.GetArticleCategoryById(c, pId) == nil {
		return errors.New("添加内容分类:父标签不存在")
	}

	// db中创建内容分类
	dbErr := s.categoryRepo.CreateArticleCategory(c, category)
	if dbErr != nil {
		return dbErr
	}

	// 获取父id
	parentIds, err := s.GetArticleCategoryParentIdsFromDb(category.ParentID)
	if err != nil {
		return errors.New("添加内容分类:获取新的父标签id异常")
	}

	// 缓存中新增内容分类
	s.addChildCategory(parentIds, &articleCategoryArray, ArticleCategoryInfo{
		Id:        category.ID,
		Name:      category.Name,
		Pid:       category.ParentID,
		Enabled:   uint8(category.Enabled),
		Order:     int(category.Order),
		ChildList: make([]ArticleCategoryInfo, 0),
	})

	// 增加版本号
	s.addVersionNum()

	return nil
}

func (s *OpArticleCategoryService) DelArticleCategory(c *gin.Context, id int) error {
	articleCategoryArrayMutex.Lock()
	defer articleCategoryArrayMutex.Unlock()

	// 查询内容分类
	category := s.categoryRepo.GetArticleCategoryById(c, id)
	if category == nil {
		return errors.New("删除内容分类:标签不存在")
	}

	// db中删除内容分类
	dbErr := s.categoryRepo.DeleteArticleCategory(c, id)
	if dbErr != nil {
		return dbErr
	}

	// 获取父id
	parentIds, err := s.GetArticleCategoryParentIdsFromDb(category.ParentID)
	if err != nil {
		return errors.New("删除内容分类:获取新的父标签id异常")
	}

	// 缓存中删除内容分类
	s.delChildCategory(parentIds, &articleCategoryArray, category.ID)

	// 增加版本号
	s.addVersionNum()

	return nil
}

func (s *OpArticleCategoryService) addChildCategory(parentIds []uint, categoryArray *[]ArticleCategoryInfo, category ArticleCategoryInfo) int {
	if len(parentIds) > 0 {
		for i, c := range *categoryArray {
			if c.Id == parentIds[0] {
				num := s.addChildCategory(parentIds[1:], &(*categoryArray)[i].ChildList, category)
				(*categoryArray)[i].Num += num
				return num
			}
		}

		parentCategory := ArticleCategoryInfo{
			Id:        parentIds[0],
			ChildList: make([]ArticleCategoryInfo, 0),
		}

		// 加入子标签
		parentCategory.Num = s.addChildCategory(parentIds[1:], &parentCategory.ChildList, category)
		// 加入父标签
		*categoryArray = append(*categoryArray, parentCategory)

		return parentCategory.Num
	}

	noExist := true
	num := 0

	for i, c := range *categoryArray {
		if c.Id == category.Id {
			oldNum := (*categoryArray)[i].Num
			(*categoryArray)[i].Name = category.Name
			(*categoryArray)[i].Enabled = category.Enabled
			(*categoryArray)[i].Order = category.Order
			(*categoryArray)[i].Num = category.Num
			for _, childCategory := range (*categoryArray)[i].ChildList {
				(*categoryArray)[i].Num += childCategory.Num
			}

			num = (*categoryArray)[i].Num - oldNum
			noExist = false

			break
		}
	}

	if noExist {
		num = category.Num
		*categoryArray = append(*categoryArray, category)
	}

	// 排序
	sort.Sort(utils.TagSort{
		Data:   *categoryArray,
		Length: len(*categoryArray),
	})

	return num
}

func (s *OpArticleCategoryService) delChildCategory(parentIds []uint, categoryArray *[]ArticleCategoryInfo, id uint) {
	if len(parentIds) > 0 {
		for i, c := range *categoryArray {
			if c.Id == parentIds[0] {
				s.delChildCategory(parentIds[1:], &(*categoryArray)[i].ChildList, id)
			}
		}
	}

	childList := make([]ArticleCategoryInfo, 0)
	for _, a := range *categoryArray {
		if a.Id != id {
			childList = append(childList, a)
		}
	}

	*categoryArray = childList

	return
}

func (s *OpArticleCategoryService) getChildCategory(parentIds []uint, categoryArray *[]ArticleCategoryInfo, id uint) []ArticleCategoryInfo {
	if len(parentIds) > 0 {
		for i, c := range *categoryArray {
			if c.Id == parentIds[0] {
				return s.getChildCategory(parentIds[1:], &(*categoryArray)[i].ChildList, id)
			}
		}
	}

	for _, c := range *categoryArray {
		if c.Id == id {
			return c.ChildList
		}
	}

	return make([]ArticleCategoryInfo, 0)
}

func (s *OpArticleCategoryService) GetArticleCategoryParentIds(pid uint, categoryList []operation.ArticleCategoryListRspItem) []uint {
	parentIds := make([]uint, 0)

	// 获取父类id
	s.getArticleCategoryParentIds(pid, categoryList, &parentIds)
	if len(parentIds) == 0 {
		return parentIds
	}

	// 因为获取的父id是从下到上,所以需要倒序一次
	utils.ReverseUintArray(&parentIds)

	return parentIds
}

func (s *OpArticleCategoryService) getArticleCategoryParentIds(pid uint, categoryList []operation.ArticleCategoryListRspItem, parentIds *[]uint) {
	for _, category := range categoryList {
		if category.ID == pid {
			// 检查父标签id是否存在回路
			if utils.UintHas(category.ID, *parentIds) {
				*parentIds = make([]uint, 0)
				return
			}

			*parentIds = append(*parentIds, category.ID)

			if category.ParentID != 0 {
				s.getArticleCategoryParentIds(category.ParentID, categoryList, parentIds)
			}

			return
		}
	}

	*parentIds = make([]uint, 0)
}

func (s *OpArticleCategoryService) GetArticleCategoryParentIdsFromDb(id uint) ([]uint, error) {
	if id == 0 {
		return nil, nil
	}

	parentIds := make([]uint, 0)

	// 获取父类id
	err := s.getArticleCategoryParentIdsFromDb(id, &parentIds)
	if err != nil {
		return nil, err
	}

	// 因为获取的父id是从下到上,所以需要倒序一次
	utils.ReverseUintArray(&parentIds)

	return parentIds, nil
}

func (s *OpArticleCategoryService) getArticleCategoryParentIdsFromDb(id uint, parentIds *[]uint) error {
	category := s.categoryRepo.GetArticleCategoryById(new(gin.Context), int(id))
	if category == nil {
		*parentIds = make([]uint, 0)
		return errors.New("标签不存在")
	}

	// 检查父标签id是否存在回路
	if utils.UintHas(category.ID, *parentIds) {
		*parentIds = make([]uint, 0)
		return errors.New("父标签异常")
	}

	*parentIds = append(*parentIds, category.ID)

	if category.ParentID != 0 {
		err := s.getArticleCategoryParentIdsFromDb(category.ParentID, parentIds)
		if err != nil {
			*parentIds = make([]uint, 0)
			return err
		}
	}

	return nil
}

func (s *OpArticleCategoryService) addVersionNum() {
	// 修改db中的版本号
	if dao.AddCacheVersion(dao.CvArticleCategory) == nil {
		articleCategoryVersion += 1
	}
}
