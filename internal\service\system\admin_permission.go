package system

import (
	"errors"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"marketing/internal/api"
	api2 "marketing/internal/api/system"
	"marketing/internal/consts"
	"marketing/internal/model"
	appError "marketing/internal/pkg/errors"
	"sort"
	"strings"
)

// AdminPermissionInterface 角色管理
type AdminPermissionInterface interface {
	Add(c *gin.Context, req api2.AddPermissionReq) error
	Lists(c *gin.Context, param api2.AdminPermissionReq) (*api.PaginationListResp, error)
	Update(c *gin.Context, req api2.AddPermissionReq) error
	Delete(c *gin.Context, id int) error
	ListAll(c *gin.Context, systemType string) (*[]api2.AdminPermissionResp, error)
	AllRoutes(c *gin.Context) (*map[string][]api2.Route, error)
}

// adminPermissionSvc 角色管理
type adminPermissionSvc struct {
	db *gorm.DB
}

// NewAdminPermissionSvc 创建 AdminRoleSvc 实例
func NewAdminPermissionSvc(db *gorm.DB) AdminPermissionInterface {
	return &adminPermissionSvc{
		db: db,
	}
}

func (svc *adminPermissionSvc) Add(c *gin.Context, req api2.AddPermissionReq) error {
	//判断是否存在
	var existPermission model.AdminPermissionV2

	err := svc.db.WithContext(c).Where("slug = ?", req.Slug).First(&existPermission).Error

	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}
	if existPermission.ID > 0 {
		return appError.NewErr("权限标志：【" + req.Slug + "】已经存在")
	}
	//创建
	var roleData = &model.AdminPermissionV2{
		Name:       req.Name,
		Slug:       req.Slug,
		GroupID:    req.GroupID,
		HTTPMethod: req.HttpMethod,
		HTTPPath:   req.HttpPath,
		SystemType: req.SystemType,
	}
	return svc.db.WithContext(c).Create(roleData).Error
}

func (svc *adminPermissionSvc) Lists(c *gin.Context, param api2.AdminPermissionReq) (*api.PaginationListResp, error) {

	var r api.PaginationListResp
	var list []api2.AdminPermissionResp

	query := svc.db.WithContext(c).Model(&model.AdminPermissionV2{}).Select("admin_permissions_v2.*,g.name as group_name").
		Joins("left join admin_permission_groups_v2 g on admin_permissions_v2.group_id = g.id")

	//条件处理
	if param.Name != "" {
		query = query.Where("admin_permissions_v2.name like ?", "%"+param.Name+"%")
	}
	if param.Slug != "" {
		query = query.Where("admin_permissions_v2.slug = ?", param.Slug)
	}
	if param.SystemType != "" {
		query = query.Where("admin_permissions_v2.system_type = ?", param.SystemType)
	}

	// 总数
	err := query.Count(&r.Total).Error
	if err != nil {
		return &r, err
	}

	// 分页查询
	limit, offset := param.PageSize, (param.Page-1)*param.PageSize
	err = query.Limit(limit).Offset(offset).Scan(&list).Error
	if err != nil {
		return &r, err
	}

	r.Data, r.PageSize, r.Page = list, limit, param.Page

	return &r, nil
}

func (svc *adminPermissionSvc) Update(c *gin.Context, req api2.AddPermissionReq) error {
	//判断是否存在
	var existPermission model.AdminPermissionV2

	err := svc.db.WithContext(c).Where("id = ?", req.ID).First(&existPermission).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return appError.NewErr("权限不存在")
	} else if err != nil {
		return err
	}

	//修改
	existPermission.Name = req.Name
	existPermission.GroupID = req.GroupID
	existPermission.Slug = req.Slug
	existPermission.HTTPPath = req.HttpPath
	existPermission.HTTPMethod = req.HttpMethod
	existPermission.SystemType = req.SystemType

	return svc.db.WithContext(c).Save(existPermission).Error
}

func (svc *adminPermissionSvc) Delete(c *gin.Context, id int) error {
	//判断是否已经存在
	var exist model.AdminPermissionV2

	err := svc.db.WithContext(c).Where("id = ?", id).First(&exist).Error

	if errors.Is(err, gorm.ErrRecordNotFound) {
		return appError.NewErr("权限不存在")
	}

	if err != nil {
		return err
	}
	return svc.db.WithContext(c).Delete(&exist).Error
}

func (svc *adminPermissionSvc) ListAll(c *gin.Context, systemType string) (*[]api2.AdminPermissionResp, error) {

	var list []api2.AdminPermissionResp

	query := svc.db.WithContext(c).Model(&model.AdminPermissionV2{}).Select("admin_permissions_v2.*,g.name as group_name").
		Joins("left join admin_permission_groups_v2 g on admin_permissions_v2.group_id = g.id")
	//条件处理
	if systemType != "" {
		query = query.Where("admin_permissions_v2.system_type =?", systemType)
	}

	err := query.Scan(&list).Error
	return &list, err
}

func (svc *adminPermissionSvc) AllRoutes(c *gin.Context) (*map[string][]api2.Route, error) {
	var data []api2.Route
	for _, v := range consts.AllRoutes {
		data = append(data, api2.Route{
			Path:   v.Path,
			Method: v.Method,
		})
	}
	// 创建一个 map 来根据第一二级路径进行分组
	groupedRoutes := make(map[string][]api2.Route)

	for _, route := range data {
		// 分割路径
		parts := splitPath(route.Path)
		if len(parts) < 3 {
			continue
		}

		// 真正的分组键是由前两个部分组成的
		groupKey := ""
		if len(parts) >= 2 {
			groupKey = parts[0] + "/" + parts[1]
		} else if len(parts) == 1 {
			groupKey = parts[0]
		} else {
			groupKey = "other"
		}

		// 将路由添加到对应的分组中
		groupedRoutes[groupKey] = append(groupedRoutes[groupKey], route)
	}

	for _, routes := range groupedRoutes {
		// 使用 sort.Slice 对切片进行排序
		sort.Slice(routes, func(i, j int) bool {
			if routes[i].Path == routes[j].Path {
				return routes[i].Method < routes[j].Method // 按路径排序
			}
			return routes[i].Path < routes[j].Path // 按方法排序
		})
	}
	return &groupedRoutes, nil
}

// 辅助函数：分割路径
func splitPath(path string) []string {
	// 去掉开头的斜杠
	if len(path) > 0 && path[0] == '/' {
		path = path[1:]
	}
	// 分割路径
	return strings.Split(path, "/")
}
