package types

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
)

// JSONStringArray 自定义 JSON 字符串数组类型
type JSONStringArray []string

func (j JSONStringArray) Value() (driver.Value, error) {
	if len(j) == 0 {
		return nil, nil // 返回空 JSON 数组
	}
	bytes, err := json.Marshal(j)
	if err != nil {
		return nil, err
	}
	return string(bytes), nil // 确保返回的是字符串
}

func (j *JSONStringArray) Scan(value interface{}) error {
	if value == nil {
		*j = JSONStringArray{}
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, j)
}

func (j *JSONStringArray) MarshalJSON() ([]byte, error) {
	if len(*j) == 0 {
		return []byte("[]"), nil
	}
	return json.Marshal([]string(*j))
}

func (j *JSONStringArray) UnmarshalJSON(data []byte) error {
	if string(data) == "null" {
		*j = make(JSONStringArray, 0)
		return nil
	}

	var s []string
	if err := json.Unmarshal(data, &s); err != nil {
		return err
	}
	*j = JSONStringArray(s)
	return nil
}

func (j *JSONStringArray) AddPrefix(prefix string) {
	for i, v := range *j {
		(*j)[i] = prefix + v
	}
}

func (j *JSONStringArray) Filter(fn func(string) bool) {
	var result JSONStringArray
	for _, v := range *j {
		if fn(v) {
			result = append(result, v)
		}
	}
	*j = result
}

func (j *JSONStringArray) Map(fn func(string) string) {
	for i, v := range *j {
		(*j)[i] = fn(v)
	}
}
