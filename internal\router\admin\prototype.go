package admin

import (
	dao1 "marketing/internal/dao"
	endpoint "marketing/internal/dao/endpoint"
	dao "marketing/internal/dao/prototype"
	"marketing/internal/handler/admin/prototype"
	service "marketing/internal/service/prototype"

	"marketing/internal/pkg/db"

	"github.com/gin-gonic/gin"
)

type PrototypeRouter struct {
	prototypeHandler         prototype.PrototypeHandler
	upEndpointNumberHandler  prototype.UpEndpointNumberHandler
	upCounterHandler         prototype.UpCounterHandler
	upCounterEndpointHandler prototype.UpCounterEndpointHandler
}

func NewPrototypeRouter() *PrototypeRouter {
	var Db = db.GetDB()
	endpointDao := endpoint.NewEndpointDao(Db)
	prototypeDao := dao.NewPrototypeDao(Db)
	prototypeCache := dao.NewPrototypeCache()
	PrototypeConfigDao := dao.NewPrototypeConfig(Db)
	prototypeService := service.NewPrototypeService(prototypeDao, endpointDao, PrototypeConfigDao, prototypeCache)
	prototypeHandler := prototype.NewPrototypeHandler(prototypeService)

	// 初始化上样终端数量服务和控制器
	upEndpointNumberDao := dao.NewUpEndpointNumberDao(Db)
	upEndpointNumberService := service.NewUpEndpointNumberService(upEndpointNumberDao, endpointDao)
	upEndpointNumberHandler := prototype.NewUpEndpointNumberHandler(upEndpointNumberService)

	// 初始化上样计数器服务和控制器
	modelCategoryDao := dao1.NewModelCategoryDao()
	upCounterDao := dao.NewUpCounterDao(Db)
	machineTypeDao := dao1.NewMachineTypeDao()
	machineTypeRelationDao := dao1.NewMachineTypeRelationDao(Db)
	upCounterService := service.NewUpCounterService(upCounterDao, modelCategoryDao, machineTypeDao, machineTypeRelationDao)
	upCounterHandler := prototype.NewUpCounterHandler(upCounterService)
	// 初始化上柜机型服务和控制器
	upCounterEndpointDao := dao.NewUpCounterEndpointDao(Db)
	upCounterEndpointService := service.NewUpCounterEndpointService(upCounterEndpointDao)
	upCounterEndpointHandler := prototype.NewUpCounterEndpointHandler(upCounterEndpointService)

	return &PrototypeRouter{
		prototypeHandler:         prototypeHandler,
		upEndpointNumberHandler:  upEndpointNumberHandler,
		upCounterHandler:         upCounterHandler,
		upCounterEndpointHandler: upCounterEndpointHandler,
	}
}

func (p *PrototypeRouter) Register(r *gin.RouterGroup) {
	r.GET("/type-dropdown", p.prototypeHandler.GetType)
	r.GET("/model-dropdown", p.prototypeHandler.GetPrototypeModelList)
	r.GET("/stat", p.prototypeHandler.PrototypeStat)
	r.POST("/import/renew-amount", p.prototypeHandler.ImportRenewData)

	prototypeRouter := r.Group("/list")
	{
		prototypeRouter.GET("", p.prototypeHandler.GetPrototype)
		prototypeRouter.PUT("/out", p.prototypeHandler.PrototypeOut)
	}

	upEndpointNumber := r.Group("/up-endpoint-number")
	{
		// 注册上样终端数量相关路由
		upEndpointNumber.GET("", p.upEndpointNumberHandler.GetEndpointNumber)
		upEndpointNumber.POST("", p.upEndpointNumberHandler.Add)
		upEndpointNumber.GET("/export", p.upEndpointNumberHandler.ExportEndpointNumber)
	}

	// 注册上柜机型相关路由
	upCounter := r.Group("/up-counter")
	{
		upCounter.GET("", p.upCounterHandler.GetList)
		upCounter.POST("/status", p.upCounterHandler.UpdateStatus)
		upCounter.GET("/endpoint", p.upCounterEndpointHandler.GetList)
		upCounter.GET("/endpoint-view", p.upCounterEndpointHandler.GetListEndpointMain)
		upCounter.POST("/approve", p.upCounterEndpointHandler.Approve)
	}

	// 注册上样下市相关路由
	configRouter := r.Group("/demo-down")
	{
		configRouter.GET("", p.prototypeHandler.GetPrototypeDemoDownList)
		configRouter.POST("/update", p.prototypeHandler.AddPrototypeDemoDown)
	}
}
