package model

import "time"

type AdminRoleUser struct {
	RoleID    uint      `gorm:"column:role_id;type:int;not null" json:"role_id"`
	UserID    uint      `gorm:"column:user_id;type:int;not null" json:"user_id"`
	CreatedAt time.Time `gorm:"column:created_at;type:timestamp;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at;type:timestamp;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP" json:"updated_at"`
}

// TableName 设置表名为 admin_role_users(旧版用户管理，在重构没完成前，先保留)
func (AdminRoleUser) TableName() string {
	return "admin_role_users"
}
