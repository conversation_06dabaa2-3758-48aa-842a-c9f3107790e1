package materials

import "time"

type MarketingCategory struct {
	ID        uint   `json:"id"`
	Title     string `json:"title" binding:"required"`
	ParentID  uint   `json:"parent_id"`
	Order     uint   `json:"order"`
	CreatedAt string `json:"created_at"`
	UpdatedAt string `json:"updated_at"`
	Route     string `json:"route"`
	Count     int    `json:"count"`
}
type Tips struct {
	ID    uint   `json:"id"`
	Title string `json:"title"`
}

// InfoListReq 资料列表搜索条件
type InfoListReq struct {
	ID       int    `json:"id"`
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
	Status   string `json:"status"`
	Kind     int    `json:"kind"`
	Label    string `json:"label"`
}

// MarketingDownload 资料列表
type MarketingDownload struct {
	ID            uint     `json:"id"`
	Name          string   `json:"name"`
	Description   string   `json:"description"`
	Preview       []string `json:"preview"`
	Path          string   `json:"path"`
	Category      string   `json:"category"`
	Status        uint8    `json:"status"`
	DownloadCount uint     `json:"download_count"`
	CreatedAt     string   `json:"created_at"`
	UpdatedAt     string   `json:"updated_at"`
	Score         float64  `json:"score"`
	//连接字段
}
type MarketingCategoryRes struct {
	ID        uint      `json:"id"`
	Title     string    `json:"title" binding:"required"`
	ParentID  uint      `json:"parent_id"`
	Order     uint      `json:"order"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	Route     string    `json:"route"`
	Count     int       `json:"count"`
}
