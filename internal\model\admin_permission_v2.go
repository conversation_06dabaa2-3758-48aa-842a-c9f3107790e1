package model

import (
	"gorm.io/gorm"
	"time"
)

// AdminPermissionV2 代表 admin_permissions_v2 表的模型
type AdminPermissionV2 struct {
	ID         uint           `gorm:"primaryKey;autoIncrement" json:"id"`                // 主键，自增
	GroupID    uint           `gorm:"not null"`                                          // 主键字段，权限组 ID
	Name       string         `gorm:"type:varchar(50);not null" json:"name"`             // 名称
	Slug       string         `gorm:"type:varchar(50);uniqueIndex;not null" json:"slug"` // 唯一标识符
	HTTPMethod string         `gorm:"type:varchar(255);default:null" json:"http_method"` // HTTP 方法
	HTTPPath   string         `gorm:"type:text;not null" json:"http_path"`               // HTTP 路径
	SystemType string         `gorm:"size:50;not null"`
	CreatedAt  time.Time      `gorm:"type:timestamp;default:CURRENT_TIMESTAMP" json:"created_at"`                             // 创建时间
	UpdatedAt  time.Time      `gorm:"type:timestamp;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP" json:"updated_at"` // 更新时间
	DeletedAt  gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`                                                      // 删除时间，soft delete
}

// TableName 设置表的别名
func (AdminPermissionV2) TableName() string {
	return "admin_permissions_v2"
}
