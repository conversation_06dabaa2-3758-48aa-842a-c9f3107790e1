package information

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/api/materials"
	"marketing/internal/handler"
	"marketing/internal/service"
)

type TrainCategoryHandle struct {
	svc service.TrainService
}

func (h *TrainCategoryHandle) Upsert(c *gin.Context) {
	var param materials.KindAddReq
	if err := c.ShouldBindJSON(&param); err != nil {
		handler.Error(c, err)
		return
	}
	id, err := h.svc.Upsert(c, param, "train_category")
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"id": id,
	})
}

func (h *TrainCategoryHandle) Save(c *gin.Context) {
	var param []materials.KindSaveReq
	if err := c.ShouldBindJSON(&param); err != nil {
		handler.Error(c, err)
		return
	}
	if err := h.svc.Save(c, param, "train_category"); err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

func (h *TrainCategoryHandle) List(c *gin.Context) {
	list, err := h.svc.List(c, "train_category")
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, list)
}

func (h *TrainCategoryHandle) Detail(c *gin.Context) {
	id := c.Param("id")
	detail, err := h.svc.Detail(c, id, "train_category")
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"detail": detail,
	})
}

func (h *TrainCategoryHandle) Tips(c *gin.Context) {
	tips, err := h.svc.Tips(c, "train_category")
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, tips)
}

func (h *TrainCategoryHandle) Delete(c *gin.Context) {
	id := c.Param("id")
	if err := h.svc.Delete(c, id, "train_category"); err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

func (h *TrainCategoryHandle) AllTips(c *gin.Context) {
	tips, err := h.svc.AllTips(c, "train_category")
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, tips)
}

func NewTrainCategoryHandle(svc service.TrainService) *TrainCategoryHandle {
	return &TrainCategoryHandle{svc: svc}
}
