package endpoint

//import (
//	"github.com/gin-gonic/gin"
//	"marketing/internal/handler"
//	"marketing/internal/service"
//	"net/http"
//	"strconv"
//)
//
//// EndpointImageHistoryController 控制器，处理终端形象历史记录相关请求
//type EndpointImageHistoryController struct {
//	endpointImageHistoryService *service.EndpointImageHistoryService
//}
//
//// NewEndpointImageHistoryController 创建 EndpointImageHistoryController 实例
//func NewEndpointImageHistoryController(endpointImageHistoryService *service.EndpointImageHistoryService) *EndpointImageHistoryController {
//	return &EndpointImageHistoryController{endpointImageHistoryService: endpointImageHistoryService}
//}
//
//// Index 获取终端形象历史记录
//func (controller *EndpointImageHistoryController) GetEndpointImageHistories(c *gin.Context) {
//	idstr := c.Param("id")
//	// 转换 id 为 uint 类型
//	idUint, err := strconv.Atoi(idstr)
//	if err != nil {
//		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
//		return
//	}
//	// 获取业务层数据
//	data, err := controller.endpointImageHistoryService.GetEndpointImageHistories(idUint)
//	if err != nil {
//		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch endpoint image histories"})
//		return
//	}
//
//	// 返回查询结果
//	handler.Success(c, gin.H{
//		"data": data,
//	})
//
//	//// 返回响应
//	//c.JSON(http.StatusOK, gin.H{
//	//	"header": "终端形象历史记录",
//	//	"body":   data,
//	//})
//}
