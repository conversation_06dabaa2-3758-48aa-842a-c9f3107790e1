package operation

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/handler"
	"marketing/internal/pkg/e"
	"marketing/internal/pkg/errors"
	"marketing/internal/service/operation"
)

type OpArticleCategory struct {
	svc operation.OpArticleCategorySvcInterface
}

func NewOpArticleCategory(svc operation.OpArticleCategorySvcInterface) *OpArticleCategory {
	return &OpArticleCategory{
		svc: svc,
	}
}

func (o *OpArticleCategory) GetArticleCategoryList(c *gin.Context) {
	list := o.svc.GetAllArticleCategory(c)
	handler.Success(c, gin.H{
		"list": list,
	})
}

func (o *OpArticleCategory) RefreshArticleCategory(c *gin.Context) {
	list := o.svc.RefreshArticleCategory(c)
	handler.Success(c, gin.H{
		"list": list,
	})
}

func (o *OpArticleCategory) EditArticleCategory(c *gin.Context) {
	id := e.ReqParamInt(c, "id", -1)
	pid := e.ReqParamInt(c, "pid")
	name := e.ReqParamStr(c, "name")
	enabled := e.ReqParamInt(c, "enabled")
	order := e.ReqParamInt(c, "order")
	createdBy := c.GetUint("uid")

	if id != -1 {
		err := o.svc.EditArticleCategory(c, id, pid, name, enabled, order)
		if err != nil {
			handler.Error(c, errors.NewErr(err.Error()))
			return
		}
	} else {
		err := o.svc.AddArticleCategory(c, pid, name, enabled, order, int(createdBy))
		if err != nil {
			handler.Error(c, errors.NewErr(err.Error()))
			return
		}
	}

	handler.Success(c, gin.H{})
}

func (o *OpArticleCategory) DeleteArticleCategory(c *gin.Context) {
	id := e.ReqParamInt(c, "id")
	err := o.svc.DelArticleCategory(c, id)
	if err != nil {
		handler.Error(c, errors.NewErr("删除内容标签失败"))
		return
	}

	handler.Success(c, gin.H{})
}
