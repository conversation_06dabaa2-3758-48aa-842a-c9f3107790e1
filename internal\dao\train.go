package dao

import (
	"marketing/internal/api/materials"
	"marketing/internal/model"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type TrainDao interface {
	Create(c *gin.Context, param materials.KindAddReq, kind string) (int, error)
	Update(c *gin.Context, param materials.KindAddReq, kind string) (int, error)
	Save(c *gin.Context, param []materials.KindSaveReq, s string) error
	List(c *gin.Context, s string) ([]materials.KindList, error)
	Detail(c *gin.Context, id string, s string) (model.TrainType, error)
	ParentName(c *gin.Context, id uint, s string) (string, error)
	Tips(c *gin.Context, s string) ([]map[string]any, error)
	AllTips(c *gin.Context, s string) ([]map[string]any, error)
	Delete(c *gin.Context, id string, s string) error
	HadInfo(c *gin.Context, id string, s string) bool
	InfoList(c *gin.Context, param materials.TrainInfoListReq) ([]materials.TrainInfoList, int64, error)
	InfoUpdate(c *gin.Context, param materials.TrainInfoUpdateReq) error
	InfoUpdates(c *gin.Context, param materials.TrainInfoUpdate[string]) error
	InfoDetail(c *gin.Context, id string) (materials.TrainInfoDetail[string], error)
	GetBatchCategory(c *gin.Context, ds []int) ([]string, error)
	VideoUpdate(c *gin.Context, param materials.VideoAddReq[string]) (int, error)
	VideoCreate(c *gin.Context, param materials.VideoAddReq[string]) (int, error)
	VideoDelete(c *gin.Context, id string) error
	VideoList(c *gin.Context, param materials.TrainVideoListReq) ([]model.TrainVideos, int64, error)
	VideoDetail(c *gin.Context, id string) (model.TrainVideos, error)
	Upload(c *gin.Context, id string) error
	GetByIDs(c *gin.Context, ids []int) ([]model.Train, error)
	GetNewestTrains(c *gin.Context, limit int) ([]model.Train, error)
}

// difference 函数用于计算两个整数切片 New 和 exist 的差集
// 即返回一个包含在 New 中但不在 exist 中的元素的新切片
func difference(New, exist []int) []int {
	mb := make(map[int]struct{})
	for _, item := range exist {
		mb[item] = struct{}{}
	}
	var diff []int
	for _, item := range New {
		if _, ok := mb[item]; !ok {
			diff = append(diff, item)
		}
	}
	return diff
}

type GormTrainDao struct {
	db *gorm.DB
}

func (g *GormTrainDao) Upload(c *gin.Context, id string) error {
	return g.db.WithContext(c).Model(model.Train{}).
		Where("id = ?", id).
		Update("download_count", gorm.Expr("download_count + ?", 1)).Error
}

func (g *GormTrainDao) AllTips(c *gin.Context, s string) ([]map[string]any, error) {
	var res []map[string]any
	g.db.WithContext(c).Table(s).Select("id,title,parent_id").Order("CASE WHEN `order` = 0 THEN 1 ELSE 0 END,`order`").Find(&res)
	return res, nil
}

func (g *GormTrainDao) VideoDetail(c *gin.Context, id string) (model.TrainVideos, error) {
	var res model.TrainVideos
	g.db.WithContext(c).Table("train_videos").Where("id = ?", id).First(&res)
	return res, nil
}

func (g *GormTrainDao) VideoList(c *gin.Context, param materials.TrainVideoListReq) ([]model.TrainVideos, int64, error) {
	curDB := g.db.WithContext(c).Table("train_videos")
	if param.Status != "" {
		curDB = curDB.Where("status = ?", param.Status)
	}
	if param.TrainID != 0 {
		curDB = curDB.Where("id = ?", param.TrainID)
	}
	if param.Label != "" {
		curDB = curDB.Where("title LIKE ?", "%"+param.Label+"%").
			Or("description LIKE ?", "%"+param.Label+"%")
	}
	var total int64
	curDB.Count(&total)
	var res []model.TrainVideos
	err := curDB.Offset((param.Page - 1) * param.PageSize).Limit(param.PageSize).Find(&res).Error
	return res, total, err
}

func (g *GormTrainDao) VideoDelete(c *gin.Context, id string) error {
	return g.db.WithContext(c).Where("id = ?", id).Delete(&model.TrainVideos{}).Error
}

func (g *GormTrainDao) VideoUpdate(c *gin.Context, param materials.VideoAddReq[string]) (int, error) {
	g.db.WithContext(c).Model(&model.TrainVideos{}).Where("id = ?", param.ID).Updates(map[string]any{
		"title":       param.Title,
		"preview":     param.Preview,
		"path":        param.Path,
		"description": param.Description,
		"status":      param.Status,
	})
	return param.ID, nil
}

func (g *GormTrainDao) VideoCreate(c *gin.Context, param materials.VideoAddReq[string]) (int, error) {
	err := g.db.WithContext(c).Create(&model.TrainVideos{
		Title:       param.Title,
		Path:        param.Path,
		Preview:     param.Preview,
		Description: param.Description,
		Status:      param.Status,
	}).Error
	return param.ID, err
}

func (g *GormTrainDao) GetBatchCategory(c *gin.Context, ds []int) ([]string, error) {
	var res []string
	err := g.db.WithContext(c).Table("train_category AS t").Where("t.id IN (?)", ds).Select("t.title").Find(&res).Error
	return res, err
}

func (g *GormTrainDao) InfoDetail(c *gin.Context, id string) (materials.TrainInfoDetail[string], error) {
	var res materials.TrainInfoDetail[string]
	err := g.db.WithContext(c).Table("train").
		Joins("LEFT JOIN train_type AS t ON t.id = train.type").
		Joins("LEFT JOIN train_category_relation AS r ON r.train_id = train.id").
		Select("train.*, t.title AS type_name, GROUP_CONCAT(r.category_id) AS category_ids").
		Where("train.id = ?", id).First(&res).Error
	return res, err
}

// InfoUpdates 更新train课程表
func (g *GormTrainDao) InfoUpdates(c *gin.Context, param materials.TrainInfoUpdate[string]) error {
	//开启事务
	err := g.db.Transaction(func(tx *gorm.DB) error {
		//更新train课程表
		err := tx.WithContext(c).Model(&model.Train{}).Where("id = ?", param.Train.ID).
			Updates(map[string]any{
				"status":               param.Train.Status,
				"name":                 param.Train.Name,
				"description":          param.Train.Description,
				"preview":              param.Train.Preview,
				"path":                 param.Train.Path,
				"article_link":         param.Train.ArticleLink,
				"banner_image":         param.Train.BannerImage,
				"type":                 param.Train.Type,
				"top":                  param.Train.Top,
				"share":                param.Train.Share,
				"star":                 param.Train.Star,
				"banner":               param.Train.Banner,
				"credit":               param.Train.Credit,
				"credit_learning_time": param.Train.CreditLearningTime,
			}).Error
		if err != nil {
			return err
		}
		//获取当前train的所有category_id
		var ids []int
		err = tx.WithContext(c).Model(&model.TrainCategoryRelation{}).
			Where("train_id = ?", param.Train.ID).
			Pluck("category_id", &ids).Error
		if err != nil {
			return err
		}
		//删除不在param.Category中的记录
		if param.Category == nil {
			err = tx.WithContext(c).Model(&model.TrainCategoryRelation{}).
				Where("train_id = ?", param.Train.ID).
				Delete(&model.TrainCategoryRelation{}).Error
		}
		err = tx.WithContext(c).Model(&model.TrainCategoryRelation{}).
			Where("train_id = ? AND category_id NOT IN (?)", param.Train.ID, param.Category).
			Delete(&model.TrainCategoryRelation{}).Error
		if err != nil {
			return err
		}
		//添加新的记录
		newCategories := difference(param.Category, ids)
		if len(newCategories) > 0 {
			relations := make([]model.TrainCategoryRelation, len(newCategories))
			for i, v := range newCategories {
				relations[i] = model.TrainCategoryRelation{
					TrainID:    param.Train.ID,
					CategoryID: uint(v),
				}
			}
			err = tx.WithContext(c).Model(&model.TrainCategoryRelation{}).Create(&relations).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
	return err
}

func (g *GormTrainDao) InfoUpdate(c *gin.Context, param materials.TrainInfoUpdateReq) error {
	err := g.db.WithContext(c).Model(&model.Train{}).Where("id = ?", param.ID).Update(param.Kind, param.Status).Error
	return err
}

func (g *GormTrainDao) InfoList(c *gin.Context, param materials.TrainInfoListReq) ([]materials.TrainInfoList, int64, error) {
	var res []materials.TrainInfoList
	curDB := g.db.WithContext(c).Table("train").
		Joins("LEFT JOIN train_type AS t ON t.id = COALESCE(train.type, 0)").
		Joins("LEFT JOIN train_category_relation AS r ON r.train_id = train.id").
		Joins("LEFT JOIN train_learning_history AS h ON h.target_id = train.id").
		Joins("LEFT JOIN admin_users AS u ON u.id=train.created_by").
		Select("train.id, train.name, train.description, train.preview, train.path, train.article_link, " +
			"t.title AS type_name,  train.status, train.top, train.share, train.star, " +
			"train.download_count, train.updated_at, train.banner, train.credit, train.credit_learning_time, " +
			"train.created_by,u.name AS admin," +
			"COUNT(DISTINCT CASE WHEN h.id IS NULL THEN 1 ELSE h.id END) AS learn_num, " +
			"COUNT(DISTINCT CASE WHEN h.user_id IS NULL THEN 1 ELSE h.user_id END) AS people_num," +
			"GROUP_CONCAT(DISTINCT CASE WHEN r.category_id IS NULL THEN 0 ELSE r.category_id END) AS CategoryName").
		Group("train.id")
	if param.Type != 0 {
		curDB = curDB.Where("train.type = ?", param.Type)
	}
	if param.Category != 0 {
		curDB = curDB.Where("train.category = ?", param.Category)
	}
	if param.Status != "" {
		curDB = curDB.Where("train.status = ?", param.Status)
	}
	if param.Star != "" {
		curDB = curDB.Where("train.star = ?", param.Star)
	}
	if param.Top != "" {
		curDB = curDB.Where("train.top = ?", param.Top)
	}
	if param.Share != "" {
		curDB = curDB.Where("train.share = ?", param.Share)
	}
	if param.Banner != "" {
		curDB = curDB.Where("train.banner = ?", param.Banner)
	}
	if param.Label != "" {
		curDB = curDB.Where("train.description LIKE ?", "%"+param.Label+"%").
			Or("train.name LIKE ?", "%"+param.Label+"%")
	}
	countDB := curDB
	var total int64
	err := countDB.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}
	err = curDB.Order("top DESC,id DESC").Offset((param.Page - 1) * param.PageSize).Limit(param.PageSize).Find(&res).Error
	return res, total, err
}
func (g *GormTrainDao) HadInfo(c *gin.Context, id string, s string) bool {
	sql := s + " =?"
	err := g.db.WithContext(c).Table("train").Where(sql, id).First(&model.Train{}).Error
	if err != nil {
		return false
	}
	return true
}

func (g *GormTrainDao) Delete(c *gin.Context, id string, s string) error {
	err := g.db.WithContext(c).Table(s).Where("id = ?", id).Delete(&model.TrainType{}).Error
	return err
}

// Tips 父节点遍历
func (g *GormTrainDao) Tips(c *gin.Context, s string) ([]map[string]any, error) {
	var res []map[string]any
	err := g.db.WithContext(c).Table(s).Select("id,title").Where("parent_id=0").Find(&res).Error
	return res, err
}

func (g *GormTrainDao) ParentName(c *gin.Context, id uint, s string) (string, error) {
	var title string
	err := g.db.WithContext(c).Table(s).Select("title").Where("id = ?", id).First(&title).Error
	return title, err
}

func (g *GormTrainDao) Detail(c *gin.Context, id string, s string) (model.TrainType, error) {
	var res model.TrainType
	err := g.db.WithContext(c).Table(s).Where("id = ?", id).First(&res).Error
	return res, err
}

func (g *GormTrainDao) List(c *gin.Context, s string) ([]materials.KindList, error) {
	var res []materials.KindList
	// 查询
	s = s + " AS s"
	err := g.db.WithContext(c).Table(s).
		Joins("LEFT JOIN train ON train.type = s.id").
		Select("s.*,count(train.id) AS num").
		Group("s.id").
		Order("`order`").Find(&res).Error
	return res, err
}

func (g *GormTrainDao) Save(c *gin.Context, param []materials.KindSaveReq, s string) error {
	err := g.db.WithContext(c).Table(s).Save(&param).Error
	return err
}

func (g *GormTrainDao) Create(c *gin.Context, param materials.KindAddReq, kind string) (int, error) {
	err := g.db.WithContext(c).Table(kind).Create(&param).Error
	return param.ID, err
}

func (g *GormTrainDao) Update(c *gin.Context, param materials.KindAddReq, kind string) (int, error) {
	err := g.db.WithContext(c).Table(kind).Where("id = ?", param.ID).Updates(map[string]any{
		"title":      param.Title,
		"parent_id":  param.ParentID,
		"updated_at": time.Now(),
	}).Error
	return param.ID, err
}

func (g *GormTrainDao) GetByIDs(c *gin.Context, ids []int) ([]model.Train, error) {
	var res []model.Train
	err := g.db.WithContext(c).Table("train").Where("id IN ?", ids).Find(&res).Error
	return res, err
}

// GetNewestTrains 获取最新培训资源
func (g *GormTrainDao) GetNewestTrains(c *gin.Context, limit int) ([]model.Train, error) {
	var res []model.Train
	err := g.db.WithContext(c).Table("train").
		Select("id, name").
		Order("updated_at DESC").
		Limit(limit).
		Find(&res).Error
	return res, err
}

func NewGormTrainDao(db *gorm.DB) TrainDao {
	return &GormTrainDao{db: db}
}
