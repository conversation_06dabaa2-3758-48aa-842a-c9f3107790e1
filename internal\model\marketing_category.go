package model

import (
	"gorm.io/gorm"
	"time"
)

type MarketingCategory struct {
	ID        uint      `json:"id" gorm:"primaryKey;autoIncrement;column:id"`
	Title     string    `json:"title" gorm:"column:title"`
	ParentID  uint      `json:"parent_id" gorm:"column:parent_id"`
	Order     uint      `json:"order" gorm:"column:order"`
	CreatedAt time.Time `json:"created_at" gorm:"column:created_at"`
	UpdatedAt time.Time `json:"updated_at" gorm:"column:updated_at"`
	Route     string    `json:"route" gorm:"column:route"`
}
type MarketingDownload struct {
	ID            uint      `json:"id" gorm:"primaryKey;autoIncrement;column:id"`
	Name          string    `json:"name" gorm:"column:name"`
	Description   string    `json:"description" gorm:"column:description;type:text"`
	Preview       string    `json:"preview" gorm:"column:preview;type:text"`
	Path          string    `json:"path" gorm:"column:path;type:text"`
	Category      uint      `json:"category" gorm:"column:category"`
	Status        uint8     `json:"status" gorm:"column:status"`
	Top           uint8     `json:"top" gorm:"column:top"`
	DownloadCount uint      `json:"download_count" gorm:"column:download_count"`
	SyncOperation uint8     `json:"sync_operation" gorm:"column:sync_operation"`
	OpArticleID   uint      `json:"op_article_id" gorm:"column:op_article_id"`
	OpShareable   uint8     `json:"op_shareable" gorm:"column:op_shareable"`
	CreatedAt     time.Time `json:"created_at" gorm:"column:created_at"`
	UpdatedAt     time.Time `json:"updated_at" gorm:"column:updated_at"`
}
type MarketingDownloadEvaluation struct {
	ID                  uint      `json:"id" gorm:"primaryKey;autoIncrement;column:id"`
	UserID              uint      `json:"user_id" gorm:"column:user_id;not null"`
	MarketingDownloadID uint      `json:"marketing_download_id" gorm:"column:marketing_download_id;not null"`
	Score               uint8     `json:"score" gorm:"column:score;not null"`
	Comment             string    `json:"comment" gorm:"column:comment;type:varchar(150);default:''"`
	CreatedAt           time.Time `json:"created_at" gorm:"column:created_at;default:CURRENT_TIMESTAMP"`
	UpdatedAt           time.Time `json:"updated_at" gorm:"column:updated_at;default:'0000-00-00 00:00:00'"`
	gorm.Model
}

func (MarketingCategory) TableName() string {
	return "marketing_category"
}
func (MarketingDownload) TableName() string {
	return "marketing_download"
}
func (MarketingDownloadEvaluation) TableName() string {
	return "marketing_download_evaluation"
}
