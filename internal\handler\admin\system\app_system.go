package system

import (
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	api "marketing/internal/api/system"
	"marketing/internal/handler"
	appError "marketing/internal/pkg/errors"
	service "marketing/internal/service/system"
)

type AppSystemHandler interface {
	Add(c *gin.Context)
	Update(c *gin.Context)
	Delete(c *gin.Context)
	GetAppSystemList(c *gin.Context)
	GetParentAppSystem(c *gin.Context)
}

type appSystem struct {
	svc service.AppSystemSvc
}

func NewAppSystemHandler(svc service.AppSystemSvc) AppSystemHandler {
	return &appSystem{
		svc: svc,
	}
}

func (h *appSystem) Add(c *gin.Context) {
	var req api.AddAppSystemReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	err := h.svc.Add(c, &req)
	if err != nil {
		handler.Error(c, err)
	}
	handler.Success(c, nil)
}

func (h *appSystem) Update(c *gin.Context) {
	var req api.AddAppSystemReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	id := c.Param("id")
	if id == "" {
		handler.Error(c, appError.NewErr("id不能为空"))
		return
	}
	req.ID = cast.ToUint(id)
	err := h.svc.Update(c, &req)
	if err != nil {
		handler.Error(c, err)
	}
	handler.Success(c, nil)
}
func (h *appSystem) Delete(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		handler.Error(c, appError.NewErr("id不能为空"))
		return
	}
	err := h.svc.Delete(c, cast.ToUint(id))
	if err != nil {
		handler.Error(c, err)
	}
	handler.Success(c, nil)
}

func (h *appSystem) GetAppSystemList(c *gin.Context) {
	var req api.AppSystemReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	req.PaginationParams.SetDefaults()
	appSystemList, total, err := h.svc.GetAppSystemList(c, req)
	if err != nil {
		handler.Error(c, err)
	}
	handler.Success(c, gin.H{
		"data":  appSystemList,
		"total": total,
	})
}

func (h *appSystem) GetParentAppSystem(c *gin.Context) {
	var req api.AppSystemReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	req.PaginationParams.SetDefaults()
	appSystemList, err := h.svc.GetParentAppSystemKey(c, req)
	if err != nil {
		handler.Error(c, err)
	}
	handler.Success(c, appSystemList)
}
