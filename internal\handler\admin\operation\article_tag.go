package operation

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/handler"
	"marketing/internal/pkg/e"
	"marketing/internal/pkg/errors"
	"marketing/internal/service/operation"
)

type OpArticleTag struct {
	svc operation.OpArticleTagSvcInterface
}

func NewOpArticleTag(svc operation.OpArticleTagSvcInterface) *OpArticleTag {
	return &OpArticleTag{
		svc: svc,
	}
}

func (o *OpArticleTag) GetArticleTagList(c *gin.Context) {
	pageNum := e.ReqParamInt(c, "page_num")
	pageSize := e.ReqParamInt(c, "page_size")
	list, total := o.svc.GetOpArticleTagList(c, pageNum, pageSize)

	handler.Success(c, gin.H{
		"list":  list,
		"total": total,
	})
}

func (o *OpArticleTag) EditArticleTag(c *gin.Context) {
	id := e.ReqParamInt(c, "id", -1)
	name := e.ReqParamStr(c, "name")
	uid := c.GetUint("uid")
	err := o.svc.EditOpArticleTag(c, id, name, uid)
	if err != nil {
		handler.Error(c, errors.NewErr("编辑文章标签失败:"+err.Error()))
		return
	}

	handler.Success(c, gin.H{})
}

func (o *OpArticleTag) DeleteArticleTag(c *gin.Context) {
	id := e.ReqParamInt(c, "id")
	err := o.svc.DeleteOpArticleTag(c, id)
	if err != nil {
		handler.Error(c, errors.NewErr("删除文章标签失败:"+err.Error()))
		return
	}

	handler.Success(c, gin.H{})
}
