package base

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/dao"
)

type EndpointTypeService interface {
	GetEndpointType(c *gin.Context) ([]map[string]any, error)
}

type endpointTypeService struct {
	repo dao.TypeRepository
}

func NewEndpointTypeService(repo dao.TypeRepository) EndpointTypeService {
	return &endpointTypeService{
		repo: repo,
	}
}

func (e *endpointTypeService) GetEndpointType(c *gin.Context) ([]map[string]any, error) {
	// 获取终端类型
	var result = make([]map[string]any, 0)
	types, err := e.repo.GetEndpointType(c, 0)
	if err != nil {
		return nil, err
	}
	for _, t := range types {
		result = append(result, map[string]any{
			"id":   t.ID,
			"name": t.Name,
		})
	}
	return result, nil
}
