package prototype

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	api "marketing/internal/api/prototype"
	"marketing/internal/consts"
	"marketing/internal/model"
)

// PrototypeConfig 上样终端数量DAO
type PrototypeConfig interface {
	GetPrototypeDemoDownList(c *gin.Context, param *api.PrototypeConfigListSearch) ([]*model.PrototypeConfig, int64, error)
	AddPrototypeConfig(c *gin.Context, param *model.PrototypeConfig) error
}

type prototypeConfig struct {
	db *gorm.DB
}

// NewPrototypeConfig 创建DAO实例
func NewPrototypeConfig(db *gorm.DB) PrototypeConfig {
	return &prototypeConfig{
		db: db,
	}
}

func (p *prototypeConfig) GetPrototypeDemoDownList(c *gin.Context, param *api.PrototypeConfigListSearch) ([]*model.PrototypeConfig, int64, error) {
	var list []*model.PrototypeConfig
	var count int64
	query := p.db.WithContext(c).Model(&model.MachineType{}).Unscoped()
	query = query.Joins("INNER JOIN prototype ON prototype.model_id = machine_type.model_id AND prototype.type = ?", consts.PrototypeTypeDemo)
	query = query.Joins("LEFT JOIN prototype_config ON prototype.model_id = prototype_config.model_id")
	query = query.Group("machine_type.model_id")
	query = query.Order("machine_type.model_id desc")

	// 过滤条件
	if param.Discontinued != nil {
		query = query.Where("prototype_config.discontinued = ?", *param.Discontinued)
	}
	if param.Model != "" {
		query = query.Where("machine_type.name LIKE ?", "%"+param.Model+"%")
	}

	err := query.Count(&count).Error
	if err != nil {
		return nil, 0, err
	}
	fields := []string{
		"machine_type.model_id",
		"machine_type.name as model",
		"prototype_config.discontinued",
		"prototype_config.launched_date",
		"prototype_config.discontinued_date",
	}
	// 分页
	offset := (param.Page - 1) * param.PageSize
	query = query.Offset(offset).Limit(param.PageSize)
	err = query.Select(fields).Find(&list).Error
	if err != nil {
		return nil, 0, err
	}
	return list, count, nil
}

func (p *prototypeConfig) AddPrototypeConfig(c *gin.Context, param *model.PrototypeConfig) error {
	err := p.db.WithContext(c).Model(&model.PrototypeConfig{}).Save(param).Error
	if err != nil {
		return err
	}
	return nil
}
