package model

import (
	"marketing/internal/pkg/types"
)

// AppUpdateHistory App更新历史模型
type AppUpdateHistory struct {
	ID            int              `json:"id" gorm:"primaryKey;autoIncrement;column:id"`
	PkgName       string           `json:"pkg_name" gorm:"type:varchar(100);not null;comment:包名;column:pkg_name"`
	AppName       string           `json:"app_name" gorm:"type:varchar(100);not null;comment:应用名称;column:app_name"`
	VersionName   string           `json:"version_name" gorm:"type:varchar(50);not null;comment:版本名称;column:version_name"`
	UpdateContent string           `json:"update_content" gorm:"type:text;comment:更新内容;column:update_content"`
	Devices       string           `json:"devices" gorm:"type:varchar(255);comment:设备信息;column:devices"`
	UpdatedAt     types.CustomTime `json:"updated_at" gorm:"comment:更新时间;column:updated_at"`
	CreatedAt     types.CustomTime `json:"created_at" gorm:"column:created_at"`
}

// TableName 设置表名
func (AppUpdateHistory) TableName() string {
	return "app_update_history"
}
