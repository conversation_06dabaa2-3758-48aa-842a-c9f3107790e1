package model

import (
	"time"

	"gorm.io/gorm"
)

// OpArticle 运营文章
type OpArticle struct {
	ID                  uint      `gorm:"column:id;primary_key;AUTO_INCREMENT"`                 //
	Title               string    `gorm:"column:title;NOT NULL"`                                //
	Content             string    `gorm:"column:content;NOT NULL"`                              //
	Sleight             string    `gorm:"column:sleight;NOT NULL"`                              // 话术
	AttachmentType      uint8     `gorm:"column:attachment_type;NOT NULL"`                      // 1-图片，2-视频，3-链接，4-文件
	Attachment          string    `gorm:"column:attachment;NOT NULL"`                           // 附件内容，JSON格式
	CategoryID          uint      `gorm:"column:category_id;NOT NULL"`                          //
	Shareable           uint8     `gorm:"column:shareable;NOT NULL"`                            // 是否可分享
	WeworkShareable     uint      `gorm:"column:wework_shareable;NOT NULL"`                     // 是否可通过企业微信分享
	CommentSetting      uint8     `gorm:"column:comment_setting;default:2;NOT NULL"`            // 评论设置，1-无须审核，2-审核可见，3-不可评论
	Enabled             uint8     `gorm:"column:enabled;NOT NULL"`                              // 是否发布
	Top                 uint8     `gorm:"column:top;default:0;NOT NULL"`                        // 是否置顶
	CategoryEnabled     uint      `gorm:"column:category_enabled;default:1;NOT NULL"`           // 所属分类及其祖先分类如果任一禁用，则为0
	MarketingDownloadID uint      `gorm:"column:marketing_download_id;default:0;NOT NULL"`      // 与marketing_download表中同步的记录id
	NumDownloads        uint      `gorm:"column:num_downloads;default:0;NOT NULL"`              //
	CreatedBy           uint      `gorm:"column:created_by;NOT NULL"`                           //
	PublisherID         uint      `gorm:"column:publisher_id;default:0;NOT NULL"`               // 创建文章时作者选择的运营号
	CreatedAt           time.Time `gorm:"column:created_at;default:CURRENT_TIMESTAMP;NOT NULL"` //
	UpdatedAt           time.Time `gorm:"column:updated_at;default:CURRENT_TIMESTAMP;NOT NULL"` //
	gorm.Model
}

func (m *OpArticle) TableName() string {
	return "op_article"
}

// OpArticleCategory 文章分类
type OpArticleCategory struct {
	ID                  uint      `gorm:"column:id;primary_key;AUTO_INCREMENT"`                 // id
	ParentID            uint      `gorm:"column:parent_id;default:0;NOT NULL"`                  // 父id
	Name                string    `gorm:"column:name;NOT NULL"`                                 // 名称
	Ancestor            string    `gorm:"column:ancestor;NOT NULL"`                             // 祖先id集合,逗号分隔(不建议使用,并不便于维护)
	Reserved            uint      `gorm:"column:reserved;default:0;NOT NULL"`                   // 是否为保留分类
	MarketingCategoryID uint      `gorm:"column:marketing_category_id;default:0;NOT NULL"`      // marketing_category同步过来的id
	Order               uint      `gorm:"column:order;default:0;NOT NULL"`                      // 排序
	Enabled             uint      `gorm:"column:enabled;default:1;NOT NULL"`                    // 是否上架 0:未上架 1:上架
	CreatedBy           uint      `gorm:"column:created_by;NOT NULL"`                           // 创建人
	CreatedAt           time.Time `gorm:"column:created_at;default:CURRENT_TIMESTAMP;NOT NULL"` // 创建时间
	UpdatedAt           time.Time `gorm:"column:updated_at;default:CURRENT_TIMESTAMP;NOT NULL"` // 修改时间
	gorm.Model
}

func (m *OpArticleCategory) TableName() string {
	return "op_article_category"
}

// 文章标签
type OpArticleTag struct {
	ID        uint      `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	Name      string    `gorm:"column:name;NOT NULL"`
	Reserved  uint      `gorm:"column:reserved;default:0;NOT NULL"` // 是否为保留标签
	CreatedBy uint      `gorm:"column:created_by;NOT NULL"`
	CreatedAt time.Time `gorm:"column:created_at;default:CURRENT_TIMESTAMP;NOT NULL"`
	UpdatedAt time.Time `gorm:"column:updated_at;default:CURRENT_TIMESTAMP;NOT NULL"`
	gorm.Model
}

func (m *OpArticleTag) TableName() string {
	return "op_article_tag"
}

// 文章评论
type OpArticleComment struct {
	ID         uint      `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	Content    string    `gorm:"column:content;NOT NULL"`
	ArticleID  uint      `gorm:"column:article_id;NOT NULL"`
	SourceID   uint      `gorm:"column:source_id;NOT NULL"`           // 始祖id，文章的评论为0，评论下的评论/回复为所在评论id
	ParentID   uint      `gorm:"column:parent_id;NOT NULL"`           // 父级id，文章的评论为0，其它为它评论目标的id
	Ancestors  string    `gorm:"column:ancestors;NOT NULL"`           // 祖先id合集，逗号分隔
	ReplyTo    uint      `gorm:"column:reply_to;NOT NULL"`            // 父id的created_by，文章的评论为0，对哪个用户的评论进行回复
	Attachment string    `gorm:"column:attachment;NOT NULL"`          // 附件内容，JSON格式
	Visible    uint      `gorm:"column:visible;NOT NULL"`             // 是否可见
	Selected   uint      `gorm:"column:selected;default:0;NOT NULL"`  // 是否精选
	Top        uint      `gorm:"column:top;default:0;NOT NULL"`       // 是否置顶
	Discarded  uint      `gorm:"column:discarded;default:0;NOT NULL"` // 是否放入回收站
	CreatedBy  uint      `gorm:"column:created_by;NOT NULL"`
	CreatedAt  time.Time `gorm:"column:created_at;default:CURRENT_TIMESTAMP;NOT NULL"`
	UpdatedAt  time.Time `gorm:"column:updated_at;default:CURRENT_TIMESTAMP;NOT NULL"`
	gorm.Model
}

func (m *OpArticleComment) TableName() string {
	return "op_article_comment"
}

// 运营-用户管理
type OpUser struct {
	ID          uint `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	UserID      uint `gorm:"column:user_id;NOT NULL"`
	Blocked     uint `gorm:"column:blocked;default:0;NOT NULL"`      // 是否已拉黑
	NumComments uint `gorm:"column:num_comments;default:0;NOT NULL"` // 评论数
}

func (m *OpUser) TableName() string {
	return "op_user"
}

// 用户点赞记录
type OpLike struct {
	ID        uint      `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	Target    uint      `gorm:"column:target;NOT NULL"`    // 点赞目标，1-文章，2-评论
	TargetID  uint      `gorm:"column:target_id;NOT NULL"` // 点赞目标的id
	UserID    uint      `gorm:"column:user_id;NOT NULL"`
	CreatedAt time.Time `gorm:"column:created_at;default:CURRENT_TIMESTAMP;NOT NULL"`
}

func (m *OpLike) TableName() string {
	return "op_like"
}

// 文章-标签关联表
type OpArticleTagRelation struct {
	ID        uint `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	ArticleID uint `gorm:"column:article_id;NOT NULL"`
	TagID     uint `gorm:"column:tag_id;NOT NULL"`
}

func (m *OpArticleTagRelation) TableName() string {
	return "op_article_tag_relation"
}

// OpView 用户浏览记录
type OpView struct {
	ID        uint      `gorm:"column:id;primary_key;AUTO_INCREMENT"`                 //
	Target    uint      `gorm:"column:target;NOT NULL"`                               //
	TargetID  uint      `gorm:"column:target_id;NOT NULL"`                            //
	UserID    uint      `gorm:"column:user_id;NOT NULL"`                              //
	CreatedAt time.Time `gorm:"column:created_at;default:CURRENT_TIMESTAMP;NOT NULL"` //
}

func (m *OpView) TableName() string {
	return "op_view"
}

// OpPublisher 运营号
type OpPublisher struct {
	ID     uint   `gorm:"column:id;primary_key;AUTO_INCREMENT"` // id
	Name   string `gorm:"column:name;"`                         // 名称
	Avatar string `gorm:"column:avatar;"`                       // 头像
	gorm.Model
}

func (m *OpPublisher) TableName() string {
	return "op_publisher"
}

// OpPublisherUser 运营号-用户关联
type OpPublisherUser struct {
	ID          uint `gorm:"column:id;primary_key;AUTO_INCREMENT"` // id
	UserId      int  `gorm:"column:user_id;"`
	PublisherId int  `gorm:"column:publisher_id;"`
}

func (m *OpPublisherUser) TableName() string {
	return "op_publisher_user"
}

// OpArticleTagInfo 标签信息
type OpArticleTagInfo struct {
	Id      uint   `gorm:"column:article_id"`
	TagId   uint   `gorm:"column:tag_id"`
	TagName string `gorm:"column:tag_name"`
}

// OpArticleLikeCountInfo 点赞统计信息
type OpArticleLikeCountInfo struct {
	Id  uint `gorm:"column:target_id"`
	Num uint `gorm:"column:num"`
}

// OpArticleViewCountInfo 浏览统计信息
type OpArticleViewCountInfo struct {
	Id  uint `gorm:"column:target_id"`
	Num uint `gorm:"column:num"`
}

// OpArticleCommentCountInfo 评论统计信息
type OpArticleCommentCountInfo struct {
	Id  uint `gorm:"column:article_id"`
	Num uint `gorm:"column:num"`
}

// OpPublisherArticleCountInfo 运营号文章统计信息
type OpPublisherArticleCountInfo struct {
	PublisherId uint `gorm:"column:publisher_id"`
	Num         uint `gorm:"column:num"`
}
