package cache

import (
	"context"
	_ "embed"
	"fmt"
	"github.com/redis/go-redis/v9"
	myErrors "marketing/internal/pkg/errors"
	"time"
)

var (
	//go:embed lua/action_apply.lua
	incr string
)

type ActionCache interface {
	Increment(ctx context.Context, id uint, num int) error
	Set(todo context.Context, t uint, remain int) error
}
type ActionRedisCache struct {
	client redis.Cmdable
}

func (a *ActionRedisCache) Set(todo context.Context, t uint, remain int) error {
	return a.client.Set(todo, a.Key(t), remain, time.Hour*12).Err()
}

func (a *ActionRedisCache) Increment(ctx context.Context, id uint, num int) error {
	key := a.Key(id)
	res, err := a.client.Eval(ctx, incr, []string{key}, num).Int()
	if err != nil {
		return err
	}
	switch res {
	case 0:
		return myErrors.NewErr("缓存不存在")
	case -1:
		return myErrors.NewErr("活动申请已达上限")
	default:
		return nil
	}
}

func (a *ActionRedisCache) Key(id uint) string {
	return fmt.Sprintf("action:apply:%d", id)
}
func NewActionRedisCache(client redis.Cmdable) ActionCache {
	return &ActionRedisCache{client: client}
}
