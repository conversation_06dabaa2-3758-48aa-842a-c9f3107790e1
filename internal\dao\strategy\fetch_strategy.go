package strategy

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	api "marketing/internal/api/warranty"
	"marketing/internal/consts"
)

type GetWarrantiesDBStrategy interface {
	GetWarranties(c *gin.Context) ([]*api.WarrantyDetail, error)
}

type StrategyForParamGetWarranty struct {
	DB    *gorm.DB
	Param string
}

func NewStrategyForParamGetWarranty(db *gorm.DB, param string) *StrategyForParamGetWarranty {
	return &StrategyForParamGetWarranty{DB: db, Param: param}
}

type StrategyForBarcodeGetWarranty struct {
	DB      *gorm.DB
	Barcode string
}

func NewStrategyForBarcodeGetWarranty(db *gorm.DB, barcode string) *StrategyForBarcodeGetWarranty {
	return &StrategyForBarcodeGetWarranty{DB: db, Barcode: barcode}
}

type StrategyForMachineLife struct {
	DB      *gorm.DB
	Barcode string
}

func NewStrategyForMachineLife(db *gorm.DB, barcode string) *StrategyForMachineLife {
	return &StrategyForMachineLife{DB: db, Barcode: barcode}
}

func (s *StrategyForParamGetWarranty) GetWarranties(c *gin.Context) ([]*api.WarrantyDetail, error) {
	var list []*api.WarrantyDetail
	err := s.DB.WithContext(c).Table("warranty as wt").
		Joins("LEFT JOIN endpoint as ep ON ep.id = wt.endpoint").
		Select("wt.id, wt.status, wt.barcode, wt.number, wt.salesman, wt.customer_name, wt.customer_phone, wt.model, "+
			"wt.buy_date, wt.created_at, wt.activated_at, wt.product_date, ep.name, ep.address, ep.manager, ep.phone").
		Where("wt.status != ? and (wt.barcode = ? or wt.customer_phone = ? or wt.number = ?)", 0, s.Param, s.Param, s.Param).
		Scan(&list).Error
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s *StrategyForBarcodeGetWarranty) GetWarranties(c *gin.Context) ([]*api.WarrantyDetail, error) {
	var list []*api.WarrantyDetail
	err := s.DB.WithContext(c).Table("warranty as wt").
		Joins("LEFT JOIN endpoint as ep ON ep.id = wt.endpoint").
		Joins("LEFT JOIN agency as a1 ON ep.top_agency = a1.id").
		Joins("LEFT JOIN agency AS a2 ON ep.second_agency = a2.id AND ep.second_agency != 0").
		Select("wt.id, wt.barcode, wt.model, wt.created_at, wt.buy_date, wt.activated_at, wt.status, wt.customer_phone, ep.name, a1.name as top_agency, a2.name as second_agency").
		Where("wt.status != ? and wt.barcode = ?", 0, s.Barcode).
		Scan(&list).Error
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s *StrategyForMachineLife) GetWarranties(c *gin.Context) ([]*api.WarrantyDetail, error) {
	var list []*api.WarrantyDetail
	orderTimeExpression := gorm.Expr("IF(wt.created_at IS NULL, wt.created_at_new, wt.created_at)")
	err := s.DB.WithContext(c).Table("warranty as wt").
		Joins("LEFT JOIN endpoint as ep ON ep.id = wt.endpoint").
		Select("wt.*, ep.name, ep.address, ep.manager, ep.phone, ? as order_time", orderTimeExpression).
		Where("wt.status != ? and wt.barcode = ?", consts.WarrantyStatusVirtual, s.Barcode).
		Scan(&list).Error
	if err != nil {
		return nil, err
	}
	return list, nil
}
