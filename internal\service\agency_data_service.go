package service

import (
	"fmt"
	"marketing/internal/dao"
	"marketing/internal/model"

	"gorm.io/gorm"
)

type AdminUserService struct {
	dao *dao.AdminUsersDao
	db  *gorm.DB
}

func NewAdminUserService(dao *dao.AdminUsersDao, db *gorm.DB) *AdminUserService {
	return &AdminUserService{dao: dao, db: db}
}

func (service *AdminUserService) GetAllAgencyTrees() ([]model.AgencyDataResponseTree, error) {
	var response []model.AgencyDataResponseTree

	// 批量获取所有必要的数据
	var departments []struct {
		ID   int64
		Name string
	}
	var partitions []struct {
		ID   int64
		Name string
	}
	var agencyKingdees []struct {
		AgencyID  int
		KingdeeID int
	}
	var kingdeeAgencies []struct {
		KingdeeID   int
		KingdeeName string
	}
	var agencies []model.AgencyData

	err := service.db.Table("department").Select("id, name").Scan(&departments).Error
	if err != nil {
		return nil, fmt.Errorf("failed to query departments: %v", err)
	}

	err = service.db.Table("partition").Select("id, name").Scan(&partitions).Error
	if err != nil {
		return nil, fmt.Errorf("failed to query partitions: %v", err)
	}

	err = service.db.Table("agency_kingdee").Select("agency_id, kingdee_id").Scan(&agencyKingdees).Error
	if err != nil {
		return nil, fmt.Errorf("failed to query agency_kingdee: %v", err)
	}

	err = service.db.Table("kingdee_agency").Select("id as kingdee_id, name as kingdee_name").Scan(&kingdeeAgencies).Error
	if err != nil {
		return nil, fmt.Errorf("failed to query kingdee_agency: %v", err)
	}

	err = service.db.Where("level = ?", 1).Find(&agencies).Error
	if err != nil {
		return nil, fmt.Errorf("failed to query agencies: %v", err)
	}

	// 创建映射表
	departmentMap := make(map[int64]string)
	for _, dept := range departments {
		departmentMap[dept.ID] = dept.Name
	}

	partitionMap := make(map[int64]string)
	for _, part := range partitions {
		partitionMap[part.ID] = part.Name
	}

	kingdeeNameMap := make(map[int]string)
	for _, ka := range kingdeeAgencies {
		kingdeeNameMap[ka.KingdeeID] = ka.KingdeeName
	}

	agencyMap := make(map[int]*model.AgencyDataResponseTree)
	for _, agency := range agencies {
		topAgency := &model.AgencyDataResponseTree{
			TopAgencyID:    agency.Id,
			DepartmentID:   int64(agency.Department),
			PartitionID:    agency.Partition,
			TopAgencyName:  agency.Name,
			PupilsNum:      agency.PupilsNum,
			JuniorsNum:     agency.JuniorsNum,
			Level:          agency.Level,
			MarketType:     agency.MarketType,
			SecondAgencies: []model.SecondAgencyResponse{},
		}

		if topAgency.DepartmentID != 0 {
			topAgency.DepartmentName = departmentMap[topAgency.DepartmentID]
		} else {
			topAgency.DepartmentName = "未知"
		}

		if topAgency.PartitionID != 0 {
			topAgency.PartitionName = partitionMap[int64(topAgency.PartitionID)]
		} else {
			topAgency.PartitionName = "未知"
		}

		agencyMap[agency.Id] = topAgency
	}

	// 使用预加载获取二级代理机构
	var secondAgencies []model.AgencyData
	err = service.db.Where("level = ?", 2).Find(&secondAgencies).Error
	if err != nil {
		return nil, fmt.Errorf("failed to query second agencies: %v", err)
	}

	for _, sa := range secondAgencies {
		if topAgency, exists := agencyMap[sa.Pid]; exists {
			topAgency.SecondAgencies = append(topAgency.SecondAgencies, model.SecondAgencyResponse{
				SecondAgencyName: sa.Name,
			})
		}
	}

	// 构建金蝶ID和名称列表
	for _, tree := range agencyMap {
		tree.KingdeeIDs = extractKingdeeIDs(agencyKingdees, tree.TopAgencyID)
		tree.KingdeeNames = extractKingdeeNames(tree.KingdeeIDs, kingdeeNameMap)
		response = append(response, *tree)
	}

	if len(response) == 0 {
		return nil, fmt.Errorf("no agency data found")
	}

	return response, nil
}

func extractKingdeeIDs(agencyKingdees []struct {
	AgencyID  int
	KingdeeID int
}, agencyID int) []int {
	var ids []int
	for _, ak := range agencyKingdees {
		if ak.AgencyID == agencyID {
			ids = append(ids, ak.KingdeeID)
		}
	}
	return ids
}

func extractKingdeeNames(kingdeeIDs []int, kingdeeNameMap map[int]string) []string {
	var names []string
	for _, id := range kingdeeIDs {
		if name, ok := kingdeeNameMap[id]; ok {
			names = append(names, name)
		}
	}
	return names
}
