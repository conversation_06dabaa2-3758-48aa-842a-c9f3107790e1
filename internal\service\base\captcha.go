package base

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	appError "marketing/internal/pkg/errors"
	"marketing/internal/pkg/log"
	"marketing/internal/pkg/sms"
	"math/rand"
	"time"
)

type CaptchaSvc interface {
	SendCaptcha(ctx *gin.Context, phone string) error
}

type captchaSvc struct {
	db *gorm.DB
}

func NewCaptchaService(db *gorm.DB) CaptchaSvc {
	return &captchaSvc{db: db}
}

func (c *captchaSvc) SendCaptcha(ctx *gin.Context, phone string) error {
	signName := "读书郎"
	templateCode := "SMS_69985149" // 短信模板代码
	templateParam := make(map[string]any)
	code := fmt.Sprintf("%06d", rand.Intn(900000)+100000)
	templateParam["code"] = code
	b, msg := sms.SendSMS(phone, signName, templateCode, templateParam)
	if b == false {
		return appError.NewErr(msg)
	}
	ts := time.Now().Format("2006-01-02 15:04:05")
	if err := c.db.Exec("REPLACE INTO phone_code SET phone=?, code=?,consume = ?, updated_at=?", phone, code, 0, ts).Error; err != nil {
		log.Error(fmt.Sprintf("database error: %s\n", err))
		return appError.NewErr("验证码发送失败，请稍后再试")
	}
	return nil
}
