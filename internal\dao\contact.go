package dao

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"marketing/internal/model"
	"marketing/internal/pkg/errors"
	"time"
)

type InterfaceContact interface {
	Load(c *gin.Context, w *model.Warranty) bool
	CheckMachine(tx *gorm.DB, endpointID int, name string) (uint, bool)
	AddMachine(db *gorm.DB, endpointID int, name string) (uint, error)
	CheckContact(tx *gorm.DB, endpointID int, phone string) (uint64, bool)
}

type contact struct {
	db *gorm.DB
}

func NewContact(db *gorm.DB) InterfaceContact {
	return &contact{
		db: db,
	}
}

// Load 从保卡数据中导入联系人
func (ct *contact) Load(c *gin.Context, w *model.Warranty) bool {
	err := ct.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		var err error
		var contactID uint64
		var contactExists bool
		var addContact *model.Contact
		var updateContact *model.Contact

		contactID, contactExists = ct.CheckContact(tx, w.Endpoint, w.CustomerPhone)
		studentBirthday := time.Time(w.StudentBirthday)
		now := time.Now()
		addContact = &model.Contact{
			EndpointID:      uint(w.Endpoint),
			CustomerName:    w.CustomerName,
			CustomerPhone:   w.CustomerPhone,
			StudentName:     w.StudentName,
			StudentSchool:   w.StudentSchool,
			StudentGrade:    w.StudentGrade,
			StudentBirthday: &studentBirthday,
			Salesman:        w.Salesman,
			Source:          0, // 默认值
			CreatedAt:       now,
			UpdatedAt:       &now,
			CustomerSex:     w.CustomerSex,
			StudentSex:      w.StudentSex,
			Type:            "purchased",
			SubType:         "satisfaction_unknown",
			LastPurchasedAt: &w.BuyDate,
		}
		// 创建联系人
		if !contactExists {
			if err := tx.Create(&addContact).Error; err != nil {
				return err
			}
			contactID = addContact.ID
		}
		// 更新联系人
		if contactID > 0 {
			updateContact = &model.Contact{
				Type:            "purchased",
				SubType:         "satisfaction_unknown",
				Manager:         uint(w.SalesmanID),
				CustomerName:    w.CustomerName,
				CustomerPhone:   w.CustomerPhone,
				StudentName:     w.StudentName,
				StudentSchool:   w.StudentSchool,
				StudentGrade:    w.StudentGrade,
				StudentBirthday: &studentBirthday,
				Salesman:        w.Salesman,
				LastPurchasedAt: addContact.LastPurchasedAt,
				CustomerSex:     w.CustomerSex,
				StudentSex:      w.StudentSex,
				UpdatedAt:       &now,
			}
			if err = tx.Model(&model.Contact{}).Where("id = ?", contactID).Updates(updateContact).Error; err != nil {
				return errors.NewErr(fmt.Sprintf("Error updating contact: %v", err))
			}

			if w.Model != "" {
				machineID, machineExists := ct.CheckMachine(tx, w.Endpoint, w.Model)
				if !machineExists {
					machineID, err = ct.AddMachine(tx, w.Endpoint, w.Model)
					if err != nil {
						return err
					}
				}
				// 创建联系人与机型的关联
				if machineID > 0 {
					var relation *model.ContactMachineRelation
					relation = &model.ContactMachineRelation{
						EndpointID: w.Endpoint,
						ContactID:  int(contactID),
						CmID:       int(machineID),
					}
					if err := tx.FirstOrCreate(&relation, relation).Error; err != nil {
						return errors.NewErr(fmt.Sprintf("Error creating contact_machine_relation: %v", err))
					}
				}
			}
		}

		return nil
	})

	return err == nil
}

func (ct *contact) AddMachine(db *gorm.DB, endpointID int, name string) (uint, error) {
	var machine *model.ContactMachine
	machine = &model.ContactMachine{
		EndpointID: endpointID,
		Name:       name,
	}
	if err := db.Table("contact_machine").Create(&machine).Error; err != nil {
		return 0, errors.NewErr(fmt.Sprintf("Error adding machine: %v", err))
	}
	return machine.ID, nil
}

func (ct *contact) CheckMachine(tx *gorm.DB, endpoint int, name string) (uint, bool) {
	var machine *model.ContactMachine
	if err := tx.Table("contact_machine").Where("endpoint_id = ? AND name = ?", endpoint, name).First(&machine).Error; err != nil {
		return 0, false
	}
	return machine.ID, true
}

func (ct *contact) CheckContact(tx *gorm.DB, endpoint int, phone string) (uint64, bool) {
	var cnt *model.Contact
	if err := tx.Table("contact").Where("endpoint_id = ? AND customer_phone = ?", endpoint, phone).First(&cnt).Error; err != nil {
		return 0, false
	}
	return cnt.ID, true
}
