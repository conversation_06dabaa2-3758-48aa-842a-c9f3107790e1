package model

import (
	"gorm.io/gorm"
	"time"
)

type SalesPromotion struct {
	ID           int64          `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Name         string         `gorm:"column:name" json:"name"`
	Type         uint8          `gorm:"column:type" json:"type"`
	StartTime    time.Time      `gorm:"column:start_time" json:"start_time"`
	EndTime      time.Time      `gorm:"column:end_time" json:"end_time"`
	Model        string         `gorm:"column:model" json:"model"` // 更明确的字段名
	HourInterval float32        `gorm:"column:hour_interval" json:"hour_interval"`
	IsSamePhone  uint8          `gorm:"column:is_same_phone" json:"is_same_phone"`
	ReceiptDay   string         `gorm:"column:receipt_day" json:"receipt_day"`
	IsSync       int            `gorm:"column:is_sync" json:"is_sync"`
	Prize        string         `gorm:"column:prize" json:"prize"`
	PrizePic     string         `gorm:"column:prize_pic" json:"prize_pic"`
	Rule         string         `gorm:"column:rule" json:"rule"`
	CreatedAt    time.Time      `gorm:"column:created_at" json:"created_at"`
	DeletedAt    gorm.DeletedAt `gorm:"column:delete_at" json:"-"` // GORM 软删除标准字段
	UpdatedAt    time.Time      `gorm:"column:updated_at" json:"updated_at"`
}
type SalesPromotionList struct {
	ID               int            `gorm:"primaryKey;autoIncrement;column:id"`
	SalesPromotionID int            `gorm:"column:sales_promotion_id;not null"`
	Barcode          string         `gorm:"column:barcode;not null"`
	WarrantyID       int            `gorm:"column:warranty_id;not null"`
	BuyDate          time.Time      `gorm:"column:buy_date"`
	Model            string         `gorm:"column:model"`
	ModelID          int            `gorm:"column:model_id"`
	Endpoint         int            `gorm:"column:endpoint"`
	CustomerName     string         `gorm:"column:customer_name"`
	CustomerPhone    string         `gorm:"column:customer_phone"`
	ActivatedPhone   string         `gorm:"column:activated_phone"`
	IsSamePhone      int            `gorm:"column:is_same_phone;not null"`
	IsReceipt        int            `gorm:"column:is_receipt;not null"`
	ReceiptAt        time.Time      `gorm:"column:receipt_at"`
	CreatedAt        time.Time      `gorm:"column:created_at;default:CURRENT_TIMESTAMP"`
	DeletedAt        gorm.DeletedAt `gorm:"column:delete_at"`
	UpdatedAt        time.Time      `gorm:"column:updated_at;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"`
}
type SalesPromotionListReceipt struct {
	ID                   int            `gorm:"column:id;type:int(11);primaryKey;autoIncrement"`
	SalesPromotionListID int            `gorm:"column:sales_promotion_list_id;type:int(11);not null"`
	Receipt              string         `gorm:"column:receipt;type:varchar(500);default:''"`
	Number               string         `gorm:"column:number;type:varchar(32);default:''"`
	Type                 int            `gorm:"column:type;type:tinyint(11);default:0"`
	CreatedAt            time.Time      `gorm:"column:created_at;type:timestamp;default:CURRENT_TIMESTAMP"`
	DeleteAt             gorm.DeletedAt `gorm:"column:delete_at;type:timestamp;default:null"`
}

func (SalesPromotionListReceipt) TableName() string {
	return "sales_promotion_list_receipt"
}
