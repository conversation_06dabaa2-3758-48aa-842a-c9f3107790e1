package material

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"marketing/internal/dao"
	"marketing/internal/handler"
	"marketing/internal/model"
	"marketing/internal/pkg/e"
	"marketing/internal/pkg/errors"
	"marketing/internal/service/material"
)

type TMaterialController struct {
	svc material.TMaterialSvc
}

func NewMaterialController(svc material.TMaterialSvc) *TMaterialController {
	return &TMaterialController{
		svc: svc,
	}
}

func (m *TMaterialController) GetMaterialList(c *gin.Context) {
	list, total := m.svc.GetMaterialList(c, &dao.GetMaterialListParam{
		ProductionNumber: e.ReqParamStr(c, "production_number"),
		Name:             e.ReqParamStr(c, "name"),
		Category:         e.ReqParamInt(c, "category"),
		IsPutAway:        e.ReqParamInt(c, "is_putaway", -1),
		PageNum:          e.ReqParamInt(c, "page_num"),
		PageSize:         e.ReqParamInt(c, "page_size"),
	})

	handler.Success(c, gin.H{
		"list":  list,
		"total": total,
	})
}

func (m *TMaterialController) GetRepairMaterialList(c *gin.Context) {
	list, total := m.svc.GetRepairMaterialList(c, &dao.GetRepairMaterialListParam{
		Key:             e.ReqParamStr(c, "key"),
		PriceStart:      e.ReqParamFloat64(c, "price_start"),
		PriceEnd:        e.ReqParamFloat64(c, "price_end"),
		WithPrice:       e.ReqParamInt(c, "with_price", -1),
		UpdateTimeStart: e.ReqParamStr(c, "update_time_start"),
		UpdateTimeEnd:   e.ReqParamStr(c, "update_time_end"),
		PageNum:         e.ReqParamInt(c, "page_num"),
		PageSize:        e.ReqParamInt(c, "page_size"),
	})

	handler.Success(c, gin.H{
		"list":  list,
		"total": total,
	})
}

func (m *TMaterialController) ExportRepairMaterialList(c *gin.Context) {
	file := m.svc.ExportRepairMaterialList(c, &dao.GetRepairMaterialListParam{
		Key:             e.ReqParamStr(c, "key"),
		PriceStart:      e.ReqParamFloat64(c, "price_start"),
		PriceEnd:        e.ReqParamFloat64(c, "price_end"),
		WithPrice:       e.ReqParamInt(c, "with_price", -1),
		UpdateTimeStart: e.ReqParamStr(c, "update_time_start"),
		UpdateTimeEnd:   e.ReqParamStr(c, "update_time_end"),
		PageNum:         e.ReqParamInt(c, "page_num"),
		PageSize:        e.ReqParamInt(c, "page_size"),
	})

	if file != nil {
		c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
		c.Header("Content-Disposition", "attachment; filename="+fmt.Sprintf("物料表.xls"))
		if err := file.Write(c.Writer); err != nil {
			handler.Error(c, errors.NewErr(err.Error()))
			return
		}
	}

	handler.Success(c, gin.H{})
}

func (m *TMaterialController) GetMaterialById(c *gin.Context) {
	data := m.svc.GetMaterialById(c, e.ReqParamInt(c, "id"))
	handler.Success(c, data)
}

func (m *TMaterialController) EditMaterial(c *gin.Context) {
	err := m.svc.EditMaterial(c, &model.Material{
		Name:             e.ReqParamStr(c, "name"),
		IsPutAway:        e.ReqParamInt(c, "is_putaway", -1),
		GrantType:        e.ReqParamStr(c, "grant_type"),
		Category:         e.ReqParamInt(c, "category"),
		ProductionNumber: e.ReqParamStr(c, "production_number"),
		Specification:    e.ReqParamStr(c, "specification"),
		Price:            e.ReqParamStr(c, "price"),
		Stock:            e.ReqParamStr(c, "stock"),
		IsRecommend:      e.ReqParamInt(c, "is_recommend", -1),
		IsNew:            e.ReqParamInt(c, "is_new", -1),
		Pic:              e.ReqParamStr(c, "pic"),
		Thumbnail:        e.ReqParamStr(c, "thumbnail"),
		Description:      e.ReqParamStr(c, "description"),
		PicOther:         e.ReqParamStr(c, "pic_other"),
		Order:            e.ReqParamInt(c, "order"),
		Unit:             e.ReqParamStr(c, "unit"),
		UsedByActivity:   e.ReqParamInt(c, "used_by_activity", -1),
		Video:            e.ReqParamStr(c, "video"),
	})
	if err != nil {
		handler.Error(c, errors.NewErr(err.Error()))
		return
	}

	handler.Success(c, gin.H{})
}

func (m *TMaterialController) DeleteMaterial(c *gin.Context) {
	err := m.svc.DeleteMaterial(c, e.ReqParamInt(c, "id"))
	if err != nil {
		handler.Error(c, errors.NewErr(err.Error()))
		return
	}

	handler.Success(c, gin.H{})
}
