package system

import (
	"marketing/internal/api"
	"marketing/internal/pkg/types"
)

// AddRoleReq 添加用户角色入参
type AddRoleReq struct {
	ID              uint   `json:"id" form:"id"`
	Name            string `json:"name" form:"name" binding:"required"`
	Slug            string `json:"slug" form:"slug" binding:"required"`
	SystemType      string `json:"system_type" form:"system_type" binding:"required"`
	Description     string `json:"description" form:"description" binding:"required"`
	ResourceGroupID uint   `json:"resource_group_id" form:"resource_group_id"`
	Permissions     []uint `json:"permissions" form:"permissions"`
	Menus           []uint `json:"menus" form:"menus"`
}

// AdminRoleReq 查询用户角色入参
type AdminRoleReq struct {
	api.PaginationParams
	Name       string `json:"name" form:"name"`
	Slug       string `json:"slug" form:"slug"`
	SystemType string `json:"system_type" form:"system_type"`
}

// AdminRoleResp 用户信息响应
type AdminRoleResp struct {
	ID                uint             `json:"id"`
	Name              string           `json:"name"`
	Slug              string           `json:"slug"`
	SystemType        string           `json:"system_type"`
	Description       string           `json:"description"`
	ResourceGroupID   uint             `json:"resource_group_id" form:"resource_group_id"`
	ResourceGroupName string           `json:"resource_group_name" form:"resource_group_name"`
	MenuIDs           []uint           `json:"menu_ids"`       // 关联的菜单ID
	PermissionIDs     []uint           `json:"permission_ids"` // 关联的权限ID
	AppsIDs           []uint           `json:"apps_ids"`       // 关联的应用ID
	CreatedAt         types.CustomTime `json:"created_at"`     // 创建时间
}

// UpdateRolePermissionReq 更新角色权限请求
type UpdateRolePermissionReq struct {
	Permissions []uint `json:"permissions" binding:"required"` // 权限ID列表
}

// UpdateRoleMenuReq 更新角色菜单请求
type UpdateRoleMenuReq struct {
	Menus []uint `json:"menus" binding:"required"` // 菜单ID列表
}

// UpdateRoleAppReq 更新角色菜单请求
type UpdateRoleAppReq struct {
	Apps []uint `json:"apps" binding:"required"` // 菜单ID列表
}
