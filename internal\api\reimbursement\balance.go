package reimbursement

import (
	"marketing/internal/api"
	"time"
)

type BalanceStandardReq struct {
	PolicyID  int `form:"policy_id" json:"policy_id" binding:"required"` // 政策ID
	CompanyID int `form:"company_id" json:"company_id"`                  // 顶级代理
}

type BalanceListReq struct {
	api.PaginationParams
	PolicyID     int    `form:"policy_id" json:"policy_id" binding:"required"` // 政策ID
	CompanyID    int    `form:"company_id" json:"company_id"`                  // 公司ID
	Code         string `form:"code" json:"code"`                              // 客户编码
	TopAgency    int    `form:"top_agency" json:"top_agency"`                  // 顶级代理商ID
	StandardType string
}

type ReimbursementBalanceStandardResp struct {
	AgencyName   string    `json:"agency_name"`   // 代理商名称
	CompanyID    int       `json:"company_id"`    // 公司ID
	Company      string    `json:"company"`       // 公司名称
	Code         string    `json:"code"`          // 客户编码
	NormQuantity float64   `json:"norm_quantity"` // 标准数量
	Balance      float64   `json:"balance"`       // 余额
	CreatedAt    time.Time `json:"created_at"`    // 创建时间
}

type BalanceListStatResp struct {
	Count        int64
	NormQuantity float64
	Balance      float64
}

type BalanceImport struct {
	Code    string
	Company string
	Balance float64
}

type BalanceResetRep struct {
	CompanyID []int `json:"company_id" form:"company_id"`                   // 公司ID
	PolicyID  int   `json:"policy_id" form:"policy_id"  binding:"required"` // 政策ID
	ResetAll  int   `json:"reset_all" form:"reset_all"`                     // 是否重置所有
}
