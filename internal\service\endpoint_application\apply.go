package endpoint_application

import (
	"bytes"
	"encoding/json"
	"fmt"
	api "marketing/internal/api/endpoint_application"
	"marketing/internal/consts"
	"marketing/internal/dao"
	endpointDao "marketing/internal/dao/endpoint"
	applicationDao "marketing/internal/dao/endpoint_application"
	warrantyDao "marketing/internal/dao/warranty"
	"marketing/internal/model"
	"marketing/internal/pkg/errors"
	"marketing/internal/pkg/log"
	"marketing/internal/pkg/oss"
	"marketing/internal/pkg/sms"
	"marketing/internal/pkg/types"
	"marketing/internal/pkg/utils"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/carmel/gooxml/document"

	"github.com/gin-gonic/gin"
	"github.com/qichengzx/coordtransform"
	"github.com/spf13/cast"
)

type EndpointApplyService interface {
	CreateEndpointApply(c *gin.Context, req *api.CreatedEndpointApplyReq) error
	UpdateEndpointApply(c *gin.Context, id int, req *api.CreatedEndpointApplyReq) error
	GetEndpointApplyList(c *gin.Context, param *api.EndpointApplicationListReq) ([]*api.EndpointApplicationListResp, int64, error)
	AuditApply(c *gin.Context, req *api.AuditApplyReq) error
	GetMaterials(c *gin.Context, name string, categories []int, page, pageSize int) ([]*api.EndpointMaterialSupport, error)
	MaterialSupport(c *gin.Context, id uint, req []*api.EndpointMaterialSupport) error
	PostbackEndpointApply(c *gin.Context, req *api.PostbackEndpointApplyReq) error
	WriteOff(c *gin.Context, req *api.WriteOffReq) error
	ChannelConfirmation(c *gin.Context, req *api.AuditApplyReq) error
	RecordConfirmation(c *gin.Context, req *api.AuditApplyReq) error
	DownloadWriteOffTable(c *gin.Context, id int) error
	LatestEndpointImage(c *gin.Context, id int) (*api.LatestEndpointImageResp, error)
	SalesAmount(c *gin.Context, id int, years int, endAt int64) (int64, float64, error)
}

type endpointApplyService struct {
	repo             applicationDao.EndpointApplyDao
	policyRepo       applicationDao.EndpointPolicyDao
	endpointRepo     endpointDao.EndpointDao
	endpointTypeRepo dao.TypeRepository
	configRepo       dao.ConfigRepository
	subjectRepo      dao.TrainSubjectDao
	materialRepo     dao.MaterialDao
	endpointImageDao endpointDao.ImageDao
	warrantyRepo     warrantyDao.InterfaceWarranty
}

func NewEndpointApplyService(repo applicationDao.EndpointApplyDao,
	policyRepo applicationDao.EndpointPolicyDao,
	endpointRepo endpointDao.EndpointDao,
	endpointTypeRepo dao.TypeRepository,
	configRepo dao.ConfigRepository,
	subjectRepo dao.TrainSubjectDao,
	material dao.MaterialDao,
	endpointImageDao endpointDao.ImageDao,
	warrantyRepo warrantyDao.InterfaceWarranty,
) EndpointApplyService {
	return &endpointApplyService{
		repo:             repo,
		policyRepo:       policyRepo,
		endpointRepo:     endpointRepo,
		endpointTypeRepo: endpointTypeRepo,
		configRepo:       configRepo,
		subjectRepo:      subjectRepo,
		materialRepo:     material,
		endpointImageDao: endpointImageDao,
		warrantyRepo:     warrantyRepo,
	}
}

func (e endpointApplyService) CreateEndpointApply(c *gin.Context, req *api.CreatedEndpointApplyReq) error {
	// 判断是否是政策建店
	if req.PolicyID > 0 {
		//政策建店判断是否超过政策上限
		checkResult, err := e.CheckApplicable(c, req.PolicyID, 0)
		if err != nil {
			return err
		}
		if !checkResult {
			return errors.NewErr("终端申请数量已超出政策上限")
		}
	}
	// 经纬度处理
	blng, err := strconv.ParseFloat(req.Blng, 64)
	if err != nil {
		return errors.NewErr("Error parsing longitude")
	}

	blat, err := strconv.ParseFloat(req.Blat, 64)
	if err != nil {
		return errors.NewErr("Error parsing latitude")
	}
	lng, lat := coordtransform.BD09toGCJ02(blng, blat)
	// 装修实景图处理
	var pics string
	if req.Pics != nil && len(req.Pics) > 0 {
		picsBytes, err := json.Marshal(req.Pics)
		if err != nil {
			return err
		}
		pics = string(picsBytes)
	}

	// 扩展字段处理
	var extend []byte
	if req.Extend != nil && len(req.Extend) > 0 {
		extend, err = json.Marshal(req.Extend)
		if err != nil {
			return err
		}
	}
	// 校验预计开业时间是否是时间格式
	var expectOpenTime time.Time
	if req.ExpectOpenTime != "" {
		expectOpenTime, err = time.Parse(time.DateOnly, req.ExpectOpenTime)
		if err != nil {
			return errors.NewErr("预计开业时间格式错误")
		}
	}

	data := model.EndpointApplication{
		TopAgency:       req.TopAgency,
		SecondAgency:    req.SecondAgency,
		Name:            req.Name,
		Type:            req.Type,
		Province:        req.Province,
		City:            req.City,
		District:        req.District,
		Address:         req.Address,
		Lng:             cast.ToString(lng),
		Lat:             cast.ToString(lat),
		ChannelLevel:    req.ChannelLevel,
		Blng:            req.Blng,
		Blat:            req.Blat,
		PolicyID:        req.PolicyID,
		Phone:           &req.Phone,
		Manager:         req.Manager,
		Investor:        req.Investor,
		InvestorPhone:   req.InvestorPhone,
		ExpectOpenTime:  types.DateOnly(expectOpenTime),
		Position:        req.Position,
		EndpointArea:    req.EndpointArea,
		Pics:            pics,
		Extend:          string(extend),
		State:           0,
		NextState:       int(consts.ApplicationApproved), // 下一个状态，管理审核
		ApplicationYear: time.Now().Year(),
		CreatedAt:       time.Now(),
	}

	return e.repo.CreateEndpointApply(c, &data)
}

func (e endpointApplyService) CheckApplicable(c *gin.Context, policyID int, id int) (bool, error) {
	policyInfo, err := e.policyRepo.GetEndpointPolicyByID(c, policyID)
	if err != nil {
		return false, err
	}
	if policyInfo == nil {
		return false, errors.NewErr("政策不存在")
	}
	if policyInfo.Enabled == 0 {
		return false, errors.NewErr("政策已禁用")
	}
	if policyInfo.StartDate.After(time.Now()) {
		return false, errors.NewErr("政策未开始")
	}
	if policyInfo.EndDate.Before(time.Now()) {
		return false, errors.NewErr("政策已结束")
	}
	// 计算申请是否已经超出上限
	if policyInfo.Maximum > 0 {
		// 计算当前政策已申请数量
		applyCount, err := e.repo.CountEndpointApplyByPolicyID(c, policyID, id)
		if err != nil {
			return false, err
		}
		if applyCount >= policyInfo.Maximum {
			return false, errors.NewErr("终端申请数量已超出政策上限")
		}
	}
	return true, nil
}

func (e endpointApplyService) UpdateEndpointApply(c *gin.Context, id int, req *api.CreatedEndpointApplyReq) error {
	data := make(map[string]any)
	//判断申请是否存在
	apply, err := e.repo.GetEndpointApplyByID(c, id)
	if err != nil {
		return err
	}
	if apply == nil {
		return errors.NewErr("终端申请不存在")
	}
	if apply.State != int(consts.ApplicationWaitingReview) {
		return errors.NewErr("终端申请已处理，不能再进行修改")
	}
	//判断政策是否存在
	if req.PolicyID > 0 && req.PolicyID != apply.PolicyID {
		//政策建店判断是否超过政策上限
		checkResult, err := e.CheckApplicable(c, req.PolicyID, id)
		if err != nil {
			return err
		}
		if !checkResult {
			return errors.NewErr("终端申请数量已超出政策上限")
		}
	}
	if req.Blat != apply.Blat || req.Blng != apply.Blng {
		// 经纬度处理
		blng, err := strconv.ParseFloat(req.Blng, 64)
		if err != nil {
			return errors.NewErr("Error parsing longitude")
		}

		blat, err := strconv.ParseFloat(req.Blat, 64)
		if err != nil {
			return errors.NewErr("Error parsing latitude")
		}
		lng, lat := coordtransform.BD09toGCJ02(blng, blat)
		data["lng"] = cast.ToString(lng)
		data["lat"] = cast.ToString(lat)
		data["blng"] = req.Blng
		data["blat"] = req.Blat
	}
	// 装修实景图处理
	if req.Pics != nil && len(req.Pics) > 0 {
		picsBytes, err := json.Marshal(req.Pics)
		if err != nil {
			return err
		}
		data["pics"] = string(picsBytes)
	}

	// 扩展字段处理
	if req.Extend != nil && len(req.Extend) > 0 {
		extend, err := json.Marshal(req.Extend)
		if err != nil {
			return err
		}
		data["extend"] = string(extend)
	}

	// 校验预计开业时间是否是时间格式
	var expectOpenTime time.Time
	if req.ExpectOpenTime != "" {
		expectOpenTime, err = time.Parse(time.DateOnly, req.ExpectOpenTime)
		if err != nil {
			return errors.NewErr("预计开业时间格式错误")
		}
		data["expect_open_time"] = expectOpenTime.Format(time.DateOnly)
	}
	//处理更新数据
	data["policy_id"] = req.PolicyID
	data["name"] = req.Name
	data["type"] = req.Type
	data["province"] = req.Province
	data["city"] = req.City
	data["district"] = req.District
	data["address"] = req.Address
	data["channel_level"] = req.ChannelLevel
	data["phone"] = req.Phone
	data["manager"] = req.Manager
	data["investor"] = req.Investor
	data["investor_phone"] = req.InvestorPhone
	data["position"] = req.Position
	data["endpoint_area"] = req.EndpointArea
	data["state"] = req.State
	data["application_year"] = time.Now().Year()
	data["next_state"] = int(consts.ApplicationApproved) // 下一个状态，管理审核
	return e.repo.UpdateEndpointApply(c, id, data)
}

func (e endpointApplyService) GetEndpointApplyList(c *gin.Context, param *api.EndpointApplicationListReq) ([]*api.EndpointApplicationListResp, int64, error) {
	data, total, err := e.repo.GetEndpointApplyList(c, param)
	if err != nil {
		return nil, 0, err
	}
	// 处理代理信息
	agencyIDs := make([]int, 0)
	for _, item := range data {
		if item.TopAgency > 0 {
			agencyIDs = append(agencyIDs, int(item.TopAgency))
		}
		if item.SecondAgency > 0 {
			agencyIDs = append(agencyIDs, int(item.SecondAgency))
		}
	}
	//代理商
	agencies, _ := e.endpointRepo.GetAgencies(c, agencyIDs)
	// 终端类型
	endpointTypes, _ := e.endpointTypeRepo.GetEndpointTypeMap(c)
	for i := range data {
		err = json.Unmarshal([]byte(data[i].Pics), &data[i].PicsArr)
		if err != nil {
			log.Error(err.Error())
		}
		err = json.Unmarshal([]byte(data[i].Extend), &data[i].ExtendMap)
		if err != nil {
			log.Error(err.Error())
		}
		data[i].TopAgencyName = agencies[data[i].TopAgency]
		data[i].SecondAgencyName = agencies[data[i].SecondAgency]
		data[i].EndpointTypeName = endpointTypes[uint(data[i].Type)]
		data[i].StateName = consts.EndpointApplicationState(data[i].State).String()
		if data[i].NextState > 0 {
			data[i].NextStateName = consts.EndpointApplicationState(data[i].NextState).String()
		}
	}
	return data, total, nil
}

func (e endpointApplyService) AuditApply(c *gin.Context, req *api.AuditApplyReq) error {
	//判断申请是否存在
	apply, err := e.repo.GetEndpointApplyByID(c, int(req.ID))
	if err != nil {
		return err
	}
	if apply == nil {
		return errors.NewErr("终端申请不存在")
	}
	if apply.State != int(consts.ApplicationWaitingReview) {
		return errors.NewErr("终端申请已处理，不能再进行处理")
	}
	//判断状态是否正确
	if req.State != int(consts.ApplicationApproved) && req.State != int(consts.ApplicationRejected) {
		return errors.NewErr("状态错误")
	}
	//判断备注是否为空
	if req.Remark == "" && req.State == int(consts.ApplicationRejected) {
		return errors.NewErr("审核不通过，备注不能为空")
	}
	//获取下一个状态
	nextState, err := e.getNextState(c, apply.PolicyID, req.State)
	if err != nil {
		return err
	}
	//审核字段
	var extend string
	if len(req.Extend) > 0 {
		extendByte, err := json.Marshal(req.Extend)
		if err != nil {
			return err
		}
		extend = string(extendByte)
	}
	//审核
	var code string
	var endpointInfo *model.Endpoint
	err = e.repo.WithTransaction(c, func(txRepo applicationDao.EndpointApplyDao) error {
		// 生成终端数据
		var endpointID int
		if req.State == int(consts.ApplicationApproved) {
			//获取区域名称
			address := e.BuildFullAddress(apply.Province, apply.City, apply.District, apply.Address)
			txDB := txRepo.GetDB(c)
			txEndpointDao := endpointDao.NewEndpointDao(txDB)
			// 生成终端数据
			endpointInfo, err = txEndpointDao.CreateEndpoint(c, &model.Endpoint{
				// 必填字段
				Name:      apply.Name,
				Type:      int8(apply.Type),
				Province:  apply.Province,
				City:      apply.City,
				District:  apply.District,
				Address:   address,
				TopAgency: int(apply.TopAgency),
				Phone:     apply.Phone,
				Manager:   &apply.Manager,
				Blng:      &apply.Blng,
				Blat:      &apply.Blat,
				Lng:       apply.Lng,
				Lat:       apply.Lat,

				// 可选字段 - 仅当请求中包含值时才设置
				SecondAgency: int(apply.SecondAgency),
				ChannelLevel: apply.ChannelLevel, // 默认 0
			})
			if err != nil {
				return err
			}
			endpointID = endpointInfo.ID
			code = endpointInfo.Code
		}
		// 更新申请状态
		err = txRepo.UpdateEndpointApply(c, int(req.ID), map[string]any{
			"state":              req.State,
			"next_state":         nextState,
			"add_to_endpoint_id": endpointID,
		})
		if err != nil {
			return err
		}
		//更新状态记录
		err = txRepo.CreateEndpointApplicationStatus(c, &model.EndpointApplicationStatus{
			ApplicationID: int(req.ID),
			BeforeState:   apply.State,
			AfterState:    req.State,
			Extend:        extend,
			HandlerID:     int(c.GetUint("uid")),
			Remark:        req.Remark,
		})
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}
	//审核通过，发送通知
	if req.State == int(consts.ApplicationApproved) {
		//发送通知
		e.SendNotify(c, apply, code)
		e.EndpointCreated(c, endpointInfo)
	}
	return nil
}

func (e endpointApplyService) GetMaterials(c *gin.Context, name string, categories []int, page, pageSize int) ([]*api.EndpointMaterialSupport, error) {
	var materials []*api.EndpointMaterialSupport
	var param dao.GetMaterialListParam
	param.Name = name
	param.PageNum = page
	param.PageSize = pageSize
	param.Categories = categories
	param.IsPutAway = 1
	data, _ := e.materialRepo.GetMaterialList(c, &param)
	for _, material := range data {
		materials = append(materials, &api.EndpointMaterialSupport{
			ID:               cast.ToUint(material.Id),
			Name:             material.Name,
			Pic:              material.Pic,
			Price:            cast.ToFloat64(material.Price),
			ProductionNumber: material.ProductionNumber,
			Thumbnail:        material.Thumbnail,
		})
	}
	return materials, nil
}

func (e endpointApplyService) MaterialSupport(c *gin.Context, id uint, req []*api.EndpointMaterialSupport) error {
	//判断申请是否存在
	apply, err := e.repo.GetEndpointApplyByID(c, int(id))
	if err != nil {
		return err
	}
	if apply == nil {
		return errors.NewErr("终端申请不存在")
	}
	//判断申请是否已处理
	states := []consts.EndpointApplicationState{consts.ApplicationApproved, consts.ApplicationMaterialSupport}
	if !slices.Contains(states, consts.EndpointApplicationState(apply.State)) {
		return errors.NewErr("物料支持已处理，不能再进行处理")
	}
	nextState, err := e.getNextState(c, apply.PolicyID, int(consts.ApplicationMaterialSupport))
	if err != nil {
		return err
	}
	err = e.repo.WithTransaction(c, func(txRepo applicationDao.EndpointApplyDao) error {
		//更新申请状态
		err = txRepo.UpdateEndpointApply(c, int(id), map[string]any{
			"state":      consts.ApplicationMaterialSupport,
			"next_state": nextState,
		})
		if err != nil {
			return err
		}
		//插入物料表
		var materials []*model.EndpointMaterialSupport
		for _, item := range req {
			materials = append(materials, &model.EndpointMaterialSupport{
				ApplicationID:    int(id),
				Name:             item.Name,
				Num:              item.Num,
				Pic:              item.Pic,
				Price:            item.Price,
				ProductionNumber: item.ProductionNumber,
				Thumbnail:        item.Thumbnail,
			})
		}
		err = txRepo.CreateEndpointMaterialSupport(c, materials)
		if err != nil {
			return err
		}
		return nil
	})

	return nil
}

func (e endpointApplyService) PostbackEndpointApply(c *gin.Context, req *api.PostbackEndpointApplyReq) error {
	//判断申请是否存在
	apply, err := e.repo.GetEndpointApplyByID(c, int(req.ID))
	if err != nil {
		return err
	}
	if apply == nil {
		return errors.NewErr("终端申请不存在")
	}
	//判断申请是否已处理
	states := []consts.EndpointApplicationState{consts.ApplicationMaterialSupport, consts.ApplicationPostback}
	if !slices.Contains(states, consts.EndpointApplicationState(apply.State)) {
		return errors.NewErr("物料支持已处理，不能再进行处理")
	}
	nextState, err := e.getNextState(c, apply.PolicyID, int(consts.ApplicationPostback))
	if err != nil {
		return err
	}
	err = e.repo.WithTransaction(c, func(txRepo applicationDao.EndpointApplyDao) error {
		//更新申请状态
		err = txRepo.UpdateEndpointApply(c, int(req.ID), map[string]any{
			"state":      consts.ApplicationPostback,
			"next_state": nextState,
		})
		if err != nil {
			return err
		}
		//更新回传信息，存在更新，不存在插入
		postback := &model.EndpointApplicationPostback{
			ApplicationID:      int(req.ID),
			WriteOffTable:      req.WriteOffTable,
			LeaseContract:      req.LeaseContract,
			AnnualRent:         req.AnnualRent,
			DesignRenderings:   utils.JsonMarshals(req.DesignRenderings),
			RenovationPhotos:   utils.JsonMarshals(req.RenovationPhotos),
			RenovationVideos:   utils.JsonMarshals(req.RenovationVideos),
			Diploma:            utils.JsonMarshals(req.Diploma),
			EndpointGroupPhoto: utils.JsonMarshals(req.EndpointGroupPhoto),
			Extend:             utils.JsonMarshals(req.Extend),
		}
		err = txRepo.UpsertEndpointApplyPostback(c, int(req.ID), postback)
		if err != nil {
			return err
		}
		return nil
	})
	return nil
}

// WriteOff 核销初审
func (e endpointApplyService) WriteOff(c *gin.Context, req *api.WriteOffReq) error {
	//判断申请是否存在
	apply, err := e.repo.GetEndpointApplyByID(c, int(req.ID))

	if err != nil {
		return err
	}
	if apply == nil {
		return errors.NewErr("终端申请不存在")
	}
	//判断申请是否已处理
	states := []consts.EndpointApplicationState{consts.ApplicationPostback, consts.ApplicationInitialWriteOff}
	if !slices.Contains(states, consts.EndpointApplicationState(apply.State)) {
		return errors.NewErr("物料支持已处理，不能再进行处理")
	}
	//判断状态是否正确
	if req.State != int(consts.ApplicationInitialWriteOff) && req.State != int(consts.ApplicationInitialWriteOffRejected) {
		return errors.NewErr("状态错误")
	}
	nextState, err := e.getNextState(c, apply.PolicyID, req.State)
	if err != nil {
		return err
	}
	err = e.repo.WithTransaction(c, func(txRepo applicationDao.EndpointApplyDao) error {
		//更新申请状态
		err = txRepo.UpdateEndpointApply(c, int(req.ID), map[string]any{
			"state":      req.State,
			"next_state": nextState,
			"pay":        req.Pay,
		})
		if err != nil {
			return err
		}
		//更新状态记录
		err = txRepo.CreateEndpointApplicationStatus(c, &model.EndpointApplicationStatus{
			ApplicationID: int(req.ID),
			BeforeState:   apply.State,
			AfterState:    int(consts.ApplicationInitialWriteOff),
			Extend:        utils.JsonMarshals(req.Extend),
			HandlerID:     int(c.GetUint("uid")),
			Remark:        req.Remark,
		})
		if err != nil {
			return err
		}
		return nil
	})
	return nil
}

// ChannelConfirmation 渠道确认
func (e endpointApplyService) ChannelConfirmation(c *gin.Context, req *api.AuditApplyReq) error {
	//判断申请是否存在
	apply, err := e.repo.GetEndpointApplyByID(c, int(req.ID))

	if err != nil {
		return err
	}
	if apply == nil {
		return errors.NewErr("终端申请不存在")
	}

	//判断状态是否正确
	if req.State != int(consts.ApplicationChannelConfirmation) && req.State != int(consts.ApplicationChannelConfirmationRejected) {
		return errors.NewErr("状态错误")
	}
	nextState, err := e.getNextState(c, apply.PolicyID, req.State)
	if err != nil {
		return err
	}
	err = e.repo.WithTransaction(c, func(txRepo applicationDao.EndpointApplyDao) error {
		//更新申请状态
		err = txRepo.UpdateEndpointApply(c, int(req.ID), map[string]any{
			"state":      req.State,
			"next_state": nextState,
		})
		if err != nil {
			return err
		}
		//更新状态记录
		err = txRepo.CreateEndpointApplicationStatus(c, &model.EndpointApplicationStatus{
			ApplicationID: int(req.ID),
			BeforeState:   apply.State,
			AfterState:    req.State,
			Extend:        utils.JsonMarshals(req.Extend),
			HandlerID:     int(c.GetUint("uid")),
			Remark:        req.Remark,
		})
		if err != nil {
			return err
		}
		return nil
	})
	return nil
}

// RecordConfirmation 入账确认
func (e endpointApplyService) RecordConfirmation(c *gin.Context, req *api.AuditApplyReq) error {
	//判断申请是否存在
	apply, err := e.repo.GetEndpointApplyByID(c, int(req.ID))

	if err != nil {
		return err
	}

	if apply == nil {
		return errors.NewErr("终端申请不存在")
	}
	//判断状态是否正确
	if req.State != int(consts.ApplicationRecordConfirmation) && req.State != int(consts.ApplicationRecordConfirmationRejected) {
		return errors.NewErr("状态错误")
	}
	nextState, err := e.getNextState(c, apply.PolicyID, req.State)
	if err != nil {
		return err
	}
	//计算是否需要分期
	installments, err := e.computeInstallment(c, apply)
	if err != nil {
		return err
	}
	err = e.repo.WithTransaction(c, func(txRepo applicationDao.EndpointApplyDao) error {
		//更新申请状态
		err = txRepo.UpdateEndpointApply(c, int(req.ID), map[string]any{
			"state":      req.State,
			"next_state": nextState,
		})
		if err != nil {
			return err
		}
		//更新状态记录
		err = txRepo.CreateEndpointApplicationStatus(c, &model.EndpointApplicationStatus{
			ApplicationID: int(req.ID),
			BeforeState:   apply.State,
			AfterState:    req.State,
			Extend:        utils.JsonMarshals(req.Extend),
			HandlerID:     int(c.GetUint("uid")),
			Remark:        req.Remark,
		})
		if err != nil {
			return err
		}
		//生成入账信息
		if installments != nil {
			//删除旧的分期信息
			err = txRepo.DeleteEndpointApplicationInstallment(c, apply.ID)
			if err != nil {
				return err
			}
			err = txRepo.CreateEndpointApplicationInstallment(c, installments)
			if err != nil {
				return err
			}
		}
		return nil
	})
	return nil
}

func (e endpointApplyService) DownloadWriteOffTable(c *gin.Context, id int) error {
	// 查询相关的政策
	apply, err := e.repo.GetEndpointApplyByID(c, id)
	if err != nil {
		return err
	}
	if apply == nil {
		return errors.NewErr("申请不存在")
	}
	// 查询相关的政策
	policy, err := e.policyRepo.GetEndpointPolicyByID(c, apply.PolicyID)
	if err != nil {
		return err
	}
	if policy == nil {
		return errors.NewErr("政策不存在")
	}
	if policy.WriteOffTable == "" {
		return errors.NewErr("政策不存在批复表模板")
	}
	var agencyName string
	var ok bool
	agencyMap, err := e.endpointRepo.GetAgencies(c, []int{int(apply.TopAgency), int(apply.SecondAgency)})
	if agencyName, ok = agencyMap[apply.TopAgency]; !ok {
		return errors.NewErr("渠道不存在")
	}
	if apply.SecondAgency == 0 {
		agencyName += "直营"
	} else {
		if secondAgencyName, ok := agencyMap[apply.SecondAgency]; ok {
			agencyName += secondAgencyName
		}
	}

	// 下载模板
	objectKey := utils.DeletePrefix(policy.WriteOffTable)
	docData, err := oss.Download(objectKey)
	if err != nil {
		log.Error("模板下载失败" + err.Error())
		return err
	}
	doc, err := document.Read(bytes.NewReader(docData), int64(len(docData)))
	if err != nil {
		return err
	}
	var (
		typeName       string
		saleAmount     float64
		expectOpenTime time.Time
	)

	//查询建店信息
	endpoint, err := e.endpointRepo.GetEndpointByID(c, apply.AddToEndpointId)
	if err != nil {
		return err
	}
	if endpoint == nil {
		return errors.NewErr("终端不存在")
	}

	table := doc.Tables()[0]
	typeMap := consts.GetEndpointNameMap()
	typeName = typeMap[consts.EndpointType(apply.Type)]

	fillTableCell := func(row, col int, text string) {
		cell := table.Rows()[row].Cells()[col]
		para := cell.Paragraphs()
		run := para[0].AddRun()
		run.Properties().SetFontFamily("黑体")
		run.AddText(text)
	}

	if apply.ApplyType == "new" {
		fillTableCell(0, 0, typeName)
		fillTableCell(1, 0, apply.Investor)
		fillTableCell(1, 1, apply.InvestorPhone)
		fillTableCell(2, 0, apply.Name)
		fillTableCell(2, 1, endpoint.Code)
		fillTableCell(3, 0, endpoint.Address)
		fillTableCell(3, 1, apply.ExpectOpenTime.String())
		fillTableCell(4, 0, apply.Pay+"元")
		fillTableCell(4, 1, agencyName)
	} else {
		fillTableCell(0, 0, apply.Name)
		fillTableCell(0, 1, endpoint.Code)
		fillTableCell(1, 0, endpoint.Address)
		fillTableCell(1, 1, expectOpenTime.String())
		fillTableCell(2, 0, apply.Investor)
		fillTableCell(2, 1, apply.InvestorPhone)
		fillTableCell(3, 0, apply.EndpointArea)
		fillTableCell(3, 1, cast.ToString(saleAmount))
		fillTableCell(4, 0, apply.Pay)
		fillTableCell(4, 1, agencyName)
	}
	materialRow := 6
	cell := table.Rows()[materialRow].Cells()[0]
	para := cell.Paragraphs()
	run := para[0].AddRun()
	run.Properties().SetFontFamily("黑体")
	run.AddText("物料支持明细：")
	run.AddBreak()

	//查询物料支持明细
	materialDetail, err := e.repo.GetEndpointMaterialSupportById(c, apply.ID)
	if err != nil {
		return err
	}

	for _, line := range materialDetail {
		run.AddText(fmt.Sprintf("%s * %v", line.Name, line.Num))
		run.AddBreak()
	}
	var bs bytes.Buffer
	if err := doc.Save(&bs); err != nil {
		return err
	}
	pdf, err := utils.ConvertDocxToPDF(bs.Bytes())
	if err != nil {
		return err
	}
	// 设置响应头，告知浏览器这是一个 PDF 文件
	fileName := fmt.Sprintf("终端批复表_%s.pdf", time.Now().Format("20060102_150405"))
	c.Header("Content-Type", "application/pdf")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", fileName))

	// 写入响应体
	_, err = c.Writer.Write(pdf)
	if err != nil {
		return err
	}
	return nil
}

func (e endpointApplyService) LatestEndpointImage(c *gin.Context, id int) (*api.LatestEndpointImageResp, error) {
	imageInfo, err := e.endpointImageDao.GetLatest(c, id)
	if err != nil {
		return nil, err
	}
	var resp api.LatestEndpointImageResp
	if imageInfo == nil {
		return &resp, nil
	}
	// 实现 SQL 逻辑：IF(status = 'approved', audit_time, null)
	if imageInfo.Status == "approved" {
		resp.AutoTime = imageInfo.AuditTime
	}
	// 实现 SQL 逻辑：IF(manual_status = 'approved', manual_audit_time, null)
	if imageInfo.ManualStatus == "approved" {
		resp.ManualTime = imageInfo.ManualAuditTime
	}
	return &resp, nil
}

func (e endpointApplyService) SalesAmount(c *gin.Context, id int, years int, endAt int64) (int64, float64, error) {
	end := time.Unix(endAt, 0)
	status := 1
	endTime := end.AddDate(0, -1, 0).Format(time.DateTime)
	// 计算起始时间为上一个月的当前时间
	startTime := end.AddDate(-years, 0, 0).Format(time.DateTime)
	var params warrantyDao.StatParams
	params.EndpointID = uint(id)
	params.StartTime = startTime
	params.EndTime = endTime
	params.Status = &status
	params.Realsale = &status
	amount, err := e.warrantyRepo.StatCountAndAmount(c, &params)
	if err != nil {
		return 0, 0, err
	}
	return amount.Count, amount.Amount, nil
}

func (e endpointApplyService) computeInstallment(c *gin.Context, apply *model.EndpointApplication) ([]*model.EndpointApplicationInstallment, error) {
	if apply.PolicyID == 0 {
		return nil, nil
	}
	if cast.ToFloat64(apply.Pay) == 0 {
		return nil, nil
	}
	// 查询相关的政策
	policy, err := e.policyRepo.GetEndpointPolicyByID(c, apply.PolicyID)
	if err != nil {
		return nil, err
	}
	if policy == nil {
		return nil, nil
	}
	if policy.Installments == 0 {
		return nil, nil
	}
	//按月分期(如果当天日期大于等于20号，则从下一个月开始)
	now := time.Now()
	if now.Day() >= 20 {
		now = now.AddDate(0, 1, 0)
	}
	// 把日期固定到当月 1 号，避免溢出
	now = time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local)

	total := cast.ToFloat64(apply.Pay) * 100 // 转成分
	eachFee := total / float64(policy.Installments)
	lastFee := total - eachFee*(float64(policy.Installments)-1)

	var installments []*model.EndpointApplicationInstallment
	for i := 0; i < policy.Installments; i++ {
		if i > 0 {
			now = now.AddDate(0, 1, 0)
		}
		fee := uint(eachFee)
		if i == policy.Installments-1 {
			fee = uint(lastFee) // 最后一期修正差额
		}

		installments = append(installments, &model.EndpointApplicationInstallment{
			ApplicationID: uint(apply.ID),
			Year:          uint16(now.Year()),
			Month:         uint8(now.Month()),
			Date:          time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.Local),
			Fee:           fee,
		})

	}

	// 计算分期
	return installments, nil
}

func (e endpointApplyService) getNextState(c *gin.Context, policyID int, state int) (int, error) {
	if policyID == 0 {
		return 0, nil
	}
	policy, err := e.policyRepo.GetEndpointPolicyByID(c, policyID)
	if err != nil {
		return 0, err
	}
	if policy.States == "" {
		return 0, nil
	}
	// 状态流转
	states := strings.Split(policy.States, ",")
	for i := range states {
		if states[i] == strconv.Itoa(state) {
			if i+1 < len(states) {
				return strconv.Atoi(states[i+1])
			}
			return 0, nil
		}
	}
	return 0, nil
}

// BuildFullAddress 根据省市区ID和详细地址构建完整地址
func (e endpointApplyService) BuildFullAddress(provinceID, cityID, districtID int, detailAddress string) string {
	// 收集非零的地区ID
	regionIDs := make([]int, 0, 3)
	if provinceID != 0 {
		regionIDs = append(regionIDs, provinceID)
	}
	if cityID != 0 {
		regionIDs = append(regionIDs, cityID)
	}
	if districtID != 0 {
		regionIDs = append(regionIDs, districtID)
	}

	// 如果没有地区ID，直接返回详细地址
	if len(regionIDs) == 0 {
		return detailAddress
	}

	// 查询地区信息
	regions := dao.GetRegionsByIds(regionIDs)

	// 创建地区ID到名称的映射
	regionMap := make(map[int]string, len(regions))
	for _, r := range regions {
		regionMap[r.RegionId] = r.RegionName
	}

	// 构建地址
	var sb strings.Builder

	// 按顺序添加省市区
	if name, ok := regionMap[provinceID]; ok {
		sb.WriteString(name)
	}
	if name, ok := regionMap[cityID]; ok {
		sb.WriteString(name)
	}
	if name, ok := regionMap[districtID]; ok {
		sb.WriteString(name)
	}

	// 添加详细地址
	sb.WriteString(detailAddress)

	return sb.String()
}

func (e endpointApplyService) SendNotify(c *gin.Context, apply *model.EndpointApplication, code string) {
	signName := "读书郎"
	templateCode := "endpoint_code" // 短信模板代码
	templateParam := make(map[string]any)
	templateParam["name"] = apply.Name
	templateParam["code"] = code

	if apply.Phone != nil {
		_, msg := sms.SendSMS(*apply.Phone, signName, templateCode, templateParam)
		log.Info(msg)
	}
	if apply.Investor != "" {
		_, msg := sms.SendSMS(apply.Investor, signName, templateCode, templateParam)
		log.Info(msg)
	}

	var designMsg string
	switch apply.ApplyType {
	case "new":
		designMsg = fmt.Sprintf("终端 %s 建店申请审核已通过，请进入营销后台查看设计需求。", code)
	case "renew":
		designMsg = fmt.Sprintf("终端 %s 老店改造申请审核已通过，请进入营销后台查看设计需求。", code)
	case "upgrade":
		designMsg = fmt.Sprintf("终端 %s 终端形象升级申请审核已通过，请进入营销后台查看设计需求。", code)
	}

	linkText := "营销后台"
	linkURL := "https://yx.readboy.com/admin/auth/login"
	content := strings.Replace(designMsg, linkText, fmt.Sprintf(`<a href="%s">%s</a>`, linkURL, linkText), 1)

	design, err := e.configRepo.GetConfig(c, "endpoint_audit_pass_notify_employess")

	if err != nil {
		log.Error("获取审核通过通知设计失败" + err.Error())
	}
	if design != "" {
		err = sms.OaSendWecomMsg(strings.Split(design, ","), content)
		if err != nil {
			log.Error("发送审核通过通知失败" + err.Error())
		}
	}
}

func (e endpointApplyService) EndpointCreated(c *gin.Context, endpointInfo *model.Endpoint) {
	//桌椅权限处理
	err := e.endpointRepo.GrantH5PermissionToEndpoint(c, endpointInfo)
	if err != nil {
		log.Error(" GrantH5PermissionToEndpoint err:" + err.Error())
	}
	//课程处理
	err = e.subjectRepo.BindToEndpoint(c, endpointInfo)
	if err != nil {
		log.Error(" GrantCourseToEndpoint err:" + err.Error())
	}
}
