package system

import (
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"marketing/internal/api/system"
	"marketing/internal/handler"
	"marketing/internal/pkg/errors"
	system2 "marketing/internal/service/system"
)

type WecomTagInterface interface {
	Lists(c *gin.Context)
	Add(c *gin.Context)
	Update(c *gin.Context)
	Delete(c *gin.Context)
	SyncTagsFormWecom(c *gin.Context)
	MoveDepartment(c *gin.Context)
}

type wecomTag struct {
	wecomTagSvc system2.WecomTagInterface
}

// NewWecomTag creates a new WecomTag instance
func NewWecomTag(wecomTagSvc system2.WecomTagInterface) WecomTagInterface {
	return &wecomTag{
		wecomTagSvc: wecomTagSvc,
	}
}

// Lists  all tags
func (a *wecomTag) Lists(c *gin.Context) {
	var req system.WecomTagReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	req.PaginationParams.SetDefaults()
	data, err := a.wecomTagSvc.List(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, data)
}

// Add adds a new tag
func (a *wecomTag) Add(c *gin.Context) {
	var req system.AddWecomTagReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	err := a.wecomTagSvc.Add(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

// Update updates an existing tag
func (a *wecomTag) Update(c *gin.Context) {
	var req system.AddWecomTagReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	req.ID = cast.ToUint(c.Param("id"))
	if req.ID == 0 {
		handler.Error(c, errors.NewErr("id不能为空"))
		return
	}
	err := a.wecomTagSvc.Update(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

// Delete deletes a tag
func (a *wecomTag) Delete(c *gin.Context) {
	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		handler.Error(c, errors.NewErr("id不能为空"))
		return
	}
	err := a.wecomTagSvc.Delete(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

func (a *wecomTag) SyncTagsFormWecom(c *gin.Context) {
	data, err := a.wecomTagSvc.SyncTagsFormWecom(c)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, data)
}

func (a *wecomTag) MoveDepartment(c *gin.Context) {

	pid := cast.ToInt(c.Query("pid"))
	id := cast.ToInt(c.Query("id"))
	if pid == 0 || id == 0 {
		handler.Error(c, errors.NewErr("pid和id不能为空"))
		return
	}
	data, err := a.wecomTagSvc.MoveDepartment(c, pid, id)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, data)
}
