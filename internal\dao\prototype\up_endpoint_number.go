package prototype

import (
	"errors"
	"marketing/internal/api/prototype"
	"marketing/internal/model"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// UpEndpointNumber 上样终端数量DAO
type UpEndpointNumber interface {
	GetList(c *gin.Context, req *prototype.UpEndpointNumberSearch) ([]*model.PrototypeUpEndpointNumber, int64, error)
	GetTopAgencyEndpointNum(c *gin.Context) (map[int]int, error)
	BatchCreate(c *gin.Context, data []*model.PrototypeUpEndpointNumber) error
	CheckModelAllowed(c *gin.Context, modelName string) (bool, error)
	CheckModelExisted(c *gin.Context, modelName string) (bool, error)
}

type upEndpointNumber struct {
	db *gorm.DB
}

// NewUpEndpointNumberDao 创建DAO实例
func NewUpEndpointNumberDao(db *gorm.DB) UpEndpointNumber {
	return &upEndpointNumber{
		db: db,
	}
}

func (s *upEndpointNumber) GetList(c *gin.Context, req *prototype.UpEndpointNumberSearch) ([]*model.PrototypeUpEndpointNumber, int64, error) {
	var list []*model.PrototypeUpEndpointNumber
	var count int64
	query := s.db.Model(&model.PrototypeUpEndpointNumber{})
	// 过滤条件
	if req.Model != "" {
		query = query.Where("model LIKE ?", "%"+req.Model+"%")
	}
	if req.TopAgency != 0 {
		query = query.Where("top_agency=?", req.TopAgency)
	}
	err := query.Count(&count).Error
	if err != nil {
		return nil, 0, err
	}
	err = query.Offset((req.Page - 1) * req.PageSize).Limit(req.PageSize).Find(&list).Error
	if err != nil {
		return nil, 0, err
	}
	return list, count, nil
}

func (s *upEndpointNumber) GetTopAgencyEndpointNum(c *gin.Context) (map[int]int, error) {
	var tempResults []struct {
		AgencyID       int   `gorm:"column:id"`
		EndpointNumber int64 `gorm:"column:endpoint_number"`
	}

	err := s.db.WithContext(c).Model(&model.Agency{}).
		Joins("left join endpoint as e on e.top_agency = agency.id").
		Where("e.status = 1").
		Where("e.open_status = 0").
		Where("agency.deleted_at IS NULL"). // 确保 agency 表的 deleted_at 条件也应用上
		Select("agency.id, count(e.id) as endpoint_number").
		Group("agency.id"). // 需要按 agency.id 分组才能正确计数每个 agency 的终端数
		Scan(&tempResults).Error

	if err != nil {
		return nil, err
	}

	result := make(map[int]int)
	for _, r := range tempResults {
		result[r.AgencyID] = int(r.EndpointNumber) // 将 int64 转换为 int
	}

	return result, nil
}

func (s *upEndpointNumber) BatchCreate(c *gin.Context, data []*model.PrototypeUpEndpointNumber) error {
	return s.db.WithContext(c).Create(data).Error
}

// CheckModelAllowed 检查机型是否允许添加
func (s *upEndpointNumber) CheckModelAllowed(c *gin.Context, modelName string) (bool, error) {
	var count int64
	err := s.db.WithContext(c).Model(&model.MachineType{}).
		Where("name = ? AND prototype_status = 1", modelName).
		Count(&count).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return false, err
	}
	return count > 0, nil
}

// CheckModelExisted 检查机型是否允许添加
func (s *upEndpointNumber) CheckModelExisted(c *gin.Context, modelName string) (bool, error) {
	var count int64
	err := s.db.WithContext(c).Model(&model.PrototypeUpEndpointNumber{}).
		Where("model = ?", modelName).
		Count(&count).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return false, err
	}
	return count > 0, nil
}
