package middleware

import (
	"bytes"
	"encoding/json"
	"fmt"
	api "marketing/internal/api/system"
	"marketing/internal/model"
	logger "marketing/internal/pkg/log"
	"marketing/internal/pkg/utils"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// bodyLogWriter 响应内容捕获器
type bodyLogWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w *bodyLogWriter) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}

func (w *bodyLogWriter) WriteString(s string) (int, error) {
	w.body.WriteString(s)
	return w.ResponseWriter.WriteString(s)
}

// 需要脱敏的字段
var sensitiveFields = []string{
	"password",
	"oldPassword",
	"newPassword",
	"token",
	"accessToken",
	"refreshToken",
}

// 需要记录日志的路由配置
var needLogRoutes = map[string]struct {
	Module          string
	OpType          string
	Action          string
	NeedRequestData bool // 是否需要记录请求参数
	NeedAfterData   bool // 是否需要记录响应数据
	NeedBeforeData  bool // 是否需要记录修改前数据
}{
	"POST:/admin/system/users": {
		Module:          "用户管理",
		OpType:          "create",
		Action:          "创建用户",
		NeedRequestData: false,
		NeedAfterData:   true,
		NeedBeforeData:  false,
	},
	"PUT:/admin/system/users/:id": {
		Module:          "用户管理",
		OpType:          "edit",
		Action:          "更新用户",
		NeedRequestData: false,
		NeedAfterData:   true,
		NeedBeforeData:  true,
	},
	"POST:/admin/system/users/:id/reset-password": {
		Module:          "用户管理",
		OpType:          "reset_pwd",
		Action:          "重置密码",
		NeedRequestData: true,
		NeedAfterData:   false,
		NeedBeforeData:  false,
	},
	"POST:/admin/system/users/:id/status": {
		Module:          "用户管理",
		OpType:          "status",
		Action:          "禁用/启用用户",
		NeedRequestData: true,
		NeedAfterData:   false,
		NeedBeforeData:  false,
	},
	"POST:/admin/system/users/:id/wecom-tags": {
		Module:          "用户管理",
		OpType:          "edit",
		Action:          "修改用户标签",
		NeedRequestData: true,
		NeedAfterData:   false,
		NeedBeforeData:  false,
	},
	"POST:/admin/system/users/:id/bind-wecom": {
		Module:          "用户管理",
		OpType:          "edit",
		Action:          "管理端绑定企业微信",
		NeedRequestData: false,
		NeedAfterData:   true,
		NeedBeforeData:  true,
	},
	"DELETE:/admin/system/users/:id": {
		Module:          "用户管理",
		OpType:          "unregister",
		Action:          "用户注销",
		NeedRequestData: true,
		NeedAfterData:   false,
		NeedBeforeData:  false,
	},
	"PUT:/admin/system/mine": {
		Module:          "我的",
		OpType:          "edit",
		Action:          "我的资料更新",
		NeedRequestData: true,
		NeedAfterData:   false,
		NeedBeforeData:  false,
	},
	"POST:/admin/system/mine/reset-password": {
		Module:          "我的",
		OpType:          "reset_pwd",
		Action:          "我的密码重置",
		NeedRequestData: false,
		NeedAfterData:   false,
		NeedBeforeData:  false,
	},
	//代理用户新增修改
	"POST:/admin/agency/users": {
		Module:          "经销商用户管理",
		OpType:          "create",
		Action:          "创建代理用户",
		NeedRequestData: false,
		NeedAfterData:   true,
		NeedBeforeData:  false,
	},
	"DELETE:/admin/agency/users/:id": {
		Module:          "经销商用户管理",
		OpType:          "unregister",
		Action:          "用户注销",
		NeedRequestData: true,
		NeedAfterData:   false,
		NeedBeforeData:  false,
	},
	"PUT:/admin/agency/users/:id": {
		Module:          "经销商用户管理",
		OpType:          "edit",
		Action:          "经销商管理更新用户",
		NeedRequestData: false,
		NeedAfterData:   true,
		NeedBeforeData:  true,
	},
	"POST:/admin/agency/users/:id/reset-password": {
		Module:          "经销商用户管理",
		OpType:          "reset_pwd",
		Action:          "重置密码",
		NeedRequestData: true,
		NeedAfterData:   false,
		NeedBeforeData:  false,
	},
	"POST:/admin/agency/users/:id/status": {
		Module:          "经销商用户管理",
		OpType:          "status",
		Action:          "禁用/启用用户",
		NeedRequestData: true,
		NeedAfterData:   false,
		NeedBeforeData:  false,
	},
	"POST:/admin/agency/users/:id/wecom-tags": {
		Module:          "经销商用户管理",
		OpType:          "edit",
		Action:          "经销商管理修改用户标签",
		NeedRequestData: true,
		NeedAfterData:   false,
		NeedBeforeData:  false,
	},
	//终端用户新增修改
	"POST:/admin/endpoint/users": {
		Module:          "终端用户管理",
		OpType:          "create",
		Action:          "新增终端用户",
		NeedRequestData: false,
		NeedAfterData:   true,
		NeedBeforeData:  false,
	},
	"DELETE:/admin/endpoint/users/:id": {
		Module:          "终端用户管理",
		OpType:          "unregister",
		Action:          "用户注销",
		NeedRequestData: true,
		NeedAfterData:   false,
		NeedBeforeData:  false,
	},
	"PUT:/admin/endpoint/users/:id": {
		Module:          "终端用户管理",
		OpType:          "edit",
		Action:          "更新用户",
		NeedRequestData: false,
		NeedAfterData:   true,
		NeedBeforeData:  true,
	},
	"POST:/admin/endpoint/users/:id/reset-password": {
		Module:          "终端用户管理",
		OpType:          "reset_pwd",
		Action:          "重置密码",
		NeedRequestData: true,
		NeedAfterData:   false,
		NeedBeforeData:  false,
	},
	"POST:/admin/endpoint/users/:id/status": {
		Module:          "终端用户管理",
		OpType:          "status",
		Action:          "禁用/启用用户",
		NeedRequestData: true,
		NeedAfterData:   false,
		NeedBeforeData:  false,
	},
	"POST:/admin/endpoint/users/:id/wecom-tags": {
		Module:          "终端用户管理",
		OpType:          "edit",
		Action:          "修改用户标签",
		NeedRequestData: true,
		NeedAfterData:   false,
		NeedBeforeData:  false,
	},
}

func AdminUserLogMiddleware(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取路由配置
		routeKey := c.Request.Method + ":" + c.FullPath()
		fmt.Println(routeKey)

		logConfig, needLog := needLogRoutes[routeKey]
		if !needLog {
			c.Next()
			return
		}

		startTime := time.Now()
		var beforeData string
		var requestData string
		var responseBody string
		var uid int
		var username string
		var phone string
		var beforeDataAny any

		uid = cast.ToInt(c.Param("id"))

		// 获取请求数据
		if logConfig.NeedRequestData {
			requestData = c.GetString("reqBody")
		}

		if logConfig.NeedBeforeData {
			beforeDataAny, beforeData = getBeforeData(db, logConfig.Module, uid)

			username = beforeDataAny.(api.UserLog).Username
			if beforeDataAny.(api.UserLog).Phone != nil {
				phone = *beforeDataAny.(api.UserLog).Phone
			}
		}

		// 捕获响应数据
		blw := &bodyLogWriter{
			ResponseWriter: c.Writer,
			body:           bytes.NewBufferString(""),
		}
		c.Writer = blw
		c.Next()
		responseBody = blw.body.String()

		// 异步记录日志
		if c.Writer.Status() >= 200 && c.Writer.Status() < 300 && responseBodyContainsOK(responseBody) {

			go func() {
				if logConfig.OpType == "create" {
					username = c.GetString("username")
					if username == "" {
						return
					}
					user, err := getUserByUsername(db, username)
					if err != nil {
						logger.Error("根据用户名查询用户信息失败", zap.Error(err))
						return
					}
					if user.Phone != nil {
						phone = *user.Phone
					}
					uid = int(user.ID)
				}
				afterData := ""
				if logConfig.NeedAfterData {
					_, afterData = getBeforeData(db, logConfig.Module, uid)
				}
				remark := logConfig.Action
				if logConfig.OpType == "status" {
					statusAny, _ := getFieldValueFromReqBody(requestData, "status")
					status := cast.ToInt(statusAny)
					if status == 1 {
						remark = "启用用户"
					} else {
						remark = "禁用用户"
					}
				}
				log := model.AdminUserLog{
					UID:        uid,
					Username:   username,
					OpType:     logConfig.OpType,
					Phone:      phone,
					Module:     logConfig.Module,
					Remark:     remark,
					Method:     c.Request.Method,
					Path:       c.FullPath(),
					Operator:   int(c.GetUint("uid")),
					OperatorIP: c.ClientIP(),
					Request:    requestData,
					Before:     beforeData,
					After:      afterData,
					CreatedAt:  startTime,
				}

				if err := db.Create(&log).Error; err != nil {
					logger.Error("failed to save operation log", zap.Error(err))
				}
			}()
		}
	}
}

func responseBodyContainsOK(responseBody string) bool {
	// 定义响应数据结构体
	type ResponseData struct {
		OK int `json:"ok"` // 直接定义为 int 类型
	}

	var responseData ResponseData
	if err := json.Unmarshal([]byte(responseBody), &responseData); err != nil {
		return false
	}

	// 检查 ok 的值
	return responseData.OK == 1 // 只判断是否等于 1
}

// getBeforeData 获取修改前的数据
func getBeforeData(db *gorm.DB, module string, id int) (any, string) {
	if id == 0 {
		return nil, ""
	}

	var data interface{}

	switch module {
	case "用户管理", "经销商用户管理", "我的", "终端用户管理":
		var user api.UserLog
		// 查询用户基本信息
		if err := db.Table("admin_users").
			Where("id = ?", id).
			First(&user).Error; err != nil {
			return nil, ""
		}

		// 查询用户角色
		if err := db.Table("admin_roles").
			Select("admin_roles.id, admin_roles.name").
			Joins("JOIN admin_role_users ON admin_roles.id = admin_role_users.role_id").
			Where("admin_role_users.user_id = ?", id).
			Where("admin_roles.deleted_at is null").
			Scan(&user.Roles).Error; err != nil {
			// 记录错误但继续执行
			logger.Error("获取用户角色失败", zap.Error(err))
		}

		// 查询用户组
		var userGroup struct {
			ID   uint   `json:"id"`
			Name string `json:"name"`
		}
		if err := db.Table("admin_user_group_v2").
			Select("admin_user_group_v2.id, admin_user_group_v2.name").
			Joins("JOIN admin_user_user_group_v2 ON admin_user_group_v2.id = admin_user_user_group_v2.user_group_id").
			Where("admin_user_user_group_v2.user_id = ?", id).
			Scan(&userGroup).Error; err == nil {
			user.UserGroup = &userGroup
			user.GroupName = &userGroup.Name
		}

		// 查询用户标签
		var tags []struct {
			ID   uint   `json:"id"`
			Name string `json:"tag_name"`
		}
		if err := db.Table("wecom_tags").
			Select("wecom_tags.id, wecom_tags.tag_name").
			Joins("JOIN wecom_user_tag ON wecom_tags.id = wecom_user_tag.tag_id").
			Where("wecom_user_tag.user_id = ?", id).
			Where("wecom_tags.deleted_at IS NULL").
			Scan(&tags).Error; err == nil {
			user.Tags = &tags
		}
		// 查询用户所属经销商
		var agencies []struct {
			ID   uint   `json:"id"`
			Name string `json:"name"`
		}
		if err := db.Table("agency a").
			Select("a.id, a.name").
			Joins("JOIN user_agency ON a.id = user_agency.top_agency OR a.id = user_agency.second_agency").
			Where("user_agency.uid =?", id).
			Where("user_agency.status = ?", 1).
			Scan(&agencies).Error; err == nil {
			user.Agency = &agencies
		}
		// 查询用户所属终端
		var endpoints []struct {
			ID   uint   `json:"id"`
			Name string `json:"name"`
		}
		if err := db.Table("endpoint e").
			Select("e.id, e.name").
			Joins("JOIN user_endpoint ON e.id = user_endpoint.endpoint").
			Where("user_endpoint.uid =?", id).
			Scan(&endpoints).Error; err == nil {
			user.Endpoint = &endpoints
		}

		data = user

	case "角色管理":
		var role struct {
			ID          uint   `json:"id"`
			Name        string `json:"name"`
			Status      uint   `json:"status"`
			Remark      string `json:"remark"`
			Permissions []struct {
				ID   uint   `json:"id"`
				Name string `json:"name"`
			} `json:"permissions"`
			CreatedAt time.Time `json:"created_at"`
			UpdatedAt time.Time `json:"updated_at"`
		}

		// 查询角色基本信息
		if err := db.Table("admin_roles").
			Where("id = ?", id).
			First(&role).Error; err != nil {
			return nil, ""
		}

		// 查询角色权限
		if err := db.Table("admin_permissions_v2 p").
			Select("p.id, p.name").
			Joins("JOIN admin_role_permission_v2 rp ON p.id = rp.permission_id").
			Where("rp.role_id = ?", id).
			Scan(&role.Permissions).Error; err != nil {
			logger.Error("获取角色权限失败", zap.Error(err))
		}

		data = role

	case "用户组管理":
		var group struct {
			ID     uint   `json:"id"`
			Name   string `json:"name"`
			Status uint   `json:"status"`
			Remark string `json:"remark"`
			Users  []struct {
				ID       uint   `json:"id"`
				Username string `json:"username"`
				Name     string `json:"name"`
			} `json:"users"`
			CreatedAt time.Time `json:"created_at"`
			UpdatedAt time.Time `json:"updated_at"`
		}

		// 查询用户组基本信息
		if err := db.Table("admin_user_groups_v2").
			Where("id = ?", id).
			First(&group).Error; err != nil {
			return nil, ""
		}

		// 查询组内用户
		if err := db.Table("admin_users").
			Select("admin_users.id, admin_users.username, admin_users.name").
			Joins("JOIN admin_user_user_group_v2 ON admin_users.id = admin_user_user_group_v2.user_id").
			Where("admin_user_user_group_v2.user_group_id = ?", id).
			Scan(&group.Users).Error; err != nil {
			logger.Error("获取用户组成员失败", zap.Error(err))
		}

		data = group
	}

	if data == nil {
		return nil, ""
	}

	// 转换为JSON并脱敏
	return data, utils.JSONMaskFields(
		utils.ToJSON(data),
		sensitiveFields,
	)
}

// getUserByUsername 根据用户名查询用户信息
func getUserByUsername(db *gorm.DB, username string) (*model.AdminUsers, error) {
	var user model.AdminUsers
	if err := db.Where("username = ? or phone = ?", username, username).First(&user).Error; err != nil {
		return nil, err
	}
	return &user, nil
}

func getFieldValueFromReqBody(reqBody string, key string) (interface{}, error) {
	// 定义响应数据结构体
	type ReqData map[string]interface{}

	var reqData ReqData
	if err := json.Unmarshal([]byte(reqBody), &reqData); err != nil {
		return nil, err
	}

	// 检查 key 是否存在
	value, exists := reqData[key]
	if !exists {
		return nil, fmt.Errorf("key '%s' not found in request body", key)
	}

	return value, nil
}
