package endpoint

import (
	"marketing/internal/api/system"
	"marketing/internal/consts"
	"marketing/internal/handler"
	"marketing/internal/pkg/errors"
	endpointSvc "marketing/internal/service/endpoint"
	system2 "marketing/internal/service/system"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

// UserHandler 总代用户接口
type UserHandler interface {
	Lists(c *gin.Context)
	Add(c *gin.Context)
	Update(c *gin.Context)
	Delete(c *gin.Context)
	GetRoleDropdown(c *gin.Context)
	SyncUser(c *gin.Context)
	SyncDepartment(c *gin.Context)
}

type endpointUser struct {
	adminUserSvc system2.AdminUserInterface
	adminRoleSvc system2.AdminRoleInterface
	endpointSvc  endpointSvc.Endpoint
}

// NewEndpointUser 创建 adminUser 实例
func NewEndpointUser(
	adminUserSvc system2.AdminUserInterface,
	adminRoleSvc system2.AdminRoleInterface,
	endpointSvc endpointSvc.Endpoint,
) UserHandler {
	return &endpointUser{
		adminUserSvc: adminUserSvc,
		adminRoleSvc: adminRoleSvc,
		endpointSvc:  endpointSvc,
	}
}

// Lists 获取用户列表
func (a *endpointUser) Lists(c *gin.Context) {
	var req system.AdminUserReq
	// 你可以使用显式绑定声明绑定 multipart form：绑定不了json格式
	// c.ShouldBindWith(&form, binding.Form)
	// 或者简单地使用 ShouldBind 方法自动绑定
	// 如果是 `GET` 请求，只使用 `Form` 绑定引擎（`query`）。
	// 如果是 `POST` 请求，首先检查 `content-type` 是否为 `JSON` 或 `XML`，然后再使用 `Form`（`form-data`）。
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	//分页处理
	req.PaginationParams.SetDefaults()
	// 用户组来区分是否是代理商
	req.Type = "endpoint"
	data, err := a.adminUserSvc.Lists(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, data)
}

// Add 新增用户
func (a *endpointUser) Add(c *gin.Context) {
	var req system.AddUserReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	err := a.adminUserSvc.AddEndpointUser(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

// Update 更新用户信息
func (a *endpointUser) Update(c *gin.Context) {
	var req system.AddUserReq
	if err := c.ShouldBind(&req); err != nil {
		err = errors.NewErr("参数错误" + err.Error())
		handler.Error(c, err)
		return
	}
	req.ID = cast.ToUint(c.Param("id"))
	if req.ID == 0 {
		handler.Error(c, errors.NewErr("id不能为空"))
		return
	}
	req.UpdateType = consts.EndpointPrefix
	err := a.adminUserSvc.Update(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

// GetRoleDropdown 获取用户角色列表
func (a *endpointUser) GetRoleDropdown(c *gin.Context) {
	roleName := c.Query("roleName")
	roles, err := a.adminRoleSvc.ListRolesNoPage(c, roleName, "endpoint")
	if err != nil {
		handler.Error(c, err)
		return
	}
	var data []map[string]interface{}
	for _, role := range roles {
		data = append(data, map[string]interface{}{
			"id":   role.ID,
			"slug": role.Slug,
			"name": role.Name,
		})
	}

	handler.Success(c, data)
}

// Delete 删除用户
func (a *endpointUser) Delete(c *gin.Context) {
	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		handler.Error(c, errors.NewErr("id不能为空"))
		return
	}
	err := a.adminUserSvc.Delete(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

// SyncUser 同步用户到微信
func (a *endpointUser) SyncUser(c *gin.Context) {
	type Request struct {
		IDs    []int  `json:"ids"`
		TagIDs []uint `json:"tags"`
	}
	var req Request
	// 解析 JSON 数据
	if err := c.BindJSON(&req); err != nil {
		handler.Error(c, errors.NewErr("请求数据格式错误，不是有效的 JSON 格式"))
		return
	}
	ids := req.IDs
	if len(ids) == 0 {
		handler.Error(c, errors.NewErr("ids不能为空"))
		return
	}
	err := a.adminUserSvc.SyncUser(c, ids, req.TagIDs)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

// SyncDepartment 同步部门到微信
func (a *endpointUser) SyncDepartment(c *gin.Context) {
	type Request struct {
		ID    uint `json:"id" form:"id" binding:"required"`
		Level int  `json:"level" form:"level" binding:"required"`
	}
	var req Request
	// 解析 JSON 数据
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	id := req.ID
	level := req.Level
	if level < 1 || level > 3 {
		handler.Error(c, errors.NewErr("level参数错误，只能是1、2、3"))
		return
	}
	err := a.adminUserSvc.SyncDepartment(c, id, level)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}
