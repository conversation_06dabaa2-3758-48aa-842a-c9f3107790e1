package base

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/handler"
	"marketing/internal/service/base"
)

// OssHandler OSS处理程序接口
type OssHandler interface {
	// GetStsToken 获取OSS上传临时凭证
	GetStsToken(c *gin.Context)
}

type ossHandlerImpl struct {
	ossService base.OssService
}

// NewOssHandler 创建OSS处理程序实例
func NewOssHandler(ossService base.OssService) OssHandler {
	return &ossHandlerImpl{
		ossService: ossService,
	}
}

// GetStsToken 获取OSS上传临时凭证
func (h *ossHandlerImpl) GetStsToken(c *gin.Context) {
	// 调用服务获取STS Token
	resp, err := h.ossService.GetStsToken(c)
	if err != nil {
		handler.Error(c, err)
		return
	}

	// 返回成功响应
	handler.Success(c, resp)
}
