package utils

import (
	"strconv"
	"strings"
)

// RemoveDuplicates 去重函数（规范命名）
func RemoveDuplicates[T comparable](slice []T) []T {
	seen := make(map[T]bool)
	result := []T{}
	for _, v := range slice {
		if !seen[v] {
			seen[v] = true
			result = append(result, v)
		}
	}
	return result
}

// FilterAndDeduplicate 去重 + 去空函数（规范命名）
func FilterAndDeduplicate[T comparable](slice []T, isEmpty func(T) bool) []T {
	seen := make(map[T]bool)
	result := []T{}
	for _, v := range slice {
		if isEmpty(v) {
			continue
		}
		if !seen[v] {
			seen[v] = true
			result = append(result, v)
		}
	}
	return result
}

// IntSliceToString 将整数切片转换为逗号分隔的字符串
func IntSliceToString(intSlice []int) string {
	strGroups := make([]string, len(intSlice))
	for i, num := range intSlice {
		strGroups[i] = strconv.Itoa(num)
	}
	return strings.Join(strGroups, ",")
}
