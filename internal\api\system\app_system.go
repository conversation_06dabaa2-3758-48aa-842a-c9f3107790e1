package system

import "marketing/internal/api"

type AppSystemReq struct {
	api.PaginationParams
	AppKey    string   `json:"app_key"`
	CorpID    string   `json:"corp_id"`
	Name      string   `json:"name"`
	Type      string   `json:"type"`
	UserGroup uint     `json:"user_group"`
	IsChild   *int     `json:"is_child"`
	Parents   []string `json:"parents"`
	Parent    string   `json:"parent"`
}

type AddAppSystemReq struct {
	ID         uint   `json:"id"`
	AppKey     string `json:"app_key"`
	Parent     string `json:"parent"`
	CorpID     string `json:"corp_id"`
	AgentID    int    `json:"agent_id"`
	CorpSecret string `json:"corp_secret"`
	Name       string `json:"name"`
	Desc       string `json:"desc"`
	Type       string `json:"type"`
	Index      string `json:"index"`
	Icon       string `json:"icon"`
	Visibility uint   `json:"visibility"`
	UserGroup  []int  `json:"user_group"`
	Rank       int    `json:"rank"`
}

type AppSystemResp struct {
	ID            int              `json:"id"`
	AppKey        string           `json:"app_key"`
	JwtKey        string           `json:"jwt_key"`
	Parent        string           `json:"parent"`
	CorpID        string           `json:"corp_id"`
	AgentID       int              `json:"agent_id"`
	CorpSecret    string           `json:"corp_secret"`
	Name          string           `json:"name"`
	Desc          string           `json:"desc"`
	Type          string           `json:"type"`
	Index         string           `json:"index"`
	Icon          string           `json:"icon"`
	Visibility    uint             `json:"visibility"`
	UserGroup     []int            `json:"user_group,omitempty"`
	UserGroupName []string         `json:"user_group_name,omitempty"`
	Children      []*AppSystemResp `json:"children,omitempty"` // 添加子节点数组
	Rank          int              `json:"rank"`
	CreatedAt     string           `json:"created_at"`
	UpdatedAt     string           `json:"updated_at"`
}

type AppSystemMenuResp struct {
	AppKey   string               `json:"app_key"`
	Name     string               `json:"name"`
	Type     string               `json:"type"`
	Index    string               `json:"index"`
	Icon     string               `json:"icon"`
	Rank     int                  `json:"rank"`
	Children []*AppSystemMenuResp `json:"children,omitempty"` // 添加子节点数组
}

type AppSystemDropDownResp struct {
	ID       int                      `json:"id"`
	AppKey   string                   `json:"app_key"`
	Name     string                   `json:"name"`
	Parent   string                   `json:"parent,omitempty"`
	Children []*AppSystemDropDownResp `json:"children,omitempty"`
}
