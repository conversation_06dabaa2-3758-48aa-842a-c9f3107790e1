package contact

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/dao"
	"marketing/internal/model"
)

type InterfaceContact interface {
	LoadContactByWarranty(c *gin.Context, w *model.Warranty) bool // 从保卡数据中导入联系人
}

type contact struct {
	repo dao.InterfaceContact
}

func NewContact(repo dao.InterfaceContact) InterfaceContact {
	return &contact{
		repo: repo,
	}
}

// LoadContactByWarranty 从保卡数据中导入联系人
func (ct *contact) LoadContactByWarranty(c *gin.Context, w *model.Warranty) bool {
	load := ct.repo.Load(c, w)
	return load
}
