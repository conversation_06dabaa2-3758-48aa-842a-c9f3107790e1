package prototype

import (
	"marketing/internal/api"
)

type PrototypeReq struct {
	api.PaginationParams
	Barcode        string `json:"barcode" form:"barcode"`
	Model          string `json:"model" form:"model"`
	Type           *int   `json:"type" form:"type"`
	ShowType       []int  `json:"show_type" form:"show_type"`
	Status         *int   `json:"status" form:"status"` //1-在库 2-离库
	TopAgency      uint   `json:"top_agency" form:"top_agency"`
	SecondAgency   uint   `json:"second_agency" form:"second_agency"`
	Endpoint       uint   `json:"endpoint" form:"endpoint"`
	CreatedAtStart string `json:"created_at_start" form:"created_at_start"`
	CreatedAtEnd   string `json:"created_at_end" form:"created_at_end"`
	ModelID        []uint `json:"model_id" form:"model_id"`
}

type PrototypeOutReq struct {
	Barcode string `json:"barcode" form:"barcode" binding:"required"`
}

type PrototypeConfigListSearch struct {
	api.PaginationParams
	Discontinued *int   `json:"discontinued"`
	Model        string `p:"model"`
}

type PrototypeConfigAddReq struct {
	ModelID int `json:"model_id" binding:"required"`
}
