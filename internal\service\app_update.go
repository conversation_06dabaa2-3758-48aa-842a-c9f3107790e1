package service

import (
	"marketing/internal/api/notice"
	"marketing/internal/dao"
	"marketing/internal/pkg/errors"

	"github.com/gin-gonic/gin"
)

type AppUpdateSvc interface {
	// GetAppOptions 获取App选项列表
	GetAppOptions(c *gin.Context) ([]*notice.AppOptionsItemRes, error)
	// GetAppUpdateHistories 获取App更新历史列表
	GetAppUpdateHistories(c *gin.Context, req *notice.AppUpdateHistoriesReq) ([]*notice.AppUpdateHistoryItem, error)
}

type appUpdateSvc struct {
	dao dao.AppUpdateHistoryDao
}

func NewAppUpdateSvc(dao dao.AppUpdateHistoryDao) AppUpdateSvc {
	return &appUpdateSvc{
		dao: dao,
	}
}

// GetAppOptions 获取App选项列表
func (s *appUpdateSvc) GetAppOptions(c *gin.Context) ([]*notice.AppOptionsItemRes, error) {
	options, err := s.dao.GetAppOptions(c)
	if err != nil {
		return nil, errors.NewErr("获取App选项失败: " + err.Error())
	}
	return options, nil
}

// GetAppUpdateHistories 获取App更新历史列表
func (s *appUpdateSvc) GetAppUpdateHistories(c *gin.Context, req *notice.AppUpdateHistoriesReq) ([]*notice.AppUpdateHistoryItem, error) {
	histories, err := s.dao.GetAppUpdateHistories(c, req)
	if err != nil {
		return nil, errors.NewErr("获取App更新历史失败: " + err.Error())
	}
	return histories, nil
}
