package model

import "time"

type MesDevices struct {
	ID           int       `gorm:"column:id;primary_key" json:"id"`
	ModelID      int       `gorm:"column:model_id" json:"model_id"`
	Model        string    `gorm:"column:model" json:"model"`
	Barcode      string    `gorm:"barcode" json:"barcode"`
	Number       string    `gorm:"number" json:"number"`
	Imei1        string    `gorm:"imei_1" json:"imei1"`
	Imei2        string    `gorm:"imei_2" json:"imei2"`
	Meid         string    `gorm:"meid" json:"meid"`
	NetID        int       `gorm:"net_id" json:"netID"`
	WifiMac      string    `gorm:"wifi_mac" json:"wifiMac"`
	BluetoothMac string    `gorm:"bluetooth_mac" json:"bluetoothMac"`
	Chipset      string    `gorm:"chipset" json:"chipset"`
	Color        string    `gorm:"color" json:"color"`
	State        int       `gorm:"state" json:"state"`
	Procedure    int       `gorm:"procedure" json:"procedure"`
	IsRepeat     int       `gorm:"is_repeat" json:"isRepeat"`
	UserID       int       `gorm:"user_id" json:"userID"`
	TaskID       int       `gorm:"task_id" json:"taskID"`
	PackTaskID   int       `gorm:"pack_task_id" json:"packTaskID"`
	AddTime      int       `gorm:"add_time" json:"addTime "`
	UpdateTime   time.Time `gorm:"update_time" json:"updateTime"`
	Key          string    `gorm:"key" json:"key"`
}

func (MesDevices) TableName() string { return "mes_devices" }
