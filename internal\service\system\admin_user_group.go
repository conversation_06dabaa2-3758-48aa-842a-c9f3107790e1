package system

import (
	"errors"
	api "marketing/internal/api/system"
	dao "marketing/internal/dao/admin_user"
	"marketing/internal/model"
	appError "marketing/internal/pkg/errors"
	"marketing/internal/pkg/utils"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type AdminUserGroupInterface interface {
	Add(c *gin.Context, req api.AddUserGroupReq) error
	List(c *gin.Context, param api.AdminUserGroupReq) ([]api.AdminUserGroupResp, error)
	Update(c *gin.Context, req api.AddUserGroupReq) error
	Delete(c *gin.Context, id uint) error
	AddUserToGroup(c *gin.Context, req api.AddUserToGroupReq) error
	AddUsersToGroup(c *gin.Context, req api.AddUsersToGroupReq) error
	ListGroupsNoPage(c *gin.Context, groupName string) ([]model.AdminUserGroupV2, error)
}

type adminUserGroupSvc struct {
	db           *gorm.DB
	adminUserLog dao.AdminUserLog
}

func NewAdminUserGroupSvc(db *gorm.DB, adminUserLog dao.AdminUserLog) AdminUserGroupInterface {
	return &adminUserGroupSvc{
		db:           db,
		adminUserLog: adminUserLog,
	}
}

func (svc *adminUserGroupSvc) Add(c *gin.Context, req api.AddUserGroupReq) error {
	var existGroup model.AdminUserGroupV2

	err := svc.db.WithContext(c).Where("slug = ?", req.Slug).First(&existGroup).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}
	if existGroup.ID > 0 {
		return appError.NewErr("User group with name \"" + req.Name + "\" already exists")
	}

	existGroup.Name = req.Name
	existGroup.Slug = req.Slug
	existGroup.ResourceId = req.ResourceID
	existGroup.CreatedAt = time.Now()
	existGroup.UpdatedAt = time.Now()

	return svc.db.WithContext(c).Create(&existGroup).Error
}

func (svc *adminUserGroupSvc) List(c *gin.Context, param api.AdminUserGroupReq) ([]api.AdminUserGroupResp, error) {
	var list []api.AdminUserGroupResp

	query := svc.db.WithContext(c).Model(&model.AdminUserGroupV2{}).
		Joins("LEFT JOIN resource_groups ON admin_user_group_v2.resource_id = resource_groups.id").
		Select("admin_user_group_v2.*, resource_groups.name as resource_name")
	if param.Name != "" {
		query = query.Where("name LIKE ?", "%"+param.Name+"%")
	}
	if param.Slug != "" {
		query = query.Where("slug = ?", param.Slug)
	}
	if param.ResourceID != 0 {
		query = query.Where("resource_id =?", param.ResourceID)
	}

	err := query.Scan(&list).Error

	return list, err
}

func (svc *adminUserGroupSvc) Update(c *gin.Context, req api.AddUserGroupReq) error {
	var existGroup model.AdminUserGroupV2

	err := svc.db.WithContext(c).Where("id = ?", req.ID).First(&existGroup).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return appError.NewErr("User group not found")
	} else if err != nil {
		return err
	}

	existGroup.Name = req.Name
	existGroup.ResourceId = req.ResourceID
	existGroup.UpdatedAt = time.Now()

	return svc.db.WithContext(c).Save(existGroup).Error
}

func (svc *adminUserGroupSvc) Delete(c *gin.Context, id uint) error {
	var existGroup model.AdminUserGroupV2

	err := svc.db.WithContext(c).Where("id = ?", id).First(&existGroup).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return appError.NewErr("User group not found")
	}
	if err != nil {
		return err
	}
	return svc.db.WithContext(c).Delete(&existGroup).Error
}

func (svc *adminUserGroupSvc) AddUserToGroup(c *gin.Context, req api.AddUserToGroupReq) error {
	// Insert the association into the join table
	var data model.AdminUserUserGroupV2
	data.UserID = req.UserID
	data.UserGroupID = req.UserGroupID
	return svc.db.WithContext(c).Create(&data).Error
}

func (svc *adminUserGroupSvc) AddUsersToGroup(c *gin.Context, req api.AddUsersToGroupReq) error {
	//检查用户组是否存在
	var existGroup model.AdminUserGroupV2
	err := svc.db.WithContext(c).Where("id =?", req.UserGroupID).First(&existGroup).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return appError.NewErr("User group not found")
	}
	// 用于存储所有需要插入的数据
	var dataList []model.AdminUserUserGroupV2
	// 遍历用户 ID 列表
	for _, userID := range req.UserIDs {
		var data model.AdminUserUserGroupV2
		data.UserID = userID
		data.UserGroupID = req.UserGroupID
		// 将数据添加到切片中
		dataList = append(dataList, data)
	}
	// 批量插入数据
	err = svc.db.WithContext(c).Create(&dataList).Error
	if err != nil {
		return err
	}
	go func() {
		var logs []model.AdminUserLog
		for _, userID := range req.UserIDs {
			var dataLog api.UserLog
			dataLog.ID = userID
			dataLog.GroupName = &existGroup.Name
			dataLog.UserGroup = &struct {
				ID   uint   `json:"id"`
				Name string `json:"name"`
			}{ID: existGroup.ID, Name: existGroup.Name}
			dataLogStr := utils.JSONMaskFields(
				utils.ToJSON(dataLog),
				[]string{},
			)
			logs = append(logs, model.AdminUserLog{
				UID:      int(userID),
				Username: "",
				OpType:   "edit",
				Phone:    "",
				Remark:   "批量添加用户到用户组",
				After:    dataLogStr,
			})
		}
		svc.adminUserLog.AddLogs(c, logs)
	}()
	return nil
}

// ListGroupsNoPage 获取所有用户组
func (svc *adminUserGroupSvc) ListGroupsNoPage(c *gin.Context, groupName string) ([]model.AdminUserGroupV2, error) {
	var groups []model.AdminUserGroupV2
	query := svc.db.WithContext(c)
	if groupName != "" {
		query = query.Where("name LIKE ?", "%"+groupName+"%")
	}
	err := query.Find(&groups).Error
	return groups, err
}
