package model

import (
	"time"
)

type Agency struct {
	ID                uint       `gorm:"primary_key;column:id" json:"id"`
	Name              string     `gorm:"type:varchar(128);not null;comment:'代理名称'" json:"name"`
	PID               int        `gorm:"column:pid;not null;default:0;comment:'父id'" json:"pid"`
	Level             int        `gorm:"not null;comment:'代理等级,目前只有一级二级'" json:"level"`
	Partition         int        `gorm:"not null;default:0;comment:'总代所属大区id，二级代理没有大区'" json:"partition"`
	Department        uint8      `gorm:"not null;default:0;comment:'部门归属，1渠道部华南大区，2渠道部华东大区，3渠道部华北大区，4渠道部西南大区，0未知'" json:"department"`
	Order             int        `gorm:"not null;default:0;comment:'排序'" json:"order"`
	LetterName        *string    `gorm:"type:varchar(50);comment:'代理的缩写,是代理名称的文字拼音首字母'" json:"letter_name"`
	MarketCoefficient *string    `gorm:"type:varchar(50);default:'0';comment:'市场系数'" json:"market_coefficient"`
	PupilsNum         uint       `gorm:"default:0;comment:'小学生数量'" json:"pupils_num"`
	JuniorsNum        uint       `gorm:"default:0;comment:'初中生数量'" json:"juniors_num"`
	StudentsNum       uint       `gorm:"default:0;comment:'在校学生数'" json:"students_num"`
	StudentsPercent   *string    `gorm:"type:varchar(20);comment:'全国在校学生数占比'" json:"students_percent"`
	MarketType        *string    `gorm:"type:enum('', 'A', 'B', 'C', 'D');default:'';comment:'A类，在校学生数占比>=2%, B类 >=0.5%且<2%，C类<0.5'" json:"market_type"`
	IsFlat            uint8      `gorm:"not null;default:0;comment:'是否扁平化'" json:"is_flat"`
	FlatCity          uint       `gorm:"not null;default:0;comment:'扁平化的代理所属城市'" json:"flat_city"`
	FlatDate          *string    `gorm:"type:char(7);comment:'扁平化时间，精确到月份，格式2020-01'" json:"flat_date"`
	Channel           *string    `gorm:"type:enum('agency','e_commerce','operator','special','ebag','other','mengkubao','external','aixue');default:'agency';comment:'渠道类型，agency-代理，e_commerce-电商，operator-运营商，special-特约客户，ebag-智慧课堂，other-其它，mengkubao-萌酷宝,aixue-爱学空间AI智习室'" json:"channel"`
	Compound          uint8      `gorm:"not null;default:0;comment:'是否为复合代理，即售卖多个品牌'" json:"compound"`
	IsDirectSales     *int64     `gorm:"default:0;comment:'是否直营 0非直营  1直营'" json:"is_direct_sales"`
	CreatedAt         time.Time  `gorm:"not null;default:CURRENT_TIMESTAMP;comment:'创建时间'" json:"created_at"`
	UpdatedAt         time.Time  `gorm:"not null;default:'0000-00-00 00:00:00';comment:'更新时间'" json:"updated_at"`
	DeletedAt         *time.Time `gorm:"comment:'删除时间'" json:"deleted_at"`
	QwPartyID         uint       `gorm:"column:qw_partyid;not null;default:0;comment:'企业微信部门id'" json:"qw_partyid"`
	GDP               *float64   `gorm:"type:decimal(10,2);comment:'上一年当地的gdp'" json:"gdp"`
}

func (Agency) TableName() string {
	return "agency"
}

type AgencyKingDee struct {
	AgencyId  int `json:"agency_id" gorm:"column:agency_id"`   // 代理id
	KingDeeId int `json:"kingdee_id" gorm:"column:kingdee_id"` // 金蝶id
}

func (AgencyKingDee) TableName() string {
	return "agency_kingdee"
}

type AgencyKingDeeInfo struct {
	AgencyId  int    `json:"agency_id" gorm:"column:agency_id"`   // 代理id
	KingDeeId int    `json:"kingdee_id" gorm:"column:kingdee_id"` // 金蝶id
	Name      string `json:"name" gorm:"column:name"`             // 金蝶名称
	ShortName string `json:"short_name" gorm:"column:short_name"` // 金蝶简称
}

type AgencyRegion struct {
	AgencyId int `json:"agency_id" gorm:"column:agency_id"` // 代理id
	RegionId int `json:"region_id" gorm:"column:region_id"` // 区域id
}

func (AgencyRegion) TableName() string {
	return "agency_region"
}

// AgencyCompanyInfo 代理公司信息
type AgencyCompanyInfo struct {
	CompanyID   *int    `json:"company_id"`
	CompanyName *string `json:"company_name"`
	AgencyID    int     `json:"agency_id"`
	AgencyName  string  `json:"agency_name"`
}

type AgencyRegionHistory struct {
	Id         int    `json:"id" gorm:"column:id"`                   // id
	AgencyId   int    `json:"agency_id" gorm:"column:agency_id"`     // 代理id
	RegionId   int    `json:"region_id" gorm:"column:region_id"`     // 区域id
	RegionName string `json:"region_name" gorm:"column:region_name"` // 区域名称
}

func (AgencyRegionHistory) TableName() string {
	return "agency_region_history"
}
