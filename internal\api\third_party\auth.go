package third_party

// ThirdPartyAuthReq 第三方鉴权请求
type ThirdPartyAuthReq struct {
	AppID           string `json:"appid" form:"appid" binding:"required"`                     // 应用ID
	Flag            string `json:"flag" form:"flag"`                                          // 标识参数
	TimestampKey    int64  `json:"timestampKey" form:"timestampKey"`                          // 时间戳
	EncryptionToken string `json:"encryptionToken" form:"encryptionToken" binding:"required"` // 加密token
}

// ThirdPartyAuthResp 第三方鉴权响应
type ThirdPartyAuthResp struct {
	Token string `json:"token"` // 返回的token
}

// ThirdPartyTokenValidationReq 第三方token验证请求
type ThirdPartyTokenValidationReq struct {
	Token string `json:"token" form:"token" binding:"required"` // 需要验证的token
}
