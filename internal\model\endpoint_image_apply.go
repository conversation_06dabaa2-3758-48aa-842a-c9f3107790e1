package model

import (
	"marketing/internal/pkg/types"
)

// EndpointImageApply 对应表 endpoint_image_apply
type EndpointImageApply struct {
	ID                   uint                  `json:"id" gorm:"primaryKey;autoIncrement"`
	EndpointID           uint                  `json:"endpoint_id" gorm:"not null"`
	Year                 uint16                `json:"year" gorm:"not null"`
	Month                uint8                 `json:"month" gorm:"not null"`
	Latitude             float64               `json:"latitude" gorm:"type:decimal(9,6);not null"`
	Longitude            float64               `json:"longitude" gorm:"type:decimal(9,6);not null"`
	Location             string                `json:"location" gorm:"type:varchar(200);not null"`
	Storefront           types.JSONStringArray `json:"storefront" gorm:"type:text;not null"`
	ProductImage         types.JSONStringArray `json:"product_image" gorm:"type:text;not null"`
	ProductExperience    types.JSONStringArray `json:"product_experience" gorm:"type:text;not null"`
	CultureWall          types.JSONStringArray `json:"culture_wall" gorm:"type:text;not null"`
	Cashier              types.JSONStringArray `json:"cashier" gorm:"type:text;not null"`
	RestArea             types.JSONStringArray `json:"rest_area" gorm:"type:text;not null"`
	SpareParts           types.JSONStringArray `json:"spare_parts" gorm:"type:text;not null"`
	BooksBorrow          types.JSONStringArray `json:"books_borrow" gorm:"type:text;not null"`
	TrainingRoom         types.JSONStringArray `json:"training_room" gorm:"type:text;not null"`
	Surroundings         types.JSONStringArray `json:"surroundings" gorm:"type:text;not null"`
	Other                types.JSONStringArray `json:"other" gorm:"type:text;not null"`
	Status               string                `json:"status" gorm:"type:enum('to_audit','approved','rejected');not null"`
	AuditUserID          uint                  `json:"audit_user_id" gorm:"not null"`
	AuditTime            types.CustomTime      `json:"audit_time,omitempty" gorm:"type:datetime;not null;default:0000-00-00 00:00:00"`
	RejectedReason       string                `json:"rejected_reason" gorm:"type:varchar(20);not null;default:''"`
	ManualStatus         string                `json:"manual_status" gorm:"type:enum('to_audit','approved','rejected');not null;default:'to_audit'"`
	ManualRejectedReason string                `json:"manual_rejected_reason" gorm:"type:varchar(100);not null;default:''"`
	ManualAuditTime      types.CustomTime      `json:"manual_audit_time,omitempty" gorm:"type:datetime"`
	OldLongitude         *float64              `json:"old_longitude" gorm:"type:decimal(9,6)"`
	OldLatitude          *float64              `json:"old_latitude" gorm:"type:decimal(9,6)"`
	CreatedAt            types.CustomTime      `json:"created_at,omitempty" gorm:"autoCreateTime"`
	UpdatedAt            types.CustomTime      `json:"updated_at,omitempty" gorm:"autoUpdateTime"`
}
