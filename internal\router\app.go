// Package router 该包主要负责定义和管理与路由相关的逻辑，
// 本文件 app.go 具体用于配置和注册营销中台应用（app）相关的路由组。
// 它通过路由构建器和中间件包装器，灵活地为不同路由配置不同的中间件。
package router

import (
	"marketing/internal/config"
	"marketing/internal/middleware"
	"marketing/internal/router/app"
	"marketing/internal/service/auth"
	"marketing/internal/service/system"

	"github.com/gin-gonic/gin"
)

// NewAppRouterGroup 创建应用端路由组
func NewAppRouterGroup(authService auth.ServiceInterface, config *config.Config, mkbRouter *app.MkbRouter, systemAppSvc system.AppSystemSvc) IRouterGroup {
	//定义应用中间件
	var authMiddleware []gin.HandlerFunc

	// 在调试模式下可以选择跳过认证
	if config.Server.Mode != "debug" {
		authMiddleware = []gin.HandlerFunc{
			middleware.AuthToken(authService),
		}
	}

	// 使用路由构建器
	builder := NewRouterBuilder("app")

	// 添加公共模块的路由
	builder.AddRouterWithMiddleware(app.NewBaseRouter(config, systemAppSvc), authMiddleware...)

	// 添加应用端的路由
	builder.AddRouterWithMiddleware(app.NewRenewRouter(), buildAppMiddleware(authMiddleware, authService, "renew-check")...)
	builder.AddRouterWithMiddleware(mkbRouter, buildAppMiddleware(authMiddleware, authService, "mkb")...)
	builder.AddRouterWithMiddleware(app.NewRenewAppRouter(), buildAppMiddleware(authMiddleware, authService, "renew-endpoint")...)

	// 构建并返回路由组
	return builder.Build()
}

func buildAppMiddleware(authMiddleware []gin.HandlerFunc, authService auth.ServiceInterface, systemType string) []gin.HandlerFunc {
	var appMiddleware []gin.HandlerFunc
	if gin.Mode() != "debug" {
		appMiddleware = append(authMiddleware, middleware.CheckTokenSystem(authService, systemType))
	}
	return appMiddleware
}
