package admin

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/dao"
	"marketing/internal/dao/admin_user"
	afterSalesController "marketing/internal/handler/admin/aftersales"
	"marketing/internal/handler/admin/agency"
	"marketing/internal/handler/admin/endpoint"
	"marketing/internal/handler/admin/machine"
	"marketing/internal/handler/admin/material"
	"marketing/internal/service"
	"marketing/internal/service/aftersales"
	machineSvc "marketing/internal/service/machine"
	materialSvc "marketing/internal/service/material"
)

type AfterSalesRouter struct {
	user admin_user.UserDao
}

func NewAfterSalesRouter(user admin_user.UserDao) *AfterSalesRouter {
	return &AfterSalesRouter{
		user: user,
	}
}

func (a *AfterSalesRouter) Register(r *gin.RouterGroup) {
	agencyDao := dao.NewAgencyDao()
	afterSalesEndpointDao := dao.NewAfterSalesEndpointDao()

	afterSalesEndpointRouter := r.Group("/after_sales_endpoint")
	{
		afterSalesEndpointService := service.NewAfterSalesEndpointService(agencyDao, afterSalesEndpointDao)
		afterSalesEndpointController := endpoint.NewAfterSalesEndpoint(afterSalesEndpointService)
		afterSalesEndpointRouter.GET("/list", afterSalesEndpointController.GetAfterSalesEndpointList)
		afterSalesEndpointRouter.GET("/agency_endpoint/list", afterSalesEndpointController.GetAfterSalesEndpointByAgency)
		afterSalesEndpointRouter.GET("/get", afterSalesEndpointController.GetAfterSalesEndpoint)
		afterSalesEndpointRouter.POST("/edit", afterSalesEndpointController.EditAfterSalesEndpoint)
		afterSalesEndpointRouter.DELETE("/delete", afterSalesEndpointController.DeleteAfterSalesEndpoint)
	}

	userAfterSalesRouter := r.Group("/user_after_sales")
	{
		userAfterSalesDao := dao.NewUserAfterSalesDao()
		userAfterSalesService := service.NewUserAfterSalesService(agencyDao, afterSalesEndpointDao, userAfterSalesDao)
		userAfterSalesController := endpoint.NewUserAfterSales(userAfterSalesService)
		userAfterSalesRouter.GET("/list", userAfterSalesController.GetUserAfterSalesList)
		userAfterSalesRouter.GET("/get", userAfterSalesController.GetUserAfterSales)
		userAfterSalesRouter.POST("/edit", userAfterSalesController.EditUserAfterSales)
		userAfterSalesRouter.DELETE("/delete", userAfterSalesController.DeleteUserAfterSales)
		userAfterSalesRouter.GET("/export", userAfterSalesController.ExportUserAfterSalesList)
		userAfterSalesRouter.GET("/encrypt", userAfterSalesController.DataEncrypt)
	}

	machineMalfunctionDao := dao.NewMachineMalfunctionDao()
	machineMalfunctionRouter := r.Group("machine_malfunction")
	{
		machineMalfunctionService := aftersales.NewMachineMalfunctionService(machineMalfunctionDao)
		machineMalfunctionController := afterSalesController.NewMachineMalfunction(machineMalfunctionService)
		machineMalfunctionRouter.GET("/list", machineMalfunctionController.MachineMalfunctionList)
		machineMalfunctionRouter.GET("/refresh", machineMalfunctionController.RefreshMachineMalfunction)
		machineMalfunctionRouter.POST("/edit", machineMalfunctionController.EditMachineMalfunction)
		machineMalfunctionRouter.DELETE("/delete", machineMalfunctionController.DeleteMachineMalfunction)
	}

	repairBillRouter := r.Group("repair_bill")
	{
		repairBillDao := dao.NewRepairBillDao()
		repairBillService := service.NewRepairBillService(a.user, afterSalesEndpointDao, machineMalfunctionDao, dao.NewMachineAccessoryRelationDao(), dao.NewMachineTypeDao(), repairBillDao)
		repairBillController := agency.NewRepairBill(repairBillService)
		repairBillRouter.GET("/list", repairBillController.GetRepairBillList)
		repairBillRouter.GET("/get", repairBillController.GetRepairBill)
		repairBillRouter.POST("/edit", repairBillController.EditRepairBill)
		repairBillRouter.DELETE("/delete", repairBillController.DeleteRepairBill)
		repairBillRouter.GET("/export", repairBillController.ExportRepairBillList)
	}

	// 售后管理-机型列表
	repairMachineTypeRouter := r.Group("repair_machine_type")
	{
		machineTypeDao := dao.NewMachineTypeDao()
		machineTypeService := machineSvc.NewMaterialService(machineTypeDao, nil, nil)
		machineTypeController := machine.NewMachineType(machineTypeService)
		repairMachineTypeRouter.GET("/list", machineTypeController.GetRepairMachineTypeList)
		repairMachineTypeRouter.GET("/export", machineTypeController.ExportRepairMachineTypeList)
	}

	// 售后管理-机型分类
	repairMachineCategoryRouter := r.Group("repair_machine_category")
	{
		modelCategoryDao := dao.NewModelCategoryDao()
		modelCategoryService := machineSvc.NewModelCategoryService(modelCategoryDao)
		modelCategoryController := machine.NewModelCategory(modelCategoryService)
		repairMachineCategoryRouter.GET("/list", modelCategoryController.GetAllRepairModelCategory)
	}

	// 售后管理-物料
	repairMaterialRouter := r.Group("repair_material")
	{
		materialDao := dao.NewMaterialDao()
		materialService := materialSvc.NewMaterialService(materialDao, nil)
		materialController := material.NewMaterialController(materialService)
		repairMaterialRouter.GET("/list", materialController.GetRepairMaterialList)
		repairMaterialRouter.GET("/export", materialController.ExportRepairMaterialList)
	}
}
