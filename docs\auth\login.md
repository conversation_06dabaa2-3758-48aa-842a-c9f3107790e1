# 认证接口文档

[TOC]

## 1. 账号密码登录

##### 简要描述
- 用户登录接口，返回 JWT token

##### 请求URL
- `/admin/auth/login`

##### 请求方式
- POST

##### 参数
```json
{
    "username": "admin",
    "password": "123456"
}
```

| 参数名   | 必选 | 类型   | 说明   |
|----------|------|--------|--------|
| username | 是   | string | 用户名 |
| password | 是   | string | 密码   |

##### 返回示例
```json
{
  "ok": 1,
  "msg": "ok",
  "data": {
    "id": 19443,
    "username": "<PERSON>D<PERSON>",
    "name": "<PERSON>",
    "token": "f0b12e14e8d3629dcb16ae102fae3d71634e04ac2f6ac14d348bd8828969aea4",
    "roles": [
      "dev"
    ],
    "permissions": [
      "system-user",
      "system-role"
    ],
    "menus": [
      {
        "path": "/",
        "component": "Layout",
        "meta": {
          "icon": "",
          "title": "首页",
          "rank": 1
        }
      },
      {
        "path": "/system",
        "component": "HomeComponent",
        "meta": {
          "icon": "",
          "title": "系统管理",
          "rank": 1
        },
        "children": [
          {
            "path": "/user",
            "component": "HomeComponent",
            "meta": {
              "icon": "",
              "title": "用户管理",
              "rank": 1
            }
          },
          {
            "path": "/role",
            "component": "HomeComponent",
            "meta": {
              "icon": "",
              "title": "角色管理",
              "rank": 1
            }
          }
        ]
      }
    ]
  }
}
```

##### 返回参数说明
| 参数名 | 类型   | 说明                     |
|--------|--------|------------------------|
| token  | string | JWT token              |
| user   | object | 用户信息                |

## 2. 企业微信登录

##### 简要描述
- 企业微信登录接口，返回 JWT token

##### 请求URL
- `/admin/auth/wecom-login`

##### 请求方式
- POST

##### 参数
```json
{
    "code": "wx_code",
    "state": "state_string"
}
```

| 参数名 | 必选 | 类型   | 说明                |
|--------|------|--------|-------------------|
| code   | 是   | string | 企业微信授权码      |
| state  | 是   | string | 状态码，防止CSRF攻击 |

##### 返回示例
```json
{
  "ok": 1,
  "msg": "ok",
  "data": {
    "id": 19443,
    "username": "AmberDev",
    "name": "Amber",
    "token": "f0b12e14e8d3629dcb16ae102fae3d71634e04ac2f6ac14d348bd8828969aea4",
    "roles": [
      "dev"
    ],
    "permissions": [
      "system-user",
      "system-role"
    ],
    "menus": [
      {
        "path": "/",
        "component": "Layout",
        "meta": {
          "icon": "",
          "title": "首页",
          "rank": 1
        }
      },
      {
        "path": "/system",
        "component": "HomeComponent",
        "meta": {
          "icon": "",
          "title": "系统管理",
          "rank": 1
        },
        "children": [
          {
            "path": "/user",
            "component": "HomeComponent",
            "meta": {
              "icon": "",
              "title": "用户管理",
              "rank": 1
            }
          },
          {
            "path": "/role",
            "component": "HomeComponent",
            "meta": {
              "icon": "",
              "title": "角色管理",
              "rank": 1
            }
          }
        ]
      }
    ]
  }
}
```

##### 返回参数说明
| 参数名 | 类型   | 说明                     |
|--------|--------|------------------------|
| token  | string | JWT token              |
| user   | object | 用户信息                |

##### 备注
- 接口返回的 code 为 0 表示成功，非 0 表示失败
- 微信登录需要先在微信开放平台配置回调域名