package endpoint

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"marketing/internal/handler"
	"marketing/internal/model"
	"marketing/internal/pkg/e"
	"marketing/internal/pkg/errors"
	"marketing/internal/pkg/utils"
	"marketing/internal/service"
)

type UserAfterSales struct {
	svc service.UserAfterSalesSvc
}

func NewUserAfterSales(svc service.UserAfterSalesSvc) *UserAfterSales {
	return &UserAfterSales{
		svc: svc,
	}
}

func (user *UserAfterSales) GetUserAfterSalesList(c *gin.Context) {
	name := e.ReqParamStr(c, "name")
	pageNum := e.ReqParamInt(c, "page_num")
	pageSize := e.ReqParamInt(c, "page_size")

	list, total := user.svc.GetUserAfterSalesList(c, name, pageNum, pageSize)

	handler.Success(c, gin.H{
		"list":  list,
		"total": total,
	})
}

func (user *UserAfterSales) GetUserAfterSales(c *gin.Context) {
	info := user.svc.GetUserAfterSalesById(c, e.ReqParamInt(c, "id"))
	if info == nil {
		handler.Error(c, errors.NewErr("售后用户不存在"))
		return
	}

	handler.Success(c, info)
}

func (user *UserAfterSales) EditUserAfterSales(c *gin.Context) {
	t, err := utils.GetTimeDay(e.ReqParamStr(c, "entry_time"))
	if err != nil {
		handler.Error(c, errors.NewErr(err.Error()))
		return
	}

	err = user.svc.EditUserAfterSales(c, &model.UserAfterSalesDetails{
		UserAfterSales: model.UserAfterSales{
			Id:              e.ReqParamInt(c, "id"),
			UserId:          e.ReqParamInt(c, "user_id"),
			EndpointId:      e.ReqParamInt(c, "endpoint_id"),
			Status:          e.ReqParamInt(c, "status"),
			AgencyType:      e.ReqParamStr(c, "agency_type"),
			CompanyType:     e.ReqParamStr(c, "company_type"),
			Phone:           e.ReqParamStr(c, "phone"),
			IdentityCard:    e.ReqParamStr(c, "identity_card"),
			ThroughTraining: e.ReqParamInt(c, "through_training"),
			Education:       e.ReqParamStr(c, "education"),
			EntryTime:       t,
			Remark:          e.ReqParamStr(c, "remark"),
			Deliverable:     e.ReqParamInt(c, "deliverable"),
			Corporation:     e.ReqParamStr(c, "corporation"),
		},
		Name:     e.ReqParamStr(c, "name"),
		Password: e.ReqParamStr(c, "password"),
		Province: e.ReqParamInt(c, "province"),
		City:     e.ReqParamInt(c, "city"),
		District: e.ReqParamInt(c, "district"),
		Address:  e.ReqParamStr(c, "address"),
	})
	if err != nil {
		handler.Error(c, errors.NewErr(err.Error()))
		return
	}

	handler.Success(c, gin.H{})
}

func (user *UserAfterSales) DeleteUserAfterSales(c *gin.Context) {
	err := user.svc.DeleteUserAfterSales(c, e.ReqParamInt(c, "id"))
	if err != nil {
		handler.Error(c, errors.NewErr(err.Error()))
		return
	}

	handler.Success(c, gin.H{})
}

func (user *UserAfterSales) ExportUserAfterSalesList(c *gin.Context) {
	name := e.ReqParamStr(c, "name")
	pageNum := e.ReqParamInt(c, "page_num")
	pageSize := e.ReqParamInt(c, "page_size")

	file := user.svc.ExportUserAfterSalesList(c, name, pageNum, pageSize)
	if file != nil {
		c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
		c.Header("Content-Disposition", "attachment; filename="+fmt.Sprintf("售后用户表.xls"))
		if err := file.Write(c.Writer); err != nil {
			handler.Error(c, errors.NewErr(err.Error()))
			return
		}
	}

	handler.Success(c, gin.H{})
}

// DataEncrypt 对数据库敏感字段加密
func (user *UserAfterSales) DataEncrypt(c *gin.Context) {
	user.svc.DataEncrypt(c)
	handler.Success(c, gin.H{})
}
