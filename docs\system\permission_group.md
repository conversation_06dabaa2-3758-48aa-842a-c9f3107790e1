# 权限组管理 API 文档

[TOC]

## 1. 获取权限组列表

##### 简要描述
- 获取系统权限组列表，支持分页和条件查询

##### 请求URL
- `/admin/system/permission-groups`

##### 请求方式
- GET

##### 参数
| 参数名    | 必选 | 类型   | 说明                           |
|-----------|------|--------|--------------------------------|
| page      | 否   | int    | 页码，默认 1                   |
| page_size | 否   | int    | 每页数量，默认 20              |
| name      | 否   | string | 权限组名称，支持模糊搜索       |

##### 返回示例
```json
{
    "ok": 1,
    "msg": "ok",
    "data": {
        "total": 100,
        "page": 1,
        "page_size": 20,
        "data": [
            {
                "id": 1,
                "name": "系统管理",
                "slug": "system",
                "sort": 1,
                "created_at": "2023-01-01 00:00:00",
                "updated_at": "2023-01-01 00:00:00"
            }
        ]
    }
}
```

## 2. 新增权限组

##### 简要描述
- 新增系统权限组

##### 请求URL
- `/admin/system/permission-groups`

##### 请求方式
- POST

##### 参数
```json
{
    "name": "系统管理",
    "slug": "system",
    "sort": 1
}
```

| 参数名 | 必选 | 类型   | 说明                           |
|--------|------|--------|--------------------------------|
| name   | 是   | string | 权限组名称                     |
| slug   | 是   | string | 权限组标识                     |
| sort   | 否   | int    | 排序，越小越靠前               |

##### 返回示例
```json
{
    "ok": 1,
    "msg": "ok"
}
```

##### 备注
- slug 必须唯一
- slug 只能包含字母、数字和中划线

## 3. 更新权限组

##### 简要描述
- 更新权限组信息

##### 请求URL
- `/admin/system/permission-groups/:id`

##### 请求方式
- PUT

##### 参数
```json
{
    "name": "系统管理",
    "slug": "system",
    "sort": 1
}
```

| 参数名 | 必选 | 类型   | 说明                           |
|--------|------|--------|--------------------------------|
| name   | 是   | string | 权限组名称                     |
| slug   | 是   | string | 权限组标识                     |
| sort   | 否   | int    | 排序，越小越靠前               |

##### 返回示例
```json
{
    "ok": 1,
    "msg": "ok"
}
```

## 4. 删除权限组

##### 简要描述
- 删除系统权限组

##### 请求URL
- `/admin/system/permission-groups/:id`

##### 请求方式
- DELETE

##### 返回示例
```json
{
    "ok": 1,
    "msg": "ok"
}
```

##### 备注
- 如果权限组下有权限，则不能删除
