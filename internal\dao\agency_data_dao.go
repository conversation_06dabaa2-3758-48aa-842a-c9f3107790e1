package dao

import (
	"marketing/internal/model"

	"gorm.io/gorm"
)

type AdminUsersDao struct{}

// GetAllDepartments 获取所有部门
func (dao *AdminUsersDao) GetAllDepartments(db *gorm.DB) ([]struct {
	ID   int64
	Name string
}, error) {
	var departments []struct {
		ID   int64
		Name string
	}
	err := db.Table("department").Select("id, name").Scan(&departments).Error
	return departments, err
}

// GetAllPartitions 获取所有分区
func (dao *AdminUsersDao) GetAllPartitions(db *gorm.DB) ([]struct {
	ID   int64
	Name string
}, error) {
	var partitions []struct {
		ID   int64
		Name string
	}
	err := db.Table("partition").Select("id, name").Scan(&partitions).Error
	return partitions, err
}

// GetAgencyKingdees 获取所有代理与金蝶ID的关联
func (dao *AdminUsersDao) GetAgencyKingdees(db *gorm.DB) ([]struct {
	AgencyID  int
	KingdeeID int
}, error) {
	var agencyKingdees []struct {
		AgencyID  int
		KingdeeID int
	}
	err := db.Table("agency_kingdee").Select("agency_id, kingdee_id").Scan(&agencyKingdees).Error
	return agencyKingdees, err
}

// GetKingdeeAgencies 获取所有金蝶机构
func (dao *AdminUsersDao) GetKingdeeAgencies(db *gorm.DB) ([]struct {
	KingdeeID   int
	KingdeeName string
}, error) {
	var kingdeeAgencies []struct {
		KingdeeID   int
		KingdeeName string
	}
	err := db.Table("kingdee_agency").Select("id as kingdee_id, name as kingdee_name").Scan(&kingdeeAgencies).Error
	return kingdeeAgencies, err
}

// GetAgencies 获取所有代理机构信息
func (dao *AdminUsersDao) GetAgencies(db *gorm.DB) ([]model.AgencyData, error) {
	var agencies []model.AgencyData
	err := db.Where("level = ?", 1).Find(&agencies).Error
	return agencies, err
}
