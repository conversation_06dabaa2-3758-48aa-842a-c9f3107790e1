package model

import (
	"time"
)

func (AgencyData) TableName() string {
	return "agency"
}

// AgencyData 代理机构信息
type AgencyData struct {
	Id                int        `json:"id" gorm:"primaryKey;autoIncrement"`
	Name              string     `json:"name" gorm:"type:varchar(128);not null"`
	Pid               int        `json:"pid" gorm:"default:0"`
	Level             int        `json:"level" gorm:"column:level"`
	Partition         int        `json:"partition" gorm:"default:0"`
	Department        uint8      `json:"department" gorm:"department"`
	Order             int        `json:"order" gorm:"default:0"`
	LetterName        string     `json:"letter_name" gorm:"type:varchar(50);default:null"`
	MarketCoefficient string     `json:"market_coefficient" gorm:"type:varchar(50);default:0"`
	PupilsNum         uint       `json:"pupils_num" gorm:"pupils_num"`
	JuniorsNum        uint       `json:"juniors_num" gorm:"juniors_num"`
	StudentsNum       uint       `json:"students_num" gorm:"students_num"`
	StudentsPercent   string     `json:"students_percent" gorm:"type:varchar(20);default:null"`
	MarketType        string     `json:"market_type" gorm:"type:enum('','A','B','C','D');default:''"`
	IsFlat            uint8      `json:"is_flat" gorm:"type:tinyint(3) unsigned;default:0"`
	FlatCity          uint       `json:"flat_city" gorm:"default:0"`
	FlatDate          string     `json:"flat_date" gorm:"type:char(7);default:''"`
	Channel           string     `json:"channel" gorm:"type:enum('agency','e_commerce','operator','special','ebag','other','mengkubao','external');default:'agency'"`
	Compound          uint8      `json:"compound" gorm:"type:tinyint(3) unsigned;default:0"`
	IsDirectSales     uint8      `json:"is_direct_sales" gorm:"type:tinyint(4);default:0"`
	CreatedAt         time.Time  `json:"created_at" gorm:"type:timestamp;default:CURRENT_TIMESTAMP"`
	UpdatedAt         time.Time  `json:"updated_at" gorm:"type:timestamp;default:'0000-00-00 00:00:00' onUpdate:CURRENT_TIMESTAMP"`
	DeletedAt         *time.Time `json:"deleted_at" gorm:"type:timestamp;default:null"`
	QwPartyid         uint       `json:"qw_partyid" gorm:"default:0"`
	Gdp               string     `json:"gdp" gorm:"type:decimal(10,2);default:null"`
}

// ResponseTree 用于响应的树状结构
type AgencyDataResponseTree struct {
	TopAgencyID    int                    `json:"top_agency_id"`
	TopAgencyName  string                 `json:"top_agency_name"`
	DepartmentID   int64                  `json:"department_id" gorm:"column:department"`
	PartitionID    int                    `json:"partition_id" gorm:"column:partition"`
	DepartmentName string                 `json:"department_name"`
	PartitionName  string                 `json:"partition_name"`
	KingdeeIDs     []int                  `json:"kingdee_ids"`
	KingdeeNames   []string               `json:"kingdee_names"`
	SecondAgencies []SecondAgencyResponse `json:"second_agencies"`
	PupilsNum      uint                   `json:"pupils_num" gorm:"pupils_num"`
	JuniorsNum     uint                   `json:"juniors_num" gorm:"juniors_num"`
	Level          int                    `json:"level" gorm:"column:level"`
	MarketType     string                 `json:"market_type" gorm:"column:market_type"`
}

// SecondAgencyResponse 二级代理响应
type SecondAgencyResponse struct {
	SecondAgencyName string `json:"second_agency_name"`
}
