package app

import (
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"marketing/internal/api/renew"
	"marketing/internal/consts"
	"marketing/internal/handler"
	appError "marketing/internal/pkg/errors"
	service "marketing/internal/service/renew"
)

type ApplicationHandlerInterface interface {
	Check(c *gin.Context)
	Lists(c *gin.Context)
	GetInfo(c *gin.Context)
	GetStatus(c *gin.Context)
	GetCheckFacade(c *gin.Context)
	Export(c *gin.Context)
}

type application struct {
	svc service.ApplicationServiceInterface
}

func NewApplicationHandler(svc service.ApplicationServiceInterface) ApplicationHandlerInterface {
	return &application{
		svc: svc,
	}
}

func (h *application) Check(c *gin.Context) {
	req := &renew.CheckMachineReq{}
	if err := c.ShouldBind(req); err != nil {
		handler.Error(c, err)
		return
	}
	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		handler.Error(c, appError.NewErr("ID不能为空"))
		return
	}
	req.ID = id

	err := h.svc.CheckMachine(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c)
}

func (h *application) Lists(c *gin.Context) {
	req := &renew.ListApplicationReq{}
	if err := c.ShouldBind(req); err != nil {
		handler.Error(c, err)
		return
	}
	req.SetDefaults()
	if req.Status == "" {
		req.StatusSlices = []string{consts.RenewStatusHeadOfficeReview, consts.RenewStatusRepair, consts.RenewStatusCompleted}
	}
	data, total, err := h.svc.Lists(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"list":  data,
		"total": total,
	})
}

func (h *application) GetInfo(c *gin.Context) {
	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		handler.Error(c, appError.NewErr("ID不能为空"))
		return
	}
	data, err := h.svc.GetInfo(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, data)
}

func (h *application) GetStatus(c *gin.Context) {
	dataMap := consts.GetRenewStatus()
	data := []map[string]string{
		{
			"value": consts.RenewStatusHeadOfficeReview,
			"label": dataMap[consts.RenewStatusHeadOfficeReview],
		},
		{
			"value": consts.RenewStatusRepair,
			"label": dataMap[consts.RenewStatusRepair],
		},
		{
			"value": consts.RenewStatusCompleted,
			"label": dataMap[consts.RenewStatusCompleted],
		},
	}
	handler.Success(c, data)
}

func (h *application) GetCheckFacade(c *gin.Context) {
	data := consts.GetRenewCheckFacade()
	handler.Success(c, data)
}

func (h *application) Export(c *gin.Context) {
	var req renew.ListApplicationReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	req.SetDefaults()
	req.StatusSlices = []string{consts.RenewStatusHeadOfficeReview, consts.RenewStatusRepair, consts.RenewStatusCompleted}
	err := h.svc.Export(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}
}
