package dao

import "marketing/internal/model"

// EndpointImageHistoryDAO 数据访问对象，操作 EndpointImageHistory 数据
type EndpointImageHistoryDAO struct {
}

// NewEndpointImageHistoryDAO 创建 EndpointImageHistoryDAO 实例
func NewEndpointImageHistoryDAO() *EndpointImageHistoryDAO {
	return &EndpointImageHistoryDAO{}
}

var EndpointImagehistorydao EndpointImageHistoryDAO

// GetAll 获取该id下所有的终端形象历史记录
func (dao *EndpointImageHistoryDAO) GetbyImageHistoryId(endpointId int) ([]model.EndpointImageApply, error) {
	var endpointImageHistories []model.EndpointImageApply
	err := GetDB().Table("endpoint_image_apply").Where("endpoint_id=?", endpointId).
		Order("updated_at desc").Find(&endpointImageHistories).Error
	return endpointImageHistories, err
}

//// GetEndpointHistory retrieves historical records for a specific endpoint.
//func (dao *EndpointImageHistoryDAO) GetEndpointImageHistory(endpointID int) ([]EndpointImageApply, error) {
//	var history []EndpointImageApply
//	db := GetDB()
//	err := db.Table("endpoint_image_apply as a").
//		//Select("a.*, u.code").
//		//Joins("LEFT JOIN endpoint as u ON a.enpoint_id = u.id").
//		Where("a.endpoint_id = ?", endpointID).
//		Order("a.id DESC").
//		Find(&history).Error
//	return history, err
//}

// GetEndpointByID retrieves an endpoint by its ID.

// GetAgencyNames retrieves the names of agencies by their IDs.
func (dao *EndpointImageHistoryDAO) GetAgencyNames(agencyIDs []int) (map[int]string, error) {
	var results []struct {
		ID   int
		Name string
	}
	db := GetDB()
	agencyNames := make(map[int]string)
	err := db.Table("agency").Where("id IN ?", agencyIDs).Select("agency.id,agency.name").Find(&results).Error
	if err != nil {
		return nil, err
	}
	for _, res := range results {
		agencyNames[res.ID] = res.Name
	}
	return agencyNames, nil
}
