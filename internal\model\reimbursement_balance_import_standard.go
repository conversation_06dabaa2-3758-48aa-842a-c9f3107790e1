package model

import "time"

// ReimbursementBalanceStandard 余额导入标准模型
type ReimbursementBalanceStandard struct {
	ID           int       `gorm:"column:id;primaryKey;autoIncrement"`
	UID          uint      `gorm:"column:uid;not null;default:0;comment:用户id"`
	OrderID      int       `gorm:"column:order_id;not null;default:0;comment:订单大单id"`
	PolicyID     int       `gorm:"column:policy_id;default:0;comment:政策id"`
	TopAgency    uint      `gorm:"column:top_agency;comment:一级代理id"`
	SecondAgency int       `gorm:"column:second_agency;default:0;comment:二级代理id"`
	CompanyID    uint      `gorm:"column:company_id;not null;comment:公司id"`
	Code         string    `gorm:"column:code;not null;comment:客户编码"`
	Company      string    `gorm:"column:company;not null;default:'';comment:公司名称"`
	Month        time.Time `gorm:"column:month;comment:月份"`
	NormQuantity float64   `gorm:"column:norm_quantity;not null;comment:标准数量"`
	Balance      float64   `gorm:"column:balance;not null;comment:标准数量剩余额度"`
	StandardType string    `gorm:"column:standard_type;not null;comment:标准类型"`
	Remark       string    `gorm:"column:remark;default:'';comment:备注"`
	CreatedAt    time.Time `gorm:"column:created_at;autoCreateTime;comment:创建时间"`
	UpdatedAt    time.Time `gorm:"column:updated_at;autoUpdateTime;comment:更新时间"`
	Type         int       `gorm:"column:type;not null;comment:操作类型"`
	OrdersID     string    `gorm:"column:orders_id;not null;comment:订单小单id"`
}

// TableName 表名
func (ReimbursementBalanceStandard) TableName() string {
	return "reimbursement_balance_import_standard"
}
