package machine

import (
	"fmt"
	"marketing/internal/api/prototype"
	"marketing/internal/handler"
	"marketing/internal/model"
	"marketing/internal/pkg/e"
	"marketing/internal/pkg/errors"
	"marketing/internal/service/machine"
	"time"

	"github.com/gin-gonic/gin"
)

type TMachineType struct {
	svc machine.TMachineTypeSvc
}

func NewMachineType(svc machine.TMachineTypeSvc) *TMachineType {
	return &TMachineType{
		svc: svc,
	}
}

func (m *TMachineType) GetMachineTypeList(c *gin.Context) {
	name := e.ReqParamStr(c, "name")
	pageNum := e.ReqParamInt(c, "page_num")
	pageSize := e.ReqParamInt(c, "page_size")
	list, total := m.svc.GetMachineTypeList(c, name, pageNum, pageSize)
	handler.Success(c, gin.H{
		"list":  list,
		"total": total,
	})
}

func (m *TMachineType) EditMachineType(c *gin.Context) {
	err := m.svc.EditMachineType(c, &model.MachineType{
		Id:                e.ReqParamInt(c, "id"),
		NavName:           e.ReqParamStr(c, "nav_name"),
		ModelName:         e.ReqParamStr(c, "model_name"),
		CategoryId:        e.ReqParamInt(c, "category_id"),
		CompanyPrice:      e.ReqParamFloat64(c, "company_price"),
		TopAgencyPrice:    e.ReqParamFloat64(c, "top_agency_price"),
		SecondAgencyPrice: e.ReqParamFloat64(c, "second_agency_price"),
		CustomerPrice:     e.ReqParamFloat64(c, "customer_price"),
		ChartShow:         e.ReqParamInt(c, "chart_show"),
		PrototypeStatus:   e.ReqParamInt(c, "prototype_status"),
		PrototypeApkPath:  e.ReqParamStr(c, "prototype_apk_path"),
		VersionCode:       e.ReqParamStr(c, "version_code"),
		ExtBarcodeNum:     e.ReqParamInt(c, "ext_barcode_num"),
		Declare:           e.ReqParamInt(c, "declare"),
		Stock:             e.ReqParamInt(c, "stock"),
		Visibility:        e.ReqParamInt(c, "visibility"),
	})
	if err != nil {
		handler.Error(c, errors.NewErr(err.Error()))
		return
	}

	handler.Success(c, gin.H{})
}

func (m *TMachineType) GetMachineTypeByModelId(c *gin.Context) {
	modelId := e.ReqParamInt(c, "model_id")
	data, err := m.svc.GetMachineTypeByModelId(c, modelId)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, data)
}

// UpdateMarketDate 更新上市日期
// @Router /admin/machine_type/market-date [put]
func (m *TMachineType) UpdateMarketDate(c *gin.Context) {
	name := e.ReqParamStr(c, "name")
	marketDateStr := e.ReqParamStr(c, "market_date")
	modelId := e.ReqParamInt(c, "model_id")
	price := e.ReqParamFloat64(c, "price")

	// 验证输入
	if name == "" || marketDateStr == "" || modelId <= 0 {
		handler.Error(c, errors.NewErr("参数错误"))
		return
	}

	// 解析市场日期
	marketDate, err := time.Parse("2006-01-02", marketDateStr)
	if err != nil {
		handler.Error(c, errors.NewErr("日期格式错误，请使用YYYY-MM-DD格式"))
		return
	}

	// 调用服务更新市场日期
	err = m.svc.UpdateMarketDate(c, name, modelId, marketDate, price)
	if err != nil {
		handler.Error(c, errors.NewErr("更新失败: "+err.Error()))
		return
	}

	handler.Success(c, gin.H{
		"status": 1,
		"info":   "更新成功",
	})
}

func (m *TMachineType) GetRepairMachineTypeList(c *gin.Context) {
	modelName := e.ReqParamStr(c, "model_name")
	categoryId := e.ReqParamInt(c, "category_id")
	visibility := e.ReqParamInt(c, "visibility", -1)
	pageNum := e.ReqParamInt(c, "page_num")
	pageSize := e.ReqParamInt(c, "page_size")

	list, total := m.svc.GetRepairMachineTypeList(c, modelName, categoryId, visibility, pageNum, pageSize)
	handler.Success(c, gin.H{
		"list":  list,
		"total": total,
	})
}

func (m *TMachineType) ExportRepairMachineTypeList(c *gin.Context) {
	modelName := e.ReqParamStr(c, "model_name")
	categoryId := e.ReqParamInt(c, "category_id")
	visibility := e.ReqParamInt(c, "visibility", -1)
	pageNum := e.ReqParamInt(c, "page_num")
	pageSize := e.ReqParamInt(c, "page_size")

	file := m.svc.ExportRepairMachineTypeList(c, modelName, categoryId, visibility, pageNum, pageSize)
	if file != nil {
		c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
		c.Header("Content-Disposition", "attachment; filename="+fmt.Sprintf("机型表.xls"))
		if err := file.Write(c.Writer); err != nil {
			handler.Error(c, errors.NewErr(err.Error()))
			return
		}
	}

	handler.Success(c, gin.H{})
}

// UpdateDiscontinued 更新样机机型下市状态
func (m *TMachineType) UpdateDiscontinued(c *gin.Context) {
	var req prototype.UpdateDiscontinuedReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}

	err := m.svc.UpdateDiscontinued(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c)
}
