package agency

import (
	"marketing/internal/dao"
	"marketing/internal/handler"
	"marketing/internal/pkg/db"
	"marketing/internal/service"

	"github.com/gin-gonic/gin"
)

func GetAgencyData(c *gin.Context) {
	service := service.NewAdminUserService(&dao.AdminUsersDao{}, db.GetDB("rbcare"))
	treeData, err := service.GetAllAgencyTrees()
	if err != nil {
		// c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		handler.Error(c, err)
		return
	}
	handler.Success(c, treeData)
}
