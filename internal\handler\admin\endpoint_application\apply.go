package endpoint_application

import (
	api "marketing/internal/api/endpoint_application"
	"marketing/internal/dao"
	"marketing/internal/handler"
	"marketing/internal/pkg/errors"
	"marketing/internal/service"
	applySvc "marketing/internal/service/endpoint_application"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

type ApplyHandler interface {
	GetList(c *gin.Context)
	AuditEndpointApply(c *gin.Context)
	GetMaterials(c *gin.Context)
	MaterialSupport(c *gin.Context)
	WriteOff(c *gin.Context)
	ChannelConfirmation(c *gin.Context)
	RecordConfirmation(c *gin.Context)
	LatestEndpointImage(c *gin.Context)
	SalesAmount(c *gin.Context)
}

type applyHandler struct {
	svc applySvc.EndpointApplyService
}

func NewApplyHandler(svc applySvc.EndpointApplyService) ApplyHandler {
	return &applyHandler{svc: svc}
}

func (h *applyHandler) GetList(c *gin.Context) {
	var req api.EndpointApplicationListReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	req.SetDefaults()

	data, total, err := h.svc.GetEndpointApplyList(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, data, total)
}

func (h *applyHandler) AuditEndpointApply(c *gin.Context) {
	var req api.AuditApplyReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		handler.Error(c, errors.NewErr("id is required"))
		return
	}
	req.ID = id
	if err := h.svc.AuditApply(c, &req); err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c)
}

func (h *applyHandler) GetMaterials(c *gin.Context) {
	name := c.Query("name")
	page := c.Query("page")
	pageSize := c.Query("page_size")
	pageInt := cast.ToInt(page)
	pageSizeInt := cast.ToInt(pageSize)
	if pageInt == 0 {
		pageInt = 1
	}
	if pageSizeInt == 0 {
		pageSizeInt = 20
	}
	//获取所有的分类
	categories := []int{17, 2}
	categoriesSvc := service.NewMaterialCategoryService(dao.NewMaterialDao(), dao.NewMaterialCategoryDao())
	allCategories, err := categoriesSvc.GetMaterialAndChildren(c, categories)
	if err != nil {
		handler.Error(c, err)
		return
	}
	data, err := h.svc.GetMaterials(c, name, allCategories, pageInt, pageSizeInt)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, data)
}

func (h *applyHandler) MaterialSupport(c *gin.Context) {
	var req []*api.EndpointMaterialSupport
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		handler.Error(c, errors.NewErr("id不能为空"))
		return
	}
	if len(req) == 0 {
		handler.Error(c, errors.NewErr("请选择物料"))
		return
	}
	err := h.svc.MaterialSupport(c, id, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c)
}

func (h *applyHandler) WriteOff(c *gin.Context) {
	var req api.WriteOffReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	err := h.svc.WriteOff(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c)
}

func (h *applyHandler) ChannelConfirmation(c *gin.Context) {
	var req api.AuditApplyReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	err := h.svc.ChannelConfirmation(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c)
}

func (h *applyHandler) RecordConfirmation(c *gin.Context) {
	var req api.AuditApplyReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	err := h.svc.RecordConfirmation(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c)
}

func (h *applyHandler) LatestEndpointImage(c *gin.Context) {
	id := cast.ToInt(c.Query("id"))
	if id == 0 {
		handler.Error(c, errors.NewErr("id不能为空"))
		return
	}
	image, err := h.svc.LatestEndpointImage(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, image)
}

func (h *applyHandler) SalesAmount(c *gin.Context) {
	id := cast.ToInt(c.Query("id"))
	if id == 0 {
		handler.Error(c, errors.NewErr("id不能为空"))
		return
	}
	years := cast.ToInt(c.Query("years"))
	if years == 0 {
		years = 1
	}
	endAt := cast.ToInt64(c.Query("end_at"))
	if endAt == 0 {
		endAt = time.Now().Unix()
	}

	count, amount, err := h.svc.SalesAmount(c, id, years, endAt)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"count":  count,
		"amount": amount,
	})
}
