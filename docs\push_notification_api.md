# 推送消息管理 API 文档

## 概述
推送消息管理模块提供了企业微信推送消息的完整管理功能，替代了原有的极光推送，支持创建、查询、推送和撤回操作。

## 架构设计
- **Handler层**: 处理HTTP请求和响应
- **Service层**: 业务逻辑处理，企微推送集成
- **DAO层**: 数据库操作，包括推送记录、用户查询、统计等
- **Model层**: 数据模型定义

## API 接口列表

### 1. 获取推送消息列表

**请求URL:** `/admin/notice/push`

**请求方法:** `GET`

**参数:**
| 参数名 | 必选 | 类型 | 说明 |
|--------|------|------|------|
| page | 否 | int | 页码，默认1 |
| page_size | 否 | int | 每页数量，默认20 |
| type_id | 否 | int | 消息类型ID筛选 |
| content | 否 | string | 内容关键词筛选 |

**返回示例:**
```json
{
  "ok": 1,
  "msg": "success",
  "data": {
    "data": [
      {
        "id": 1,
        "type_name": "系统通知",
        "content": "新版本介绍新功能上线",
        "platform": "[\"wecom\"]",
        "audience": "{\"all\":\"all\"}",
        "total": 100,
        "fetched": 95,
        "read": 80,
        "checked": 60,
        "revoked": 0
      }
    ],
    "total": 1,
    "page": 1,
    "page_size": 20
  }
}
```

**返回参数说明:**
| 参数名 | 类型 | 说明 |
|--------|------|------|
| id | int | 推送消息ID |
| type_name | string | 消息类型名称 |
| content | string | 消息内容 |
| platform | string | 推送平台 |
| audience | string | 推送目标 |
| total | int | 推送总量 |
| fetched | int | 拉取量 |
| read | int | 浏览量 |
| checked | int | 查看量 |
| revoked | int | 是否撤回 |

### 2. 创建推送消息页面数据

**请求URL:** `/admin/notice/push/create`

**请求方法:** `GET`

**参数:**
| 参数名 | 必选 | 类型 | 说明 |
|--------|------|------|------|
| 无 | - | - | - |

**返回示例:**
```json
{
  "ok": 1,
  "msg": "success",
  "data": {
    "notification_types": [
      {
        "id": 1,
        "name": "系统通知",
        "slug": "system_notice"
      }
    ],
    "notification_tags": [
      {
        "id": 1,
        "text": "管理员"
      }
    ],
    "roles": [
      {
        "slug": "admin",
        "name": "管理员"
      }
    ],
    "newest_trains": [],
    "newest_notices": [
      {
        "id": 1,
        "title": "系统维护通知"
      }
    ]
  }
}
```

### 3. 发送推送消息

**请求URL:** `/admin/notice/push`

**请求方法:** `POST`

**参数:**
| 参数名 | 必选 | 类型 | 说明 |
|--------|------|------|------|
| type_id | 是 | string | 消息类型ID，可选值：app_upgrade, train_resource, notification, feedback |
| content | 是 | object/string | 消息内容 |
| platform | 是 | array | 推送平台，目前支持["wecom"] |
| audience | 是 | object | 推送目标 |
| action | 是 | string | 点击动作 |
| url | 否 | string | 跳转URL（action为forward时必填） |
| action_text | 否 | string | 动作文本（action不为none时必填） |
| popup | 是 | int | 是否弹窗，0-否，1-是 |
| banner | 是 | int | 是否横幅，0-否，1-是 |
| banner_start_time | 否 | string | 横幅开始时间（banner为1时必填） |
| banner_end_time | 否 | string | 横幅结束时间（banner为1时必填） |

**推送目标audience格式:**
```json
// 推送给所有用户
{
  "all": "all"
}

// 推送给指定标签用户
{
  "tags": [
    {"id": 1, "text": "管理员"},
    {"id": 2, "text": "店长"}
  ]
}

// 推送给指定用户
{
  "users": [
    {"id": 1, "text": "张三"},
    {"id": 2, "text": "李四"}
  ]
}

// 推送给指定角色用户
{
  "roles": [
    {"id": "admin", "text": "管理员"},
    {"id": "manager", "text": "店长"}
  ]
}
```

**请求示例:**
```json
{
  "type_id": "notification",
  "content": "系统将于今晚进行维护，请提前保存工作",
  "platform": ["wecom"],
  "audience": {
    "tags": [
      {"id": 1, "text": "管理员"}
    ]
  },
  "action": "none",
  "popup": 1,
  "banner": 0
}
```

**返回示例:**
```json
{
  "ok": 1,
  "msg": "success",
  "data": {
    "success": true
  }
}
```

### 4. 搜索用户

**请求URL:** `/admin/notice/push/users`

**请求方法:** `GET`

**参数:**
| 参数名 | 必选 | 类型 | 说明 |
|--------|------|------|------|
| search | 是 | string | 搜索关键词 |
| page | 否 | int | 页码，默认1 |

**返回示例:**
```json
{
  "ok": 1,
  "msg": "success",
  "data": [
    {
      "id": 1,
      "text": "张三"
    },
    {
      "id": 2,
      "text": "李四"
    }
  ]
}
```

### 5. 撤回推送消息

**请求URL:** `/admin/notice/push/{id}/revoke`

**请求方法:** `PUT`

**参数:**
| 参数名 | 必选 | 类型 | 说明 |
|--------|------|------|------|
| id | 是 | int | 推送消息ID（路径参数） |

**返回示例:**
```json
{
  "ok": 1,
  "msg": "success",
  "data": {
    "success": true
  }
}
```

## 注意事项

1. **企微推送配置**: 需要在配置文件中正确设置企业微信的CorpID和CorpSecret
2. **用户企微绑定**: 用户需要先绑定企业微信账号才能接收推送消息
3. **撤回限制**: 推送消息只能在发送后48小时内撤回
4. **推送目标**: 支持全员推送、按标签推送、按角色推送和指定用户推送
5. **消息类型**: 目前支持文本消息推送到企业微信

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 配置说明

需要在配置文件中添加企业微信相关配置：

```yaml
wecom:
  corp_id: "your_corp_id"
  corp_secret: "your_corp_secret"
  agent_id: 1000001
```

## 数据库表结构

### app_notification 表
- id: 主键
- type_id: 消息类型ID
- content: 消息内容JSON
- platform: 推送平台JSON
- audience: 推送目标JSON
- revoked: 是否撤回
- revoked_at: 撤回时间
- created_at: 创建时间
- updated_at: 更新时间

### app_notification_inbox 表
- id: 主键
- user_id: 用户ID
- notification_id: 消息ID
- fetched: 是否拉取
- read: 是否浏览
- checked: 是否查看
- created_at: 创建时间
- updated_at: 更新时间
