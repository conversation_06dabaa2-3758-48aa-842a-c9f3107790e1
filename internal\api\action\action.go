package action

import (
	"time"
)

// ApplyAction 活动申请提交
type ApplyAction struct {
	//19
	Name      string `json:"name" form:"name" binding:"required"`
	Level     int    `json:"level" form:"level" binding:"required"`
	Phone     string `json:"phone" form:"phone" binding:"required"`
	Principal string `json:"principal" form:"principal" binding:"required"` //负责人
	Plan      string `json:"plan" form:"plan" binding:"required"`           //计划内容
	Staff     string `json:"staff" form:"staff" binding:"required"`         //人员配置
	/*	Description string   `json:"description" form:"description"`                  //活动阐述
	 */SitePhoto []string `json:"site_photo" form:"site_photo" binding:"required"` //现场照片
	Space        uint     `json:"space" form:"space" binding:"required"`
	DateStart    string   `json:"start_date" form:"start_date" binding:"required"`
	DateEnd      string   `json:"end_date" form:"end_date" binding:"required"`
	//新增字段
	WriteOffCorporation string `json:"write_off_corporation" form:"write_off_corporation"` //核销公司
	Location            string `json:"location" form:"location" binding:"required"`
	Type                uint   `json:"type" form:"type" binding:"required"`
	/*	ActivityID          uint   `json:"activity_id" form:"activity_id"` //关联的终端活动ID 0不关联
		PayGiftSupport      uint8  `json:"pay_gift_support" form:"pay_gift_support"`*/
	//终端
	EndpointID     uint `json:"endpoint_id" form:"endpoint_id" binding:"required"`
	SecondAgencyID uint `json:"second_agency_id" form:"second_agency_id" binding:"required"`
	TopAgencyID    uint `json:"top_agency_id" form:"top_agency_id" binding:"required" `
}

type ApplyActionV1 struct { //19
	ApplyAction
}
type UpdateAction struct {
	ID uint `json:"id" form:"id"`
	ApplyAction
}

type TypeAction struct {
	ID                 uint   `json:"id" form:"id" binding:"-"`
	Name               string `json:"name" form:"name" binding:"required"`
	StartDate          string `json:"start_date" form:"start_date" binding:"required"`
	EndDate            string `json:"end_date" form:"end_date" binding:"required"`
	Ceiling            uint16 `json:"ceiling" form:"ceiling" binding:"required"`
	Enabled            uint8  `json:"enabled" form:"enabled" binding:"required"`
	Slug               string `json:"slug" form:"slug" binding:"required"`
	UpdatedAt          string
	CreatedAt          string
	AvailableTopAgency []int  `json:"available_top_agency" form:"available_top_agency"` // 可参与的一级代理()
	GiftSupport        uint8  `json:"giftSupport" form:"gift_support"`                  // 礼品支持
	EndpointCeiling    uint16 `json:"endpoint_ceiling" form:"endpoint_ceiling"`
	Lines              int    `json:"lines" form:"lines"`
}
type TypeActionList struct {
	ID        uint   `json:"id" `
	Name      string `json:"name"`
	StartDate string `json:"start_date"`
	EndDate   string `json:"end_date"`
	Ceiling   int    `json:"ceiling"`
	Enabled   int    `json:"enabled"`
	Slug      string `json:"slug"`
	HadApply  int    `json:"had_apply"` // 已申请场次
}
type UpdateType struct {
	ID                 int    `json:"id"`
	Name               string `json:"name"`
	StartDate          string `json:"start_date"`
	EndDate            string `json:"end_date"`
	Ceiling            int    `json:"ceiling"`
	Enabled            int    `json:"enabled"`
	Slug               string `json:"slug"`
	AvailableTopAgency []int  `json:"available_top_agency"`
	EndpointCeiling    uint16 `json:"endpoint_ceiling"`
	Lines              int    `json:"lines"` // 可参与的一级代理()
}

// AuditAction 活动审核
type AuditAction struct {
	ID           uint   `json:"id" form:"id" binding:"required"`
	AuditOpinion string `json:"audit_opinion" form:"audit_opinion" binding:"required"`
	AuditPass    uint8  `json:"audit_pass" form:"audit_pass"`
	UID          uint   `json:"-"`
}

type FinishAction struct { //去掉一个准备附件加上抖音和物料，video共用
	ID             uint      `json:"id" form:"id" binding:"required"`
	Theme          string    `json:"theme" form:"theme" binding:"required"`
	Content        string    `json:"content" form:"content" binding:"required" `
	PhotoPreparing []string  `json:"photo_preparing" form:"photo_preparing" binding:"required"`
	Summary        string    `json:"summary" form:"summary" binding:"required"`
	Advertise      []string  `json:"advertise" form:"advertise" binding:"required"`
	PhotoFinished  []string  `json:"photo_finished" form:"photo_finished" binding:"required"`
	Finish         uint8     `json:"finish" form:"finish" binding:"required"`
	Photo          []string  `json:"photo" form:"photo" binding:"required"`
	SalesDetails   string    `json:"sales_details" form:"sales_details"`
	Total          int       `json:"total" form:"total"`
	Amount         int       `json:"amount" form:"amount"`
	ExpenseAttach  []string  `json:"expenseAttach" form:"expenseAttach"`
	Status         uint8     `json:"-"`
	UpdatedAt      time.Time `json:"-"`
	//新字段
	Video       []string `json:"video" form:"video" binding:"required"`
	Materials   []string `json:"materials"`
	DouyinPhoto []string `json:"douyin_photo" form:"douyin_photo"`
	//旧字段
	/*	PrepareAttach []string `json:"prepareAttach" form:"prepareAttach"`*/
}

// VerifyAction 活动核验
type VerifyAction struct {
	ID         uint   `json:"id" form:"id" binding:"required"`
	PayPass    uint8  `json:"pay_pass" form:"pay_pass"  `
	PayOpinion string `json:"pay_opinion" form:"pay_option" binding:"required"`
	PayTime    string `json:"-"`
	PayUserID  uint   `json:"-"`
	Status     uint8  `json:"-"`
}

// InfoAction 活动信息
type InfoAction struct {
	ID                  uint   `json:"id"`
	TypeName            string `json:"type_name"`
	DateStart           string `json:"date_start"`
	DateEnd             string `json:"date_end"`
	EndpointName        string `json:"endpoint_name"`
	Level               int    `json:"level"`
	Principal           string `json:"principal"`
	Phone               string `json:"phone"`
	Status              uint8  `json:"status"`
	StatusName          string `json:"status_name"`
	WriteOffCorporation string `json:"write_off_corporation"`
	Location            string `json:"location"`
	Year                int    `json:"year"`
}

type ResInfoAction struct {
	ID                  uint   `json:"id"`
	Type                string `json:"type"`
	DateStart           string `json:"date_start"`
	DateEnd             string `json:"date_end"`
	EndpointID          uint   `json:"endpoint_id"`
	Level               int    `json:"level"`
	Principal           string `json:"principal"`
	Phone               string `json:"phone"`
	Status              uint8  `json:"status"`
	WriteOffCorporation string `json:"write_off_corporation"`
	Location            string `json:"location"`
}

type RecordedAction struct {
	ID             uint   `json:"id"`
	AccountOpinion string `json:"account_opinion"`
	SupportMoney   string `json:"support_money"`
	Uid            uint   `json:"-"`
}

type ApplyInfoAction struct { //22
	UserName     string   `json:"user_name"`     //申请人
	CreatedAt    string   `json:"date"`          //申请时间
	Photos       []string `json:"photos"`        //场地图片
	Description  string   `json:"description"`   //活动阐述
	DateStart    string   `json:"date_start"`    //活动开始时间
	DateEnd      string   `json:"date_end"`      //活动结束时间
	Endpoint     string   `json:"endpoint"`      // 终端
	SecondAgency string   `json:"second_agency"` //二级代理
	TopAgency    string   `json:"top_agency"`    //一级代理
	Space        uint     `json:"space"`         //场地面积
	Staff        string   `json:"staff"`         //人员配置
	Phone        string   `json:"phone"`         //负责人电话
	Principal    string   `json:"principal"`     //负责人
	Plan         string   `json:"plan"`          //计划内容
	Level        string   `json:"level"`         //活动等级
	ActivityName string   `json:"activity_name"` //活动名称
	//新增字段
	WriteOffCorporation string `json:"write_off_corporation"` //核销公司
	Location            string `json:"location"`
	TypeName            string `json:"type_name"`
	ActivityID          int    `json:"activity_id"` //关联的终端活动ID 0不关联
	PayGiftSupport      string `json:"pay_gift_support"`
	//活动类型slug 版本信息
	Type int `json:"-"`
	Year int `json:"year"`
}

type FinishInfoAction struct {
	FinishTime     string   `json:"date"`
	Photos         []string `json:"photos"`
	PhotoPreparing []string `json:"photo_preparing"`
	PhotoFinished  []string `json:"photo_finished"`
	Video          []string `json:"video"`
	SaleDetails    string   `json:"sale_details"`
	Summary        string   `json:"summary"`
	Advertise      []string `json:"advertise"`
	Theme          string   `json:"theme"`
	Content        string   `json:"content"`
	Total          uint     `json:"total"`
	Amount         uint     `json:"amount"`
	ExpenseAttach  []string `json:"expense_attach"`
	//旧字段
	PrepareAttach []string `json:"prepare_attach"`
	//新增字段
	ApplicationYear int      `json:"applicationYear"`
	DouyinPhoto     []string `json:"douyin_photo"`
	Materials       []string `json:"materials"`
	Quota           uint     `json:"quota"`
}
type AuditInfoAction struct {
	AuditName    string `json:"audit_name"`
	AuditOpinion string `json:"audit_opinion"`
	AuditTime    string `json:"audit_time"`
	AuditPass    string `json:"audit_pass"`
}
type VerifyInfoAction struct {
	VerifyName string `json:"verify_name"`
	PayOpinion string `json:"pay_opinion"`
	PayTime    string `json:"pay_time"`
	PayPass    string `json:"pay_pass"`
}
type PrintFinishAction struct {
	Number         string `json:"number"`
	TypeName       string `json:"type_name"`
	EndpointName   string `json:"endpoint_name"`
	Space          uint   `json:"space"`
	Level          string `json:"level"`
	DateStart      string `json:"date_start"`
	DateEnd        string `json:"date_end"`
	Principal      string `json:"principal"`
	Phone          string `json:"phone"`
	Plan           string `json:"plan"`
	Staff          string `json:"staff"`
	Description    string `json:"description"`
	SalesDetails   string `json:"sales_details"`
	Summary        string `json:"summary"`
	Total          uint   `json:"total"`
	Amount         uint   `json:"amount"`
	DQAuditOpinion string `json:"dq_audit_opinion"`
	DQAuditPass    string `json:"dq_audit_pass"`
	DQAuditTime    string `json:"dq_audit_time"`
	AuditPass      string `json:"audit_pass"`
	AuditTime      string `json:"audit_time"`
	AuditOpinion   string `json:"audit_opinion"`
	PayPass        string `json:"pay_pass"`
	PayOpinion     string `json:"pay_opinion"`
	PayTime        string `json:"pay_time"`
}

type Tips struct {
	ID   int    `json:"id"`
	Name string `json:"name"`
}
type EndpointCard[T any] struct {
	UpdatedAt T   `json:"updated_at"`
	CreatedAt T   `json:"created_at"`
	Count     int `json:"count"`
	NearTime  T   `json:"near_time"`
}

// SearchActionParams 活动搜索参数
type SearchActionParams struct {
	TypeName  string `json:"type_name" form:"type_name"`
	StartDate string `json:"start_date" form:"start_date"`
	EndDate   string `json:"end_date" form:"end_date"`
	Year      int    `json:"year" form:"year"`
	TopAgency int    `json:"top_agency" form:"top_agency"`
	Agency    int    `json:"second_agency" form:"second_agency"`
	Endpoint  int    `json:"endpoint" form:"endpoint"`
	Status    int    `json:"status" form:"status"`
	Partition int    `json:"dp" form:"dp"`
	Page      int    `json:"page" form:"page"`
	Size      int    `json:"page_size" form:"page_size"`
	UID       uint   `json:"-" form:"-"`
	IsNew     string `json:"-" form:"-"`
	IDs       []int  `json:"ids" form:"ids"`
}
