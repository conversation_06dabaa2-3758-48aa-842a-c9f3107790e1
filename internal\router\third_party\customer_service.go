package third_party

import (
	"marketing/internal/config"
	"marketing/internal/dao/app_auth"
	"marketing/internal/handler/third_party"
	"marketing/internal/middleware"
	service "marketing/internal/service/third_party"

	"gorm.io/gorm"

	"github.com/gin-gonic/gin"
)

type CustomerServiceRouter struct {
	Db          *gorm.DB
	config      *config.Config
	appAuthDao  app_auth.AppAuthDao
	authService service.ThirdPartyAuthServiceInterface
}

func NewCustomerServiceRouter(Db *gorm.DB, cfg *config.Config, authService service.ThirdPartyAuthServiceInterface) *CustomerServiceRouter {
	return &CustomerServiceRouter{
		Db:          Db,
		config:      cfg,
		authService: authService,
	}
}

func (a *CustomerServiceRouter) Register(r *gin.RouterGroup) {
	// 创建warranty相关组件
	customerService := service.NewCustomerService(a.config)
	customerServiceHandler := third_party.NewCustomerServiceHandler(customerService)
	//短信发送
	hollycrmSmsMiddleware := middleware.AppKeyAuthMiddleware(a.authService)
	smsRouter := r.Group("/sms")
	smsRouter.Use(hollycrmSmsMiddleware)
	{
		smsRouter.POST("/send", customerServiceHandler.SendCaptcha)
	}

	//寄修接口对接
	repairAuthMiddleware := middleware.HollyMD5Middleware(a.authService)
	repairRouter := r.Group("/repair")
	repairRouter.Use(repairAuthMiddleware)
	{
		repairRouter.GET("/query", customerServiceHandler.QueryRepairProgress)
		repairRouter.GET("/urgent", customerServiceHandler.RepairUrgent) // 寄修进度加急
	}
	// 测试接口aes
	//r.GET("/test", customerServiceHandler.Test)
}
