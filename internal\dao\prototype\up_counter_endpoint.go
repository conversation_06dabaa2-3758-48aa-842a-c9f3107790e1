package prototype

import (
	"marketing/internal/api/prototype"
	"marketing/internal/model"

	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// UpCounterEndpointResult 上柜终端查询结果
type UpCounterEndpointResult struct {
	ID          int       `gorm:"column:id"`
	Model       string    `gorm:"column:model"`
	EndpointID  int       `gorm:"column:endpoint_id"`
	UID         int       `gorm:"column:uid"`
	Photo       string    `gorm:"column:photo"`
	Status      int       `gorm:"column:status"`
	Longitude   string    `gorm:"column:longitude"`
	Latitude    string    `gorm:"column:latitude"`
	Address     string    `gorm:"column:address"`
	CreatedAt   time.Time `gorm:"column:created_at"`
	UpdatedAt   time.Time `gorm:"column:updated_at"`
	ApproveTime time.Time `gorm:"column:approve_time"`
	Barcode     string    `gorm:"column:barcode"`
	Reason      string    `gorm:"column:reason"`
	Code        string    `gorm:"column:code"`
	Name        string    `gorm:"column:name"`
	Manager     string    `gorm:"column:manager"`
	Phone       string    `gorm:"column:phone"`
	IsPreSale   int       `gorm:"column:is_pre_sale"`
	IsAfterSale int       `gorm:"column:is_after_sale"`
	Type        int       `gorm:"column:type"`
}

// UpCounterEndpoint 上柜终端DAO
type UpCounterEndpoint interface {
	GetList(c *gin.Context, req *prototype.UpCounterEndpointSearch) ([]*UpCounterEndpointResult, int64, error)
	Approve(c *gin.Context, approve *prototype.UpCounterApprove) error
}

type upCounterEndpoint struct {
	db *gorm.DB
}

// NewUpCounterEndpointDao 创建DAO实例
func NewUpCounterEndpointDao(db *gorm.DB) UpCounterEndpoint {
	return &upCounterEndpoint{
		db: db,
	}
}

func (s *upCounterEndpoint) GetList(c *gin.Context, req *prototype.UpCounterEndpointSearch) ([]*UpCounterEndpointResult, int64, error) {
	var list []*UpCounterEndpointResult
	var count int64

	var query *gorm.DB
	if req.IsEndpointMain {
		query = s.db.WithContext(c).Model(&model.Endpoint{}).
			Select("prototype_up_counter.*, endpoint.code, endpoint.name, endpoint.manager, endpoint.phone, endpoint.is_pre_sale, endpoint.is_after_sale, endpoint.type").
			Joins("LEFT JOIN prototype_up_counter ON prototype_up_counter.endpoint_id = endpoint.id").Where("endpoint.status = 1")
	} else {
		query = s.db.WithContext(c).Model(&model.PrototypeUpCounter{}).
			Select("prototype_up_counter.*, endpoint.code, endpoint.name, endpoint.manager, endpoint.phone, endpoint.is_pre_sale, endpoint.is_after_sale, endpoint.type").
			Joins("LEFT JOIN endpoint ON prototype_up_counter.endpoint_id = endpoint.id").Where("endpoint.status = 1")
	}

	// 过滤条件
	if req.EndpointName != "" {
		query = query.Where("endpoint.name LIKE ?", "%"+req.EndpointName+"%")
	}
	if req.EndpointCode != "" {
		query = query.Where("endpoint.code = ?", req.EndpointCode)
	}
	if req.EndpointAddress != "" {
		query = query.Where("endpoint.address LIKE ?", "%"+req.EndpointAddress+"%")
	}
	if req.TopAgencyID > 0 {
		query = query.Where("endpoint.top_agency = ?", req.TopAgencyID)
	}
	if req.SecondAgencyID > 0 {
		query = query.Where("endpoint.second_agency = ?", req.SecondAgencyID)
	}

	if req.ApproveStatus != 0 {
		query = query.Where("prototype_up_counter.status = ?", req.ApproveStatus)
	}
	if req.Status != "" {
		switch req.Status {
		case "passed_audit":
			if req.ModelName != "" {
				query = query.Where("prototype_up_counter.model = ?", req.ModelName)
			}
			query = query.Where("prototype_up_counter.status = 2")
		case "failed_audit":
			if req.ModelName != "" {
				query = query.Where("prototype_up_counter.model = ?", req.ModelName)
			}
			query = query.Where("prototype_up_counter.status = -2")
		case "not_audit":
			if req.ModelName != "" {
				query = query.Where("prototype_up_counter.model = ?", req.ModelName)
			}
			query = query.Where("prototype_up_counter.status = 1")
		case "not_upload":
			query = query.Where("prototype_up_counter.status is null")
		}
	}

	err := query.Count(&count).Error
	if err != nil {
		return nil, 0, err
	}

	err = query.Offset((req.Page - 1) * req.PageSize).
		Limit(req.PageSize).
		Order("prototype_up_counter.id DESC").
		Find(&list).Error
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

func (s *upCounterEndpoint) Approve(c *gin.Context, approve *prototype.UpCounterApprove) error {
	return s.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		// 更新审核状态
		err := tx.Table("prototype_up_counter").
			Where("id = ?", approve.Id).
			Updates(map[string]any{
				"status":       approve.Id,
				"reason":       approve.Reason,
				"approve_time": gorm.Expr("NOW()"),
			}).Error
		if err != nil {
			return err
		}
		return nil
	})
}
