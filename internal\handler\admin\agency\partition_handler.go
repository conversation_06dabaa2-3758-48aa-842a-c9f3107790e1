package agency

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/dao"
	"marketing/internal/handler"
	"marketing/internal/pkg/e"
	"marketing/internal/service"
)

// PartitionHandler 负责处理与分区相关的HTTP请求
type PartitionHandler struct {
	service *service.PartitionService // 引用一个分区服务实例，用于处理业务逻辑
}

// NewPartitionHandler 创建并返回一个新的 PartitionHandler 实例
func NewPartitionHandler(svc *service.PartitionService) *PartitionHandler {
	return &PartitionHandler{service: svc}
}

// GetPartitions 处理获取分区信息的请求
func (p *PartitionHandler) GetPartitions(c *gin.Context) {
	name := e.ReqParamStr(c, "name")
	pageNum := e.ReqParamInt(c, "page_num")
	pageSize := e.ReqParamInt(c, "page_size")

	data, total := p.service.GetPartitions(name, pageNum, pageSize)
	handler.Success(c, gin.H{
		"list":  data,
		"total": total,
	})
}

// CreatePartition 处理创建新分区的请求
func (p *PartitionHandler) CreatePartition(c *gin.Context) {
	name := e.ReqParamStr(c, "name")
	err := p.service.CreatePartition(name)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, gin.H{})
}

// UpdatePartition 处理更新现有分区的请求
func (p *PartitionHandler) UpdatePartition(c *gin.Context) {
	id := e.ReqParamInt(c, "id")
	name := e.ReqParamStr(c, "name")
	err := p.service.UpdatePartition(id, name)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, gin.H{})
}

// DeletePartition 处理删除分区的请求
func (p *PartitionHandler) DeletePartition(c *gin.Context) {
	id := e.ReqParamInt(c, "id")
	err := p.service.DeletePartition(id)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, gin.H{})
}

func (p *PartitionHandler) GetPartition(c *gin.Context) {
	id := e.ReqParamInt(c, "id")
	partition, err := p.service.GetPartitionByID(id)
	if err != nil {
		handler.Error(nil, err)
		return
	}

	if partition == nil {
		partition = new(dao.Partition)
	}

	handler.Success(c, partition)
}
