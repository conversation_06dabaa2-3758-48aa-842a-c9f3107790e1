package service

import (
	"errors"
	"gorm.io/gorm"
	"marketing/internal/dao"
	"time"
)

// GetAllRegionTrees 获取所有的区域树
func GetAllRegionTrees(parentId, regionType int, regionName string, pageNum, pageSize int) ([]dao.Region, int64) {
	list, total := dao.GetAllRegionTreesFromDB(parentId, regionType, regionName, pageNum, pageSize)

	parentIds := make([]int, 0)
	for _, l := range list {
		parentIds = append(parentIds, l.RegionId)
	}

	infos := dao.GetChildRegionNumByParentIds(parentIds)
	for i, l := range list {
		for _, info := range infos {
			if l.RegionId == info.ParentId {
				list[i].ChildRegionNum = info.ChildRegionNum
				break
			}
		}
	}

	return list, total
}

// EditRegion 编辑区域
func EditRegion(r *dao.Region) error {
	oldRegion, _ := dao.GetRegionById(r.RegionId)
	if oldRegion == nil {
		return CreateRegion(r)
	}

	uMap := make(map[string]interface{})

	if r.ParentId > 0 && oldRegion.ParentId != r.ParentId {
		parentRegion, _ := dao.GetRegionById(r.ParentId)
		if parentRegion == nil {
			return errors.New("编辑区域:上级区域不存在")
		}

		uMap["parent_id"] = r.ParentId
		uMap["region_type"] = parentRegion.RegionType + 1
	}

	uMap["region_name"] = r.RegionName
	uMap["shortname"] = r.ShortName
	uMap["region_level"] = r.RegionLevel
	uMap["city_level"] = r.CityLevel
	uMap["lng"] = r.Lng
	uMap["lat"] = r.Lat
	uMap["remark"] = r.Remark

	if r.Population >= 0 {
		uMap["population"] = r.Population
	}

	if r.PrimarySchoolsNum >= 0 {
		uMap["primary_schools_num"] = r.PrimarySchoolsNum
	}

	if r.PupilsNum >= 0 {
		uMap["pupils_num"] = r.PupilsNum
	}

	if r.MiddleSchoolsNum >= 0 {
		uMap["middle_schools_num"] = r.MiddleSchoolsNum
	}

	if r.JuniorsNum >= 0 {
		uMap["juniors_num"] = r.JuniorsNum
	}

	dbErr := dao.UpdateRegion(r.RegionId, uMap)
	if dbErr != nil {
		return dbErr
	}

	// 编辑父区域的人数等数据
	_editParentRegion(oldRegion, r)

	return nil
}

// CreateRegion 创建区域
func CreateRegion(r *dao.Region) error {
	if r.ParentId > 0 {
		parentRegion, _ := dao.GetRegionById(r.ParentId)
		if parentRegion == nil {
			return errors.New("创建区域:上级区域不存在")
		}

		r.RegionType = parentRegion.RegionType + 1
	}

	dbErr := dao.CreateRegion(r)
	if dbErr != nil {
		return dbErr
	}

	// 编辑父区域的人数等数据
	_editParentRegion(nil, r)

	return nil
}

// DeleteRegion 删除区域
func DeleteRegion(regionId int) error {
	oldRegion, _ := dao.GetRegionById(regionId)
	if oldRegion == nil {
		return nil
	}

	uMap := make(map[string]interface{})
	subMap := make(map[string]interface{})

	uMap["deleted_at"] = time.Now()
	dbErr := dao.UpdateRegion(regionId, uMap)
	if dbErr != nil {
		return dbErr
	}

	subMap["population"] = gorm.Expr("population - ?", oldRegion.Population)
	subMap["primary_schools_num"] = gorm.Expr("primary_schools_num - ?", oldRegion.PrimarySchoolsNum)
	subMap["pupils_num"] = gorm.Expr("pupils_num - ?", oldRegion.PupilsNum)
	subMap["middle_schools_num"] = gorm.Expr("middle_schools_num - ?", oldRegion.MiddleSchoolsNum)
	subMap["juniors_num"] = gorm.Expr("juniors_num - ?", oldRegion.JuniorsNum)

	if len(subMap) > 0 {
		_ = dao.UpdateRegionByRegionIds(_getParentRegionId(oldRegion.ParentId), subMap)
	}

	return nil
}

func _editParentRegion(oldRegion, r *dao.Region) {
	uMap := make(map[string]interface{})
	subMap := make(map[string]interface{})

	if oldRegion != nil && oldRegion.ParentId == r.ParentId {
		if r.Population >= 0 {
			if r.Population >= oldRegion.Population {
				uMap["population"] = gorm.Expr("population + ?", r.Population-oldRegion.Population)
			} else {
				uMap["population"] = gorm.Expr("population - ?", oldRegion.Population-r.Population)
			}
		}

		if r.PrimarySchoolsNum >= 0 {
			if r.PrimarySchoolsNum >= oldRegion.PrimarySchoolsNum {
				uMap["primary_schools_num"] = gorm.Expr("primary_schools_num + ?", r.PrimarySchoolsNum-oldRegion.PrimarySchoolsNum)
			} else {
				uMap["primary_schools_num"] = gorm.Expr("primary_schools_num - ?", oldRegion.PrimarySchoolsNum-r.PrimarySchoolsNum)
			}
		}

		if r.PupilsNum >= 0 {
			if r.PupilsNum >= oldRegion.PupilsNum {
				uMap["pupils_num"] = gorm.Expr("pupils_num + ?", r.PupilsNum-oldRegion.PupilsNum)
			} else {
				uMap["pupils_num"] = gorm.Expr("pupils_num - ?", oldRegion.PupilsNum-r.PupilsNum)
			}
		}

		if r.MiddleSchoolsNum >= 0 {
			if r.MiddleSchoolsNum >= oldRegion.MiddleSchoolsNum {
				uMap["middle_schools_num"] = gorm.Expr("middle_schools_num + ?", r.MiddleSchoolsNum-oldRegion.MiddleSchoolsNum)
			} else {
				uMap["middle_schools_num"] = gorm.Expr("middle_schools_num - ?", oldRegion.MiddleSchoolsNum-r.MiddleSchoolsNum)
			}
		}

		if r.JuniorsNum >= 0 {
			if r.JuniorsNum >= oldRegion.JuniorsNum {
				uMap["juniors_num"] = gorm.Expr("juniors_num + ?", r.JuniorsNum-oldRegion.JuniorsNum)
			} else {
				uMap["juniors_num"] = gorm.Expr("juniors_num - ?", oldRegion.JuniorsNum-r.JuniorsNum)
			}
		}
	} else {
		if r.Population > 0 {
			uMap["population"] = gorm.Expr("population + ?", r.Population)
		}

		if r.PrimarySchoolsNum > 0 {
			uMap["primary_schools_num"] = gorm.Expr("primary_schools_num + ?", r.PrimarySchoolsNum)
		}

		if r.PupilsNum > 0 {
			uMap["pupils_num"] = gorm.Expr("pupils_num + ?", r.PupilsNum)
		}

		if r.MiddleSchoolsNum > 0 {
			uMap["middle_schools_num"] = gorm.Expr("middle_schools_num + ?", r.MiddleSchoolsNum)
		}

		if r.JuniorsNum > 0 {
			uMap["juniors_num"] = gorm.Expr("juniors_num + ?", r.JuniorsNum)
		}

		if oldRegion != nil {
			subMap["population"] = gorm.Expr("population - ?", oldRegion.Population)
			subMap["primary_schools_num"] = gorm.Expr("primary_schools_num - ?", oldRegion.PrimarySchoolsNum)
			subMap["pupils_num"] = gorm.Expr("pupils_num - ?", oldRegion.PupilsNum)
			subMap["middle_schools_num"] = gorm.Expr("middle_schools_num - ?", oldRegion.MiddleSchoolsNum)
			subMap["juniors_num"] = gorm.Expr("juniors_num - ?", oldRegion.JuniorsNum)
		}
	}

	if len(uMap) > 0 {
		_ = dao.UpdateRegionByRegionIds(_getParentRegionId(r.ParentId), uMap)
	}

	if len(subMap) > 0 {
		_ = dao.UpdateRegionByRegionIds(_getParentRegionId(oldRegion.ParentId), subMap)
	}
}

func _getParentRegionId(regionId int) (parentRegionId []int) {
	region, _ := dao.GetRegionById(regionId)
	if region == nil {
		return
	}

	if region.ParentId > 0 {
		parentRegionId = append(parentRegionId, _getParentRegionId(region.ParentId)...)
	}

	parentRegionId = append(parentRegionId, regionId)

	return
}
