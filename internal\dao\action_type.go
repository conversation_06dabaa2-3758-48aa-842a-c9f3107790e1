package dao

import (
	"errors"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"marketing/internal/api/action"
	"marketing/internal/model"
	"marketing/internal/pkg/db"
	"strconv"
	"strings"
	"time"
)

// 在 ActionTypeDao 接口中添加方法
type ActionTypeDao interface {
	Create(c *gin.Context, action model.ActionType) (id uint, err error)
	Update(ctx *gin.Context, action model.ActionType) bool
	GetActionTypeList(c *gin.Context, page int, size int) (actions []action.TypeActionList, total int64)
	GetByID(c *gin.Context, id int) (action model.ActionType, err error)
	ReviseStatus(c *gin.Context, id int, status int) error
	ReviseTypeName(c *gin.Context, id int, name int) error
	GetDropDown(c *gin.Context, id int64) ([]map[string]any, error)
	DropDownName(c *gin.Context) []string
	GetActionTypeInfo(c *gin.Context, id int, num int) (model.InfoActionType, error)
	SearchType(c *gin.Context, page int, size int, name string, slug string) ([]action.TypeActionList, int, error)
	IsExistSlug(c *gin.Context, slug string, id int) bool
	GetEndpointByID(c *gin.Context, id uint) string
	JudgeEffectTime(t uint, start string, end string) bool
	CheckStatus(id int) bool
	GetEnabledType(c *gin.Context, name string) ([]action.Tips, error)
	GiftSupport(c *gin.Context, id int) bool
	GetLinesEveryMouth(c *gin.Context, t int) int
	GetActionSession(c *gin.Context, t uint) (int, error)
	CheckEnd(c *gin.Context, u uint) bool
	GetTypeQuota(c *gin.Context, t int) (uint, error)
	CheckTopAgencyAvailable(ctx *gin.Context, topAgencyID uint, typeID uint) bool
	GetBatchActionSessions(c *gin.Context, ds []int) (map[int]int, error)
}

type ActionTypeGorm struct {
	db *gorm.DB
}

func (a *ActionTypeGorm) GetBatchActionSessions(c *gin.Context, typeIDs []int) (map[int]int, error) {
	result := make(map[int]int)

	// 如果没有需要查询的类型ID，直接返回空映射
	if len(typeIDs) == 0 {
		return result, nil
	}

	// 查询所有活动类型的上限
	var typeCeilings []struct {
		ID      int `gorm:"column:id"`
		Ceiling int `gorm:"column:ceiling"`
	}

	err := a.db.WithContext(c).Table("action_type").
		Select("id, ceiling").
		Where("id IN ?", typeIDs).
		Find(&typeCeilings).Error

	if err != nil {
		return nil, err
	}

	// 创建类型ID到上限的映射
	ceilingMap := make(map[int]int)
	for _, tc := range typeCeilings {
		ceilingMap[tc.ID] = tc.Ceiling
	}

	// 查询每个活动类型已使用的场次数
	var usedSessions []struct {
		TypeID int `gorm:"column:type"`
		Count  int `gorm:"column:count"`
	}

	err = a.db.WithContext(c).Table("actions").
		Select("type, count(id) as count").
		Where("type IN ? AND status <> 6", typeIDs).
		Group("type").
		Find(&usedSessions).Error

	if err != nil {
		return nil, err
	}

	// 创建类型ID到已使用场次的映射
	usedMap := make(map[int]int)
	for _, us := range usedSessions {
		usedMap[us.TypeID] = us.Count
	}

	// 计算每个活动类型的剩余场次
	for _, typeID := range typeIDs {
		ceiling, hasCeiling := ceilingMap[typeID]
		used, hasUsed := usedMap[typeID]

		if hasCeiling {
			if hasUsed {
				result[typeID] = ceiling - used
			} else {
				result[typeID] = ceiling
			}
		} else {
			// 如果找不到上限信息，设置为100000（表示不限制）
			result[typeID] = 100000
		}
	}

	return result, nil
}

func (a *ActionTypeGorm) CheckTopAgencyAvailable(ctx *gin.Context, topAgencyID uint, typeID uint) bool {
	var actionType model.ActionType
	err := a.db.WithContext(ctx).Where("id = ?", typeID).First(&actionType).Error
	if err != nil {
		return false
	}

	if actionType.AvailableTopAgency == nil || *actionType.AvailableTopAgency == "" {
		return true
	}

	agencyIDs := strings.Split(*actionType.AvailableTopAgency, ",")

	topAgencyIDStr := strconv.FormatUint(uint64(topAgencyID), 10)

	for _, agencyIDStr := range agencyIDs {
		if agencyIDStr == topAgencyIDStr {
			return true
		}
	}

	return false
}

func (a *ActionTypeGorm) GetTypeQuota(c *gin.Context, t int) (uint, error) {
	var ceiling uint
	err := a.db.WithContext(c).Table("action_type").Select("lines").Where("id = ?", t).First(&ceiling).Error
	if err != nil {
		return 0, err
	}
	return ceiling, nil
}

func (a *ActionTypeGorm) CheckEnd(c *gin.Context, u uint) bool {
	var end string
	err := a.db.WithContext(c).Table("action_type").Select("end_date").Where("id = ?", u).First(&end).Error
	if err != nil {
		return false
	}
	if end == "" {
		return true
	}
	ends, err := time.Parse("2006-01-02", end)
	if err != nil {
		return false
	}
	return ends.Before(time.Now())
}

// GetActionSession 查看剩余容量
func (a *ActionTypeGorm) GetActionSession(ctx *gin.Context, t uint) (int, error) {
	var session struct {
		Remain  int `gorm:"column:remain"`
		Ceiling int `gorm:"column:ceiling"`
	}
	err := a.db.WithContext(ctx).Table("action_type").
		Joins("LEFT JOIN actions ON action_type.id = actions.type").
		Select("action_type.ceiling, count(actions.id) as remain").
		Where("action_type.id = ?", t).
		Group("action_type.id").
		Find(&session).Error
	remain := session.Ceiling - session.Remain
	return remain, err
}
func (a *ActionTypeGorm) GetLinesEveryMouth(c *gin.Context, t int) int {
	var lines int
	a.db.WithContext(c).Table("action_type").Select("`endpoint_ceiling`").
		Where("id = ?", t).
		Find(&lines)
	return lines
}

func (a *ActionTypeGorm) GiftSupport(c *gin.Context, id int) bool {
	var giftSupport bool
	a.db.WithContext(c).Table("action_type").Select("gift_support").Where("id = ?", id).Find(&giftSupport)
	return giftSupport
}

func (a *ActionTypeGorm) GetEnabledType(c *gin.Context, name string) ([]action.Tips, error) {
	var tips []action.Tips
	a.db.WithContext(c).Table("action_type").
		Select("id", "name").
		Where("name LIKE ?", "%"+name+"%").
		Where("enabled=1").
		Find(&tips)
	return tips, nil
}

func (a *ActionTypeGorm) CheckStatus(id int) bool {
	var status int
	a.db.Table("action_type").Select("enabled").Where("id = ?", id).Find(&status)
	return status == 1
}

// JudgeEffectTime 判断活动开展时间是否合法（需要大于两天）
func (a *ActionTypeGorm) JudgeEffectTime(t uint, start string, end string) bool {
	if start == "" || end == "" {
		return true
	}

	var s, e time.Time
	var err error

	// 解析开始时间
	if strings.Contains(start, "T") {
		// 尝试解析ISO 8601格式
		s, err = time.Parse(time.RFC3339, start)
		if err != nil {
			// 尝试解析没有时区的ISO格式
			s, err = time.Parse("2006-01-02T15:04:05", start[:19])
			if err != nil {
				return false
			}
		}
	} else {
		// 尝试解析标准日期格式
		s, err = time.Parse("2006-01-02", start)
		if err != nil {
			return false
		}
	}

	// 解析结束时间
	if strings.Contains(end, "T") {
		// 尝试解析ISO 8601格式
		e, err = time.Parse(time.RFC3339, end)
		if err != nil {
			// 尝试解析没有时区的ISO格式
			e, err = time.Parse("2006-01-02T15:04:05", end[:19])
			if err != nil {
				return false
			}
		}
	} else {
		// 尝试解析标准日期格式
		e, err = time.Parse("2006-01-02", end)
		if err != nil {
			return false
		}
	}

	// 计算时间差（天数）
	diff := e.Sub(s).Hours() / 24

	// 活动时间必须大于等于2天
	if diff < 2 {
		return false
	}
	return true
}

func (a *ActionTypeGorm) JudgeCreateTime(id uint, start string) bool {
	var date string
	row := a.db.Table("action_type").
		Select("start_date").
		Where("id = ? AND enabled=?", id, 1).
		Find(&date)
	if row.Error != nil {
		return false
	}
	if row.RowsAffected == 0 {
		return false
	}
	if len(date) == 5 {
		year, _ := strconv.Atoi(start[:4])
		if year > 2025 {
			return false
		}
		t1, err := time.Parse("01-02", date)
		if err != nil {
			return false
		}
		t1 = t1.AddDate(0, 0, -5)
		start = start[5:]
		t2, err := time.Parse("01-02", start)
		if err != nil {
			return false
		}
		return t1.After(t2)
	}
	t1, err := time.Parse("2006-01-02", date)
	if err != nil {
		return false
	}
	t1 = t1.AddDate(0, 0, -5)
	t2, err := time.Parse("2006-01-02", start)
	if err != nil {
		return false
	}
	return t1.After(t2)
}

func (a *ActionTypeGorm) GetEndpointByID(c *gin.Context, id uint) string {
	var agency string
	a.db.WithContext(c).Table("agency").Select("name").Where("id = ?", id).Find(&agency)
	return agency
}

func (a *ActionTypeGorm) IsExistSlug(c *gin.Context, slug string, id int) bool {
	row := a.db.WithContext(c).Table("action_type").Where("slug = ? and id = ?", slug, id).Find(&model.ActionType{})
	return row.RowsAffected > 0
}

func (a *ActionTypeGorm) SearchType(c *gin.Context, page int, size int, name string, slug string) ([]action.TypeActionList, int, error) {
	curDB := a.db.WithContext(c).Table("action_type").
		Joins("LEFT JOIN actions ON actions.type = action_type.id and actions.status <> 6").
		Select("action_type.id as id ,action_type.name as name," + "action_type.ceiling as ceiling," +
			"action_type.start_date as start_date," + "action_type.end_date," +
			"action_type.enabled," + "action_type.slug," +
			"count(actions.id) as had_apply").
		Group("action_type.id")
	if name != "" {
		curDB = curDB.Where("action_type.name LIKE ?", "%"+name+"%")
	}
	if slug != "" {
		curDB = curDB.Where("slug LIKE ?", "%"+slug+"%")
		if slug == "ORDINARY" {
			var con action.TypeName
			slugs := con.Range()
			curDB = curDB.Or("slug not in (?)", slugs)
		}
	}
	actions := make([]action.TypeActionList, 0)
	var total int64
	curDB.Count(&total)
	curDB.Offset((page - 1) * size).Limit(size).Find(&actions)
	return actions, int(total), nil
}

func (a *ActionTypeGorm) GetActionTypeInfo(c *gin.Context, id int, num int) (model.InfoActionType, error) {
	var tt model.InfoActionType
	curDB := a.db.WithContext(c).Table("action_type")
	if num == 0 {
		curDB = curDB.Select("name")
	} else {
		curDB = curDB.Select("slug", "name")
	}
	err := curDB.Where("id=?", id).Find(&tt).Error
	return tt, err
}
func (a *ActionTypeGorm) GetActionTypSlug(c *gin.Context, id int) (string, error) {
	var slug string
	err := a.db.WithContext(c).Table("action_type").
		Select("slug").Where("id=?", id).Find(&slug).Error
	return slug, err
}
func (a *ActionTypeGorm) DropDownName(c *gin.Context) (name []string) {
	a.db.WithContext(c).Table("action_type").Select("name").Find(&name)
	return name
}

func (a *ActionTypeGorm) GetDropDown(c *gin.Context, id int64) (act []map[string]any, err error) {
	err = a.db.WithContext(c).
		Table("action_type").
		Where("enabled = ?", 1).
		Joins("LEFT JOIN actions ON actions.type = action_type.id AND actions.uid=?", id).
		Select("action_type.name",
			"action_type.id",
			"action_type.start_date",
			"action_type.end_date",
			"count(actions.id) as count").
		Group("action_type.id").
		Having("count=0 OR count>0").
		Scan(&act).Error
	return act, err
}

func (a *ActionTypeGorm) GetActionTypeList(c *gin.Context, page int, size int) (actions []action.TypeActionList, total int64) {
	start := (page - 1) * size
	curDb := a.db.WithContext(c).Table("action_type")
	curDb.Count(&total)
	curDb.Joins("LEFT JOIN actions ON actions.type = action_type.id and actions.status <> 6").
		Select("action_type.name as name," + "action_type.ceiling as ceiling," +
			"action_type.start_date as start_date," + "action_type.end_date," +
			"action_type.enabled," + "action_type.slug," +
			"count(actions.id) as had_apply," + "action_type.id as id").
		Group("action_type.id").
		Offset(start).Limit(size).Find(&actions)
	return actions, total
}

func (a *ActionTypeGorm) GetByID(c *gin.Context, id int) (action model.ActionType, err error) {
	return action, a.db.WithContext(c).Table("action_type").Where("id=?", id).First(&action).Error
}

func (a *ActionTypeGorm) ReviseStatus(c *gin.Context, id int, status int) error {
	row := a.db.WithContext(c).Table("action_type").Where("id=?", id).Update("enabled", status)
	if row.Error != nil {
		return row.Error
	}
	if row.RowsAffected == 0 {
		return errors.New("活动ID不存在")
	}
	return nil
}

func (a *ActionTypeGorm) ReviseTypeName(c *gin.Context, id int, name int) error {
	return a.db.WithContext(c).Table("action_type").Where("id=?", id).Update("name", name).Error
}

// Create 创建活动类型
func (a *ActionTypeGorm) Create(ctx *gin.Context, action model.ActionType) (id uint, err error) {
	err = a.db.WithContext(ctx).Select("*").Create(&action).Error
	if err != nil {
		return 0, err
	}
	return action.ID, nil
}

// Update 修改活动类型
func (a *ActionTypeGorm) Update(ctx *gin.Context, action model.ActionType) bool {
	res := a.db.WithContext(ctx).Model(&action).Where("id=?", action.ID).Updates(action)
	//数据库操作失败
	if res.Error != nil {
		return false
	}
	// 活动主键id不存在没有更新
	if res.RowsAffected == 0 {
		return false
	}
	return true
}

func NewActionTypeGorm() ActionTypeDao {
	return &ActionTypeGorm{
		db: db.DB,
	}
}

func NewTestActionTypeDao(db *gorm.DB) ActionTypeDao {
	return &ActionTypeGorm{
		db: db,
	}
}
