package endpoint

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"marketing/internal/model"
)

type InfoApplyDao interface {
	GetByID(c *gin.Context, id uint) (*model.EndpointInfoApply, error)
	Update(c *gin.Context, id uint, updateData map[string]interface{}) error
	List(c *gin.Context, query *gorm.DB) ([]*model.EndpointInfoApply, int64, error)
}

type endpointInfoApply struct {
	db *gorm.DB
}

func NewEndpointInfoApplyDao(db *gorm.DB) InfoApplyDao {
	return &endpointInfoApply{db: db}
}

func (dao *endpointInfoApply) GetByID(c *gin.Context, id uint) (*model.EndpointInfoApply, error) {
	var apply model.EndpointInfoApply
	err := dao.db.WithContext(c).Where("id = ?", id).First(&apply).Error
	if err != nil {
		return nil, err
	}
	return &apply, nil
}

func (dao *endpointInfoApply) Update(c *gin.Context, id uint, updateData map[string]interface{}) error {
	return dao.db.WithContext(c).Model(&model.EndpointInfoApply{}).Where("id = ?", id).Updates(updateData).Error
}

func (dao *endpointInfoApply) List(c *gin.Context, query *gorm.DB) ([]*model.EndpointInfoApply, int64, error) {
	var applies []*model.EndpointInfoApply
	var total int64

	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	err = query.Find(&applies).Error
	if err != nil {
		return nil, 0, err
	}

	return applies, total, nil
}
