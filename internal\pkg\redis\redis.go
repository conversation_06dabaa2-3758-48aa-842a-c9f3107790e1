package redis

import (
	"context"
	"marketing/internal/pkg/log"
	"sync"

	"github.com/redis/go-redis/v9"
	"github.com/spf13/viper"
	"go.uber.org/zap"
)

var (
	redisClient *redis.Client
	redisOnce   sync.Once
)

func NewRedis() *redis.Client {
	redisOnce.Do(func() {
		redisClient = redis.NewClient(&redis.Options{
			Username: viper.GetString("redis.username"),
			Addr:     viper.GetString("redis.addr"),
			Password: viper.GetString("redis.password"),
			DB:       viper.GetInt("redis.db"),
		})
		if err := redisClient.Ping(context.Background()).Err(); err != nil {
			log.Fatal("redis ping err", zap.Error(err))
		}
	})
	return redisClient
}
