package model

import "time"

type ReimbursementPromotionalProductsRelation struct {
	ID              int       `gorm:"column:id;primaryKey;autoIncrement"`
	ReimbursementID int       `gorm:"column:reimbursement_id;default:0;comment:报销记录id--申请单id"`
	ProductID       int       `gorm:"column:product_id;default:0;comment:产品id"`
	Norm            int       `gorm:"column:norm;default:1;comment:规格"`
	Quantity        int       `gorm:"column:quantity;default:0;comment:数量"`
	PriceType       string    `gorm:"column:price_type;type:enum('include_tax','exclude_tax');comment:'include_tax'  含税,'exclude_tax'--不含税"`
	Price           float64   `gorm:"column:price;type:decimal(10,2);default:0.00;comment:价格"`
	CreatedAt       time.Time `gorm:"column:created_at;autoUpdateTime"`
	UpdatedAt       time.Time `gorm:"column:updated_at"`
}

// TableName 指定表名
func (ReimbursementPromotionalProductsRelation) TableName() string {
	return "reimbursement_promotional_products_relation"
}
