package model

import (
	"time"
)

type DevicesHistory struct {
	ID         uint       `gorm:"column:id;primaryKey;autoIncrement;comment:ID"`
	ModelID    int        `gorm:"column:model_id;comment:机型ID"`
	Model      string     `gorm:"column:model;type:varchar(16);comment:机型"`
	Barcode    string     `gorm:"column:barcode;type:varchar(32);comment:条形码"`
	Number     string     `gorm:"column:number;type:varchar(48);comment:序列号"`
	Imei       string     `gorm:"column:imei;type:varchar(16);comment:IMEI号"`
	NumberNew  string     `gorm:"column:number_new;type:varchar(48);comment:序列号（新）"`
	ImeiNew    string     `gorm:"column:imei_new;type:varchar(16);comment:IMEI号（新）"`
	Remark     string     `gorm:"column:remark;type:varchar(200);default:'';comment:备注"`
	From       int8       `gorm:"column:from;default:0;comment:来源：0-未知，1-寄修服务，2-用户反馈"`
	Status     int8       `gorm:"column:status;not null;default:0;comment:状态：0-未处理，1-已处理"`
	AddTime    *time.Time `gorm:"column:add_time;type:timestamp;default:CURRENT_TIMESTAMP;comment:添加时间"`
	UpdateTime *time.Time `gorm:"column:update_time;type:timestamp;default:0000-00-00 00:00:00;comment:更新时间"`
}

func (DevicesHistory) TableName() string {
	return "devices_history"
}
