package operation

import (
	"encoding/json"
	"marketing/internal/api/operation"
	"marketing/internal/model"
	"marketing/internal/pkg/db"
	"marketing/internal/pkg/oss"
	"marketing/internal/pkg/utils"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"gorm.io/gorm"
)

type OpArticleDao interface {
	CreateOpArticle(c *gin.Context, req operation.ReqCreateArticleParam) error
	DeleteOpArticle(c *gin.Context, id int) error
	UpdateOpArticle(c *gin.Context, req operation.ReqCreateArticleParam) error
	GetOpArticleList(c *gin.Context, req operation.ReqOpArticleParam) (infos []*operation.OpArticleInfo, total int64)
	GetOpArticleByCommentType(c *gin.Context, commentType, pageNum, pageSize int) ([]*model.OpArticle, int64)
	GetUserRoles(c *gin.Context, uid uint) (roles []string, err error)
	GetUserPublisherIds(c *gin.Context, uid uint, roles []string) (publisherIds []uint, err error)
	UpdateShareable(c *gin.Context, id int, shareable uint8) error
	UpdateEnabled(c *gin.Context, id int, enabled uint8) error
	UpdateTop(c *gin.Context, id int, top uint8) error
}

// OpArticleDaoImpl 实现 OpArticleDao 接口
type OpArticleDaoImpl struct {
	db *gorm.DB
}

// NewOpArticleDao 创建 OpArticleDao 实例
func NewOpArticleDao() OpArticleDao {
	return &OpArticleDaoImpl{
		db: db.GetDB(""),
	}
}

func (d *OpArticleDaoImpl) CreateOpArticle(c *gin.Context, req operation.ReqCreateArticleParam) error {
	// 开始数据库事务
	err := d.db.Transaction(func(tx *gorm.DB) error {
		var (
			marketingId uint // 同步id
			// categoryEnabled bool  // 标签及其父标签是否全部启动
			err error // err
		)

		// 去除文件路径中OSS URL前缀
		for i := range req.Attachment.Files {
			req.Attachment.Files[i], _ = strings.CutPrefix(req.Attachment.Files[i], operation.OssBaseUrl)
		}

		for i := range req.Attachment.Covers {
			req.Attachment.Covers[i], _ = strings.CutPrefix(req.Attachment.Covers[i], operation.OssBaseUrl)
		}

		// 如果需要同步到营销资料，则创建记录
		if req.SyncMarketing == 1 {
			marketingId, err = d.createMarketingDownload(c, tx, req)
			if err != nil {
				return err
			}
		}

		// 检查类别及其祖先是否启用
		//categoryEnabled, err = d.isAncestorsAndSelfEnabled(c, req.CategoryID)
		//if err != nil {
		//	return err
		//}

		// 将附件信息序列化为JSON格式
		attachment, err := json.Marshal(req.Attachment)
		if err != nil {
			return err
		}

		// 创建文章对象
		var article = model.OpArticle{
			Title:               req.Title,
			Content:             req.Content,
			Sleight:             req.Sleight,
			AttachmentType:      req.AttachmentType,
			Attachment:          string(attachment),
			CategoryID:          req.CategoryID,
			Shareable:           req.Shareable,
			WeworkShareable:     uint(req.WeWorkShareable),
			CommentSetting:      req.CommentSetting,
			Enabled:             req.Enabled,
			Top:                 req.Top,
			MarketingDownloadID: marketingId,
			CreatedBy:           req.CreatedBy,
			PublisherID:         req.PublisherID,
		}

		// 如果类别启用，则设置文章的类别启用标志
		//if categoryEnabled {
		//	article.CategoryEnabled = 1
		//}

		// 将文章记录插入数据库
		if err = tx.WithContext(c).Create(&article).Error; err != nil {
			return err
		}

		// 更新文章与标签的关系
		return d.tagRelationUpdate(c, tx, article.ID, req.TagIds)
	})
	if err != nil {
		return err
	}

	return nil
}

func (d *OpArticleDaoImpl) UpdateOpArticle(c *gin.Context, req operation.ReqCreateArticleParam) error {
	// 开始数据库事务
	err := d.db.Transaction(func(tx *gorm.DB) error {
		var (
			// categoryEnabled bool  // 标签及其父标签是否全部启动
			err error // err
		)

		// 去除文件路径中OSS URL前缀
		for i := range req.Attachment.Files {
			req.Attachment.Files[i], _ = strings.CutPrefix(req.Attachment.Files[i], operation.OssBaseUrl)
		}

		for i := range req.Attachment.Covers {
			req.Attachment.Covers[i], _ = strings.CutPrefix(req.Attachment.Covers[i], operation.OssBaseUrl)
		}

		// 检查类别及其祖先是否启用
		//categoryEnabled, err = d.isAncestorsAndSelfEnabled(c, req.CategoryID)
		//if err != nil {
		//	return err
		//}

		// 将附件信息序列化为JSON格式
		attachment, err := json.Marshal(req.Attachment)
		if err != nil {
			return err
		}

		uMap := make(map[string]interface{})
		uMap["title"] = req.Title
		uMap["content"] = req.Content
		uMap["sleight"] = req.Sleight
		uMap["attachment_type"] = req.AttachmentType
		uMap["attachment"] = string(attachment)
		uMap["category_id"] = req.CategoryID
		uMap["shareable"] = req.Shareable
		uMap["wework_shareable"] = uint(req.WeWorkShareable)
		uMap["comment_setting"] = req.CommentSetting
		uMap["enabled"] = req.Enabled
		uMap["top"] = req.Top
		uMap["publisher_id"] = req.PublisherID

		// 将文章记录插入数据库
		if err = tx.WithContext(c).Model(&model.OpArticle{}).Where("id = ?", req.ID).Updates(uMap).Error; err != nil {
			return err
		}

		// 更新文章与标签的关系
		return d.tagRelationUpdate(c, tx, req.ID, req.TagIds)
	})
	if err != nil {
		return err
	}

	return nil
}

func (d *OpArticleDaoImpl) DeleteOpArticle(c *gin.Context, id int) error {
	tx := d.db.Begin()

	if dbErr := tx.WithContext(c).Delete(&model.OpArticle{}, "id = ?", id).Error; dbErr != nil {
		tx.Rollback()
		return dbErr
	}

	if dbErr := tx.WithContext(c).Delete(&model.OpArticleTagRelation{}, "article_id = ?", id).Error; dbErr != nil {
		tx.Rollback()
		return dbErr
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}

	return nil
}

func (d *OpArticleDaoImpl) GetOpArticleList(c *gin.Context, req operation.ReqOpArticleParam) (infos []*operation.OpArticleInfo, total int64) {
	query := d.db.WithContext(c).Model(&model.OpArticle{}).Where("op_article.deleted_at is null")

	if req.PageNum <= 0 {
		req.PageNum = 1
	}

	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	if req.CategoryID > 0 {
		query.Where("op_article.category_id = ?", req.CategoryID)
	}

	if req.Keyword != "" {
		query.Where("op_article.title like ? or op_article.content like ? or op_article.sleight like ?", "%"+req.Keyword+"%", "%"+req.Keyword+"%", "%"+req.Keyword+"%")
	}

	if req.Enabled >= 0 {
		query.Where("op_article.enabled", req.Enabled)
	}

	if req.Top >= 0 {
		query.Where("op_article.top", req.Top)
	}

	if req.CreatorID > 0 {
		query.Where("op_article.created_by", req.CreatorID)
	}

	query.Count(&total)
	if total == 0 {
		return
	}

	// 统计数量不用排序
	if req.OrderBy != "" {
		query.Order(req.OrderBy + " " + req.Order)
	} else {
		query.Order("created_at desc")
	}

	var list []struct {
		model.OpArticle
		Creator       string `gorm:"column:creator"`
		CreatorID     uint   `gorm:"column:creator_id"`
		CategoryName  string `gorm:"column:category_name"`
		PublisherName string `gorm:"column:publisher_name"`
	}

	err := query.Joins("left join admin_users on op_article.created_by = admin_users.id").
		Joins("left join op_publisher on op_article.publisher_id = op_publisher.id").
		Joins("left join op_article_category on op_article.category_id = op_article_category.id").
		Select("op_article.id,title,content,comment_setting,category_id,sleight,shareable,op_article.enabled,op_article.top," +
			"attachment_type,attachment,num_downloads,op_article.created_at,op_article.updated_at," +
			"admin_users.name creator,admin_users.id creator_id,op_article_category.name category_name,op_publisher.id publisher_id,op_publisher.name publisher_name").
		Offset((req.PageNum - 1) * req.PageSize).Limit(req.PageSize).
		Scan(&list).Error
	if err != nil {
		return
	}

	for _, l := range list {
		var attachment operation.OpArticleAttachment
		_ = json.Unmarshal([]byte(l.Attachment), &attachment)

		//var httpPrefixReg = regexp.MustCompile(`(?i)^https?://`)
		for i := range attachment.Files {
			if !oss.HttpPrefixReg.MatchString(attachment.Files[i]) {
				attachment.Files[i] = operation.OssBaseUrl + attachment.Files[i]
			}
		}
		for i := range attachment.Covers {
			if !oss.HttpPrefixReg.MatchString(attachment.Covers[i]) {
				attachment.Covers[i] = operation.OssBaseUrl + attachment.Covers[i]
			}
		}

		if len(attachment.Files) == 0 {
			attachment.Files = make([]string, 0)
		}

		if len(attachment.Covers) == 0 {
			attachment.Covers = make([]string, 0)
		}

		infos = append(infos, &operation.OpArticleInfo{
			ID:             l.ID,
			Title:          l.Title,
			Content:        l.Content,
			CommentSetting: l.CommentSetting,
			CategoryID:     l.CategoryID,
			CategoryName:   l.CategoryName,
			Sleight:        l.Sleight,
			Shareable:      l.Shareable,
			NumDownloads:   l.NumDownloads,
			Creator:        l.Creator,
			CreatorID:      l.CreatorID,
			PublisherId:    int(l.PublisherID),
			PublisherName:  l.PublisherName,
			AttachmentType: l.AttachmentType,
			Attachment:     attachment,
			Enabled:        l.Enabled,
			Top:            l.Top,
			CreatedAt:      utils.GetTimeStr(l.CreatedAt),
			UpdatedAt:      utils.GetTimeStr(l.UpdatedAt),
		})
	}

	return
}

func (d *OpArticleDaoImpl) GetOpArticleByCommentType(c *gin.Context, commentType, pageNum, pageSize int) ([]*model.OpArticle, int64) {
	query := d.db.WithContext(c).Model(&model.OpArticle{}).Select("id,title,created_at").Where("op_article.deleted_at is null")

	if commentType > 0 {
		query = query.Where("id in (select article_id from op_article_comment group by article_id)")
	}

	data, total := utils.PaginateQueryV1(query.Order("id desc"), pageNum, pageSize, new([]*model.OpArticle))

	return *data, total
}

func (d *OpArticleDaoImpl) GetUserRoles(c *gin.Context, uid uint) (roles []string, err error) {
	err = d.db.WithContext(c).Model(&model.AdminRoleUser{}).
		Joins("left join admin_roles on admin_role_users.role_id = admin_roles.id").
		Where("admin_role_users.user_id = ?", uid).
		Pluck("admin_roles.slug", &roles).Error
	if err != nil {
		roles = make([]string, 0)
	}

	return
}

func (d *OpArticleDaoImpl) GetUserPublisherIds(c *gin.Context, uid uint, roles []string) (publisherIds []uint, err error) {
	query := d.db.WithContext(c).Table("op_publisher").
		Select("op_publisher.id")

	// 非超管,只能获取与自己账号关联的运营号
	if !lo.Contains(roles, "administrator") {
		query.Joins("join op_publisher_user on op_publisher.id = op_publisher_user.publisher_id").
			Where("op_publisher_user.user_id = ?", uid)
	}

	err = query.Pluck("op_publisher.id", &publisherIds).Error
	if err != nil {
		publisherIds = make([]uint, 0)
	}

	return
}

func (d *OpArticleDaoImpl) createMarketingDownload(c *gin.Context, tx *gorm.DB, req operation.ReqCreateArticleParam) (uint, error) {
	var marketing = model.MarketingDownload{
		Name:          req.Title,
		Description:   req.Content,
		Category:      req.MarketingCategoryId,
		Status:        req.MarketingEnabled,
		SyncOperation: 1,
		OpShareable:   req.Shareable,
	}

	if req.Attachment != nil {
		if len(req.Attachment.Files) > 0 {
			marketing.Path = req.Attachment.Files[0]
		} else {
			marketing.Path = ""
		}
		if req.AttachmentType == operation.ArticleAttachmentTypeImage {
			preview, _ := json.Marshal(req.Attachment.Files)
			marketing.Preview = string(preview)
		} else {
			preview, _ := json.Marshal(req.Attachment.Covers)
			marketing.Preview = string(preview)
		}
	}

	if err := tx.WithContext(c).Create(&marketing).Error; err != nil {
		return 0, err
	}
	return marketing.ID, nil
}

func (d *OpArticleDaoImpl) isAncestorsAndSelfEnabled(c *gin.Context, id uint) (bool, error) {
	var category model.OpArticleCategory
	if err := d.db.WithContext(c).Table("op_article_category").Where("id = ?", id).Select("ancestor").First(&category).Error; err != nil {
		return false, err
	}
	ancestors := strings.Split(category.Ancestor, ",")
	ancestors = append(ancestors, strconv.Itoa(int(id)))
	var count int64
	if err := d.db.WithContext(c).Table("op_article_category").Where("id IN (?)", ancestors).Where("enabled", 0).Count(&count).Error; err != nil {
		return false, err
	}
	return count == 0, nil
}

func (d *OpArticleDaoImpl) tagRelationUpdate(c *gin.Context, tx *gorm.DB, aid uint, tagIds []uint) error {
	err := tx.WithContext(c).Model(&model.OpArticleTagRelation{}).Where("article_id = ?", aid).Delete(&model.OpArticleTagRelation{}).Error
	if err != nil {
		return err
	}

	tags := make([]model.OpArticleTagRelation, 0)
	for _, tagId := range tagIds {
		tags = append(tags, model.OpArticleTagRelation{
			ArticleID: aid,
			TagID:     tagId,
		})
	}

	return tx.WithContext(c).Model(&model.OpArticleTagRelation{}).CreateInBatches(tags, 100).Error
}

// UpdateShareable 更新文章分享状态
func (d *OpArticleDaoImpl) UpdateShareable(c *gin.Context, id int, shareable uint8) error {
	return d.db.WithContext(c).Model(&model.OpArticle{}).Where("id = ?", id).Update("shareable", shareable).Error
}

// UpdateEnabled 更新文章发布状态
func (d *OpArticleDaoImpl) UpdateEnabled(c *gin.Context, id int, enabled uint8) error {
	return d.db.WithContext(c).Model(&model.OpArticle{}).Where("id = ?", id).Update("enabled", enabled).Error
}

// UpdateTop 更新文章置顶状态
func (d *OpArticleDaoImpl) UpdateTop(c *gin.Context, id int, top uint8) error {
	return d.db.WithContext(c).Model(&model.OpArticle{}).Where("id = ?", id).Update("top", top).Error
}
