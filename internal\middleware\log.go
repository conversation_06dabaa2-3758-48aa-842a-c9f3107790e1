package middleware

import (
	"bytes"
	"encoding/json"
	"io"
	"marketing/internal/pkg/log"
	"marketing/internal/pkg/utils"
	"net/http"
	"time"

	ginzap "github.com/gin-contrib/zap"
	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

func SetBody() gin.HandlerFunc {
	return func(c *gin.Context) {
		reqBody := captureRequestData(c)
		c.Set("reqBody", reqBody)
		c.Set("reqTime", time.Now().Format("2006-01-02 15:04:05"))
		c.Set("reqTime1", time.Now().Format(time.RFC3339))
		c.Next()
	}
}

func Logger() gin.HandlerFunc {
	return ginzap.GinzapWithConfig(log.Default(), &ginzap.Config{
		UTC:        false,
		TimeFormat: time.DateTime,
		Context: ginzap.Fn(func(c *gin.Context) []zapcore.Field {
			fields := []zapcore.Field{}
			// log trace and span ID
			if trace.SpanFromContext(c.Request.Context()).SpanContext().IsValid() {
				fields = append(fields, zap.String("trace_id", trace.SpanFromContext(c.Request.Context()).SpanContext().TraceID().String()))
				fields = append(fields, zap.String("span_id", trace.SpanFromContext(c.Request.Context()).SpanContext().SpanID().String()))
			}

			// log request body
			body, _ := c.Get("reqBody")
			bodyStr := make(map[string]interface{})
			err := json.Unmarshal([]byte(body.(string)), &bodyStr)
			if err != nil {
				fields = append(fields, zap.Any("reqBody", body))
			} else {
				fields = append(fields, zap.Any("reqBody", bodyStr))
			}
			// log request time
			reqTime, _ := c.Get("reqTime")
			fields = append(fields, zap.String("reqTime", reqTime.(string)))
			reqTime1, _ := c.Get("reqTime1")
			fields = append(fields, zap.String("reqTime1", reqTime1.(string)))

			// log selected request headers
			selectedHeaders := []string{"Content-Type", "Authorization", "X-Gate-Type", "X-Request-ID"} // 这里可以根据需要修改要记录的请求头字段
			headers := make(map[string]string)
			for _, key := range selectedHeaders {
				if value := c.Request.Header.Get(key); value != "" {
					headers[key] = value
				}
			}
			fields = append(fields, zap.Any("headers", headers))

			//log writer body
			responseBody, _ := c.Get("responseBody")
			fields = append(fields, zap.Any("responseBody", responseBody))

			return fields
		}),
	})
}

// captureRequestData 捕获请求数据
func captureRequestData(c *gin.Context) string {

	params := make(map[string]interface{})

	// 获取 URL 参数
	for k, v := range c.Request.URL.Query() {
		if len(v) == 1 {
			params[k] = v[0]
		} else {
			params[k] = v
		}
	}

	// 获取 POST 表单数据
	if c.Request.Method == http.MethodPost {
		c.Request.ParseForm()
		for k, v := range c.Request.PostForm {
			if len(v) == 1 {
				params[k] = v[0]
			} else {
				params[k] = v
			}
			if k == "username" {
				c.Set("username", v[0])
			}
			if k == "status" {
				c.Set("status", v[0])
			}
		}
	}

	// 获取 JSON 数据
	if c.Request.Body != nil {
		bodyBytes, _ := io.ReadAll(c.Request.Body)

		c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

		var jsonData map[string]interface{}
		if json.Unmarshal(bodyBytes, &jsonData) == nil {
			for k, v := range jsonData {
				params[k] = v
				// 检查参数名是否为 username
				if k == "username" {
					c.Set("username", v)
				}
				if k == "status" {
					c.Set("status", v)
				}
			}
		}
	}

	return utils.JSONMaskFields(
		utils.ToJSON(params),
		[]string{"password", "token", "secret"},
	)
}
