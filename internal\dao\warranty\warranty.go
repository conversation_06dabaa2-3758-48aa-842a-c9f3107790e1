package warranty

import (
	"marketing/internal/model"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type ListParams struct {
	Barcode        string `json:"barcode"`
	StartTime      string `json:"start_time"`
	EndTime        string `json:"end_time"`
	Page           int    `json:"page"`
	PageSize       int    `json:"page_size"`
	Status         int    `json:"status"`
	ModelID        []uint `json:"model_id"`
	IsActivated    *bool  `json:"is_activated"`
	ActivatedStart string `json:"activated_start"`
	ActivatedEnd   string `json:"activated_end"`
	EndpointID     uint   `json:"endpoint_id"` // 终端ID
	AgencyID       uint   `json:"agency_id"`   // 代理ID
}
type StatParams struct {
	StartTime  string `json:"start_time"`
	EndTime    string `json:"end_time"`
	ModelID    []uint `json:"model_id"`
	EndpointID uint   `json:"endpoint_id"`
	Status     *int   `json:"status"`
	Realsale   *int   `json:"realsale"`
}

type StatCountAndAmount struct {
	Count  int64   `json:"count"`
	Amount float64 `json:"amount"`
}

type StatModelCountAndAmount struct {
	ModelID uint    `json:"model_id"`
	Model   string  `json:"model"`
	Count   int64   `json:"count"`
	Amount  float64 `json:"amount"`
}

type InterfaceWarranty interface {
	GetLists(c *gin.Context, param *ListParams) ([]*model.Warranty, int64, error)
	GetWarranties(c *gin.Context, param *ListParams) ([]*model.Warranty, error)
	// StatCountAndAmount 统计保修数量和金额
	StatCountAndAmount(c *gin.Context, param *StatParams) (StatCountAndAmount, error)
	StatModelCountAndAmount(c *gin.Context, param *StatParams) ([]*StatModelCountAndAmount, error)
	StatReturnCountAndAmount(c *gin.Context, param *StatParams) (StatCountAndAmount, error)
	// GetByPhone 根据手机号查询保修信息
	GetByPhone(c *gin.Context, phone string) ([]*model.Warranty, error)
}

type warranty struct {
	db *gorm.DB
}

func NewWarrantyDao(db *gorm.DB) InterfaceWarranty {
	return &warranty{
		db: db,
	}
}

func (w *warranty) GetLists(c *gin.Context, param *ListParams) ([]*model.Warranty, int64, error) {
	var warrantyData []*model.Warranty

	query := w.db.WithContext(c).Table("warranty")

	if param.Barcode != "" {
		query = query.Where("barcode = ?", param.Barcode)
	}

	if param.StartTime != "" {
		query = query.Where("buy_date >= ?", param.StartTime)
	}

	if param.EndTime != "" {
		query = query.Where("buy_date <= ?", param.EndTime)
	}
	if param.ActivatedStart != "" {
		query = query.Where("activated_at >=?", param.ActivatedStart)
	}
	if param.ActivatedEnd != "" {
		query = query.Where("activated_at <=?", param.ActivatedEnd)
	}

	if param.Status > 0 {
		query = query.Where("status = ?", param.Status)
	}
	if len(param.ModelID) > 0 {
		query = query.Where("model_id IN ?", param.ModelID)
	}
	if param.IsActivated != nil && *param.IsActivated {
		query = query.Where("activated_id > 0")
	}
	count := int64(0)
	err := query.Count(&count).Error
	if err != nil {
		return warrantyData, 0, err
	}

	offset := (param.Page - 1) * param.PageSize
	query = query.Offset(offset).Limit(param.PageSize)

	err = query.Find(&warrantyData).Error
	if err != nil {
		return warrantyData, 0, err
	}

	return warrantyData, count, nil
}

func (w *warranty) GetWarranties(c *gin.Context, param *ListParams) ([]*model.Warranty, error) {
	var warrantyData []*model.Warranty

	query := w.db.WithContext(c).Table("warranty as w").
		Joins("LEFT JOIN endpoint e ON w.endpoint = e.id")

	if param.Barcode != "" {
		query = query.Where("w.barcode = ?", param.Barcode)
	}

	if param.StartTime != "" {
		query = query.Where("w.buy_date >= ?", param.StartTime)
	}

	if param.EndTime != "" {
		query = query.Where("w.buy_date <= ?", param.EndTime)
	}

	if param.Status > 0 {
		query = query.Where("w.status = ?", param.Status)
	}
	if len(param.ModelID) > 0 {
		query = query.Where("w.model_id IN ?", param.ModelID)
	}
	if param.EndpointID > 0 {
		query = query.Where("w.endpoint = ?", param.EndpointID)
	}
	if param.AgencyID > 0 {
		query = query.Where("e.top_agency = ? or e.second_agency = ?", param.AgencyID, param.AgencyID)
	}

	query = query.Where("w.status > ?", 0)
	err := query.Order("w.activated_at_old desc,w.id desc").Find(&warrantyData).Error
	if err != nil {
		return warrantyData, err
	}

	return warrantyData, nil
}

func (w *warranty) StatCountAndAmount(c *gin.Context, param *StatParams) (StatCountAndAmount, error) {
	var result StatCountAndAmount
	query := w.db.WithContext(c).Table("warranty")

	if param.StartTime != "" {
		query = query.Where("buy_date >= ?", param.StartTime)
	}
	if param.EndTime != "" {
		query = query.Where("buy_date <= ?", param.EndTime)
	}
	if len(param.ModelID) > 0 {
		query = query.Where("model_id IN ?", param.ModelID)
	}
	if param.Status != nil {
		query = query.Where("status = ?", *param.Status)
	}
	if param.Realsale != nil {
		query = query.Where("realsale = ?", *param.Realsale)
	}
	if param.EndpointID > 0 {
		query = query.Where("endpoint = ?", param.EndpointID)
	}

	err := query.Select("COUNT(*) as count, SUM(customer_price) as amount").Scan(&result).Error
	if err != nil {
		return result, err
	}

	return result, nil
}

func (w *warranty) StatModelCountAndAmount(c *gin.Context, param *StatParams) ([]*StatModelCountAndAmount, error) {
	var result []*StatModelCountAndAmount
	query := w.db.WithContext(c).Table("warranty")

	if param.StartTime != "" {
		query = query.Where("buy_date >= ?", param.StartTime)
	}
	if param.EndTime != "" {
		query = query.Where("buy_date <= ?", param.EndTime)

	}
	if len(param.ModelID) > 0 {
		query = query.Where("model_id IN ?", param.ModelID)
	}
	if param.Status != nil {
		query = query.Where("status = ?", *param.Status)
	}
	if param.Realsale != nil {
		query = query.Where("realsale = ?", *param.Realsale)
	}

	err := query.Group("model_id").Select("model_id, model, COUNT(*) as count, SUM(customer_price) as amount").Find(&result).Error
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (w *warranty) StatReturnCountAndAmount(c *gin.Context, param *StatParams) (StatCountAndAmount, error) {
	var result StatCountAndAmount
	query := w.db.WithContext(c).Table("warranty_return AS wr").
		Select(" COUNT(*) as count, SUM(wr.amount) as amount").
		Joins("JOIN warranty AS w ON wr.warranty_id = w.id")

	if param.StartTime != "" {
		query = query.Where("wr.return_at >= ?", param.StartTime)
	}
	if param.EndTime != "" {
		query = query.Where("wr.return_at <= ?", param.EndTime)
	}
	if len(param.ModelID) > 0 {
		query = query.Where("w.model_id IN ?", param.ModelID)
	}

	err := query.Select("COUNT(*) as count, SUM(w.customer_price) as amount").Scan(&result).Error
	if err != nil {
		return result, err
	}

	return result, nil
}

// GetByPhone 根据手机号查询保修信息 - 第三方接口使用
func (w *warranty) GetByPhone(c *gin.Context, phone string) ([]*model.Warranty, error) {
	var warranties []*model.Warranty

	// 查询数据库
	err := w.db.WithContext(c).
		Select("barcode, number, imei, salesman, customer_sex, customer_name, customer_phone, "+
			"customer_addr, student_name, student_school, student_sex, student_grade, "+
			"student_birthday, buy_date, product_date, model, purchase_way, recommender, recommender_phone").
		Where("customer_phone = ? AND status = ?", phone, 1).
		Find(&warranties).Error

	if err != nil {
		return nil, err
	}

	return warranties, nil
}
