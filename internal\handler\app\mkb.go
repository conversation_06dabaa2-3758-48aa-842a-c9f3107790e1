package app

import (
	"marketing/internal/api/mkb"
	"marketing/internal/api/prototype"
	"marketing/internal/handler"
	svc "marketing/internal/service/mkb"

	"github.com/gin-gonic/gin"
)

type MkbHandler interface {
	GetWarrantyLists(c *gin.Context)
	GetPrototypeLists(c *gin.Context)
	GetActivatedLists(c *gin.Context)
	PrototypeOut(c *gin.Context)
}

type mkbHandler struct {
	mkbService svc.MkbService
}

func NewMkbHandler(mkbService svc.MkbService) MkbHandler {
	return &mkbHandler{
		mkbService: mkbService,
	}
}

func (h *mkbHandler) GetWarrantyLists(c *gin.Context) {
	var req mkb.WarrantyReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	req.SetDefaults()
	lists, count, err := h.mkbService.GetWarrantyLists(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"data":  lists,
		"total": count,
	})
}

func (h *mkbHandler) GetPrototypeLists(c *gin.Context) {
	var req mkb.PrototypeReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	req.SetDefaults()
	lists, count, err := h.mkbService.GetPrototypeLists(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"data":  lists,
		"total": count,
	})
}

func (h *mkbHandler) GetActivatedLists(c *gin.Context) {
	var req mkb.WarrantyReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	req.SetDefaults()
	lists, count, err := h.mkbService.GetActivatedLists(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"data":  lists,
		"total": count,
	})
}

func (h *mkbHandler) PrototypeOut(c *gin.Context) {
	var req prototype.PrototypeOutReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	err := h.mkbService.PrototypeOut(c, req.Barcode)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}
