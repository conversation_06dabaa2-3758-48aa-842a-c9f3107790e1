package dao

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"marketing/internal/model"
	"marketing/internal/pkg/db"
)

type ModelCategoryDao interface {
	CreateModelCategory(c *gin.Context, category *model.TModelCategory) error
	DeleteModelCategory(c *gin.Context, id int) error
	UpdateModelCategory(c *gin.Context, id int, uMap map[string]interface{}) error
	GetAllModelCategory(c *gin.Context) (list []*model.TModelCategory)
	GetModelCategoryById(c *gin.Context, id int) *model.TModelCategory
	GetModelCategoryByIds(c *gin.Context, ids []int) (list []*model.TModelCategory)
	GetAllRepairModelCategory(c *gin.Context) (list []*model.RepairMachineCategory)
}

// ModelCategoryDaoImpl 实现 ModelCategoryDao 接口
type ModelCategoryDaoImpl struct {
	db *gorm.DB
}

// NewModelCategoryDao 创建 ModelCategoryDao 实例
func NewModelCategoryDao() ModelCategoryDao {
	return &ModelCategoryDaoImpl{
		db: db.GetDB(""),
	}
}

func (d *ModelCategoryDaoImpl) CreateModelCategory(c *gin.Context, category *model.TModelCategory) error {
	return d.db.WithContext(c).Create(category).Error
}

func (d *ModelCategoryDaoImpl) DeleteModelCategory(c *gin.Context, id int) error {
	return d.db.WithContext(c).Delete(&model.TModelCategory{}, "id = ?", id).Error
}

func (d *ModelCategoryDaoImpl) UpdateModelCategory(c *gin.Context, id int, uMap map[string]interface{}) error {
	return d.db.WithContext(c).Model(&model.TModelCategory{}).Where("id = ?", id).Updates(uMap).Error
}

func (d *ModelCategoryDaoImpl) GetAllModelCategory(c *gin.Context) (list []*model.TModelCategory) {
	d.db.WithContext(c).Model(&model.TModelCategory{}).Find(&list)
	return
}

func (d *ModelCategoryDaoImpl) GetModelCategoryById(c *gin.Context, id int) *model.TModelCategory {
	var category model.TModelCategory
	err := d.db.WithContext(c).Model(&model.TModelCategory{}).Where("id = ?", id).First(&category).Error
	if err != nil {
		return nil
	}
	return &category
}

func (d *ModelCategoryDaoImpl) GetModelCategoryByIds(c *gin.Context, ids []int) (list []*model.TModelCategory) {
	d.db.WithContext(c).Model(&model.TModelCategory{}).Where("id in (?)", ids).Find(&list)
	return
}

func (d *ModelCategoryDaoImpl) GetAllRepairModelCategory(c *gin.Context) (list []*model.RepairMachineCategory) {
	db.GetDB("post_repair").WithContext(c).Model(&model.RepairMachineCategory{}).Find(&list)
	return
}
