package third_party

import (
	"crypto/md5"
	"crypto/rand"
	"fmt"
	"marketing/internal/api/third_party"
	"marketing/internal/consts"
	"marketing/internal/dao/app_auth"
	"marketing/internal/dao/auth"
	"marketing/internal/model"
	appError "marketing/internal/pkg/errors"
	"marketing/internal/pkg/log"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

const (
	ThirdPartyTokenExpiresIn = 30 * time.Minute // 第三方token过期时间24小时
	MaxRetryCount            = 5                // 生成唯一token的最大重试次数
)

// ThirdPartyAuthServiceInterface 第三方认证服务接口
type ThirdPartyAuthServiceInterface interface {
	// Authenticate 第三方认证
	Authenticate(c *gin.Context, req third_party.ThirdPartyAuthReq) (*third_party.ThirdPartyAuthResp, error)
	// AuthenticateV2 第三方认证（合力亿捷）
	AuthenticateV2(c *gin.Context, req third_party.ThirdPartyAuthReq) (*third_party.ThirdPartyAuthResp, error)
	// ValidateToken 验证第三方token
	ValidateToken(c *gin.Context, token string) (bool, error)
	// ValidateAppKey 验证应用ID和应用密钥
	ValidateAppKey(c *gin.Context, appID, appKey string) (bool, error)
	// GetAppAuth 获取应用认证信息
	GetAppAuth(c *gin.Context, appID string) (*model.AppAuth, error)
}

type thirdPartyAuthService struct {
	db           *gorm.DB
	appAuthDao   app_auth.AppAuthDao
	appAuthCache app_auth.AppAuthCacheInterface    //appid、appkey缓存
	authCache    auth.ThirdPartyAuthCacheInterface // 第三方 token 缓存
}

// NewThirdPartyAuthService 创建第三方认证服务实例
func NewThirdPartyAuthService(db *gorm.DB, appAuthDao app_auth.AppAuthDao, appAuthCache app_auth.AppAuthCacheInterface, authCache auth.ThirdPartyAuthCacheInterface) ThirdPartyAuthServiceInterface {
	return &thirdPartyAuthService{
		db:           db,
		appAuthDao:   appAuthDao,
		appAuthCache: appAuthCache,
		authCache:    authCache,
	}
}

// Authenticate 生成第三方认证（合力亿捷）
func (s *thirdPartyAuthService) Authenticate(c *gin.Context, req third_party.ThirdPartyAuthReq) (*third_party.ThirdPartyAuthResp, error) {
	//先查缓存
	appAuth, err := s.GetAppAuth(c, req.AppID)
	if err != nil {
		return nil, appError.NewErr("应用认证失败")
	}

	// 2. 验证时间戳（防重放攻击）
	timestamp := req.TimestampKey
	// Convert the millisecond timestamp to seconds
	timestampInSeconds := req.TimestampKey / 1000

	// Get the current time in seconds
	now := time.Now().Unix()

	// Check if the timestamp is within a 5-minute window
	if now-timestampInSeconds > 300 || timestampInSeconds-now > 300 {
		return nil, appError.NewErr("时间戳无效")
	}

	// 3. 验证加密token
	expectedToken := s.generateEncryptionToken(strconv.FormatInt(timestamp, 10), appAuth.AppKey)
	if req.EncryptionToken != expectedToken {
		log.Error("加密token验证失败",
			zap.String("expected", expectedToken),
			zap.String("received", req.EncryptionToken),
			zap.String("appID", req.AppID))
		return nil, appError.NewErr("认证失败")
	}

	// 4. 生成唯一访问token（防重复）
	accessToken, err := s.generateUniqueAccessToken(c, appAuth)
	if err != nil {
		log.Error("生成访问token失败", zap.Error(err))
		return nil, appError.NewErr("生成token失败")
	}

	// 5. 将token存储到专用缓存中（避免与用户认证冲突）
	err = s.authCache.SetToken(c, accessToken, int64(appAuth.ID), ThirdPartyTokenExpiresIn)
	if err != nil {
		log.Error("存储token到缓存失败", zap.Error(err))
		return nil, appError.NewErr("存储token失败")
	}

	// 6. 返回纯token（不带前缀）
	return &third_party.ThirdPartyAuthResp{
		Token: accessToken,
	}, nil
}

// AuthenticateV2 生成第三方认证（合力亿捷）
func (s *thirdPartyAuthService) AuthenticateV2(c *gin.Context, req third_party.ThirdPartyAuthReq) (*third_party.ThirdPartyAuthResp, error) {
	//先查缓存
	appAuth, err := s.GetAppAuth(c, req.AppID)
	if err != nil {
		return nil, appError.NewErr("应用认证失败")
	}
	if appAuth.AppKey != req.EncryptionToken {
		log.Error("应用密钥验证失败",
			zap.String("appID", req.AppID),
			zap.String("received", req.EncryptionToken))
		return nil, appError.NewErr("应用密钥不正确")
	}

	// 4. 生成唯一访问token（防重复）
	accessToken, err := s.generateUniqueAccessToken(c, appAuth)
	if err != nil {
		log.Error("生成访问token失败", zap.Error(err))
		return nil, appError.NewErr("生成token失败")
	}

	// 5. 将token存储到专用缓存中（避免与用户认证冲突）
	err = s.authCache.SetToken(c, accessToken, int64(appAuth.ID), ThirdPartyTokenExpiresIn)
	if err != nil {
		log.Error("存储token到缓存失败", zap.Error(err))
		return nil, appError.NewErr("存储token失败")
	}

	// 6. 返回纯token（不带前缀）
	return &third_party.ThirdPartyAuthResp{
		Token: accessToken,
	}, nil
}

// ValidateAppKey 验证应用ID和应用密钥
func (s *thirdPartyAuthService) ValidateAppKey(c *gin.Context, appID, appKey string) (bool, error) {
	appAuth, err := s.GetAppAuth(c, appID)
	if err != nil {
		return false, appError.NewErr(appID + ":应用密钥校验失败")
	}

	// 2. 验证应用密钥是否匹配
	if appAuth.AppKey != appKey {
		log.Warn("应用密钥不匹配", zap.String("appID", appID), zap.String("appKey", appKey))
		return false, appError.NewErr("应用密钥不正确")
	}

	return true, nil
}

func (s *thirdPartyAuthService) GetAppAuth(c *gin.Context, appID string) (*model.AppAuth, error) {
	// 从缓存获取应用认证信息
	appAuth, err := s.appAuthCache.GetAppAuthByKey(c, appID)
	if err != nil {
		log.Error("从缓存获取应用认证信息失败", zap.Error(err), zap.String("appID", appID))
	}

	if appAuth == nil {
		// 从数据库获取应用认证信息
		appAuth, err = s.appAuthDao.GetByAppID(c, appID)
		if err != nil {
			log.Error("获取应用认证信息失败", zap.Error(err), zap.String("appID", appID))
			return nil, appError.NewErr("应用不存在或已禁用")
		}
		// 缓存应用认证信息
		err = s.appAuthCache.SetAppAuth(c, appAuth, appID, consts.AppSystemExpiresIn)
		if err != nil {
			log.Error("缓存应用认证信息失败", zap.Error(err), zap.String("appID", appID))
		}
	}
	return appAuth, nil
}

// ValidateToken 验证第三方token
func (s *thirdPartyAuthService) ValidateToken(c *gin.Context, token string) (bool, error) {
	// 1. 从专用缓存中获取token对应的应用ID
	appID, err := s.authCache.GetToken(c, token)
	if err != nil {
		log.Error("从缓存获取token失败", zap.Error(err), zap.String("token", token))
		return false, appError.NewErr("token无效或已过期")
	}
	if appID == 0 {
		log.Warn("token不存在或已过期", zap.String("token", token))
		return false, appError.NewErr("token不存在或已过期")
	}

	return true, nil
}

// generateEncryptionToken 生成加密token
func (s *thirdPartyAuthService) generateEncryptionToken(timestampKey, appSecret string) string {
	data := timestampKey + appSecret
	return fmt.Sprintf("%x", md5.Sum([]byte(data)))
}

// generateUniqueAccessToken 生成唯一访问token（防重复）
func (s *thirdPartyAuthService) generateUniqueAccessToken(c *gin.Context, appAuth *model.AppAuth) (string, error) {
	for i := 0; i < MaxRetryCount; i++ {
		// 生成随机token
		token, err := s.generateRandomToken(appAuth)
		if err != nil {
			continue
		}

		// 检查token是否已存在
		if !s.authCache.CheckTokenExists(c, token) {
			// token不存在，可以使用
			return token, nil
		}

		// token已存在，重新生成
		log.Warn("生成的token已存在，重新生成", zap.String("token", token), zap.Int("retry", i+1))
	}

	return "", fmt.Errorf("生成唯一token失败，已重试%d次", MaxRetryCount)
}

// generateRandomToken 生成随机token
func (s *thirdPartyAuthService) generateRandomToken(appAuth *model.AppAuth) (string, error) {
	// 生成16字节随机数
	randomBytes := make([]byte, 16)
	_, err := rand.Read(randomBytes)
	if err != nil {
		return "", err
	}

	// 结合应用信息和时间戳生成token
	data := fmt.Sprintf("%s_%d_%x_%s",
		appAuth.AppID,
		time.Now().UnixNano(),
		randomBytes,
		appAuth.AppKey)

	token := fmt.Sprintf("%x", md5.Sum([]byte(data)))
	return token, nil
}
