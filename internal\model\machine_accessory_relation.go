package model

import (
	"time"
)

type MachineAccessoryRelation struct {
	Id            int       `json:"id" gorm:"column:id"`                     // id
	ModelId       int       `json:"model_id" gorm:"column:model_id"`         // 机型id
	AccessoryId   int       `json:"accessory_id" gorm:"column:accessory_id"` // 配件id
	AccessoryName string    `json:"accessory_name" gorm:"-"`                 // 配件名称
	Price         float64   `json:"price" gorm:"column:price"`               // 价格
	CreatedAt     time.Time `json:"-" gorm:"created_at"`                     // CreatedAt 创建时间
	CreateTime    string    `json:"create_time" gorm:"-"`                    // 创建时间
	UpdatedAt     time.Time `json:"-" gorm:"updated_at"`                     // UpdatedAt 修改时间
	UpdateTime    string    `json:"update_time" gorm:"-"`                    // 修改时间
}

func (MachineAccessoryRelation) TableName() string {
	return "machine_accessory_relation"
}
