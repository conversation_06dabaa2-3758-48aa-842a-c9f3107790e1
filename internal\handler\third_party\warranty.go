package third_party

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/handler"
	"marketing/internal/pkg/cryptor"
	"marketing/internal/pkg/errors"
	"marketing/internal/pkg/utils"
	"marketing/internal/service/warranty"
	"os"
)

// WarrantyHandler 保修处理器
type WarrantyHandler interface {
	// GetByPhone 根据手机号查询保修信息
	GetByPhone(c *gin.Context) // 根据手机号查询保修信息
}

// WarrantyHandler 第三方warranty处理器
type warrantyHandler struct {
	warrantyService warranty.InterfaceWarranty
}

// NewWarrantyHandler 创建warranty处理器实例
func NewWarrantyHandler(warrantyService warranty.InterfaceWarranty) WarrantyHandler {
	return &warrantyHandler{
		warrantyService: warrantyService,
	}
}

// GetByPhone 根据手机号查询保修信息
// @Router /third_party/warranty/get-by-phone [get]
func (h *warrantyHandler) GetByPhone(c *gin.Context) {
	phoneStr := c.Query("phone")
	// 获取加密参数
	aesKey := os.Getenv("HOLLY_WARRANTY_AES_SECRET")
	data := cryptor.AesCbcDecrypt(phoneStr, aesKey)

	// 验证手机号格式
	if data == "" {
		handler.Error(c, errors.NewErr("解密失败"))
		return
	}
	phone := data
	if !utils.IsValidPhone(phone) {
		handler.Error(c, errors.NewErr("电话格式错误"))
		return
	}

	// 调用DAO层查询数据
	warranties, err := h.warrantyService.GetByPhone(c, phone)
	if err != nil {
		handler.Error(c, errors.NewErr("查询失败"))
		return
	}

	handler.Success(c, warranties)
}
