package errors

import (
	"bytes"
	"errors"
	"fmt"
	"strings"
)

// Error is error with info
type Error struct {
	Code    int
	Message string
	Err     error
	Stack   string
}

// SystemError 系统错误类型
type SystemError struct {
	Err   error
	Stack string
}

// New create error
func New(code int) *Error {
	return &Error{
		Code:  code,
		Stack: LogStack(2, 0),
	}
}

func NewErr(message string) *Error {
	if message == "" {
		message = "unknown error"
	}
	return &Error{
		Code:    0,
		Message: message,
		Stack:   LogStack(2, 0),
	}
}

// Error return error with info
func (e *Error) Error() string {
	return e.Message
}

// WithMsg with message
func (e *Error) WithMsg(message string) *Error {
	e.Message = message
	return e
}

// WithError with original error
func (e *Error) WithError(err error) *Error {
	e.Err = err
	// 如果原始错误是Error类型，保留其堆栈信息
	if err != nil {
		var err2 *Error
		if errors.As(err, &err2) {
			e.Stack = err2.Stack
		} else {
			// 如果是其他类型错误，记录当前调用位置
			e.Stack = LogStack(2, 0)
		}
	} else {
		// 如果没有原始错误，记录当前调用位置
		e.Stack = LogStack(2, 0)
	}
	return e
}

// WithStack with stack
func (e *Error) WithStack() *Error {
	e.Stack = LogStack(2, 0)
	return e
}

func (e *Error) Format(state fmt.State, verb rune) {
	switch verb {
	case 'v':
		str := bytes.NewBuffer([]byte{})
		str.WriteString(fmt.Sprintf("code: %d, ", e.Code))
		str.WriteString("message: ")
		str.WriteString(e.Message)
		if e.Err != nil {
			str.WriteString(", error: ")
			str.WriteString(e.Err.Error())
		}
		if len(e.Stack) > 0 {
			str.WriteString("\n")
			str.WriteString(e.Stack)
		}
		fmt.Fprintf(state, "%s", strings.Trim(str.String(), "\r\n\t"))
	default:
		fmt.Fprintf(state, e.Message)
	}
}

// Unwrap 实现errors.Unwrap接口
func (e *Error) Unwrap() error {
	return e.Err
}

// NewSystemError 创建系统错误
func NewSystemError(err error) *SystemError {
	return &SystemError{
		Err:   err,
		Stack: LogStack(2, 0),
	}
}

// Error 实现error接口
func (e *SystemError) Error() string {
	return e.Err.Error()
}

// Unwrap 实现errors.Unwrap接口
func (e *SystemError) Unwrap() error {
	return e.Err
}

// Format 实现fmt.Formatter接口
func (e *SystemError) Format(state fmt.State, verb rune) {
	switch verb {
	case 'v':
		str := bytes.NewBuffer([]byte{})
		str.WriteString("system error: ")
		str.WriteString(e.Err.Error())
		if len(e.Stack) > 0 {
			str.WriteString("\n")
			str.WriteString(e.Stack)
		}
		fmt.Fprintf(state, "%s", strings.Trim(str.String(), "\r\n\t"))
	default:
		fmt.Fprintf(state, e.Err.Error())
	}
}
