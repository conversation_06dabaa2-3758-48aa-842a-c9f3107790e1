# 用户管理 API 文档

[TOC]

## 1. 获取用户列表

##### 简要描述
- 获取系统用户列表，支持分页和条件查询

##### 请求URL
- `/admin/system/users`

##### 请求方式
- GET

##### 参数
| 参数名    | 必选 | 类型   | 说明                           |
|-----------|------|--------|--------------------------------|
| page      | 否   | int    | 页码，默认 1                   |
| page_size | 否   | int    | 每页数量，默认 20              |
| username  | 否   | string | 用户名，支持模糊搜索           |
| name      | 否   | string | 姓名，支持模糊搜索             |
| phone     | 否   | string | 手机号                         |
| role      | 否   | string | 角色名称                       |
| status    | 否   | int    | 状态：0-禁用，1-启用           |

##### 返回示例
```json
{
    "ok": 1,
    "msg": "ok",
    "data": {
        "total": 100,
        "page": 1,
        "page_size": 20,
        "data": [
            {
                "id": 1,
                "username": "admin",
                "name": "���理员",
                "phone": "13800138000",
                "avatar": "http://example.com/avatar.jpg",
                "roles": [
                    {
                        "id": 1,
                        "name": "超级管理员",
                        "slug": "administrator"
                    }
                ],
                "status": 1,
                "created_at": "2023-01-01 00:00:00",
                "updated_at": "2023-01-01 00:00:00"
            }
        ]
    }
}
```

## 2. 新增用户

##### 简要描述
- 新增系统用户

##### 请求URL
- `/admin/system/users`

##### 请求方式
- POST

##### 参数
```json
{
    "username": "test",
    "name": "测试用户",
    "password": "123456",
    "phone": "13800138000",
    "avatar": "http://example.com/avatar.jpg",
    "role_ids": [1,2],
    "status": 1
}
```

| 参数名   | 必选 | 类型           | 说明                         |
|----------|------|----------------|------------------------------|
| username | 是   | string         | 用户名                       |
| name     | 是   | string         | 姓名                         |
| password | 是   | string         | 密码                         |
| phone    | 否   | string         | 手机号                       |
| avatar   | 否   | string         | 头像URL                      |
| role_ids | 否   | array of uint  | 角色ID列表                   |
| status   | 否   | int            | 状态：0-禁用，1-启用         |

##### 返回示例
```json
{
    "ok": 1,
    "msg": "ok"
}
```

##### 备注
- username 必须唯一
- phone 必须唯一

## 3. 更新用户

##### 简要描述
- 更新用户信息

##### 请求URL
- `/admin/system/users/:id`

##### 请求方式
- PUT

##### 参数
```json
{
    "name": "测试用户",
    "phone": "13800138000",
    "avatar": "http://example.com/avatar.jpg",
    "role_ids": [1,2],
    "status": 1
}
```

| 参数名   | 必选 | 类型           | 说明                         |
|----------|------|----------------|------------------------------|
| name     | 是   | string         | 姓名                         |
| phone    | 否   | string         | 手机号                       |
| avatar   | 否   | string         | 头像URL                      |
| role_ids | 否   | array of uint  | 角色ID列表                   |
| status   | 否   | int            | 状态：0-禁用，1-启用         |

##### 返回示例
```json
{
    "ok": 1,
    "msg": "ok"
}
```

## 4. 删除用户

##### 简要描述
- 删除系统用户

##### 请求URL
- `/admin/system/users/:id`

##### 请求方式
- DELETE

##### 返回示例
```json
{
    "ok": 1,
    "msg": "ok"
}
```

##### 备注
- 超级管理员用户不能删除

## 5. 重置密码

##### 简要描述
- 重置用户密码

##### 请求URL
- `/admin/system/users/:id/reset-password`

##### 请求方式
- POST

##### 参数
```json
{
    "password": "123456"
}
```

| 参数名   | 必选 | 类型   | 说明   |
|----------|------|--------|--------|
| password | 是   | string | 新密码 |

##### 返回示例
```json
{
    "ok": 1,
    "msg": "ok"
}
```

## 6. 更新状态

##### 简要描述
- 更新用户状态

##### 请求URL
- `/admin/system/users/:id/status`

##### 请求方式
- POST

##### 参数
```json
{
    "status": 1
}
```

| 参数名 | 必选 | 类型 | 说明                  |
|--------|------|------|---------------------|
| status | 是   | int  | 状态：0-禁用，1-启用 |

##### 返回示例
```json
{
    "ok": 1,
    "msg": "ok"
}
```

##### 备注
- 超级管理员用户不能禁用

## 7. 获取用户详情

##### 简要描述
- 获取单个用户的详细信息

##### 请求URL
- `/admin/system/users/:id`

##### 请求方式
- GET

##### 返回示例
```json
{
    "ok": 1,
    "msg": "ok",
    "data": {
        "id": 1,
        "username": "admin",
        "name": "管理员",
        "phone": "13800138000",
        "avatar": "http://example.com/avatar.jpg",
        "roles": [
            {
                "id": 1,
                "name": "超级管理员",
                "slug": "administrator"
            }
        ],
        "status": 1,
        "created_at": "2023-01-01 00:00:00",
        "updated_at": "2023-01-01 00:00:00"
    }
}
```

##### 返回参数说明
| 参数名   | 类型   | 说明                     |
|----------|--------|------------------------|
| id       | int    | 用户ID                 |
| username | string | 用户名                 |
| name     | string | 姓名                   |
| phone    | string | 手机号                 |
| avatar   | string | 头像URL                |
| roles    | array  | 角色列表               |
| status   | int    | 状态：0-禁用，1-启用    |

##### 备注
- 所有接口都需要登录认证
- 所有接口都需要相应的权限
- 返回的 ok 为 1 表示成功，为 0 表示失败


