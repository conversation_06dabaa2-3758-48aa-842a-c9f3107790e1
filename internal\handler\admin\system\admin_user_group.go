// Description: Define admin user group handler

package system

import (
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"marketing/internal/api/system"
	"marketing/internal/handler"
	"marketing/internal/pkg/errors"
	system2 "marketing/internal/service/system"
)

type AdminUserGroupInterface interface {
	List(c *gin.Context)
	Add(c *gin.Context)
	Update(c *gin.Context)
	Delete(c *gin.Context)
	AddUserToGroup(c *gin.Context)
}

type adminUserGroup struct {
	adminUserGroupSvc system2.AdminUserGroupInterface
}

// NewAdminUserGroup creates a new AdminUserGroup instance
func NewAdminUserGroup(adminUserGroupSvc system2.AdminUserGroupInterface) AdminUserGroupInterface {
	return &adminUserGroup{
		adminUserGroupSvc: adminUserGroupSvc,
	}
}

// List lists all user groups
func (a *adminUserGroup) List(c *gin.Context) {
	var req system.AdminUserGroupReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	req.PaginationParams.SetDefaults()
	data, err := a.adminUserGroupSvc.List(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, data)
}

// Add adds a new user group
func (a *adminUserGroup) Add(c *gin.Context) {
	var req system.AddUserGroupReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	err := a.adminUserGroupSvc.Add(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

// Update updates an existing user group
func (a *adminUserGroup) Update(c *gin.Context) {
	var req system.AddUserGroupReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	req.ID = cast.ToUint(c.Param("id"))
	if req.ID == 0 {
		handler.Error(c, errors.NewErr("id不能为空"))
		return
	}
	err := a.adminUserGroupSvc.Update(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

// Delete deletes a user group
func (a *adminUserGroup) Delete(c *gin.Context) {
	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		handler.Error(c, errors.NewErr("id不能为空"))
		return
	}
	err := a.adminUserGroupSvc.Delete(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

// AddUserToGroup adds a user to a user group（弃用）
func (a *adminUserGroup) AddUserToGroup(c *gin.Context) {
	var req system.AddUserToGroupReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}

	if err := a.adminUserGroupSvc.AddUserToGroup(c, req); err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}
