package machine

import (
	"marketing/internal/pkg/types"
)

type MachineTypeInfo struct {
	Id                int              `json:"id"`                  // id
	Name              string           `json:"name"`                // 型号
	NavName           string           `json:"nav_name"`            // 实销机型名称
	ModelId           int              `json:"model_id"`            // 机型id
	ModelName         string           `json:"model_name"`          // 固件机型
	CategoryId        int              `json:"category_id"`         // 分类id
	CategoryName      string           `json:"category_name"`       // 分类名称
	CompanyPrice      float64          `json:"company_price"`       // 公司售价
	TopAgencyPrice    float64          `json:"top_agency_price"`    // 总代售价
	SecondAgencyPrice float64          `json:"second_agency_price"` // 顾客售价 // 二代售价
	CustomerPrice     float64          `json:"customer_price"`
	ChartShow         int              `json:"chart_show"`         // 实销统计是否展示 0:不展示 1:展示
	PrototypeStatus   int              `json:"prototype_status"`   // 可作样机 0:不可以 1:可以
	PrototypeApkPath  string           `json:"prototype_apk_path"` // 演示Apk地址
	VersionCode       string           `json:"version_code"`       // Apk版本号
	ExtBarcodeNum     int              `json:"ext_barcode_num"`    // 副机条码数量
	Declare           int              `json:"declare"`            // 允许申报 0:不允许 1:允许
	Stock             int              `json:"stock"`              // 允许备货 0:不允许 1:允许
	Visibility        int              `json:"visibility"`         // 是否隐藏 0:隐藏 1:不隐藏
	Delist            int              `json:"delist"`             // 是否下架 0:不下架 1:下架
	DelistOnOffTime   types.CustomTime `json:"delist_on_off_time"` // 下架时间
	IsUpCounter       int              `json:"is_up_counter"`      // 是否上报 0:不上报 1:上报
	Price             float64          `json:"price"`              // 售价
	UpCounterTime     types.CustomTime `json:"up_counter_time"`    // 上报时间
	DownCounterTime   types.CustomTime `json:"down_counter_time"`  // 下报时间
	MarketDate        types.CustomTime `json:"market_date"`        // 上市时间
	DelistTime        types.CustomTime `json:"delist_time"`        // 下架时间
	Discontinued      int              `json:"discontinued"`       // 演示样机是否下市 0:不 1:是
	DiscontinuedDate  types.CustomTime `json:"discontinued_date"`  // 演示样机下市时间
}

type MachineTypeReq struct {
	Prototype *int `json:"prototype"`
}
