package machine

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/handler"
	"marketing/internal/pkg/e"
	"marketing/internal/pkg/errors"
	"marketing/internal/service/machine"
)

type TMachineAccessoryRelation struct {
	svc machine.TMachineAccessoryRelationSvcInterface
}

func NewMachineAccessoryRelation(svc machine.TMachineAccessoryRelationSvcInterface) *TMachineAccessoryRelation {
	return &TMachineAccessoryRelation{
		svc: svc,
	}
}

func (m *TMachineAccessoryRelation) GetMachineAccessoryRelationList(c *gin.Context) {
	modelId := e.ReqParamInt(c, "model_id")
	pageNum := e.ReqParamInt(c, "page_num")
	pageSize := e.ReqParamInt(c, "page_size")
	list, total := m.svc.GetMachineAccessoryRelationList(c, modelId, pageNum, pageSize)
	handler.Success(c, gin.H{
		"list":  list,
		"total": total,
	})
}

func (m *TMachineAccessoryRelation) GetMachineAccessoryRelation(c *gin.Context) {
	modelId := e.ReqParamInt(c, "model_id")
	list := m.svc.GetMachineAccessoryRelationByMid(c, modelId)
	handler.Success(c, gin.H{
		"list": list,
	})
}

func (m *TMachineAccessoryRelation) CreateMachineAccessoryRelation(c *gin.Context) {
	modelId := e.ReqParamInt(c, "model_id")
	accessoryId := e.ReqParamInt(c, "accessory_id")
	price := e.ReqParamFloat64(c, "price")
	err := m.svc.CreateMachineAccessoryRelation(c, modelId, accessoryId, price)
	if err != nil {
		handler.Error(c, errors.NewErr("创建机型配件关联错误:"+err.Error()))
		return
	}

	handler.Success(c, gin.H{})
}

func (m *TMachineAccessoryRelation) DeleteMachineAccessoryRelation(c *gin.Context) {
	modelId := e.ReqParamInt(c, "model_id")
	accessoryId := e.ReqParamInt(c, "accessory_id")
	err := m.svc.DeleteMachineAccessoryRelation(c, modelId, accessoryId)
	if err != nil {
		handler.Error(c, errors.NewErr("删除机型配件关联错误:"+err.Error()))
		return
	}

	handler.Success(c, gin.H{})
}
