package provider

import (
	"marketing/internal/handler/admin/endpoint"
	"marketing/internal/handler/admin/mine"
	agencyMine "marketing/internal/handler/agency/mine"
	"marketing/internal/handler/app"
	"marketing/internal/handler/auth"
	"marketing/internal/handler/base"

	"github.com/google/wire"
)

var HandlerSet = wire.NewSet(
	// base路由
	base.NewCaptchaHandler,
	// auth路由
	auth.NewAuth,

	// 管理端
	endpoint.NewEndpoint,
	endpoint.NewEndpointImage,
	endpoint.NewEndpointInfoApply,
	endpoint.NewSettingHandler,
	base.NewRegionHandler,
	mine.NewMine,
	agencyMine.NewMine,

	app.NewMkbHandler,
)
