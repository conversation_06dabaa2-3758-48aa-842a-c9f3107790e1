package dao

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"marketing/internal/api/operation"
	"marketing/internal/model"
	"marketing/internal/pkg/db"
)

type ShareInfoDao interface {
	GetOpArticleShareInfos(c *gin.Context, aids []uint) (list []*model.ShareCountInfo)
}

// ShareInfoDaoImpl 实现 ShareInfoDao 接口
type ShareInfoDaoImpl struct {
	db *gorm.DB
}

// NewShareInfoDao 创建 ShareInfoDao 实例
func NewShareInfoDao() ShareInfoDao {
	return &ShareInfoDaoImpl{
		db: db.GetDB(""),
	}
}

func (d *ShareInfoDaoImpl) GetOpArticleShareInfos(c *gin.Context, aids []uint) (list []*model.ShareCountInfo) {
	d.db.WithContext(c).Model(&model.ShareInfo{}).
		Select("share_info.object_id,count(*) num").
		Joins("join share_log on share_info.id = share_log.share_id").
		Where("share_info.object = '"+operation.ArticleShareSlug+"' and share_info.object_id in (?)", aids).
		Group("share_info.object_id").
		Find(&list)
	return
}
