package dao

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"marketing/internal/api/materials"
	"marketing/internal/model"
)

type PaperDao interface {
	List(c *gin.Context, param materials.PaperListReq) ([]materials.PaperList, int64, error)
	Detail(c *gin.Context, id string) (materials.PaperDetail, error)
	Create(c *gin.Context, paper materials.PaperUpsertReq) (int, error)
	Update(c *gin.Context, paper materials.PaperUpsertReq) (int, error)
	Delete(c *gin.Context, id string) error
	UpdateStatus(c *gin.Context, param materials.PaperStatusReq) error
	QuestionCount(c *gin.Context, id int) (int, error)
	QuestionDetailByPaper(c *gin.Context, id string) ([]materials.QuestionTips, error)
	QuestionTips(c *gin.Context) ([]materials.QuestionTips, error)
	QuestionList(c *gin.Context, param materials.QuestionListReq) ([]materials.QuestionListResp, int64, error)
	QuestionDetail(c *gin.Context, id string) (materials.QuestionDetailResp, error)
	QuestionType(c *gin.Context, t uint8) (string, error)
	QuestionDelete(c *gin.Context, id string) error
	QuestionWithPaper(c *gin.Context, id string, isPaper bool) ([]model.PaperQuestion, error)
	QuestionCreate(c *gin.Context, param model.Question) (int, error)
	QuestionUpdate(c *gin.Context, param model.Question) (int, error)
	QuestionTypeTips(c *gin.Context) ([]materials.QuestionTypeTips, error)
}

type GormPaper struct {
	db *gorm.DB
}

func (g *GormPaper) QuestionTypeTips(c *gin.Context) ([]materials.QuestionTypeTips, error) {
	var res []materials.QuestionTypeTips
	err := g.db.WithContext(c).Table("question_type").Select("id", "name").Find(&res).Error
	return res, err
}

func (g *GormPaper) QuestionUpdate(c *gin.Context, param model.Question) (int, error) {
	err := g.db.WithContext(c).Model(model.Question{}).Where("id = ?", param.ID).Updates(&param).Error
	return int(param.ID), err
}

func (g *GormPaper) QuestionCreate(c *gin.Context, param model.Question) (int, error) {
	err := g.db.WithContext(c).Table("question").Create(&param).Error
	return int(param.ID), err
}

func (g *GormPaper) QuestionWithPaper(c *gin.Context, id string, isPaper bool) (res []model.PaperQuestion, err error) {
	if isPaper {
		err = g.db.WithContext(c).Table("paper_question").Where("paper_id = ?", id).Find(&res).Error
	} else {
		err = g.db.WithContext(c).Table("paper_question").Where("question_id = ?", id).Find(&res).Error
	}
	return res, err
}

// QuestionDelete 删除问题
func (g *GormPaper) QuestionDelete(c *gin.Context, id string) error {
	return g.db.WithContext(c).Table("question").Where("id = ?", id).Delete(&model.Question{}).Error
}

func (g *GormPaper) QuestionType(c *gin.Context, t uint8) (string, error) {
	var name string
	err := g.db.WithContext(c).Table("question_type").Select("name").Where("id = ?", t).Find(&name).Error
	return name, err
}

func (g *GormPaper) QuestionDetail(c *gin.Context, id string) (materials.QuestionDetailResp, error) {
	var papers []struct {
		model.Question
		PaperID          int    `gorm:"column:paper_id"`
		PaperName        string `gorm:"column:paper_name"`
		PaperDescription string `gorm:"column:paper_description"`
	}
	err := g.db.WithContext(c).Table("question AS q").
		Joins("LEFT JOIN paper_question AS pq ON q.id = pq.question_id").
		Joins("LEFT JOIN paper AS p ON p.id = pq.paper_id").
		Select("q.id,q.detail,q.description,q.options,q.updated_at,"+
			"q.answer,q.answers,q.judge,q.blank,q.type,q.created_at,"+
			"p.id AS paper_id,p.name AS paper_name,p.description AS paper_description").
		Where("q.id = ?", id).Find(&papers).Error
	if err != nil {
		return materials.QuestionDetailResp{}, err
	}
	var paper materials.QuestionDetailResp
	if len(papers) > 0 {
		paper.Question = papers[0].Question
		for _, result := range papers {
			if result.PaperID != 0 {
				paper.Papers = append(paper.Papers, materials.PaperView{
					ID:          result.PaperID,
					Name:        result.PaperName,
					Description: result.PaperDescription,
				})
			}
		}
	}
	return paper, err
}

// QuestionList 问题列表
func (g *GormPaper) QuestionList(c *gin.Context, param materials.QuestionListReq) ([]materials.QuestionListResp, int64, error) {
	var questions []materials.QuestionListResp
	var count int64
	curDB := g.db.WithContext(c).Table("question AS q").
		Joins("LEFT JOIN question_type AS t ON q.type = t.id").
		Joins("LEFT JOIN question_response AS r ON q.id = r.question_id").
		Select("q.id,q.detail,q.description,q.options,q.updated_at,"+
			"q.answer,q.answers,q.judge,q.blank,"+
			" t.name AS question_type,count(r.paper_id) AS answer_num").
		Where("q.deleted_at IS NULL").
		Where("q.attribution=?", "exam").
		Group("q.id")
	if param.Type != "" {
		curDB = curDB.Where("t.name = ?", param.Type)
	}
	if param.Label != "" {
		curDB = curDB.Where("q.detail LIKE ?", "%"+param.Label+"%").
			Or("q.description LIKE ?", "%"+param.Label+"%")
	}
	if param.ID != 0 {
		curDB = curDB.Where("q.id = ?", param.ID)
	}
	if err := curDB.Count(&count).Error; err != nil {
		return nil, 0, err
	}
	if err := curDB.Offset((param.Page - 1) * param.PageSize).Limit(param.PageSize).Find(&questions).Error; err != nil {
		return nil, 0, err
	}
	return questions, count, nil
}

func (g *GormPaper) QuestionTips(c *gin.Context) ([]materials.QuestionTips, error) {
	var questions []materials.QuestionTips
	err := g.db.WithContext(c).Table("question").
		Joins("LEFT JOIN question_type AS q2 ON question.type = q2.id").
		Select("question.id AS question_id, detail AS question_name, q2.name AS question_type").
		Find(&questions).Error
	return questions, err
}

func (g *GormPaper) QuestionDetailByPaper(c *gin.Context, id string) ([]materials.QuestionTips, error) {
	var questions []materials.QuestionTips
	err := g.db.WithContext(c).Table("paper_question AS q1").
		Joins("LEFT JOIN question AS q2 ON q1.question_id = q2.id").
		Joins("LEFT JOIN question_type AS q3 ON q2.type = q3.id").
		Where("q1.paper_id = ?", id).
		Select("q2.id AS question_id, q2.detail AS question_name, q3.name AS question_type").
		Find(&questions).Error
	return questions, err
}

func (g *GormPaper) UpdateStatus(c *gin.Context, param materials.PaperStatusReq) error {
	return g.db.WithContext(c).Model(&model.Paper{}).Where("id = ?", param.ID).Update("status", param.Status).Error
}

// Delete 软删除试卷不清理试卷问题联系
func (g *GormPaper) Delete(c *gin.Context, id string) error {
	return g.db.WithContext(c).Delete(&model.Paper{}, "id = ?", id).Error
}

func (g *GormPaper) QuestionCount(c *gin.Context, id int) (int, error) {
	var count int64
	err := g.db.WithContext(c).Table("paper_question").Where("paper_id = ?", id).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return int(count), nil
}

// Create 创建试卷
func (g *GormPaper) Create(c *gin.Context, paper materials.PaperUpsertReq) (int, error) {
	//开启事务
	var id int
	err := g.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		err := tx.WithContext(c).Table("paper").Create(&paper.Paper).Error
		if err != nil {
			return err
		}
		questions := make([]model.PaperQuestion, len(paper.Questions))
		for k, v := range paper.Questions {
			questions[k].QuestionID = uint(v)
			questions[k].PaperID = uint(paper.Paper.ID)
		}
		err = tx.WithContext(c).CreateInBatches(questions, 100).Error
		if err != nil {
			return err
		}
		id = paper.Paper.ID
		return nil
	})
	return id, err
}

// Update 更新试卷
func (g *GormPaper) Update(c *gin.Context, paper materials.PaperUpsertReq) (int, error) {
	err := g.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		//更新paper
		curDB := tx.Table("paper").Where("id = ?", paper.Paper.ID).Updates(map[string]any{
			"name":        paper.Paper.Name,
			"description": paper.Paper.Description,
			"duration":    paper.Paper.Duration,
			"status":      paper.Paper.Status,
			"creator":     paper.Paper.Creator,
			"start":       paper.Paper.Start,
			"end":         paper.Paper.End,
			"number":      paper.Paper.Number,
		})
		if curDB.Error != nil {
			return curDB.Error
		}
		if curDB.RowsAffected == 0 {
			return gorm.ErrRecordNotFound
		}
		//查找现有的问题
		var ids []int
		err := tx.Table("paper_question").Where("paper_id = ?", paper.Paper.ID).Pluck("question_id", &ids).Error
		if err != nil {
			return err
		}
		//增加没有的问题
		add := difference(paper.Questions, ids)
		questions := make([]model.PaperQuestion, len(paper.Questions))
		for k, v := range add {
			questions[k].QuestionID = uint(v)
			questions[k].PaperID = uint(paper.Paper.ID)
		}
		err = tx.Table("paper_question").CreateInBatches(questions, 100).Error
		if err != nil {
			return err
		}
		//删除不要的问题
		err = tx.Table("paper_question").Where("paper_id = ? AND question_id NOT IN (?)", paper.Paper.ID, paper.Questions).Delete(&model.PaperQuestion{}, ids).Error
		if err != nil {
			return err
		}
		return nil
	})
	return paper.Paper.ID, err
}

func (g *GormPaper) Detail(c *gin.Context, id string) (materials.PaperDetail, error) {
	var detail materials.PaperDetail
	err := g.db.WithContext(c).Model(&model.Paper{}).Where("id = ?", id).First(&detail).Error
	if err != nil {
		return materials.PaperDetail{}, err
	}
	return detail, nil
}

// List 获取试卷列表
func (g *GormPaper) List(c *gin.Context, param materials.PaperListReq) ([]materials.PaperList, int64, error) {
	var list []materials.PaperList
	curDB := g.db.WithContext(c).Table("paper").
		Joins("LEFT JOIN paper_question ON paper.id = paper_question.paper_id").
		Joins("LEFT JOIN paper_response ON paper.id = paper_response.paper_id").
		Where("attribution=?", "exam").
		Select("paper.*, COUNT(paper_question.id) as had_num,"+
			"COUNT(DISTINCT endpoint_id) as had_endpoint,"+
			"COUNT(DISTINCT salesman_id) as had_salesman").
		Where("paper.deleted_at IS NULL").
		Where("paper.attribution=?", "exam").
		Group("paper.id")
	if param.Label != "" {
		curDB = curDB.Where("description LIKE ?", param.Label).Or("paper.name LIKE ?", param.Label)
	}
	if param.ID != 0 {
		curDB = curDB.Where("paper.id = ?", param.ID)
	}
	var total int64
	curDB.Count(&total)
	err := curDB.Offset((param.Page - 1) * param.PageSize).Limit(param.PageSize).Scan(&list).Error
	return list, total, err
}

func NewGormPaper(db *gorm.DB) PaperDao {
	return &GormPaper{db: db}
}
