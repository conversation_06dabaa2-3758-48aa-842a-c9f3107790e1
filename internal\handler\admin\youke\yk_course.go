package youke

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/api/youke"
	"marketing/internal/handler"
	"marketing/internal/service"
)

type CourseHandle struct {
	svc service.YkCourseService
}

// LiveList 直播课列表
func (h *CourseHandle) LiveList(c *gin.Context) {
	var param youke.LiveListReq
	if err := c.<PERSON>(&param); err != nil {
		handler.Error(c, err)
		return
	}
	list, total, err := h.svc.LiveList(c, param)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"list":  list,
		"total": total,
	})
}

func NewCourseHandle(svc service.YkCourseService) *CourseHandle {
	return &CourseHandle{svc: svc}
}
