package prototype

import (
	"marketing/internal/api"
)

// UpEndpointNumberSearch 上样终端数量查询参数
type UpEndpointNumberSearch struct {
	api.PaginationParams
	Model     string `json:"model" form:"model"`
	TopAgency int    `json:"top_agency" form:"top_agency"`
}

// UpEndpointNumberAdd 上样终端数量添加参数
type UpEndpointNumberAdd struct {
	Model string `json:"model" form:"model" binding:"required"`
}

// UpEndpointNumberListVo 上样终端数量列表返回
type UpEndpointNumberListVo struct {
	ID             int    `json:"id"`
	Model          string `json:"model"`
	TopAgency      int    `json:"top_agency"`
	TopAgencyName  string `json:"top_agency_name"`
	EndpointNumber int64  `json:"endpoint_number"`
	CreatedAt      string `json:"created_at"`
}
