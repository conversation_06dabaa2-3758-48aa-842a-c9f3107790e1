package model

import (
	"marketing/internal/pkg/types"
)

// EndpointSetting represents the structure for the 'endpoint_setting' table.
type EndpointSetting struct {
	ID                      uint             ` json:"id" gorm:"primaryKey;autoIncrement;column:id"`
	EndpointID              uint             `json:"endpoint_id" gorm:"uniqueIndex;not null;column:endpoint_id"`
	BirthdayRemindFrequency string           `json:"birthday_remind_frequency" gorm:"type:enum('daily','weekly','monthly');not null;default:'weekly';column:birthday_remind_frequency"`
	ReturnVisitRemindDays   string           `json:"return_visit_remind_days" gorm:"size:10;not null;default:'7';column:return_visit_remind_days"`
	CreatedAt               types.CustomTime `json:"created_at" gorm:"not null;default:'0000-00-00 00:00:00';column:created_at"`
	UpdatedAt               types.CustomTime `json:"updated_at" gorm:"not null;default:'0000-00-00 00:00:00';column:updated_at"`
	PrototypeLimit          int8             `json:"prototype_limit" gorm:"not null;default:-1;column:prototype_limit" `
	PrototypeFrequencyLimit uint8            `json:"prototype_frequency_limit" gorm:"not null;default:2;column:prototype_frequency_limit" `
	ReturnVisit             string           `json:"-" gorm:"size:255;not null;default:'';column:return_visit"`
	PaBannerActivities      string           `json:"pa_banner_activities" gorm:"size:150;not null;column:pa_banner_activities"`
	PaServiceActivities     string           `json:"pa_service_activities" gorm:"size:150;not null;default:'check_in';column:pa_service_activities"`
}

// TableName sets the insert table name for this struct type
func (EndpointSetting) TableName() string {
	return "endpoint_setting"
}
