package dao

import (
	"context"
	"gorm.io/gorm"
	api "marketing/internal/api/system"
	"marketing/internal/model"
)

type ResourceGroupRepository interface {
	Create(ctx context.Context, group *model.ResourceGroup) error
	Update(ctx context.Context, group *model.ResourceGroup) error
	Delete(ctx context.Context, id uint) error
	Get(ctx context.Context, id uint) (*model.ResourceGroup, error)
	List(ctx context.Context, params api.ListResourceGroupReq) ([]*model.ResourceGroup, int64, error)
	ExistsByName(ctx context.Context, name string) (bool, error)
	AssignToRole(ctx context.Context, roleID, groupID uint) error
	RemoveFromRole(ctx context.Context, roleID, groupID uint) error
	GetByRole(ctx context.Context, roleID uint) (*model.ResourceGroup, error)
	ListByRole(ctx context.Context, roleIDs []uint) (map[uint]model.ResourceGroup, error)
	ListResourceGroupsNoPage(ctx context.Context, name string) ([]*model.ResourceGroup, error)
}

type resourceGroupRepo struct {
	db *gorm.DB
}

func NewResourceGroupRepository(db *gorm.DB) ResourceGroupRepository {
	return &resourceGroupRepo{db: db}
}

func (r *resourceGroupRepo) Create(ctx context.Context, group *model.ResourceGroup) error {
	return r.db.WithContext(ctx).Create(group).Error
}

func (r *resourceGroupRepo) Update(ctx context.Context, group *model.ResourceGroup) error {
	return r.db.WithContext(ctx).Model(group).Updates(group).Error
}

func (r *resourceGroupRepo) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&model.ResourceGroup{}, id).Error
}

func (r *resourceGroupRepo) Get(ctx context.Context, id uint) (*model.ResourceGroup, error) {
	var group model.ResourceGroup
	err := r.db.WithContext(ctx).First(&group, id).Error
	if err != nil {
		return nil, err
	}
	return &group, nil
}

func (r *resourceGroupRepo) List(ctx context.Context, params api.ListResourceGroupReq) ([]*model.ResourceGroup, int64, error) {
	var groups []*model.ResourceGroup
	var total int64

	query := r.db.WithContext(ctx).Model(&model.ResourceGroup{})
	if params.ID > 0 {
		query = query.Where("id =?", params.ID)
	}

	if params.Name != "" {
		query = query.Where("name LIKE ?", "%"+params.Name+"%")
	}

	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	if params.Page > 0 && params.PageSize > 0 {
		offset := (params.Page - 1) * params.PageSize
		query = query.Offset(offset).Limit(params.PageSize)
	}

	err = query.Find(&groups).Error
	if err != nil {
		return nil, 0, err
	}

	return groups, total, nil
}

func (r *resourceGroupRepo) ExistsByName(ctx context.Context, name string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&model.ResourceGroup{}).Where("name = ?", name).Count(&count).Error
	return count > 0, err
}

func (r *resourceGroupRepo) AssignToRole(ctx context.Context, roleID, groupID uint) error {
	roleResource := &model.AdminRoleResource{
		RoleID:  roleID,
		GroupID: groupID,
	}
	return r.db.WithContext(ctx).Create(roleResource).Error
}

func (r *resourceGroupRepo) RemoveFromRole(ctx context.Context, roleID, groupID uint) error {
	return r.db.WithContext(ctx).Unscoped().Where("role_id = ? AND group_id = ?", roleID, groupID).
		Delete(&model.AdminRoleResource{}).Error
}

func (r *resourceGroupRepo) GetByRole(ctx context.Context, roleID uint) (*model.ResourceGroup, error) {
	var group *model.ResourceGroup
	err := r.db.WithContext(ctx).
		Joins("JOIN admin_role_resources ON resource_groups.id = admin_role_resources.group_id").
		Where("admin_role_resources.role_id = ?", roleID).
		First(&group).Error
	return group, err

}

func (r *resourceGroupRepo) ListByRole(ctx context.Context, roleIDs []uint) (map[uint]model.ResourceGroup, error) {
	type RoleResourceGroup struct {
		RoleID uint
		ID     uint
		Name   string
	}
	var roleResourceGroups []RoleResourceGroup

	err := r.db.WithContext(ctx).
		Table("admin_role_resources r").
		Select("r.role_id, g.*").
		Joins("JOIN resource_groups as g ON g.id = r.group_id").
		Where("r.role_id in (?)", roleIDs).
		First(&roleResourceGroups).Error
	if err != nil {
		return nil, err
	}
	result := make(map[uint]model.ResourceGroup)
	for _, rrg := range roleResourceGroups {
		var rg model.ResourceGroup
		rg.ID = rrg.ID
		rg.Name = rrg.Name
		result[rrg.RoleID] = rg
	}

	return result, nil
}

func (r *resourceGroupRepo) ListResourceGroupsNoPage(ctx context.Context, name string) ([]*model.ResourceGroup, error) {
	var groups []*model.ResourceGroup
	query := r.db.WithContext(ctx).Model(&model.ResourceGroup{})

	if name != "" {
		query = query.Where("name LIKE ?", "%"+name+"%")
	}

	err := query.Find(&groups).Error
	if err != nil {
		return nil, err
	}

	return groups, nil
}
