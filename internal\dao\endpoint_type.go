package dao

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"marketing/internal/model"
)

type TypeRepository interface {
	GetEndpointType(c *gin.Context, id uint) ([]*model.EndpointType, error)
	GetEndpointTypeMap(c *gin.Context) (map[uint]string, error)
}

type typeRepository struct {
	db *gorm.DB
}

func NewEndpointTypeRepository(db *gorm.DB) TypeRepository {
	return &typeRepository{
		db: db,
	}
}

func (r *typeRepository) GetEndpointType(c *gin.Context, id uint) ([]*model.EndpointType, error) {
	var types []*model.EndpointType
	query := r.db.WithContext(c)
	if id > 0 {
		query = query.Where("id = ?", id)
	}
	if err := query.Find(&types).Error; err != nil {
		return nil, err
	}
	return types, nil
}

func (r *typeRepository) GetEndpointTypeMap(c *gin.Context) (map[uint]string, error) {
	var types []*model.EndpointType
	query := r.db.WithContext(c).Model(&model.EndpointType{})
	if err := query.Find(&types).Error; err != nil {
		return nil, err
	}
	typeMap := make(map[uint]string)
	for _, t := range types {
		typeMap[uint(t.ID)] = t.Code
	}
	return typeMap, nil
}
