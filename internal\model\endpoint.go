package model

import (
	"time"
)

type Endpoint struct {
	ID              int        `json:"id" gorm:"primary_key"`                                   // 终端ID
	Name            string     `json:"name" gorm:"not null"`                                    // 终端名称
	Code            string     `json:"code" gorm:"type:char(6);not null;default:''"`            // 终端编码
	Type            int8       `json:"type" gorm:"type:tinyint(1);not null;default:0"`          // 终端添加终端分类信息（1:专柜、2:运营商渠道、3:专卖店、4:城市综合体、5:商超）
	IsFortress      int8       `json:"-" gorm:"type:tinyint(1);not null;default:0"`             // 是否是城中堡垒店1-是，0-否
	ChannelLevel    uint8      `json:"channel_level" gorm:"type:tinyint(3);not null;default:0"` // 渠道等级，0-未知，1-省会级，2-地市级，3-县级，4-镇级
	Province        int        `json:"province" gorm:"not null"`                                // 省
	City            int        `json:"city" gorm:"not null"`                                    // 市
	District        int        `json:"district" gorm:"not null"`                                // 区
	Address         string     `json:"address" gorm:"type:varchar(512);not null"`               // 详细地址
	TopAgency       int        `json:"top_agency" gorm:"not null"`                              // 一级代理
	SecondAgency    int        `json:"second_agency" gorm:"not null;default:0"`                 // 二级代理
	Phone           *string    `json:"phone" gorm:"type:varchar(100)"`                          // 电话
	Manager         *string    `json:"manager" gorm:"type:varchar(50)"`                         // 负责人
	Dealer          *string    `json:"dealer" gorm:"type:varchar(100)"`                         // 经销商手机号码，多个以逗号分割
	Status          int8       `json:"status" gorm:"type:tinyint(2);not null;default:1"`        // 启用状态：0是禁用，1是启用
	OpenStatus      int8       `json:"open_status" gorm:"type:tinyint(2);not null;default:0"`   // 营业状态：0正常营业，1疑似歇业（非活跃），2歇业，3关店
	ActiveAt        *time.Time `json:"-"`                                                       // 最后活跃时间（电子保卡录入、换机、退机）
	UseDate         *time.Time `json:"use_date" gorm:"type:date"`                               // 最后使用时间（终端服务接口调用）
	IsPreSale       int8       `json:"is_pre_sale" gorm:"type:tinyint(1);not null;default:1"`   // 是否是售前(0不是,1是)
	IsAfterSale     int8       `json:"is_after_sale" gorm:"type:tinyint(1);not null;default:0"` // 是否是售后(0不是,1是)
	IsDirectSales   *int8      `json:"is_direct_sales" gorm:"type:tinyint(4);default:0"`        // 是否直营 0非直营  1直营
	NoRank          int8       `json:"no_rank" gorm:"type:tinyint(1);not null;default:0"`       // 是否禁用APP排名（0不禁用，1禁用）
	Collated        int8       `json:"collated" gorm:"type:tinyint(4);not null;default:0"`      // 是否通过核销,0是未通过,1是通过
	Compound        uint8      `json:"compound" gorm:"type:tinyint(3);not null;default:0"`      // 是否为复合终端，即售卖多个品牌
	OpenAt          *string    `json:"open_at" gorm:"type:time;default:'00:00:00'"`             // 开始营业时间
	CloseAt         *string    `json:"close_at" gorm:"type:time;default:'00:00:00'"`            // 结束营业时间
	OpenCloseTips   *string    `json:"open_close_tips" gorm:"type:varchar(50);default:''"`      // 营业时间备注
	DayOff          *string    `json:"day_off" gorm:"type:varchar(255);default:''"`             // 休息日
	Images          *string    `json:"images" gorm:"type:text"`                                 // 终端形象图片
	Lng             string     `json:"lng" gorm:"type:varchar(20);default:'0.000000'"`          // 经度（高德坐标）
	Lat             string     `json:"lat" gorm:"type:varchar(20);default:'0.000000'"`          // 纬度（高德坐标）
	Blng            *string    `json:"blng" gorm:"type:varchar(20)"`                            // 经度（百度坐标）
	Blat            *string    `json:"blat" gorm:"type:varchar(20)"`                            // 纬度（百度坐标）
	CreatedAt       time.Time  `json:"-" gorm:"not null;default:CURRENT_TIMESTAMP"`             // 创建时间
	UpdatedAt       *time.Time `json:"-"`                                                       // 更新时间
	QwPartyid       uint       `json:"qw_partyid" gorm:"type:int(11);not null;default:0"`       // 企业微信部门id
	License         *string    `json:"license" gorm:"type:varchar(300)"`                        // 营业执照
	Wifi            *string    `json:"wifi" gorm:"type:varchar(500)"`                           // 门店wifi名称（用于智习室）
	AiLearnSchedule *string    `json:"ai_learn_schedule" gorm:"type:text"`                      // 智习室预设排课时间段
}

func (Endpoint) TableName() string {
	return "endpoint"
}
