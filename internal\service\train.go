package service

import (
	"marketing/internal/api/materials"
	"marketing/internal/dao"
	"marketing/internal/model"
	myErrors "marketing/internal/pkg/errors"
	"marketing/internal/pkg/utils"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

type TrainService interface {
	Upsert(c *gin.Context, param materials.KindAddReq, kind string) (int, error)
	Save(c *gin.Context, param []materials.KindSaveReq, kind string) error
	List(c *gin.Context, kind string) ([]materials.KindList, error)
	Detail(c *gin.Context, id string, kind string) (materials.Kind, error)
	Tips(c *gin.Context, kind string) ([]map[string]any, error)
	AllTips(c *gin.Context, s string) ([]map[string]any, error)
	Delete(c *gin.Context, id string, kind string) error
	InfoList(c *gin.Context, param materials.TrainInfoListReq) ([]map[string]any, int64, error)
	InfoUpdate(c *gin.Context, param materials.TrainInfoUpdateReq) error
	InfoUpdates(c *gin.Context, param materials.TrainInfoUpdate[[]string]) error
	InfoDetail(c *gin.Context, id string) (map[string]any, error)
	VideoUpsert(c *gin.Context, param materials.VideoAddReq[[]string]) (int, error)
	VideoDelete(c *gin.Context, id string) error
	VideoList(c *gin.Context, param materials.TrainVideoListReq) ([]map[string]any, int64, error)
	VideoDetail(c *gin.Context, id string) (map[string]any, error)
	Upload(c *gin.Context, id string) error
	GetByIDs(c *gin.Context, ids []int) ([]model.Train, error)
}

func StringToSlice(str string) []int {
	if str == "" {
		return []int{}
	}
	ids := strings.Split(str, ",")
	ds := make([]int, len(ids))
	for K, v := range ids {
		temp, _ := strconv.Atoi(v)
		ds[K] = temp
	}
	return ds
}

type GormTrainService struct {
	dao dao.TrainDao
}

func (g *GormTrainService) Upload(c *gin.Context, id string) error {
	return g.dao.Upload(c, id)
}

func (g *GormTrainService) AllTips(c *gin.Context, s string) ([]map[string]any, error) {
	return g.dao.AllTips(c, s)
}

func (g *GormTrainService) VideoDetail(c *gin.Context, id string) (map[string]any, error) {
	detail, err := g.dao.VideoDetail(c, id)
	if err != nil {
		return nil, err
	}
	res := make(map[string]any)
	preview, _ := utils.UrlImageTrans(detail.Preview)
	res["id"] = detail.ID
	res["title"] = detail.Title
	res["preview"] = preview
	res["path"] = utils.AddPrefix(detail.Path)
	res["description"] = detail.Description
	res["status"] = detail.Status
	res["created_at"] = detail.CreatedAt.Format("2006-01-02 15:04:05")
	res["updated_at"] = detail.UpdatedAt.Format("2006-01-02 15:04:05")
	return res, nil
}

func (g *GormTrainService) VideoList(c *gin.Context, param materials.TrainVideoListReq) ([]map[string]any, int64, error) {
	list, total, err := g.dao.VideoList(c, param)
	res := make([]map[string]any, len(list))
	for K, v := range list {
		preview, _ := utils.UrlImageTrans(v.Preview)
		res[K] = map[string]any{
			"id":          v.ID,
			"title":       v.Title,
			"preview":     preview,
			"path":        utils.AddPrefix(v.Path),
			"description": v.Description,
			"status":      v.Status,
		}
	}
	return res, total, err
}

func (g *GormTrainService) VideoDelete(c *gin.Context, id string) error {
	return g.dao.VideoDelete(c, id)
}

func (g *GormTrainService) VideoUpsert(c *gin.Context, param materials.VideoAddReq[[]string]) (int, error) {
	var req materials.VideoAddReq[string]
	{
		req.ID = param.ID
		req.Title = param.Title
		req.Preview = utils.SliceTrans(param.Preview)
		req.Path = utils.DeletePrefix(param.Path)
		req.Description = param.Description
		req.Status = param.Status
	}
	if param.ID == 0 {
		return g.dao.VideoCreate(c, req)
	}
	return g.dao.VideoUpdate(c, req)
}

func (g *GormTrainService) InfoDetail(c *gin.Context, id string) (map[string]any, error) {
	detail, err := g.dao.InfoDetail(c, id)
	req := make(map[string]any)
	if err != nil {
		return req, myErrors.NewErr("获取失败")
	}
	{
		req["id"] = detail.ID
		req["name"] = detail.Name
		req["description"] = detail.Description
		req["preview"], _ = utils.UrlImageTrans(detail.Preview)
		req["path"] = utils.AddPrefix(detail.Path)
		req["article_link"] = detail.ArticleLink
		req["banner_image"] = utils.AddPrefix(detail.BannerImage)
		req["type_name"] = detail.TypeName
		req["status"] = detail.Status
		req["top"] = detail.Top
		req["share"] = detail.Share
		req["star"] = detail.Star
		req["banner"] = detail.Banner
		req["credit"] = detail.Credit
		req["credit_learning_time"] = detail.CreditLearningTime
	}
	//字符串数组转换
	ds := StringToSlice(detail.CategoryIDs)
	req["category_name"], err = g.dao.GetBatchCategory(c, ds)
	return req, err
}

func (g *GormTrainService) InfoUpdates(c *gin.Context, param materials.TrainInfoUpdate[[]string]) error {
	var req materials.TrainInfoUpdate[string]
	{
		req.Train.ID = param.Train.ID
		req.Train.Name = param.Train.Name
		req.Train.Description = param.Train.Description
		req.Train.Preview = utils.SliceTrans(param.Train.Preview)
		req.Train.Path = utils.DeletePrefix(param.Train.Path)
		req.Train.ArticleLink = param.Train.ArticleLink
		req.Train.BannerImage = utils.DeletePrefix(param.Train.BannerImage)
		req.Train.Type = param.Train.Type
		req.Train.Status = param.Train.Status
		req.Train.Top = param.Train.Top
		req.Train.Share = param.Train.Share
		req.Train.Star = param.Train.Star
		req.Train.Banner = param.Train.Banner
		req.Train.Credit = param.Train.Credit
		req.Train.CreditLearningTime = param.Train.CreditLearningTime
	}
	req.Category = make([]int, len(param.Category))
	for k, v := range param.Category {
		req.Category[k] = v
	}
	return g.dao.InfoUpdates(c, req)
}

func (g *GormTrainService) InfoUpdate(c *gin.Context, param materials.TrainInfoUpdateReq) error {
	return g.dao.InfoUpdate(c, param)
}

func (g *GormTrainService) InfoList(c *gin.Context, param materials.TrainInfoListReq) ([]map[string]any, int64, error) {
	list, total, err := g.dao.InfoList(c, param)
	lists := make([]map[string]any, len(list))
	for k, v := range list {
		lists[k] = make(map[string]interface{})
		lists[k]["id"] = v.ID
		lists[k]["name"] = v.Name
		lists[k]["description"] = v.Description
		lists[k]["preview"], _ = utils.UrlImageTrans(v.Preview)
		lists[k]["path"] = utils.AddPrefix(v.Path)
		lists[k]["article_link"] = v.ArticleLink
		lists[k]["type_name"] = v.TypeName
		lists[k]["category_name"] = v.CategoryName
		lists[k]["status"] = v.Status
		lists[k]["top"] = v.Top
		lists[k]["share"] = v.Share
		lists[k]["star"] = v.Star
		lists[k]["download_count"] = v.DownloadCount
		lists[k]["updated_at"] = v.UpdatedAt.Format("2006-01-02 15:04:05")
		lists[k]["banner"] = v.Banner
		lists[k]["credit"] = v.Credit
		lists[k]["credit_learning_time"] = v.CreditLearningTime
		lists[k]["created_by"] = v.CreatedBy
		lists[k]["num"] = v.PeopleNum
		lists[k]["learn_num"] = v.LearnNum
		lists[k]["admin"] = v.Admin
		ids := StringToSlice(v.CategoryName)
		lists[k]["category"], _ = g.dao.GetBatchCategory(c, ids)
	}
	return lists, total, err
}

func (g *GormTrainService) Delete(c *gin.Context, id string, s string) error {
	//确定是否含有资料
	if g.dao.HadInfo(c, id, s) {
		return myErrors.NewErr("含有资料，无法删除")
	}
	return g.dao.Delete(c, id, s)
}

// Tips 父类提示
func (g *GormTrainService) Tips(c *gin.Context, s string) ([]map[string]any, error) {
	tips, err := g.dao.Tips(c, s)
	res := make([]map[string]any, len(tips)+1)
	res[0] = map[string]any{
		"id":    0,
		"title": "ROOT",
	}
	if err != nil || len(tips) == 0 {
		return res, err
	}
	for k, v := range tips {
		res[k+1] = v
	}
	return res, nil
}

func (g *GormTrainService) Detail(c *gin.Context, id string, s string) (materials.Kind, error) {
	detail, err := g.dao.Detail(c, id, s)
	if err != nil {
		return materials.Kind{}, err
	}
	var res materials.Kind
	res.ID = int(detail.ID)
	res.Title = detail.Title
	res.Order = int(detail.Order)
	res.CreatedAt = detail.CreatedAt.Format("2006-01-02 15:04:05")
	res.UpdatedAt = detail.UpdatedAt.Format("2006-01-02 15:04:05")
	res.Parent, _ = g.dao.ParentName(c, detail.ParentID, s)
	return res, nil
}

func (g *GormTrainService) List(c *gin.Context, s string) ([]materials.KindList, error) {
	return g.dao.List(c, s)
}

func (g *GormTrainService) Save(c *gin.Context, param []materials.KindSaveReq, s string) error {
	return g.dao.Save(c, param, s)
}

// Upsert 新增或更新
func (g *GormTrainService) Upsert(c *gin.Context, param materials.KindAddReq, kind string) (int, error) {
	if param.ID == 0 {
		return g.dao.Create(c, param, kind)
	}
	return g.dao.Update(c, param, kind)
}

func (g *GormTrainService) GetByIDs(c *gin.Context, ids []int) ([]model.Train, error) {
	return g.dao.GetByIDs(c, ids)
}

func NewGormTrainService(dao dao.TrainDao) TrainService {
	return &GormTrainService{dao: dao}
}
