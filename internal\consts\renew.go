package consts

import "sort"

const (
	RenewStatusPending           = "Pending"
	RenewStatusSecondLevelReview = "SecondLevelReview"
	RenewStatusFirstLevelReview  = "FirstLevelReview"
	RenewStatusHeadOfficeReview  = "HeadOfficeReview"
	RenewStatusRepair            = "Repair"
	RenewStatusCompleted         = "Completed"
	RenewStatusRejected          = "Rejected"
)

var renewStatusMap = map[string]string{
	RenewStatusPending:           "待审核",
	RenewStatusSecondLevelReview: "二代已审核",
	RenewStatusFirstLevelReview:  "总代已审核",
	RenewStatusHeadOfficeReview:  "总部已审核",
	RenewStatusRepair:            "已品检",
	RenewStatusCompleted:         "已完成",
	RenewStatusRejected:          "已驳回",
}

var renewNextStatusMap = map[string]string{
	RenewStatusPending:           "代理审核",
	RenewStatusSecondLevelReview: "总代审核",
	RenewStatusFirstLevelReview:  "总部审核",
	RenewStatusHeadOfficeReview:  "品质检测",
	RenewStatusRepair:            "您的机器预计次月初返还",
	RenewStatusCompleted:         "",
	RenewStatusRejected:          "",
}

// StatusItem 定义一个结构体来存储状态和对应的中文描述
type StatusItem struct {
	Status string `json:"value"`
	Desc   string `json:"label"`
}

// 定义一个自定义排序顺序
var statusOrder = []string{
	RenewStatusPending,
	RenewStatusSecondLevelReview,
	RenewStatusFirstLevelReview,
	RenewStatusHeadOfficeReview,
	RenewStatusRepair,
	RenewStatusCompleted,
	RenewStatusRejected,
}

var renewIssues = [...]string{
	"花屏",
	"线条",
	"触摸异常",
	"不开机",
	"自动重启",
	"摄像头异常",
	"摄像头翻转异常",
	"耗电快",
	"不充电",
	"其他",
}

var renewCheckFacade = [...]string{
	"整机好",
	"整机坏",
	"外观好性能坏",
	"外观坏性能好",
}

func GetRenewStatus() map[string]string {
	// 创建一个新的 map 来存储副本
	copyMap := make(map[string]string)
	// 复制原始 map 的键值对到新的 map 中
	for key, value := range renewStatusMap {
		copyMap[key] = value
	}
	return copyMap
}

func GetRenewNextStatusMap() map[string]string {
	// 创建一个新的 map 来存储副本
	copyMap := make(map[string]string)
	// 复制原始 map 的键值对到新的 map 中
	for key, value := range renewNextStatusMap {
		copyMap[key] = value
	}
	return copyMap
}

// GetRenewStatusSlice 定义一个函数将 map 转换为切片并排序
func GetRenewStatusSlice() []StatusItem {
	var statusSlice []StatusItem
	// 将 map 中的元素添加到切片中
	for status, desc := range renewStatusMap {
		statusSlice = append(statusSlice, StatusItem{Status: status, Desc: desc})
	}

	// 自定义排序函数
	sort.Slice(statusSlice, func(i, j int) bool {
		var idxI, idxJ int
		for k, v := range statusOrder {
			if v == statusSlice[i].Status {
				idxI = k
			}
			if v == statusSlice[j].Status {
				idxJ = k
			}
		}
		return idxI < idxJ
	})

	return statusSlice
}

func GetRenewStatusStringSlice() []string {
	return statusOrder[:]
}

func GetRenewIssues() []string {
	return renewIssues[:]
}

func GetRenewCheckFacade() []string {
	return renewCheckFacade[:]
}
