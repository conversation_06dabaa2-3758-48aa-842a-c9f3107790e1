package endpoint

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/handler"
	"marketing/internal/service"
	"net/http"
)

// 定义 MemberInfoController
type AdminUsersController struct {
	Service *service.AdminUsersService
}

func (control *AdminUsersController) GetAllAgencyTrees(c *gin.Context) {
	data, err := control.Service.GetAllAgencyTrees()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
	}
	handler.Success(c, gin.H{
		"data": data,
	})
	return
}
