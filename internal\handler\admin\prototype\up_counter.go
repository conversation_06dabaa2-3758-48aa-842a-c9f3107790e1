package prototype

import (
	"fmt"
	"marketing/internal/api/prototype"
	"marketing/internal/handler"
	service "marketing/internal/service/prototype"

	"github.com/gin-gonic/gin"
)

// UpCounterHandler 上柜机型
type UpCounterHandler interface {
	GetList(c *gin.Context)
	UpdateStatus(c *gin.Context)
}

type upCounterHandler struct {
	service service.UpCounterService
}

// NewUpCounterHandler 创建上样计数器控制器
func NewUpCounterHandler(service service.UpCounterService) UpCounterHandler {
	return &upCounterHandler{
		service: service,
	}
}

// GetList 获取上柜机型列表
func (h *upCounterHandler) GetList(c *gin.Context) {
	var req prototype.UpCounterSearch
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	req.SetDefaults()
	list, total, err := h.service.GetList(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, gin.H{
		"list":  list,
		"total": total,
	})
}

// UpdateStatus 更新样机上柜状态
// @Summary 更新样机上柜状态
// @Description 更新样机上柜状态，当is_up_counter=1时需要提供market_date和price参数
// @Tags 样机管理
// @Accept json
// @Produce json
// @Param id query int true "机型ID"
// @Param is_up_counter query int true "是否上柜，0-否，1-是"
// @Param market_date query string false "上市日期，格式：2006-01-02，当is_up_counter=1时必填"
// @Param price query number false "上市价格，当is_up_counter=1时必填"
// @Success 200 {object} handler.Response
// @Failure 400 {object} handler.Response
// @Router /api/admin/prototype/up_counter/update_status [post]
func (h *upCounterHandler) UpdateStatus(c *gin.Context) {
	var req prototype.UpCounterStatusUpdate
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}

	// 验证参数
	if *req.IsUpCounter == 1 && req.MarketDate == "" {
		handler.Error(c, fmt.Errorf("当is_up_counter=1时，market_date和price参数必填"))
		return
	}

	err := h.service.UpdateStatus(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c)
}
