package reimbursement

import (
	"marketing/internal/api"
	"marketing/internal/model"
	"marketing/internal/pkg/types"
	"time"
)

type PolicyListSearch struct {
	api.PaginationParams
	Archive int    `form:"archive" json:"archive"` // 是否归档
	Name    string `form:"name" json:"name"`       // 政策名称
	Type    int    `form:"type" json:"type"`       // 政策类型
}

// PolicyDetailResp represents the response for policy detail
type PolicyDetailResp struct {
	Policy   *model.ReimbursementPolicy `json:"policy"`        // 政策详情
	Products []ProductResp              `json:"products_info"` // 产品信息列表
}

// PolicySummaryReq represents the request for policy summary
type PolicySummaryReq struct {
	api.PaginationParams
	Archive int `form:"archive" json:"archive"` // 是否归档
}

// PolicySummaryItem represents a summary item for a reimbursement policy
type PolicySummaryItem struct {
	ID          int    `json:"id"`           // 政策ID
	Name        string `json:"name"`         // 政策名称
	StartTime   string `json:"start_time"`   // 开始时间
	EndTime     string `json:"end_time"`     // 结束时间
	PolicyType  string `json:"policy_type"`  // 政策类型
	Archive     int    `json:"archive"`      // 是否归档
	ArchiveTime string `json:"archive_time"` // 归档时间
	Count       int    `json:"count"`        // 申请总数
	UnderAudit  int    `json:"under_audit"`  // 审核中数量
	AuditFinish int    `json:"audit_finish"` // 审核通过数量
	AuditFail   int    `json:"audit_fail"`   // 审核失败数量
}

// PolicyCreateReq represents the request to create a new reimbursement policy
type PolicyCreateReq struct {
	Name             string    `json:"name" binding:"required"`       // 政策名称
	StartTime        string    `json:"start_time" binding:"required"` // 开始时间
	EndTime          string    `json:"end_time" binding:"required"`   // 结束时间
	UserName         string    `json:"user_name"`                     // 用户名称
	Phone            string    `json:"phone"`                         // 电话
	Province         string    `json:"province"`                      // 省份
	City             string    `json:"city"`                          // 城市
	District         string    `json:"district"`                      // 区域
	Address          string    `json:"address"`                       // 地址
	Explain          string    `json:"explain"`                       // 说明
	Content          string    `json:"content"`                       // 内容
	Remark           string    `json:"remark"`                        // 备注
	Type             int       `json:"type" binding:"required"`       // 类型
	Declare          int       `json:"declare" default:"1"`           // 申报
	LookOver         string    `json:"look_over" default:"all"`       // 可查看范围, 默认all
	ApproveUids      string    `json:"approve_uids"`                  // 指定核销审核人UID
	ApproveApplyUids string    `json:"approve_apply_uids"`            // 指定核销申请审核人UID
	GiftLimit        int       `json:"gift_limit" default:"0"`        // 核销上限
	ProductsInfo     []Product `json:"products_info"`                 // 产品信息
}

// PolicyUpdateReq represents the request to update an existing reimbursement policy
type PolicyUpdateReq struct {
	ID               int       `json:"id" binding:"required"`         // 政策ID
	Name             string    `json:"name" binding:"required"`       // 政策名称
	StartTime        string    `json:"start_time" binding:"required"` // 开始时间
	EndTime          string    `json:"end_time" binding:"required"`   // 结束时间
	UserName         string    `json:"user_name"`                     // 用户名称
	Phone            string    `json:"phone"`                         // 电话
	Province         string    `json:"province"`                      // 省份
	City             string    `json:"city"`                          // 城市
	District         string    `json:"district"`                      // 区域
	Address          string    `json:"address"`                       // 地址
	Explain          string    `json:"explain"`                       // 说明
	Content          string    `json:"content"`                       // 内容
	Remark           string    `json:"remark"`                        // 备注
	Declare          int       `json:"declare" default:"1"`           // 申报
	LookOver         string    `json:"look_over" default:"all"`       // 可查看范围, 默认all
	ApproveUids      string    `json:"approve_uids"`                  // 指定核销审核人UID
	ApproveApplyUids string    `json:"approve_apply_uids"`            // 指定核销申请审核人UID
	GiftLimit        int       `json:"gift_limit" default:"0"`        // 核销上限
	ProductsInfo     []Product `json:"products_info"`                 // 产品信息
}

// PolicyArchiveReq represents the request to archive a reimbursement policy
type PolicyArchiveReq struct {
	ID      int `json:"id" binding:"required"` // 政策ID
	Archive int `json:"archive"`               // 归档状态：0-取消归档，1-归档
}

// FileMetadata represents file information with metadata
type FileMetadata struct {
	Name    string `json:"name"`    // 文件名
	Mime    string `json:"mime"`    // MIME类型
	Size    int64  `json:"size"`    // 文件大小
	URL     string `json:"url"`     // 文件URL
	Explain string `json:"explain"` // 说明
	UID     int64  `json:"uid"`     // 文件UID
	Status  string `json:"status"`  // 状态
}

// Product represents a promotional product in the reimbursement policy
type Product struct {
	Name                  string         `json:"name" binding:"required"`                 // 产品名称
	Norm                  string         `json:"norm" binding:"required"`                 // 产品规格
	Unit                  string         `json:"unit" binding:"required"`                 // 产品单位
	IncludeTaxPrice       float64        `json:"include_tax_price"`                       // 含税价格
	ExcludeTaxPrice       float64        `json:"exclude_tax_price"`                       // 不含税价格
	ReimbursementPrice    float64        `json:"reimbursement_price" binding:"required"`  // 核销价格
	IncludeTaxAccountInfo []FileMetadata `json:"include_tax_account_info"`                // 含税账户信息
	ExcludeTaxAccountInfo []FileMetadata `json:"exclude_tax_account_info"`                // 不含税账户信息
	CommunicationLetter   []FileMetadata `json:"communication_letter" binding:"required"` // 售后交流函
	Preview               []FileMetadata `json:"preview" binding:"required"`              // 预览图
}

// ProductResp represents a promotional product in the response
type ProductResp struct {
	ID                    int              `json:"id"`                       // 产品ID
	PolicyID              int              `json:"policy_id"`                // 政策ID
	Name                  string           `json:"name"`                     // 产品名称
	Norm                  string           `json:"norm"`                     // 产品规格
	Unit                  string           `json:"unit"`                     // 产品单位
	IncludeTaxPrice       float64          `json:"include_tax_price"`        // 含税价格
	ExcludeTaxPrice       float64          `json:"exclude_tax_price"`        // 不含税价格
	ReimbursementPrice    float64          `json:"reimbursement_price"`      // 核销价格
	IncludeTaxAccountInfo []FileMetadata   `json:"include_tax_account_info"` // 含税账户信息
	ExcludeTaxAccountInfo []FileMetadata   `json:"exclude_tax_account_info"` // 不含税账户信息
	CommunicationLetter   []FileMetadata   `json:"communication_letter"`     // 售后交流函
	Preview               []FileMetadata   `json:"preview"`                  // 预览图
	CreatedAt             types.CustomTime `json:"created_at"`               // 创建时间
}

// ReimbursementSummaryGroupByPolicyResp 核销申请单按政策分组统计响应
type ReimbursementSummaryGroupByPolicyResp struct {
	PolicyID       int    `json:"policy_id"`        // 政策ID
	PolicyName     string `json:"policy_name"`      // 政策名称
	PolicyType     string `json:"policy_type"`      // 政策类型
	StandardType   string `json:"standard_type"`    // 标准类型
	Archive        int    `json:"archive"`          // 是否归档
	Type           int    `json:"type"`             // 政策类型
	PendingCount   int    `json:"pending_count"`    // 待核销数量
	InProcessCount int    `json:"in_process_count"` // 核销中数量
	CompletedCount int    `json:"completed_count"`  // 已核销数量
	InvalidCount   int    `json:"invalid_count"`    // 已作废数量
}

type CompanySummaryReq struct {
	api.PaginationParams
	TopAgency    int    `form:"top_agency"`                                    // 顶级代理
	CompanyID    int    `form:"company_id"`                                    // 公司ID
	StartTime    string `form:"start_time"`                                    // 开始时间
	EndTime      string `form:"end_time"`                                      // 结束时间
	PolicyID     int    `form:"policy_id" json:"policy_id" binding:"required"` // 政策ID
	Code         string `form:"code"`                                          // 编码
	PolicyType   string
	StandardType string
}

// CompanySummaryStatResp 核销列表统计响应
type CompanySummaryStatResp struct {
	Amount                    float64 `json:"amount"`                       // 总金额
	HalfAmount                float64 `json:"half_amount"`                  // 折半金额
	ActualAmount              float64 `json:"actual_amount"`                // 实际金额
	ActualQuantity            float64 `json:"actual_quantity"`              // 实际数量
	QuantityTotal             float64 `json:"quantity_total"`               // 总数量
	NoReimbursementAmount     float64 `json:"no_reimbursement_amount"`      // 未核销金额
	TurnAmount                float64 `json:"turn_amount"`                  // 转入金额
	HalfQuantityTotal         float64 `json:"half_quantity_total"`          // 折半总数量
	StandardAmount            float64 `json:"standard_amount"`              // 标准金额
	StandardQuantity          float64 `json:"standard_quantity"`            // 标准数量
	ReduceAdvertExpenseAmount float64 `json:"reduce_advert_expense_amount"` // 减少广告费用金额
	AeIncreaseDiscountAmount  float64 `json:"ae_increase_discount_amount"`  // 增加折扣金额
	StandardBalanceQuota      float64 `json:"standard_balance_quota"`       // 标准余额配额
	InvalidAmount             float64 `json:"invalid_amount"`               // 作废金额
}

// ReimbursementSummaryPageResp 核销列表分页响应
type ReimbursementSummaryPageResp struct {
	Code                      string    `json:"code"`                         // 编码
	TopAgency                 string    `json:"top_agency"`                   // 顶级代理
	CompanyID                 string    `json:"company_id"`                   // 公司ID
	Company                   string    `json:"company"`                      // 公司名称
	StandardType              string    `json:"standard_type"`                // 标准类型
	AgencyName                string    `json:"agency_name"`                  // 代理商名称
	StandardAmount            float64   `json:"standard_amount"`              // 标准金额
	StandardQuantity          float64   `json:"standard_quantity"`            // 标准数量
	StandardBalanceQuota      float64   `json:"standard_balance_quota"`       // 标准余额配额
	ReduceAdvertExpenseAmount float64   `json:"reduce_advert_expense_amount"` // 减少广告费用金额
	AeIncreaseDiscountAmount  float64   `json:"ae_increase_discount_amount"`  // 增加折扣金额
	Amount                    float64   `json:"amount"`                       // 总金额
	HalfAmount                float64   `json:"half_amount"`                  // 折半金额
	ActualAmount              float64   `json:"actual_amount"`                // 实际金额
	ActualQuantity            float64   `json:"actual_quantity"`              // 实际数量
	QuantityTotal             float64   `json:"quantity_total"`               // 总数量
	HalfQuantityTotal         float64   `json:"half_quantity_total"`          // 折半总数量
	NoReimbursementAmount     float64   `json:"no_reimbursement_amount"`      // 未核销金额
	TurnAmount                float64   `json:"turn_amount"`                  // 转入金额
	InvalidAmount             float64   `json:"invalid_amount"`               // 作废金额
	ReimbursementType         int       `json:"reimbursement_type"`           // 核销类型
	UpdatedAt                 time.Time `json:"-"`                            // 更新时间
	UpdatedAtStr              string    `json:"updated_at"`                   // 更新时间字符串
}
