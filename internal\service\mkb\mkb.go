package mkb

import (
	"errors"
	"gorm.io/gorm"
	"marketing/internal/api/mkb"
	"marketing/internal/consts"
	"marketing/internal/dao/admin_user"
	"marketing/internal/dao/endpoint"
	"marketing/internal/dao/prototype"
	"marketing/internal/dao/rbcare_data"
	"marketing/internal/dao/warranty"
	appError "marketing/internal/pkg/errors"
	"marketing/internal/pkg/types"
	"time"

	"github.com/gin-gonic/gin"
)

// 萌酷宝可见model
var modelIDs = []uint{449}

type MkbService interface {
	GetWarrantyLists(c *gin.Context, param *mkb.WarrantyReq) ([]*mkb.WarrantyResp, int64, error)
	GetPrototypeLists(c *gin.Context, param *mkb.PrototypeReq) ([]*mkb.PrototypeResp, int64, error)
	GetActivatedLists(c *gin.Context, req *mkb.WarrantyReq) ([]*mkb.WarrantyResp, int64, error)
	PrototypeOut(c *gin.Context, barcode string) error
}

type mkbService struct {
	warrantyDao      warranty.InterfaceWarranty
	endpointDao      endpoint.EndpointDao
	prototypeDao     prototype.PrototypeInterface
	userDao          admin_user.UserDao
	acDevicesUniqDao rbcare_data.AcDevicesUniqDao
	prototypeCache   prototype.PrototypeCache
}

func NewMkbService(warrantyDao warranty.InterfaceWarranty,
	endpointDao endpoint.EndpointDao,
	prototypeDao prototype.PrototypeInterface,
	userDao admin_user.UserDao,
	acDevicesUniqDao rbcare_data.AcDevicesUniqDao,
	prototypeCache prototype.PrototypeCache) MkbService {
	return &mkbService{
		warrantyDao:      warrantyDao,
		endpointDao:      endpointDao,
		prototypeDao:     prototypeDao,
		userDao:          userDao,
		acDevicesUniqDao: acDevicesUniqDao,
		prototypeCache:   prototypeCache,
	}
}

func (s *mkbService) GetWarrantyLists(c *gin.Context, req *mkb.WarrantyReq) ([]*mkb.WarrantyResp, int64, error) {
	var list []*mkb.WarrantyResp
	var count int64
	var param warranty.ListParams
	param.Barcode = req.Barcode
	param.StartTime = req.BuyDateStart
	param.EndTime = req.BuyDateEnd
	param.ModelID = modelIDs
	param.Page = req.Page
	param.PageSize = req.PageSize
	param.Status = 1 //1:正常保卡
	data, count, err := s.warrantyDao.GetLists(c, &param)

	if err != nil {
		return nil, 0, err
	}
	endpointIDs := make([]int, 0)
	for _, v := range data {
		if v.Endpoint != 0 {
			endpointIDs = append(endpointIDs, v.Endpoint)
		}
	}
	endpointMap, err := s.endpointDao.GetEndpointMap(c, endpointIDs)
	if err != nil {
		return nil, 0, err
	}
	for _, v := range data {
		list = append(list, &mkb.WarrantyResp{
			Barcode:      v.Barcode,
			BuyDate:      types.CustomTime(v.BuyDate),
			Model:        v.Model,
			EndpointName: endpointMap[uint(v.Endpoint)],
		})
	}
	return list, count, nil
}

func (s *mkbService) GetPrototypeLists(c *gin.Context, req *mkb.PrototypeReq) ([]*mkb.PrototypeResp, int64, error) {
	var list []*mkb.PrototypeResp
	var count int64
	var param prototype.ListParams
	param.ModelID = modelIDs
	param.Status = req.Status
	param.CreatedAtStart = req.CreatedAtStart
	param.CreatedAtEnd = req.CreatedAtEnd
	param.Page = req.Page
	param.PageSize = req.PageSize
	data, count, err := s.prototypeDao.GetList(c, &param)
	if err != nil {
		return nil, 0, err
	}
	endpointIDs := make([]int, 0)
	userIDs := make([]uint, 0)
	for _, v := range data {
		endpointIDs = append(endpointIDs, v.Endpoint)
		userIDs = append(userIDs, uint(v.UserID))
	}
	endpointMap, err := s.endpointDao.GetEndpointMap(c, endpointIDs)
	if err != nil {
		return nil, 0, err
	}
	userMap, err := s.userDao.GetMapByIDs(c, userIDs)
	typeMap := consts.GetPromotionTypeMap()
	if err != nil {
		return nil, 0, err
	}
	for _, v := range data {
		list = append(list, &mkb.PrototypeResp{
			Barcode:      v.Barcode,
			Number:       v.Number,
			Model:        v.Model,
			TopAgency:    v.TopAgency,
			SecondAgency: v.SecondAgency,
			Endpoint:     endpointMap[uint(v.Endpoint)],
			UserName:     userMap[uint(v.UserID)].Name,
			Type:         typeMap[uint8(v.Type)],
			Status:       v.Status,
			CreatedAt:    types.CustomTime(v.CreatedAt),
			UpdatedAt:    types.CustomTime(v.UpdatedAt),
			RemovedAt:    types.CustomTime(v.RemovedAt.Time),
		})
	}
	return list, count, nil
}

func (s *mkbService) GetActivatedLists(c *gin.Context, req *mkb.WarrantyReq) ([]*mkb.WarrantyResp, int64, error) {
	var list []*mkb.WarrantyResp
	var count int64
	var param warranty.ListParams
	param.Barcode = req.Barcode
	param.ActivatedStart = req.ActivatedDateStart
	param.ActivatedEnd = req.ActivatedDateEnd
	param.ModelID = modelIDs
	activated := true
	param.IsActivated = &activated
	param.Page = req.Page
	param.PageSize = req.PageSize
	data, count, err := s.warrantyDao.GetLists(c, &param)
	if err != nil {
		return nil, 0, err
	}
	acDevicesUniqIDs := make([]int, 0)
	for _, v := range data {
		acDevicesUniqIDs = append(acDevicesUniqIDs, int(v.ActivatedID))
	}

	acDevicesUniqMap, err := s.acDevicesUniqDao.GetMapByIDs(c, acDevicesUniqIDs)
	if err != nil {
		return nil, 0, err
	}
	for _, v := range data {
		list = append(list, &mkb.WarrantyResp{
			Barcode:           v.Barcode,
			Model:             v.Model,
			ActivatedProvince: acDevicesUniqMap[int(v.ActivatedID)].Province,
			ActivatedCity:     acDevicesUniqMap[int(v.ActivatedID)].City,
			ActivatedDate:     types.CustomTime(v.ActivatedAt),
		})
	}
	return list, count, nil
}

// PrototypeOut 样机离库
func (s *mkbService) PrototypeOut(c *gin.Context, barcode string) error {
	pt, err := s.prototypeDao.GetByBarcode(c, barcode, 1)
	if errors.Is(err, gorm.ErrRecordNotFound) || pt == nil {
		return appError.NewErr("样机已离库或者不存在")
	}
	if err != nil {
		return err
	}
	var ptMap = make(map[string]any)
	ptMap["status"] = 0
	ptMap["removed_at"] = time.Now()

	err = s.prototypeDao.Update(c, pt.ID, ptMap)
	if err != nil {
		return err
	}
	//修改缓存
	if pt.Number != "" {
		err = s.prototypeCache.Del(c, pt.Number)
		if err != nil {
			return err
		}
	}

	return nil
}
