package utils

import (
	"fmt"
	"strconv"
)

func IntHas(i int, vs []int) bool {
	for _, v := range vs {
		if v == i {
			return true
		}
	}
	return false
}

func UintHas(i uint, vs []uint) bool {
	for _, v := range vs {
		if v == i {
			return true
		}
	}
	return false
}

func StringHas(s string, strS []string) bool {
	for _, str := range strS {
		if str == s {
			return true
		}
	}
	return false
}

// GetStrArraySameValue 取两个字符串中相同的值
func GetStrArraySameValue(str1 []string, str2 []string) (v []string) {
	for _, s := range str1 {
		if StringHas(s, str2) {
			v = append(v, s)
		}
	}
	return
}

// GetStrArrayDiffValue 获取字符串2中,不存在字符串1中的值
func GetStrArrayDiffValue(str1 []string, str2 []string) (v []string) {
	for _, s := range str2 {
		if !StringHas(s, str1) {
			v = append(v, s)
		}
	}
	return
}

// MergeStrArrayDiffValue 合并字符串
func MergeStrArrayDiffValue(str1 []string, str2 []string) []string {
	for _, s := range str2 {
		if !StringHas(s, str1) {
			str1 = append(str1, s)
		}
	}
	return str1
}

func StringToInt(s string) int {
	i, err := strconv.Atoi(s)
	if err != nil {
		fmt.Println(err.Error())
		return 0
	}
	return i
}

func IntToString(i int) string {
	return strconv.Itoa(i)
}

func StringToFloat64(s string) float64 {
	f, err := strconv.ParseFloat(s, 64)
	if err != nil {
		return 0
	}
	return f
}

func AddIntArrayValue(vs []int, v int) []int {
	if !IntHas(v, vs) {
		vs = append(vs, v)
	}
	return vs
}

func AddStrArrayValue(vs []string, v string) []string {
	if !StringHas(v, vs) {
		vs = append(vs, v)
	}
	return vs
}

func ReverseIntArray(vs *[]int) {
	length := len(*vs)
	for i, j := 0, length; i < length/2; i++ {
		(*vs)[i], (*vs)[j-1] = (*vs)[j-1], (*vs)[i]
		j--
	}
}

func ReverseUintArray(vs *[]uint) {
	length := len(*vs)
	for i, j := 0, length; i < length/2; i++ {
		(*vs)[i], (*vs)[j-1] = (*vs)[j-1], (*vs)[i]
		j--
	}
}
