package system

import (
	"marketing/internal/model"
	"time"
)

type UserLog struct {
	// 定义需要记录的字段
	ID       uint    `json:"id,omitempty"`
	Username string  `json:"username,omitempty"`
	Name     string  `json:"name,omitempty"`
	Phone    *string `json:"phone,omitempty"`
	Avatar   *string `json:"avatar,omitempty"`
	Status   *uint   `json:"status,omitempty"`
	Roles    *[]struct {
		ID   uint   `json:"id"`
		Name string `json:"name"`
	} `json:"roles,omitempty" gorm:"-"`
	UserGroup *struct {
		ID   uint   `json:"id"`
		Name string `json:"name"`
	} `json:"user_group,omitempty" gorm:"-"`
	GroupName *string `json:"group_name,omitempty" gorm:"-"`
	Tags      *[]struct {
		ID   uint   `json:"id"`
		Name string `json:"tag_name"`
	} `json:"tags,omitempty" gorm:"-"`
	Agency *[]struct {
		ID   uint   `json:"id"`
		Name string `json:"name"`
	} `json:"agency,omitempty" gorm:"-"`
	Endpoint *[]struct {
		ID   uint   `json:"id"`
		Name string `json:"name"`
	} `json:"endpoints,omitempty" gorm:"-"`
	CreatedAt *time.Time `json:"created_at,omitempty"`
	UpdatedAt *time.Time `json:"updated_at,omitempty"`
}

// AdminUserLogResp represents the user log model
type AdminUserLogResp struct {
	model.AdminUserLog
	RequestJson UserLog `json:"request,omitempty"`
	BeforeJson  UserLog `json:"before,omitempty"`
	AfterJson   UserLog `json:"after,omitempty"`
}
