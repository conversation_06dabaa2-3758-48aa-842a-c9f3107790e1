package prototype

import (
	apiProto "marketing/internal/api/prototype"
	"marketing/internal/consts"
	daoProto "marketing/internal/dao/prototype"
	"marketing/internal/pkg/types"

	"github.com/gin-gonic/gin"
)

// UpCounterEndpointService 上柜终端服务
type UpCounterEndpointService interface {
	GetList(ctx *gin.Context, req *apiProto.UpCounterEndpointSearch) ([]*apiProto.UpCounterEndpointListVo, int64, error)
	Approve(ctx *gin.Context, approve *apiProto.UpCounterApprove) error
}

type upCounterEndpointService struct {
	upCounterDao daoProto.UpCounterEndpoint
}

// NewUpCounterEndpointService 创建服务实例
func NewUpCounterEndpointService(upCounterDao daoProto.UpCounterEndpoint) UpCounterEndpointService {
	return &upCounterEndpointService{
		upCounterDao: upCounterDao,
	}
}

func (s *upCounterEndpointService) GetList(ctx *gin.Context, req *apiProto.UpCounterEndpointSearch) ([]*apiProto.UpCounterEndpointListVo, int64, error) {
	list, count, err := s.upCounterDao.GetList(ctx, req)
	if err != nil {
		return nil, 0, err
	}

	var result []*apiProto.UpCounterEndpointListVo
	endpointTypeMap := consts.GetEndpointNameMap()
	for _, item := range list {
		vo := &apiProto.UpCounterEndpointListVo{
			ID:            item.ID,
			Code:          item.Code,
			Name:          item.Name,
			Address:       item.Address,
			Manager:       item.Manager,
			Phone:         item.Phone,
			Type:          item.Type,
			TypeName:      endpointTypeMap[consts.EndpointType(item.Type)],
			Barcode:       item.Barcode,
			ApproveStatus: item.Status,
			ApproveTime:   types.CustomTime(item.ApproveTime),
			Reason:        item.Reason,
			Photos:        item.Photo,
			Model:         item.Model,
		}
		result = append(result, vo)
	}

	return result, count, nil
}

func (s *upCounterEndpointService) Approve(ctx *gin.Context, approve *apiProto.UpCounterApprove) error {
	return s.upCounterDao.Approve(ctx, approve)
}
