package report

import (
	api "marketing/internal/api/report"
	"marketing/internal/handler"
	appError "marketing/internal/pkg/errors"
	"marketing/internal/service/report"

	"github.com/gin-gonic/gin"
)

type LearningRoomHandler interface {
	GetLearningRoomReport(c *gin.Context)
}

type learningRoomHandler struct {
	service report.LearningRoomService
}

func NewLearningRoomHandler(service report.LearningRoomService) LearningRoomHandler {
	return &learningRoomHandler{
		service: service,
	}
}

func (h *learningRoomHandler) GetLearningRoomReport(c *gin.Context) {
	var req api.MarginReportReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	if req.CategoryID != 24 && req.CategoryID != 28 {
		handler.Error(c, appError.NewErr("分类ID不对"))
		return
	}
	result, err := h.service.GetLearningRoomReport(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, result)
}
