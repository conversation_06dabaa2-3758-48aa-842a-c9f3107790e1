package agency

import (
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"marketing/internal/dao"
	"marketing/internal/handler"
	"marketing/internal/model"
	"marketing/internal/pkg/e"
	"marketing/internal/pkg/errors"
	"marketing/internal/pkg/utils"
	"marketing/internal/service"
)

type RepairBill struct {
	svc service.RepairBillSvc
}

func NewRepairBill(svc service.RepairBillSvc) *RepairBill {
	return &RepairBill{
		svc: svc,
	}
}

func (r *RepairBill) GetRepairBillList(c *gin.Context) {
	list, total := r.svc.GetRepairBillList(c, dao.GetRepairBillListParam{
		AgencyId:      e.ReqParamInt(c, "agency_id"),
		BillNumber:    e.ReqParamStr(c, "bill_number"),
		SnCode:        e.ReqParamStr(c, "sn_code"),
		CustomerName:  e.ReqParamStr(c, "customer_name"),
		CustomerPhone: e.ReqParamStr(c, "customer_phone"),
		PageNum:       e.ReqParamInt(c, "page_num"),
		PageSize:      e.ReqParamInt(c, "page_size"),
	})

	handler.Success(c, gin.H{
		"list":  list,
		"total": total,
	})
}

func (r *RepairBill) GetRepairBill(c *gin.Context) {
	repairBill := r.svc.GetRepairBill(c, e.ReqParamInt(c, "id"))
	if repairBill == nil {
		handler.Error(c, errors.NewErr("维修订单不存在"))
		return
	}

	handler.Success(c, repairBill)
}

func (r *RepairBill) EditRepairBill(c *gin.Context) {
	buyDate, err := utils.GetTimeDay(e.ReqParamStr(c, "buy_date"))
	if err != nil {
		handler.Error(c, errors.NewErr("购机日期错误:"+err.Error()))
		return
	}

	receiveMachineDate, err := utils.GetTimeDay(e.ReqParamStr(c, "receive_machine_date"))
	if err != nil {
		handler.Error(c, errors.NewErr("维修收机日期错误:"+err.Error()))
		return
	}

	expressDate, err := utils.GetTimeDay(e.ReqParamStr(c, "express_date"))
	if err != nil {
		handler.Error(c, errors.NewErr("快递寄出日期错误:"+err.Error()))
		return
	}

	malfunctionIds := make([]int, 0)
	_ = json.Unmarshal([]byte(e.ReqParamStr(c, "malfunction_ids")), &malfunctionIds)

	accessoryParam := make([]*dao.RepairBillMachineAccessoryParam, 0)
	_ = json.Unmarshal([]byte(e.ReqParamStr(c, "accessory_list")), &accessoryParam)

	err = r.svc.EditRepairBill(c, model.RepairBill{
		Id:                 e.ReqParamInt(c, "id"),
		BuyDate:            buyDate,
		ReceiveMachineDate: receiveMachineDate,
		RepairSource:       e.ReqParamStr(c, "repair_source"),
		SnCode:             e.ReqParamStr(c, "sn_code"),
		CustomerName:       e.ReqParamStr(c, "customer_name"),
		CustomerPhone:      e.ReqParamStr(c, "customer_phone"),
		ModelId:            e.ReqParamStr(c, "model_id"),
		Description:        e.ReqParamStr(c, "description"),
		RepairReasonType:   e.ReqParamInt(c, "repair_reason_type"),
		RepairType:         e.ReqParamInt(c, "repair_type"),
		ExpressDate:        expressDate,
		ExpressNumber:      e.ReqParamStr(c, "express_number"),
		RepairDeal:         e.ReqParamStr(c, "repair_deal"),
		Remark:             e.ReqParamStr(c, "remark"),
		IsChecked:          e.ReqParamInt(c, "is_checked"),
	}, malfunctionIds, accessoryParam)
	if err != nil {
		handler.Error(c, errors.NewErr("编辑维修订单:"+err.Error()))
		return
	}

	handler.Success(c, gin.H{})
}

func (r *RepairBill) DeleteRepairBill(c *gin.Context) {
	err := r.svc.DeleteRepairBill(c, e.ReqParamInt(c, "id"))
	if err == nil {
		handler.Error(c, errors.NewErr("删除维修订单:"+err.Error()))
		return
	}

	handler.Success(c, gin.H{})
}

func (r *RepairBill) ExportRepairBillList(c *gin.Context) {
	file := r.svc.ExportRepairBillList(c, dao.GetRepairBillListParam{
		AgencyId:      e.ReqParamInt(c, "agency_id"),
		BillNumber:    e.ReqParamStr(c, "bill_number"),
		SnCode:        e.ReqParamStr(c, "sn_code"),
		CustomerName:  e.ReqParamStr(c, "customer_name"),
		CustomerPhone: e.ReqParamStr(c, "customer_phone"),
		PageNum:       e.ReqParamInt(c, "page_num"),
		PageSize:      e.ReqParamInt(c, "page_size"),
	})
	if file != nil {
		c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
		c.Header("Content-Disposition", "attachment; filename="+fmt.Sprintf("维修工作单表.xls"))
		if err := file.Write(c.Writer); err != nil {
			handler.Error(c, errors.NewErr(err.Error()))
			return
		}
	}

	handler.Success(c, gin.H{})
}
