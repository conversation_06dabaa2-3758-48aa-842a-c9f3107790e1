package dao

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"marketing/internal/model"
	"marketing/internal/pkg/db"
)

type MachineAccessoryDao interface {
	CreateMachineAccessory(c *gin.Context, machineAccessory *model.MachineAccessory) error
	DeleteMachineAccessory(c *gin.Context, id int) error
	UpdateMachineAccessory(c *gin.Context, id int, uMap map[string]interface{}) error
	GetAllMachineAccessory(c *gin.Context) (list []*model.MachineAccessory)
	GetMachineAccessoryById(c *gin.Context, id int) *model.MachineAccessory
	GetMachineAccessoryByIds(c *gin.Context, ids []int) (list []*model.MachineAccessory)
}

// MachineAccessoryDaoImpl 实现 MachineAccessoryDao 接口
type MachineAccessoryDaoImpl struct {
	db *gorm.DB
}

// NewMachineAccessoryDao 创建 MachineAccessoryDao 实例
func NewMachineAccessoryDao() MachineAccessoryDao {
	return &MachineAccessoryDaoImpl{
		db: db.GetDB(""),
	}
}

func (d *MachineAccessoryDaoImpl) CreateMachineAccessory(c *gin.Context, machineAccessory *model.MachineAccessory) error {
	return d.db.WithContext(c).Create(machineAccessory).Error
}

func (d *MachineAccessoryDaoImpl) DeleteMachineAccessory(c *gin.Context, id int) error {
	return d.db.WithContext(c).Delete(&model.MachineAccessory{}, "id = ?", id).Error
}

func (d *MachineAccessoryDaoImpl) UpdateMachineAccessory(c *gin.Context, id int, uMap map[string]interface{}) error {
	return d.db.WithContext(c).Model(&model.MachineAccessory{}).Where("id = ?", id).Updates(uMap).Error
}

func (d *MachineAccessoryDaoImpl) GetAllMachineAccessory(c *gin.Context) (list []*model.MachineAccessory) {
	d.db.WithContext(c).Model(&model.MachineAccessory{}).Find(&list)
	return
}

func (d *MachineAccessoryDaoImpl) GetMachineAccessoryById(c *gin.Context, id int) *model.MachineAccessory {
	var machineAccessory model.MachineAccessory
	err := d.db.WithContext(c).Model(&model.MachineAccessory{}).Where("id = ?", id).First(&machineAccessory).Error
	if err != nil {
		return nil
	}
	return &machineAccessory
}

func (d *MachineAccessoryDaoImpl) GetMachineAccessoryByIds(c *gin.Context, ids []int) (list []*model.MachineAccessory) {
	d.db.WithContext(c).Model(&model.MachineAccessory{}).Where("id in (?)", ids).Find(&list)
	return
}
