package auth

import (
	"errors"
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
	v9 "github.com/redis/go-redis/v9"
)

const _ThirdPartyTokenMap string = "third_party_token:%s" // 第三方应用ID到token的映射

// ThirdPartyAuthCacheInterface 定义第三方认证缓存接口
type ThirdPartyAuthCacheInterface interface {
	SetToken(ctx *gin.Context, token string, appID int64, expireDuration time.Duration) error
	GetToken(ctx *gin.Context, token string) (int64, error)
	CheckTokenExists(ctx *gin.Context, token string) bool
}

// ThirdPartyAuthCache 第三方认证缓存实现
type ThirdPartyAuthCache struct {
	redisClient *v9.Client
}

// NewThirdPartyAuthCache 创建第三方认证缓存实例
func NewThirdPartyAuthCache(redisClient *v9.Client) ThirdPartyAuthCacheInterface {
	return &ThirdPartyAuthCache{
		redisClient: redisClient,
	}
}

// SetToken 存储第三方认证token
func (a *ThirdPartyAuthCache) SetToken(ctx *gin.Context, token string, appID int64, expireDuration time.Duration) error {
	// 清理该应用的过期token
	tokenMapKey := fmt.Sprintf(_ThirdPartyTokenMap, token)

	// 存储 token -> appID 映射，并设置过期时间
	if err := a.redisClient.Set(ctx, tokenMapKey, appID, expireDuration).Err(); err != nil {
		return err
	}

	return nil
}

// GetToken 获取token对应的应用ID
func (a *ThirdPartyAuthCache) GetToken(ctx *gin.Context, token string) (int64, error) {
	tokenMapKey := fmt.Sprintf(_ThirdPartyTokenMap, token)
	appID, err := a.redisClient.Get(ctx, tokenMapKey).Int64()
	if errors.Is(err, v9.Nil) {
		return 0, fmt.Errorf("token not found or expired")
	}
	return appID, err
}

// CheckTokenExists 检查token是否已存在
func (a *ThirdPartyAuthCache) CheckTokenExists(ctx *gin.Context, token string) bool {
	tokenMapKey := fmt.Sprintf(_ThirdPartyTokenMap, token)
	exists, err := a.redisClient.Exists(ctx, tokenMapKey).Result()
	if err != nil {
		return false
	}
	return exists > 0
}
