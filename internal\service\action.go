package service

import (
	"context"
	"marketing/internal/api/action"
	"marketing/internal/consts"
	"marketing/internal/dao"
	"marketing/internal/model"
	myErrors "marketing/internal/pkg/errors"
	"marketing/internal/pkg/log"
	"marketing/internal/pkg/utils"
	"marketing/internal/service/cache"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

type ActionService interface {
	//操作
	ApplyAction(ctx *gin.Context, act model.Actions) (int, error)
	ApplyActionV1(ctx *gin.Context, act action.ApplyActionV1) (int, error)
	AuditAction(ctx *gin.Context, audit action.AuditAction) error
	FinishAction(ctx *gin.Context, finish action.FinishAction) error
	VerifyAction(ctx *gin.Context, verify action.VerifyAction) error
	RecordedAction(c *gin.Context, account action.RecordedAction) error
	DeleteAction(c *gin.Context, id int) error
	UpdateAction(c *gin.Context, uid uint, apply action.UpdateAction) error
	FallbackAction(c *gin.Context, id int) error
	//查找
	SearchActionList(c *gin.Context, params action.SearchActionParams) ([]action.InfoAction, int64, int, int, error)
	GetActionApplyInfo(c *gin.Context, id, uid int) (acts action.ApplyInfoAction, err error)
	GetDropDown(c *gin.Context) ([]map[string]any, error)
	GetActionFinishInfo(c *gin.Context, id, uid int) (action.FinishInfoAction, error)
	GetActionAuditInfo(c *gin.Context, id, uid int) (action.AuditInfoAction, error)
	GetActionVerifyInfo(c *gin.Context, id, uid int) (action.VerifyInfoAction, error)
	PrintFinishAction(c *gin.Context, id int) (action.PrintFinishAction, error)
	//提示
	GetTypeTips(c *gin.Context, name string) ([]action.Tips, error)
	GetEndpointTips(c *gin.Context, agency int, name string) ([]map[string]any, error)
	GetAgencyTips(c *gin.Context, id int, name string) ([]map[string]any, error)
	GetPartitionTips(c *gin.Context) ([]map[string]any, error)
	GetSecondAgencyTips(c *gin.Context, agency int, name string) ([]map[string]any, error)
	GetActionCard(c *gin.Context, id int, tp int) (action.EndpointCard[string], error)
	GetActionRecordedInfo(c *gin.Context, id, uid int) (map[string]any, error)
}

type GormActionService struct {
	dao    dao.ActionDao
	dao1   dao.ActionTypeDao
	cache  cache.ActionCache
	common dao.CommonDao
}

func (g *GormActionService) GetActionRecordedInfo(c *gin.Context, id, uid int) (map[string]any, error) {
	return g.dao.GetActionRecordedInfo(c, id, uid)
}

func (g *GormActionService) FallbackAction(c *gin.Context, id int) error {
	status, err := g.dao.GetActionStatus(c, uint(id))
	if err != nil {
		return err
	}
	return g.dao.FallbackAction(c, uint(id), status)
}

func (g *GormActionService) GetActionCard(c *gin.Context, id int, tp int) (res action.EndpointCard[string], err error) {
	// 获取终端id
	fileds, err := g.dao.GetActionFiled(c, id, "endpoint_id")
	if err != nil {
		return res, err
	}
	endpoint := fileds["endpoint_id"].(uint32)

	// 获取保卡信息
	card, err := g.dao.GetActionCard(c, int(endpoint), tp)
	if err != nil {
		return res, err
	}

	res.CreatedAt = card.CreatedAt.Format("2006-01-02 15:04:05")
	res.UpdatedAt = card.UpdatedAt.Format("2006-01-02 15:04:05")
	res.Count = card.Count
	res.NearTime = card.NearTime.Format("2006-01-02 15:04:05")
	return res, nil
}

// GetSecondAgencyTips 获取二级代理
func (g *GormActionService) GetSecondAgencyTips(c *gin.Context, agency int, name string) (res []map[string]any, err error) {
	// 构建查询参数
	params := dao.AgencyListParams{
		Name: name,
		Pid:  agency,
	}

	// 调用通用代理商列表查询接口
	agencies, _, err := g.common.GetAgencyList(c, params)
	if err != nil {
		return nil, err
	}

	// 转换为前端需要的格式
	result := make([]map[string]any, 0, len(agencies))
	for _, agency := range agencies {
		result = append(result, map[string]any{
			"id":   agency.ID,
			"name": agency.Name,
		})
	}

	return result, nil
}

// GetAgencyTips 获取一级代理
func (g *GormActionService) GetAgencyTips(c *gin.Context, id int, name string) (res []map[string]any, err error) {
	// 构建查询参数
	params := dao.AgencyListParams{
		Name:      name,
		Partition: id,
	}

	// 调用通用代理商列表查询接口
	agencies, _, err := g.common.GetAgencyList(c, params)
	if err != nil {
		return nil, err
	}

	// 转换为前端需要的格式
	result := make([]map[string]any, 0, len(agencies))
	for _, agency := range agencies {
		// 只返回一级代理商
		if agency.Level == 1 {
			result = append(result, map[string]any{
				"id":   agency.ID,
				"name": agency.Name,
			})
		}
	}

	return result, nil
}

func (g *GormActionService) GetPartitionTips(c *gin.Context) (res []map[string]any, err error) {
	return g.common.GetPartitionTips(c)
}

// GetEndpointTips 获取终端
func (g *GormActionService) GetEndpointTips(c *gin.Context, agency int, name string) (res []map[string]any, err error) {
	// 构建查询参数
	params := dao.EndpointListParams{
		Name:         name,
		SecondAgency: agency,
	}

	// 调用通用终端列表查询接口
	endpoints, _, err := g.common.GetEndpointList(c, params)
	if err != nil {
		return nil, err
	}

	// 转换为前端需要的格式
	result := make([]map[string]any, 0, len(endpoints))
	for _, endpoint := range endpoints {
		result = append(result, map[string]any{
			"id":   endpoint.ID,
			"name": endpoint.Name,
		})
	}

	return result, nil
}

func (g *GormActionService) GetTypeTips(c *gin.Context, name string) ([]action.Tips, error) {
	// 获取所有启用的活动类型
	enabledTypes, err := g.dao1.GetEnabledType(c, name)
	if err != nil {
		return nil, err
	}

	// 如果没有启用的活动类型，直接返回空结果
	if len(enabledTypes) == 0 {
		return []action.Tips{}, nil
	}

	// 收集所有活动类型ID
	var typeIDs []int
	for _, tip := range enabledTypes {
		typeIDs = append(typeIDs, tip.ID)
	}

	// 批量获取所有活动类型的剩余场次
	remainSessions, err := g.dao1.GetBatchActionSessions(c, typeIDs)
	if err != nil {
		return nil, err
	}

	// 过滤出场次没有达到上限的活动类型
	var result []action.Tips
	for _, tip := range enabledTypes {
		if remain, exists := remainSessions[tip.ID]; exists && remain > 0 {
			result = append(result, tip)
		}
	}

	// 获取在时间范围内的

	return result, nil
}

func (g *GormActionService) UpdateAction(c *gin.Context, uid uint, act action.UpdateAction) error {
	actions := model.Actions{
		ID:                  act.ID,
		Name:                act.Name,
		Level:               act.Level,
		Phone:               act.Phone,
		Principal:           act.Principal,
		Plan:                act.Plan,
		Staff:               act.Staff,
		Space:               act.Space,
		DateStart:           act.DateStart,
		DateEnd:             act.DateEnd,
		WriteOffCorporation: act.WriteOffCorporation,
		Location:            act.Location,
		Type:                int(act.Type),
		EndpointID:          act.EndpointID,
		SecondAgencyID:      act.SecondAgencyID,
		TopAgencyID:         act.TopAgencyID,
	}
	actions.SitePhoto = utils.SliceTrans(act.SitePhoto)
	actions.UpdateAt = time.Now()
	_, err := g.dao.UpdateAction(c, uid, actions)
	return err
}

// judgeOpinion 判断意见
func judgeOpinion(uint2 *uint8) string {
	if *uint2 == 1 {
		return "通过"
	}
	return "不通过"
}

func (g *GormActionService) PrintFinishAction(c *gin.Context, id int) (cat action.PrintFinishAction, err error) {
	act, err := g.dao.GetActionModel(c, id)
	if err != nil {
		return action.PrintFinishAction{}, err
	}
	{
		cat.Number = act.Number
		cat.TypeName = act.TypeName
		cat.EndpointName = act.EndpointName
		cat.Space = act.Space
		cat.Level = action.Level(act.Level).ToString()
		cat.DateStart = act.DateStart[0:10]
		cat.DateEnd = act.DateEnd[0:10]
		cat.Principal = act.Principal
		cat.Phone = act.Phone
		cat.Plan = act.Plan
		cat.Staff = act.Staff
		cat.Description = act.Description
		cat.SalesDetails = act.SalesDetails
		cat.Summary = act.Summary
		cat.Total = act.Total
		cat.Amount = act.Amount
		cat.DQAuditOpinion = act.DQAuditOpinion
		cat.DQAuditPass = judgeOpinion(act.DQAuditPass)
		cat.AuditPass = judgeOpinion(act.AuditPass)
		cat.AuditOpinion = act.AuditOpinion
		cat.PayPass = judgeOpinion(act.PayPass)
		cat.PayOpinion = act.PayOpinion
		cat.PayTime = act.PayTime
		cat.AuditTime = act.AuditTime
		cat.DQAuditTime = act.DQAuditTime
	}
	return cat, err
}

func (g *GormActionService) GetActionVerifyInfo(c *gin.Context, id, uid int) (action.VerifyInfoAction, error) {
	act, err := g.dao.GetActionVerifyInfo(c, id, uid)
	if err != nil {
		return action.VerifyInfoAction{}, err
	}
	if act == (action.VerifyInfoAction{}) {
		return action.VerifyInfoAction{}, nil
	}
	if act.PayPass == "1" {
		act.PayPass = "核验通过"
	} else {
		act.PayPass = "核验不通过"
	}
	return act, err
}

func (g *GormActionService) GetActionAuditInfo(c *gin.Context, id, uid int) (act action.AuditInfoAction, err error) {
	act, err = g.dao.GetActionAuditInfo(c, id, uid)
	if err != nil {
		return action.AuditInfoAction{}, err
	}
	if act == (action.AuditInfoAction{}) {
		return action.AuditInfoAction{}, nil
	}
	if act.AuditPass == "1" {
		act.AuditPass = "审核通过"
	} else {
		act.AuditPass = "审核不通过"
	}
	return act, err
}

// GetActionFinishInfo 查看完成活动信息
func (g *GormActionService) GetActionFinishInfo(c *gin.Context, id, uid int) (action.FinishInfoAction, error) {
	act, err := g.dao.GetActionFinishInfo(c, id, uid)
	if err != nil {
		return action.FinishInfoAction{}, err
	}
	var cat action.FinishInfoAction
	//判断act是否为空
	if act.UpdatedAt.IsZero() {
		return action.FinishInfoAction{}, err
	}
	cat.Quota, _ = g.dao1.GetTypeQuota(c, act.Type)
	cat.FinishTime = act.UpdatedAt.Format("2006-01-02")
	cat.Summary = act.Summary
	cat.SaleDetails = act.SalesDetails
	cat.Video, err = utils.UrlImageTrans(act.Video)
	cat.Photos, err = utils.UrlImageTrans(act.Photo)
	cat.PhotoFinished, err = utils.UrlImageTrans(act.PhotoFinished)
	cat.PhotoPreparing, err = utils.UrlImageTrans(act.PhotoPreparing)
	cat.Advertise, err = utils.UrlImageTrans(act.Advertise)
	cat.PrepareAttach, err = utils.UrlImageTrans(act.PrepareAttach)
	cat.DouyinPhoto, err = utils.UrlImageTrans(act.DouyinPhoto)
	cat.Materials, err = utils.UrlImageTrans(act.Materials)
	cat.ExpenseAttach, err = utils.UrlImageTrans(act.ExpenseAttach)
	cat.Total = act.Total
	cat.Amount = act.Amount
	cat.Content = act.Content
	cat.Theme = act.Theme
	cat.ApplicationYear = act.ApplicationYear
	return cat, err
}

func (g *GormActionService) GetDropDown(c *gin.Context) ([]map[string]any, error) {
	uid := c.GetUint("uid")
	return g.dao1.GetDropDown(c, int64(uid))
}

func (g *GormActionService) RecordedAction(c *gin.Context, account action.RecordedAction) error {
	update := model.Actions{
		AccountOpinion: account.AccountOpinion,
		SupportMoney:   account.AccountOpinion,
		AccountTime:    time.Now(),
		Status:         action.ActionStatusEntrySuccess.ToUint(),
		AccountUserID:  account.Uid,
	}
	return g.dao.UpdateActionFieldsWithoutHooks(c, update, action.ActionStatusWriteOff.ToUint())
}

func (g *GormActionService) GetActionApplyInfo(c *gin.Context, id, uid int) (acts action.ApplyInfoAction, err error) {
	act, err := g.dao.GetActionApplyInfo(c, id, uid)
	if err != nil {
		return action.ApplyInfoAction{}, err
	}
	{ //15
		acts.Endpoint = act.EndpointName
		acts.Description = act.Description
		acts.ActivityName = act.Name
		acts.DateStart = act.DateStart.Format("2006-01-02")
		acts.DateEnd = act.DateEnd.Format("2006-01-02")
		acts.Space = act.Space
		acts.Staff = act.Staff
		acts.Phone = act.Phone
		acts.Principal = act.Principal
		acts.Plan = act.Plan
		acts.WriteOffCorporation = act.WriteOffCorporation
		acts.Location = act.Location
		acts.ActivityID = act.ActivityID
		acts.CreatedAt = act.CreateAt.Format("2006-01-02")
		acts.Photos, err = utils.UrlImageTrans(act.SitePhoto)
		acts.Year = act.ApplicationYear
		acts.TypeName = act.TypeName
		acts.Type = act.Type
	}
	if act.GiftSupport == uint8(0) {
		acts.PayGiftSupport = "不支持"
	} else {
		acts.PayGiftSupport = "支持"
	}
	acts.Level = action.Level(act.Level).ToString()
	acts.TopAgency = g.dao1.GetEndpointByID(c, act.TopAgencyID)
	acts.SecondAgency = g.dao1.GetEndpointByID(c, act.SecondAgencyID)
	return acts, err
}

func (g *GormActionService) DeleteAction(c *gin.Context, id int) error {
	uid := c.GetUint("uid")
	userID := int(uid)
	err := g.dao.DeleteAction(c, id, userID)
	if err != nil {
		return err
	}
	_ = g.cache.Increment(c, uint(id), -1)
	return err
}

// SearchActionList 搜索活动列表
func (g *GormActionService) SearchActionList(c *gin.Context, params action.SearchActionParams) ([]action.InfoAction, int64, int, int, error) {
	var err error
	var statusArray = consts.ActionStatusMap

	// 调用DAO层查询
	actions, total, page, pageSize, err := g.dao.SearchActionList(c, params)

	// 格式化日期
	for i := range actions {
		actions[i].DateEnd = actions[i].DateEnd[0:10]
		actions[i].DateStart = actions[i].DateStart[0:10]
		actions[i].StatusName = statusArray[actions[i].Status]
	}

	return actions, total, page, pageSize, err
}

func (g *GormActionService) VerifyAction(ctx *gin.Context, verify action.VerifyAction) error {
	var actions model.Actions
	if verify.PayPass == 1 {
		actions.Status = action.ActionStatusWriteOff.ToUint()
	} else {
		actions.Status = action.ActionStatusWriteOffFailed.ToUint()
		return g.dao.UpdateActionFieldsWithoutHooks(ctx, actions, action.ActionStatusFinished.ToUint())
	}
	// 设置核销信息
	actions = model.Actions{
		ID:         verify.ID,
		PayOpinion: verify.PayOpinion,
		PayPass:    &verify.PayPass,
		PayUserID:  &verify.PayUserID,
		PayTime:    time.Now().Format("2006-01-02"),
	}
	if verify.PayPass == 1 {
		actions.Status = action.ActionStatusWriteOff.ToUint()
	} else {
		actions.Status = action.ActionStatusWriteOffFailed.ToUint()
	}
	return g.dao.UpdateActionFieldsWithoutHooks(ctx, actions, action.ActionStatusFinished.ToUint())
}

func (g *GormActionService) FinishAction(ctx *gin.Context, finish action.FinishAction) error {
	var finished model.Actions
	if finish.Finish == 1 {
		finished.Status = action.ActionStatusFinished.ToUint()
	} else {
		finished.Status = action.ActionStatusUnfinished.ToUint()
		return g.dao.UpdateActionFileds(ctx, finished, action.ActionStatusPending.ToUint(), action.ActionStatusApproved.ToUint())
	}
	{
		finished.ID = finish.ID
		finished.Theme = finish.Theme
		finished.Content = finish.Content
		finished.Summary = finish.Summary
		finished.Finish = &finish.Finish
		finished.SalesDetails = finish.SalesDetails
	}
	{
		finished.PhotoPreparing = utils.SliceTrans(finish.PhotoPreparing)
		finished.Advertise = utils.SliceTrans(finish.Advertise)
		finished.PhotoFinished = utils.SliceTrans(finish.PhotoFinished)
		finished.Video = utils.SliceTrans(finish.Video)
		finished.ExpenseAttach = utils.SliceTrans(finish.ExpenseAttach)
		finished.Photo = utils.SliceTrans(finish.Photo)
		finished.DouyinPhoto = utils.SliceTrans(finish.DouyinPhoto)
		finished.Materials = utils.SliceTrans(finish.Materials)
	}
	//保卡信息
	total, count, err := g.dao.GetWarrantyCard(ctx, finished.ID)
	if err != nil {
		return err
	}

	//筛选线下符合条件的活动名单
	warranty, err := g.dao.AccordWarranty(ctx, finished.ID)
	if err != nil {
		log.New().Error("[完成活动]线下活动名单同步失败")
	}
	//线下活动名单同步
	if len(warranty) != 0 {
		//批量插入
		err = g.dao.CreateSalesList(ctx, warranty)
		if err != nil {
			log.New().Error("[完成活动]线下活动名单同步失败")
		}
	}

	finished.Total = uint(count)
	finished.Amount = uint(total)
	return g.dao.UpdateActionFileds(ctx, finished, action.ActionStatusPending.ToUint(), action.ActionStatusApproved.ToUint())
}

func (g *GormActionService) AuditAction(ctx *gin.Context, audit action.AuditAction) error {
	actions := model.Actions{
		ID:           audit.ID,
		AuditOpinion: audit.AuditOpinion,
		AuditPass:    &audit.AuditPass,
		AuditUserID:  &audit.UID,
	}
	now := time.Now()
	actions.AuditTime = now.Format("2006-01-02")
	if audit.AuditPass == 1 {
		actions.Status = action.ActionStatusApprovedByLeader.ToUint()
	} else {
		actions.Status = action.ActionStatusRejected.ToUint()
	}
	return g.dao.UpdateActionFileds(ctx, actions, action.ActionStatusPending.ToUint())
}

func (g *GormActionService) ApplyAction(ctx *gin.Context, act model.Actions) (int, error) {
	//1.时间控制(输入控制)
	if !JudgeCreateTime(act.DateStart) || !g.dao1.JudgeEffectTime(uint(act.Type), act.DateStart, act.DateEnd) {
		return 0, myErrors.NewErr("活动需要提前五天申请且开展时间大于等于两天")
	}

	//2.类型控制
	if !g.dao1.CheckStatus(act.Type) {
		return 0, myErrors.NewErr("活动已停止申请")
	}

	// 2.1 检查大区是否可参与该活动类型
	if !g.dao1.CheckTopAgencyAvailable(ctx, act.TopAgencyID, uint(act.Type)) {
		return 0, myErrors.NewErr("该大区不可参与此类型活动")
	}

	//3.每月申请上限
	had := g.dao.GetActionHadApply(ctx, act.EndpointID, act.Type)
	endCeiling := g.dao1.GetLinesEveryMouth(ctx, act.Type)
	if had >= endCeiling && endCeiling != 0 {
		return 0, myErrors.NewErr("该终端本月申请已达上限")
	}

	//4.活动场次上限
	err := g.cache.Increment(context.TODO(), uint(act.Type), 1)
	if err != nil {
		if err.Error() == "缓存不存在" {
			remain, err := g.dao1.GetActionSession(ctx, uint(act.Type))
			if err != nil {
				return 0, err
			}
			if remain <= 0 {
				return 0, myErrors.NewErr("活动申请已达上限")
			}
			remain--
			err = g.cache.Set(context.TODO(), uint(act.Type), remain)
			if err != nil {
				// 日志记录
				log.New().Info("[活动申请]场次剩余缓存设置失败")
			}
		} else {
			return 0, err
		}
	}

	//5.插入数据
	id, err := g.dao.CreateActivity(ctx, act)
	if err != nil {
		_ = g.cache.Increment(context.TODO(), uint(act.Type), -1)
		return 0, err
	}
	return id, nil
}

func (g *GormActionService) ApplyActionV1(ctx *gin.Context, act action.ApplyActionV1) (int, error) {
	actions := toDomainApplyOld(act.ApplyAction)
	actions.Status = action.ActionStatusPending.ToUint()
	uid := ctx.GetUint("uid")
	actions.UID = uid
	year, _ := strconv.Atoi(time.Now().Format("2006"))
	actions.ApplicationYear = year
	return g.ApplyAction(ctx, actions)
}

func JudgeCreateTime(start string) bool {
	now := time.Now()

	var startTime time.Time
	var err error

	// 尝试解析ISO 8601格式
	if strings.Contains(start, "T") {
		startTime, err = time.Parse(time.RFC3339, start)
		if err != nil {
			// 尝试解析没有时区的ISO格式
			startTime, err = time.Parse("2006-01-02T15:04:05", start[:19])
			if err != nil {
				return false
			}
		}
	} else {
		// 尝试解析标准日期格式
		startTime, err = time.Parse("2006-01-02", start)
		if err != nil {
			return false
		}
	}

	// 计算申请截止时间：活动开始前5天
	applyDeadline := startTime.AddDate(0, 0, -5)

	// 如果当前时间已经超过了申请截止时间，则不合法
	if now.After(applyDeadline) {
		return false
	}
	return true
}

func toDomainApplyOld(act action.ApplyAction) model.Actions {
	//表单提供的19个字段+2时间
	now := time.Now()
	return model.Actions{
		Name:                act.Name,
		Level:               act.Level,
		Phone:               act.Phone,
		Principal:           act.Principal,
		Plan:                act.Plan,
		Staff:               act.Staff,
		Space:               act.Space,
		DateStart:           act.DateStart,
		DateEnd:             act.DateEnd,
		WriteOffCorporation: act.WriteOffCorporation,
		Location:            act.Location,
		Type:                int(act.Type),
		SitePhoto:           utils.SliceTrans(act.SitePhoto),
		EndpointID:          act.EndpointID,
		SecondAgencyID:      act.SecondAgencyID,
		TopAgencyID:         act.TopAgencyID,
		CreateAt:            now,
		UpdateAt:            now,
		//忽略两个字段
		/*		Description: act.Description,
				ActivityID:  int(act.ActivityID),*/
	}
}

func NewGormActionService(dao dao.ActionDao, dao1 dao.ActionTypeDao, cache cache.ActionCache, common dao.CommonDao) ActionService {
	return &GormActionService{
		dao:    dao,
		dao1:   dao1,
		cache:  cache,
		common: common,
	}
}
