package utils

import "sort"

// TreeNode 接口定义
type TreeNode interface {
	GetID() uint
	GetParentID() uint
	GetRank() int
	AddChild(TreeNode)
	IsLeaf() bool
}

// BuildTree 构建树结构的函数
func BuildTree[T TreeNode](nodes []T) []T {
	if len(nodes) == 0 {
		return nil
	}

	// 先按 rank 排序
	sort.SliceStable(nodes, func(i, j int) bool {
		return nodes[i].GetRank() < nodes[j].GetRank()
	})

	nodeMap := make(map[uint]T)
	parentChildMap := make(map[uint][]T) // 存储父节点ID对应的子节点
	var roots []T

	// 第一次遍历：构建映射关系
	for _, node := range nodes {
		nodeMap[node.GetID()] = node

		if node.GetParentID() == 0 {
			roots = append(roots, node)
		} else {
			// 将节点添加到对应父节点的子节点列表中
			parentChildMap[node.GetParentID()] = append(parentChildMap[node.GetParentID()], node)
		}
	}

	// 第二次遍历：处理父子关系
	for _, node := range nodes {
		// 如果节点不是叶子节点，且有子节点
		if !node.IsLeaf() {
			if children, exists := parentChildMap[node.GetID()]; exists {
				// 添加所有子节点
				for _, child := range children {
					node.AddChild(child)
				}
			}
		}
	}

	return roots
}
