package prototype

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"marketing/internal/api/prototype"
	"marketing/internal/dao/endpoint"
	dao "marketing/internal/dao/prototype"
	"marketing/internal/model"
	appError "marketing/internal/pkg/errors"
	"strconv"
	"time"

	"github.com/xuri/excelize/v2"
)

// UpEndpointNumberService 上样终端数量服务接口
type UpEndpointNumberService interface {
	GetList(ctx *gin.Context, req *prototype.UpEndpointNumberSearch) ([]*prototype.UpEndpointNumberListVo, int64, error)
	ExportList(ctx *gin.Context, req *prototype.UpEndpointNumberSearch) error
	Add(ctx *gin.Context, req *prototype.UpEndpointNumberAdd) error
}

type upEndpointNumberService struct {
	upEndpointNumberDao dao.UpEndpointNumber
	endpointDao         endpoint.EndpointDao
}

// NewUpEndpointNumberService 创建上样终端数量服务
func NewUpEndpointNumberService(dao dao.UpEndpointNumber, endpointDao endpoint.EndpointDao) UpEndpointNumberService {
	return &upEndpointNumberService{
		upEndpointNumberDao: dao,
		endpointDao:         endpointDao,
	}
}

// GetList 获取上样终端数量列表
func (s *upEndpointNumberService) GetList(ctx *gin.Context, req *prototype.UpEndpointNumberSearch) ([]*prototype.UpEndpointNumberListVo, int64, error) {
	var result []*prototype.UpEndpointNumberListVo

	list, count, err := s.upEndpointNumberDao.GetList(ctx, req)
	if err != nil {
		return nil, 0, err
	}
	var agencyIDs []int
	for _, item := range list {
		if item.TopAgency != 0 {
			agencyIDs = append(agencyIDs, cast.ToInt(item.TopAgency))
		}
	}

	agencies, err := s.endpointDao.GetAgencies(ctx, agencyIDs)

	for _, item := range list {
		var topAgency string
		if item.TopAgency != 0 {
			topAgency = agencies[item.TopAgency]
		}
		result = append(result, &prototype.UpEndpointNumberListVo{
			ID:             item.ID,
			Model:          item.Model,
			TopAgency:      int(item.TopAgency),
			TopAgencyName:  topAgency,
			EndpointNumber: item.EndpointNumber,
			CreatedAt:      item.CreatedAt.Format(time.DateTime),
		})
	}

	return result, count, nil
}

// ExportList 导出上样终端数量列表
func (s *upEndpointNumberService) ExportList(ctx *gin.Context, req *prototype.UpEndpointNumberSearch) error {
	var (
		title = []string{
			"一级经销商",
			"机型",
			"初始终端数",
			"创建时间",
		}
		titleStyle int
		err        error
		sheet      = "Sheet1"
	)

	// 查询数据
	req.Page = 1
	req.PageSize = 10000 // 导出时取较大数量
	data, _, err := s.GetList(ctx, req)
	if err != nil {
		return err
	}

	// 创建Excel文件
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Printf("关闭Excel文件失败: %v\n", err)
		}
	}()

	// 设置样式
	if titleStyle, err = f.NewStyle(&excelize.Style{
		Font:      &excelize.Font{Color: "1f7f3b", Bold: true, Family: "Microsoft YaHei"},
		Fill:      excelize.Fill{Type: "pattern", Color: []string{"E6F4EA"}, Pattern: 1},
		Alignment: &excelize.Alignment{Vertical: "center"},
		Border: []excelize.Border{
			{Type: "top", Style: 2, Color: "1f7f3b"},
			{Type: "bottom", Style: 1, Color: "1f7f3b"},
			{Type: "left", Style: 1, Color: "1f7f3b"},
		},
	}); err != nil {
		return err
	}

	// 设置标题行
	if err = f.SetSheetRow(sheet, "A1", &title); err != nil {
		return err
	}
	if err = f.SetRowHeight(sheet, 1, 30); err != nil {
		return err
	}
	if err = f.SetCellStyle(sheet, "A1", "D1", titleStyle); err != nil {
		return err
	}

	// 设置列宽
	if err = f.SetColWidth(sheet, "A", "A", 35); err != nil {
		return err
	}
	if err = f.SetColWidth(sheet, "B", "D", 20); err != nil {
		return err
	}

	// 填充数据
	for i, row := range data {
		var tempRow []interface{}
		tempRow = append(tempRow, row.TopAgencyName)
		tempRow = append(tempRow, row.Model)
		tempRow = append(tempRow, row.EndpointNumber)
		tempRow = append(tempRow, row.CreatedAt)
		if err = f.SetSheetRow(sheet, "A"+strconv.Itoa(i+2), &tempRow); err != nil {
			return err
		}
	}

	// 设置响应头
	fileName := fmt.Sprintf("上样终端数_%s.xlsx", time.Now().Format("20060102_150405"))
	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", fileName))
	ctx.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")

	// 直接将 Excel 文件内容写入响应
	if err = f.Write(ctx.Writer); err != nil {
		return err
	}

	return nil
}

// Add 添加上样终端数量
func (s *upEndpointNumberService) Add(ctx *gin.Context, req *prototype.UpEndpointNumberAdd) error {
	b, err := s.upEndpointNumberDao.CheckModelAllowed(ctx, req.Model)
	if err != nil {
		return err
	}
	if !b {
		return appError.NewErr("该机型没有开启样机功能")
	}
	b, err = s.upEndpointNumberDao.CheckModelExisted(ctx, req.Model)
	if err != nil {
		return err
	}
	if b {
		return appError.NewErr("该机型已添加，不允许重复上样")
	}
	// 查出当前有效的一级经销商
	agency, err := s.upEndpointNumberDao.GetTopAgencyEndpointNum(ctx)
	if err != nil {
		return err
	}
	var data []*model.PrototypeUpEndpointNumber
	for id, num := range agency {
		data = append(data, &model.PrototypeUpEndpointNumber{
			Model:          req.Model,
			TopAgency:      uint(id),
			EndpointNumber: int64(num),
			CreatedAt:      time.Now(),
		})
	}

	err = s.upEndpointNumberDao.BatchCreate(ctx, data)
	if err != nil {
		return err
	}
	return nil
}
