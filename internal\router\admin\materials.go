package admin

import (
	"marketing/internal/dao"
	"marketing/internal/handler/admin/machine"
	"marketing/internal/handler/admin/material"
	"marketing/internal/pkg/db"
	"marketing/internal/pkg/notification"
	"marketing/internal/service"
	machineSvc "marketing/internal/service/machine"
	materialSvc "marketing/internal/service/material"

	"github.com/gin-gonic/gin"
)

type MaterialRouter struct{}

func NewMaterialRouter() *MaterialRouter {
	return &MaterialRouter{}
}

func (m *MaterialRouter) Register(r *gin.RouterGroup) {
	materialV1 := r.Group("/material")
	{
		materialDao := dao.NewMaterialDao()
		categoryDao := dao.NewMaterialCategoryDao()

		materialService := materialSvc.NewMaterialService(materialDao, categoryDao)
		materialController := material.NewMaterialController(materialService)
		materialV1.GET("/list", materialController.GetMaterialList)
		materialV1.GET("/get", materialController.GetMaterialById)
		materialV1.POST("/edit", materialController.EditMaterial)
		materialV1.DELETE("/delete", materialController.DeleteMaterial)

		categoryService := service.NewMaterialCategoryService(materialDao, categoryDao)
		categoryController := material.NewMaterialCategory(categoryService)
		materialCategoryRouter := materialV1.Group("/category")
		materialCategoryRouter.GET("/list", categoryController.List)
		materialCategoryRouter.GET("/refresh", categoryController.RefreshMaterialCategory)
		materialCategoryRouter.POST("/edit", categoryController.EditMaterialCategory)
		materialCategoryRouter.DELETE("/delete", categoryController.DeleteMaterialCategory)
	}

	modelCategory := r.Group("/model_category")
	{
		modelCategoryDao := dao.NewModelCategoryDao()
		modelCategoryService := machineSvc.NewModelCategoryService(modelCategoryDao)
		modelCategoryController := machine.NewModelCategory(modelCategoryService)
		modelCategory.GET("/list", modelCategoryController.ModelCategoryList)
		modelCategory.GET("/refresh", modelCategoryController.RefreshModelCategory)
		modelCategory.POST("/edit", modelCategoryController.EditModelCategory)
		modelCategory.DELETE("/delete", modelCategoryController.DeleteModelCategory)

		machineType := r.Group("/machine_type")
		machineTypeDao := dao.NewMachineTypeDao()
		machineTypeRelationDao := dao.NewMachineTypeRelationDao(db.GetDB())
		machineTypeService := machineSvc.NewMaterialService(machineTypeDao, modelCategoryDao, machineTypeRelationDao)
		machineTypeController := machine.NewMachineType(machineTypeService)
		machineType.GET("/list", machineTypeController.GetMachineTypeList)
		machineType.POST("/edit", machineTypeController.EditMachineType)
		machineType.GET("/info", machineTypeController.GetMachineTypeByModelId)

		machineType.PUT("/market-date", machineTypeController.UpdateMarketDate)
		machineType.PUT("/discontinued", machineTypeController.UpdateDiscontinued)

		machineAccessory := r.Group("/machine_accessory")
		machineAccessoryDao := dao.NewMachineAccessoryDao()
		machineAccessoryService := machineSvc.NewMachineAccessoryService(machineAccessoryDao)
		machineAccessoryController := machine.NewMachineAccessory(machineAccessoryService)
		machineAccessory.GET("/list", machineAccessoryController.MachineAccessoryList)
		machineAccessory.GET("/refresh", machineAccessoryController.RefreshMachineAccessory)
		machineAccessory.POST("/edit", machineAccessoryController.EditMachineAccessory)
		machineAccessory.DELETE("/delete", machineAccessoryController.DeleteMachineAccessory)

		machineAccessoryRelation := machineAccessory.Group("/relation")
		machineAccessoryRelationDao := dao.NewMachineAccessoryRelationDao()
		machineAccessoryRelationService := machineSvc.NewMachineAccessoryRelationService(machineAccessoryDao, machineAccessoryRelationDao)
		machineAccessoryRelationController := machine.NewMachineAccessoryRelation(machineAccessoryRelationService)
		machineAccessoryRelation.GET("/list", machineAccessoryRelationController.GetMachineAccessoryRelationList)
		machineAccessoryRelation.GET("/get", machineAccessoryRelationController.GetMachineAccessoryRelation)
		machineAccessoryRelation.POST("/create", machineAccessoryRelationController.CreateMachineAccessoryRelation)
		machineAccessoryRelation.DELETE("/delete", machineAccessoryRelationController.DeleteMachineAccessoryRelation)
	}

	// 下市管理
	delistManage := r.Group("/delist")
	notificationService := notification.NewYxApiNotificationService()
	delistManageService := machineSvc.NewDelistManageService(
		db.GetDB(),
		dao.NewModelCategoryDao(),
		dao.NewMachineTypeDao(),
		dao.NewMachineTypeRelationDao(db.GetDB()),
		notificationService,
	)
	delistManageController := machine.NewDelistManage(delistManageService)
	delistManage.GET("/list", delistManageController.GetMachineTypeList)
	delistManage.PUT("/status", delistManageController.UpdateDelistStatus)
	delistManage.PUT("/declare", delistManageController.UpdateDeclareStatus)
	delistManage.PUT("/stock", delistManageController.UpdateStockStatus)
	delistManage.GET("/send_notice", delistManageController.SendNotice)
}
