package dao

import (
	"errors"
	"marketing/internal/api/action"
	"marketing/internal/model"
	"marketing/internal/pkg/db"
	appErr "marketing/internal/pkg/errors"
	"slices"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

type PromotionDao interface {
	Create(c *gin.Context, promo model.SalesPromotion) (id int64, err error)
	Delete(c *gin.Context, id []int) error
	Update(c *gin.Context, promo model.SalesPromotion) error
	GetList(c *gin.Context, param action.PromotionsReq) ([]map[string]any, int64, error)
	GetInfo(c *gin.Context, id string) (map[string]any, error)
	GetJoinList(c *gin.Context, id int, page int, size int, agency string, endpoint string, status string,
		model string, barcode string) ([]action.PromotionList, int64, error)
	GetJoinInfo(c *gin.Context, id string) (map[string]any, map[string]any, error)
	IsExist(c *gin.Context, id int, barcode string) (*model.SalesPromotionList, error)
	AddJoin(c *gin.Context, promotion model.SalesPromotionList) (int, error)
	DeleteJoin(c *gin.Context, ids []int) error
	GetDetail(c *gin.Context, id int) (model.SalesPromotion, error)
	SyncJoin(c *gin.Context, join []action.SiftWarranty) error
	Lists(c *gin.Context, id int, page int, size int, agency string, endpoint string, status string,
		model string) ([]action.ExportList, error)
	BatchGetReceipt(c *gin.Context, ids []int) ([]action.PromotionListReceiptResp, error)
	UpdateReceiptTime(c *gin.Context, req action.UpdatePromotionReq) error
	ListsStatus(c *gin.Context, id uint64, status int) bool
	IsReceipt(c *gin.Context, id int) bool
	GetJoinDetail(c *gin.Context, id int) (model.SalesPromotionList, error)
	UploadReceipt(c *gin.Context, id int, receipt []string, receiptType uint8) error
	FallBack(c *gin.Context, id string) error
	GetBarcodeInfo(c *gin.Context, barcode string) (*action.BarcodeResp, error)
	IsWarrantyDeleted(c *gin.Context, warrantyId int) bool
	GetPromotionByNumber(c *gin.Context, number string) ([]action.NumberRepeat, error)
}

type GormPromotionDao struct {
	db *gorm.DB
}

func (g *GormPromotionDao) IsReceipt(c *gin.Context, id int) bool {
	return g.db.WithContext(c).Model(&model.SalesPromotionList{}).Where("id = ? and is_receipt = 1", id).First(&model.SalesPromotionList{}).Error == nil
}

// FallBack 活动名单回退
func (g *GormPromotionDao) FallBack(c *gin.Context, id string) error {
	return g.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		if tx.Model(&model.SalesPromotionList{}).Where("id = ?", id).Updates(map[string]any{
			"is_receipt": 0,
		}).Error != nil {
			return tx.Error
		}
		if tx.Where("sales_promotion_list_id = ?", id).Delete(&model.SalesPromotionListReceipt{}).Error != nil {
			return tx.Error
		}
		return nil
	})
}

// UploadReceipt 上传回执 (receiptType 上传类型 1-终端上传 2-代理上传)
func (g *GormPromotionDao) UploadReceipt(c *gin.Context, id int, receipt []string, receiptType uint8) error {
	now := time.Now()
	return g.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		// 更新名单回执状态
		if tx.Model(&model.SalesPromotionList{}).Where("id = ?", id).Updates(map[string]any{
			"receipt_at": now,
			"is_receipt": 1,
		}).Error != nil {
			return tx.Error
		}
		//删除旧回执
		if err := tx.Where("sales_promotion_list_id = ?", id).Where("type = ?", receiptType).Delete(&model.SalesPromotionListReceipt{}).Error; err != nil {
			return err
		}
		//判断是否不同类型上传的
		var existReceipts []string
		if err := tx.Model(&model.SalesPromotionListReceipt{}).Where("sales_promotion_list_id =?", id).
			Where("type != ?", receiptType).Select("number").Find(&existReceipts).Error; err != nil {
			return err
		}
		//插入新回执
		var insertData []model.SalesPromotionListReceipt
		for _, item := range receipt {
			number := ""
			parts := strings.Split(item, "/")
			if len(parts) > 0 {
				lastPart := parts[len(parts)-1]
				subParts := strings.Split(lastPart, "_")
				if len(subParts) > 0 {
					if strings.Contains(subParts[0], ".") {
						number = strings.Split(subParts[0], ".")[0] // 获取结果
					} else {
						number = subParts[0] // 获取结果
					}
				}
			}

			if len(existReceipts) > 0 && slices.Contains(existReceipts, number) {
				//非自己上传的不重新保存，否则数据库会有重复数据
				continue
			}
			insertData = append(insertData, model.SalesPromotionListReceipt{
				SalesPromotionListID: id,
				Receipt:              item,
				Number:               number,
				Type:                 int(receiptType),
			})
		}
		if len(insertData) > 0 {
			if err := tx.Create(&insertData).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

func (g *GormPromotionDao) GetJoinDetail(c *gin.Context, id int) (model.SalesPromotionList, error) {
	var res model.SalesPromotionList
	curDB := g.db.WithContext(c).Model(&model.SalesPromotionList{}).Where("id = ?", id).First(&res)
	if curDB.RowsAffected == 0 {
		return res, gorm.ErrRecordNotFound
	}
	return res, curDB.Error
}

func (g *GormPromotionDao) ListsStatus(c *gin.Context, id uint64, status int) bool {
	var (
		t   time.Time
		err error
	)
	switch status {
	case 0: //判断是否开始
		err = g.db.WithContext(c).Model(&model.SalesPromotion{}).Select("start_time").Where("id = ?", id).Scan(&t).Error
	case 1: // 判断是否结束
		err = g.db.WithContext(c).Model(&model.SalesPromotion{}).Select("end_time").Where("id = ?", id).Scan(&t).Error
	}
	return err == nil && t.Before(time.Now())
}

func (g *GormPromotionDao) UpdateReceiptTime(c *gin.Context, req action.UpdatePromotionReq) error {
	curDB := g.db.WithContext(c).
		Model(&model.SalesPromotion{}).
		Where("id = ?", req.ID).
		Updates(map[string]any{"receipt_day": req.Receipt})
	if curDB.RowsAffected == 0 {
		return gorm.ErrRecordNotFound
	}
	return curDB.Error
}

// BatchGetReceipt 批量获取回执列表
func (g *GormPromotionDao) BatchGetReceipt(c *gin.Context, ids []int) ([]action.PromotionListReceiptResp, error) {
	var res []action.PromotionListReceiptResp
	err := g.db.WithContext(c).
		Table("sales_promotion_list_receipt as r").
		Joins("LEFT JOIN (SELECT count(*) as count, number FROM sales_promotion_list_receipt WHERE delete_at IS NULL GROUP BY number) as c ON c.number = r.number").
		Select("r.sales_promotion_list_id, r.receipt, r.number, c.count").
		Where("r.delete_at IS NULL").
		Where("r.sales_promotion_list_id IN (?)", ids).Find(&res).Error
	return res, err
}

// GetPromotionByNumber 根据图片number 查出对应的活动和barcode
func (g *GormPromotionDao) GetPromotionByNumber(c *gin.Context, number string) ([]action.NumberRepeat, error) {
	var res []action.NumberRepeat
	curDB := g.db.WithContext(c).Model(&model.SalesPromotionListReceipt{}).
		Where("number = ?", number).
		Joins("LEFT JOIN sales_promotion_list as l ON l.id = sales_promotion_list_id").
		Joins("LEFT JOIN sales_promotion as p ON p.id = l.sales_promotion_id").
		Where("l.delete_at IS NULL").
		Where("p.delete_at IS NULL").
		Select("p.id as promotion_id,p.name as promotion,l.barcode").
		Group("p.id,l.barcode").
		Find(&res)
	if curDB.RowsAffected == 0 {
		return res, gorm.ErrRecordNotFound
	}
	return res, curDB.Error
}

func (g *GormPromotionDao) Lists(c *gin.Context, id int, page int, size int, agency string, endpoint string, status string,
	model string) ([]action.ExportList, error) {
	var res []action.ExportList
	curDb := g.db.WithContext(c).Table("sales_promotion_list as l").
		Joins("LEFT JOIN warranty as w ON l.warranty_id = w.id").
		Joins("LEFT JOIN endpoint as e ON w.endpoint = e.id").
		Joins("LEFT JOIN region as r ON e.province = r.region_id").
		Joins("LEFT JOIN region as c ON e.city = c.region_id").
		Joins("LEFT JOIN agency as a ON e.top_agency = a.id").
		Select("l.barcode,l.buy_date,l.customer_name, l.customer_phone,l.activated_phone,is_receipt,"+
			"w.model,w.number,w.student_name,w.student_uid,w.activated_status, w.activated_at,"+
			"e.name AS endpoint_name,e.code AS endpoint_code, e.address,e.phone AS endpoint_phone,e.manager,"+
			"CONCAT(r.region_name, c.region_name) as region_name,a.name AS agency_name").
		Where("l.sales_promotion_id = ?", id).
		Where("w.status = 1").
		Where("l.delete_at IS NULL")
	if agency != "" {
		curDb = curDb.Where("a.id = ?", agency)
	}
	if endpoint != "" {
		curDb = curDb.Where("e.id = ?", endpoint)
	}
	if status != "" {
		curDb = curDb.Where("w.is_receipt = ?", status)
	}
	if model != "" {
		curDb = curDb.Where("w.model_id = ?", model)
	}
	curDb = curDb.Offset((page - 1) * size).Limit(size).Find(&res)
	if curDb.RowsAffected == 0 {
		return nil, gorm.ErrRecordNotFound
	}
	return res, curDb.Error
}

func (g *GormPromotionDao) SyncJoin(c *gin.Context, join []action.SiftWarranty) error {
	return g.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		if err := tx.CreateInBatches(join, 500).Error; err != nil {
			return err
		}
		return nil
	})
}

func (g *GormPromotionDao) GetDetail(c *gin.Context, id int) (detail model.SalesPromotion, err error) {
	row := g.db.WithContext(c).Table("sales_promotion").Where("id = ?", id).First(&detail)
	if row.RowsAffected == 0 {
		return detail, gorm.ErrRecordNotFound
	}
	return detail, err
}

func (g *GormPromotionDao) DeleteJoin(c *gin.Context, ids []int) error {
	return g.db.WithContext(c).Table("sales_promotion_list").Where("id IN (?)", ids).Delete(&model.SalesPromotion{}).Error
}

func (g *GormPromotionDao) AddJoin(c *gin.Context, promotion model.SalesPromotionList) (int, error) {
	err := g.db.WithContext(c).Create(&promotion).Error
	return promotion.ID, err
}

// IsWarrantyDeleted 检查保卡是否已删除
func (g *GormPromotionDao) IsWarrantyDeleted(c *gin.Context, warrantyId int) bool {
	var count int64
	g.db.WithContext(c).Table("warranty").
		Where("id = ? AND deleted_at IS NOT NULL", warrantyId).
		Count(&count)
	return count > 0
}

func (g *GormPromotionDao) IsExist(c *gin.Context, id int, barcode string) (*model.SalesPromotionList, error) {
	var promotion model.SalesPromotionList
	err := g.db.WithContext(c).Table("sales_promotion_list").
		Where("delete_at is null").
		Where("sales_promotion_id = ? AND barcode = ?", id, barcode).First(&promotion).Error

	return &promotion, err
}

func (g *GormPromotionDao) GetJoinInfo(c *gin.Context, id string) (map[string]any, map[string]any, error) {
	var store map[string]any
	err := g.db.WithContext(c).Table("sales_promotion_list").
		Joins("LEFT JOIN endpoint ON endpoint.id = sales_promotion_list.endpoint").
		Joins("LEFT JOIN agency ON agency.id = endpoint.top_agency").
		Select("endpoint.top_agency,endpoint.second_agency,agency.name AS agency_name,endpoint.name AS endpoint_name,code,manager,phone,sales_promotion_id").
		Where("sales_promotion_list.id = ?", id).Find(&store).Error
	if err != nil {
		return nil, nil, err
	}
	var order map[string]any
	err = g.db.WithContext(c).Table("sales_promotion_list").
		Joins("LEFT JOIN warranty ON warranty.id=sales_promotion_list.warranty_id").
		Select("warranty.model,number,warranty.barcode,warranty.buy_date,warranty.customer_name,warranty.customer_phone,"+
			"student_name,student_uid,activated_at,activated_phone").
		Where("sales_promotion_list.id = ?", id).Find(&order).Error
	return store, order, err
}

func (g *GormPromotionDao) GetJoinList(c *gin.Context, id int, page int, size int, agency string, endpoint string,
	status string, model string, barcode string) ([]action.PromotionList, int64, error) {
	var promos []action.PromotionList
	curDB := g.db.WithContext(c).Table("sales_promotion_list AS a").
		Joins("LEFT JOIN endpoint ON endpoint.id = a.endpoint").
		Joins("LEFT JOIN agency ON agency.id = endpoint.top_agency").
		Joins("LEFT JOIN warranty_return wr ON wr.warranty_id = a.warranty_id").
		Select("a.id as id,model,customer_name,customer_phone,a.barcode,"+
			"endpoint.name AS endpoint_name,code,agency.name AS agency_name,a.warranty_id,a.is_receipt AS is_receipt").
		Where("a.sales_promotion_id = ?", id).
		Where("wr.id IS NULL").
		Where("a.delete_at IS NULL")
	if agency != "" {
		curDB = curDB.Where("endpoint.top_agency = ?", agency)
	}
	if endpoint != "" {
		curDB = curDB.Where("a.endpoint = ?", endpoint)
	}
	if status != "" {
		curDB = curDB.Where("is_receipt = ?", status)
	}
	if model != "" {
		curDB = curDB.Where("a.model = ?", model)
	}
	if barcode != "" {
		curDB = curDB.Where("a.barcode = ?", barcode)
	}
	var total int64
	err := curDB.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}
	err = curDB.Offset((page - 1) * size).Limit(size).Order("a.buy_date").Find(&promos).Error
	return promos, total, nil
}

func (g *GormPromotionDao) GetInfo(c *gin.Context, id string) (map[string]any, error) {
	var promo map[string]any
	err := g.db.WithContext(c).Table("sales_promotion").
		Where("id = ?", id).
		Where("delete_at IS NULL").
		Select("id, name, start_time, end_time, model,hour_interval,is_same_phone," +
			"receipt_day,receipt_day,prize,prize_pic,rule").
		Find(&promo).Error
	return promo, err
}

// GetList 获取列表
func (g *GormPromotionDao) GetList(c *gin.Context, param action.PromotionsReq) ([]map[string]any, int64, error) {
	var promos []map[string]any
	curDB := g.db.WithContext(c).Model(&model.SalesPromotion{})
	if param.Type != 0 {
		curDB = curDB.Where("sales_promotion.type =?", param.Type)
	}
	if param.Name != "" {
		curDB = curDB.Where("sales_promotion.name LIKE ?", "%"+param.Name+"%")
	}

	// 拆分字段为字符串切片
	fields := []string{
		"sales_promotion.id",
		"sales_promotion.name",
		"sales_promotion.start_time",
		"sales_promotion.end_time",
		"sales_promotion.model",
		"receipt_day",
		"sales_promotion.is_sync",
		"prize_pic",
		"type",
	}
	curDB = curDB.Group("sales_promotion.id").
		Select(fields)

	var count int64
	curDB.Count(&count)
	err := curDB.Offset((param.Page - 1) * param.PageSize).Limit(param.PageSize).Order("sales_promotion.id DESC").Scan(&promos).Error
	if err != nil {
		return nil, 0, err
	}
	if len(promos) == 0 {
		return promos, 0, nil
	}
	var ids []int
	for _, item := range promos {
		ids = append(ids, cast.ToInt(item["id"]))
	}

	// 查出活动的参与名单数量
	baseQuery := g.db.WithContext(c).Model(&model.SalesPromotionList{}).
		Joins("LEFT JOIN warranty_return wr ON sales_promotion_list.warranty_id = wr.warranty_id").
		Where("wr.id IS NULL").
		Where("sales_promotion_id IN (?)", ids)

	if param.AgencyID > 0 {
		baseQuery = baseQuery.Joins("LEFT JOIN endpoint ON endpoint.id = sales_promotion_list.endpoint").
			Joins("LEFT JOIN agency as top_agency ON top_agency.id = endpoint.top_agency").
			Joins("LEFT JOIN agency as second_agency ON second_agency.id = endpoint.second_agency").
			Where("top_agency.id = ? OR second_agency.id = ?", param.AgencyID, param.AgencyID)
	}

	if param.EndpointID > 0 {
		baseQuery = baseQuery.Joins("LEFT JOIN endpoint ON endpoint.id = sales_promotion_list.endpoint").
			Where("endpoint.id = ?", param.EndpointID)
	}

	baseQuery.Select("count(sales_promotion_list.id) as count,sales_promotion_id").Group("sales_promotion_id")

	// 使用 Session 克隆新的会话
	joinCountQuery := baseQuery.Session(&gorm.Session{})
	var joinCount []map[string]any
	err = joinCountQuery.Scan(&joinCount).Error

	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, 0, err
	}

	// 再次使用 Session 克隆新的会话
	receiptCountQuery := baseQuery.Session(&gorm.Session{}).Where("is_receipt = 1")
	var receiptCount []map[string]any
	err = receiptCountQuery.Scan(&receiptCount).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, 0, err
	}
	for i := range promos {
		id := cast.ToUint(promos[i]["id"])
		// 初始化计数
		promos[i]["list_count"] = 0
		promos[i]["receipt_count"] = 0

		// 更新 list_count
		for _, joinItem := range joinCount {
			joinId := cast.ToUint(joinItem["sales_promotion_id"])
			if joinId == id {
				promos[i]["list_count"] = joinItem["count"]
			}
		}

		// 更新 receipt_count
		for _, receiptItem := range receiptCount {
			receiptId := cast.ToUint(receiptItem["sales_promotion_id"])
			if receiptId == id {
				promos[i]["receipt_count"] = receiptItem["count"]
			}
		}
	}

	return promos, count, nil
}

// Update 更新
func (g *GormPromotionDao) Update(c *gin.Context, promo model.SalesPromotion) error {
	now := time.Now()
	promo.UpdatedAt = now
	row := g.db.WithContext(c).Model(&model.SalesPromotion{}).
		Where("id = ?", promo.ID).
		Where("end_time > ?", now).
		Updates(promo)
	if row.RowsAffected == 0 {
		return gorm.ErrRecordNotFound
	}
	return row.Error
}

// Delete 批量删除
func (g *GormPromotionDao) Delete(c *gin.Context, ids []int) error {
	return g.db.WithContext(c).Where("id IN (?)", ids).Delete(&model.SalesPromotion{}).Error
}

// Create 创建
func (g *GormPromotionDao) Create(c *gin.Context, promo model.SalesPromotion) (id int64, err error) {
	now := time.Now()
	promo.UpdatedAt = now
	promo.CreatedAt = now
	err = g.db.WithContext(c).Create(&promo).Error
	return promo.ID, err
}

// GetBarcodeInfo 根据条码获取详细信息
func (g *GormPromotionDao) GetBarcodeInfo(c *gin.Context, barcode string) (*action.BarcodeResp, error) {
	var data action.BarcodeResp

	// 主查询
	err := g.db.WithContext(c).
		Table("warranty as w").
		Select("w.id,w.id as warranty_id, w.barcode, w.model, w.endpoint, w.model_id, "+
			"w.buy_date, w.student_uid, w.student_name, a.name as agency_name, "+
			"w.activated_at_old as activated_at, w.number, w.customer_name, "+
			"w.customer_phone, w.activated_id, "+
			"TIMESTAMPDIFF(HOUR, w.buy_date, w.activated_at_old) as hour_interval, "+
			"e.code as endpoint_code, e.name as endpoint_name, e.manager, wr.return_at").
		Joins("LEFT JOIN endpoint e ON e.id = w.endpoint").
		Joins("LEFT JOIN agency a ON e.top_agency = a.id").
		Joins("LEFT JOIN region rp ON e.province = rp.region_id").
		Joins("LEFT JOIN region rc ON e.city = rc.region_id").
		Joins("LEFT JOIN warranty_return wr ON w.barcode = wr.barcode").
		Where("w.barcode = ?", barcode).
		Where("w.status = ?", 1).
		Group("w.barcode").
		First(&data).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, appErr.NewErr("保卡不存在")
		}
		return nil, err
	}

	// 查询激活手机号
	if data.ActivatedID != "" {
		var activatedPhone string
		err = db.GetDB("rbcare_data").Table("rbcare_data.ac_devices_uniq").
			Select("origin").
			Where("id = ?", data.ActivatedID).
			Scan(&activatedPhone).Error
		if err != nil {
			return nil, err
		}
		data.ActivatedPhone = activatedPhone
	}

	return &data, nil
}

func NewGormPromotionDao(db *gorm.DB) PromotionDao {
	return &GormPromotionDao{db: db}
}
