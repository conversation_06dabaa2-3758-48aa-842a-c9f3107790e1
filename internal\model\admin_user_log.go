package model

import (
	"time"
)

// AdminUserLog represents the user log model
type AdminUserLog struct {
	ID           uint      `gorm:"primaryKey" json:"id"`
	UID          int       `gorm:"not null" json:"uid"`
	Username     string    `gorm:"size:50;not null" json:"username"`
	Phone        string    `gorm:"size:100" json:"phone"`
	Operator     int       `gorm:"not null;default:0" json:"operator"`
	OperatorName string    `gorm:"-" json:"operator_name"`
	OpType       string    `gorm:"size:50;not null" json:"op_type"`
	Module       string    `json:"module"`      // 模块
	Method       string    `json:"method"`      // 请求方法
	Path         string    `json:"path"`        // 请求路径
	OperatorIP   string    `json:"operator_ip"` // 操作人IP
	Remark       string    `gorm:"size:255;not null" json:"remark"`
	Request      string    `gorm:"size:512;not null" json:"-"`
	Before       string    `gorm:"size:512;not null" json:"-"`
	After        string    `gorm:"size:512;not null" json:"-"`
	CreatedAt    time.Time `gorm:"autoCreateTime" json:"-"`
	CreatedAtStr string    `gorm:"-" json:"created_at"`
}

// TableName returns the table name of the user log model
func (AdminUserLog) TableName() string {
	return "admin_user_log"
}
