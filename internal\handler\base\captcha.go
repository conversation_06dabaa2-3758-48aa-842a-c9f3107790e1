package base

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/handler"
	appError "marketing/internal/pkg/errors"
	"marketing/internal/service/base"
)

type CaptchaHandler interface {
	SendCaptcha(ctx *gin.Context)
}

type captchaHandler struct {
	svc base.CaptchaSvc
}

func NewCaptchaHandler(svc base.CaptchaSvc) CaptchaHandler {
	return &captchaHandler{svc: svc}
}

func (c *captchaHandler) SendCaptcha(ctx *gin.Context) {
	type Request struct {
		Phone string `json:"phone"`
	}
	var req Request
	// 解析 JSON 数据
	if err := ctx.ShouldBind(&req); err != nil {
		handler.Error(ctx, appError.NewErr("请求数据格式错误，不是有效的 JSON 格式"))
		return
	}
	phone := req.Phone
	if phone == "" {
		handler.Error(ctx, appError.NewErr("手机号不能为空"))
		return
	}
	err := c.svc.SendCaptcha(ctx, phone)
	if err != nil {
		handler.Error(ctx, err)
		return
	}
	handler.Success(ctx, nil)
}
