package agency

import (
	"github.com/gin-gonic/gin"
	userDao "marketing/internal/dao/admin_user"
	"marketing/internal/dao/endpoint"
	dao "marketing/internal/dao/renew"
	dao1 "marketing/internal/dao/warranty"
	"marketing/internal/handler/agency/renew"
	"marketing/internal/pkg/db"
	service "marketing/internal/service/renew"
)

type RenewRouter struct {
	renewHandler renew.ApplicationHandlerInterface
}

func NewRenewRouter() *RenewRouter {
	var Db = db.GetDB()
	userD := userDao.NewUserDao(Db)
	endpointDao := endpoint.NewEndpointDao(Db)
	renewDao := dao.NewApplicationDao(Db)
	warrantyDao := dao1.NewWarrantyDao(Db)
	renewService := service.NewApplicationService(userD, renewDao, endpointDao, warrantyDao)
	renewHandler := renew.NewApplicationHandler(renewService)
	return &RenewRouter{
		renewHandler: renewHandler,
	}
}

func (rr *RenewRouter) Register(r *gin.RouterGroup) {
	renewRouter := r.Group("renew/apply")
	{
		renewRouter.POST("", rr.renewHandler.Add)
		renewRouter.PUT("/:id", rr.renewHandler.Update)
		renewRouter.GET("", rr.renewHandler.Lists)
		renewRouter.GET("/:id", rr.renewHandler.GetInfo)
		renewRouter.DELETE("/:id", rr.renewHandler.Delete)
		renewRouter.PUT("/:id/audit", rr.renewHandler.Audit)
		renewRouter.GET("/machine", rr.renewHandler.GetMachine)
		renewRouter.GET("/endpoint", rr.renewHandler.GetEndpoint)
		renewRouter.GET("/issue", rr.renewHandler.GetIssue)
		renewRouter.GET("/status", rr.renewHandler.GetStatus)
		renewRouter.GET("/export", rr.renewHandler.Export)
		renewRouter.GET("/warranty", rr.renewHandler.GetWarranties)
	}
}
