package dao

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"marketing/internal/api/youke"
	"marketing/internal/model"
)

type YkCourseDao interface {
	LiveList(c *gin.Context, param youke.LiveListReq) ([]model.YkCourse, int64, error)
}
type GormYkCourse struct {
	db *gorm.DB
}

func (g *GormYkCourse) LiveList(c *gin.Context, param youke.LiveListReq) ([]model.YkCourse, int64, error) {
	var list []model.YkCourse
	curDB := g.db.WithContext(c).Where("`type`=?", "live").Table("yk_course")
	if param.Enabled != 0 {
		curDB = curDB.Where("enabled=?", param.Enabled)
	}
	if param.Name != "" {
		curDB = curDB.Where("name like ?", "%"+param.Name+"%")
	}
	if param.Grads != "" {
		curDB = curDB.Where("grads like ?", param.Grads)
	}
	if param.Subject != "" {
		curDB = curDB.Where("subject = ?", param.Subject)
	}
	var total int64
	err := curDB.Count(&total).Error
	err = curDB.Order("sort").Find(&list).Error
	if err != nil {
		return list, total, err
	}
	return list, total, nil
}

func NewGormYoukeCourse(db *gorm.DB) YkCourseDao {
	return &GormYkCourse{db: db}
}
