package endpoint_application

import (
	"encoding/json"
	"errors"
	"marketing/internal/model"
	"marketing/internal/pkg/log"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// EndpointPolicyDao 定义终端政策数据访问接口
type EndpointPolicyDao interface {
	// CreateEndpointPolicy 创建终端政策
	CreateEndpointPolicy(c *gin.Context, policy *model.EndpointPolicy) error
	// DeleteEndpointPolicy 删除终端政策
	DeleteEndpointPolicy(c *gin.Context, id int) error
	// UpdateEndpointPolicy 更新终端政策
	UpdateEndpointPolicy(c *gin.Context, id int, uMap map[string]interface{}) error
	// GetEndpointPolicyByID 根据ID获取终端政策
	GetEndpointPolicyByID(c *gin.Context, id int) (*model.EndpointPolicy, error)
	// GetEndpointPolicyList 获取终端政策列表
	GetEndpointPolicyList(c *gin.Context, name string, enabled *int, pageNum, pageSize int) ([]*model.EndpointPolicy, int64, error)
	// PolicyLog 政策修改日志
	PolicyLog(c *gin.Context, policyID int, old, update map[string]any)
}

// EndpointPolicyDaoImpl 实现 EndpointPolicyDao 接口
type EndpointPolicyDaoImpl struct {
	db *gorm.DB
}

// NewEndpointPolicyDao 创建 EndpointPolicyDao 实例
func NewEndpointPolicyDao(db *gorm.DB) EndpointPolicyDao {
	return &EndpointPolicyDaoImpl{
		db: db,
	}
}

// CreateEndpointPolicy 创建终端政策
func (d *EndpointPolicyDaoImpl) CreateEndpointPolicy(c *gin.Context, policy *model.EndpointPolicy) error {
	now := time.Now()
	policy.CreatedAt = now
	policy.UpdatedAt = &now
	return d.db.WithContext(c).Create(policy).Error
}

// DeleteEndpointPolicy 删除终端政策
func (d *EndpointPolicyDaoImpl) DeleteEndpointPolicy(c *gin.Context, id int) error {
	return d.db.WithContext(c).Delete(&model.EndpointPolicy{}, "id = ?", id).Error
}

// UpdateEndpointPolicy 更新终端政策
func (d *EndpointPolicyDaoImpl) UpdateEndpointPolicy(c *gin.Context, id int, uMap map[string]interface{}) error {
	uMap["updated_at"] = time.Now()
	return d.db.WithContext(c).Model(&model.EndpointPolicy{}).Where("id = ?", id).Updates(uMap).Error
}

// GetEndpointPolicyByID 根据ID获取终端政策
func (d *EndpointPolicyDaoImpl) GetEndpointPolicyByID(c *gin.Context, id int) (*model.EndpointPolicy, error) {
	var policy model.EndpointPolicy
	err := d.db.WithContext(c).Where("id = ?", id).First(&policy).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &policy, nil
}

// GetEndpointPolicyList 获取终端政策列表
func (d *EndpointPolicyDaoImpl) GetEndpointPolicyList(c *gin.Context, name string, enabled *int, pageNum, pageSize int) ([]*model.EndpointPolicy, int64, error) {
	var policies []*model.EndpointPolicy
	var total int64

	query := d.db.WithContext(c).Model(&model.EndpointPolicy{})
	//query 2025以前的数据不查
	query = query.Where("created_at>?", "2025-01-01 00:00:00")

	// 如果有名称筛选条件
	if name != "" {
		query = query.Where("name LIKE ?", "%"+name+"%")
	}

	if enabled != nil {
		query = query.Where("enabled = ?", enabled)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (pageNum - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&policies).Error; err != nil {
		return nil, 0, err
	}

	return policies, total, nil
}

func (d *EndpointPolicyDaoImpl) PolicyLog(c *gin.Context, policyID int, old, update map[string]any) {
	oldJSON, err := json.Marshal(old)
	if err != nil {
		log.Error("政策日志修改记录出错" + err.Error())
	}

	newJSON, err := json.Marshal(update)
	if err != nil {
		log.Error("政策日志修改记录出错" + err.Error())
	}
	// 构建要插入的数据对象
	logData := map[string]interface{}{
		"policy_id":  policyID,
		"old_data":   string(oldJSON),
		"new_data":   string(newJSON),
		"user_id":    c.GetUint("uid"),
		"created_at": time.Now(), // 或使用数据库中的自动时间戳
	}
	err = d.db.WithContext(c).Table("endpoint_policy_log").Create(logData).Error
	if err != nil {
		log.Error("终端政策日志记录失败" + err.Error())
	}
}
