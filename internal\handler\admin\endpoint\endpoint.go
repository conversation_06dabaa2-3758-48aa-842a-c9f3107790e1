package endpoint

import (
	api "marketing/internal/api/endpoint"
	"marketing/internal/handler"
	appError "marketing/internal/pkg/errors"
	service "marketing/internal/service/endpoint"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

// Endpoint 处理与 Endpoint 相关的 HTTP 请求
type Endpoint interface {
	// Lists 获取所有 Endpoint
	Lists(c *gin.Context)
	// Get 获取指定 ID 的 Endpoint
	Get(c *gin.Context)
	// Create 创建新的 Endpoint
	Create(c *gin.Context)
	// UpdateEndpoint 更新指定 ID 的 Endpoint
	UpdateEndpoint(c *gin.Context)
	// UpdateStatus 更新用户状态
	UpdateStatus(c *gin.Context)
	// GetTopAgency 获取总代下拉列表
	GetTopAgency(c *gin.Context)
	// GetSecondAgency 获取二级代理下拉列表
	GetSecondAgency(c *gin.Context)
	// GetEndpointType 获取终端设置
	GetEndpointType(c *gin.Context)
	// GetDepartmentTree 获取终端树
	GetDepartmentTree(c *gin.Context)
	// GetAgencyEndpoint 获取终端下拉列表
	GetAgencyEndpoint(c *gin.Context)
}

type endpoint struct {
	svc service.Endpoint
}

func NewEndpoint(svc service.Endpoint) Endpoint {
	return &endpoint{
		svc: svc,
	}
}

// Lists 获取所有 Endpoint
func (e *endpoint) Lists(c *gin.Context) {
	var param api.GetEndpointReq
	// 绑定请求体中的 JSON 数据
	if err := c.ShouldBind(&param); err != nil {
		handler.Error(c, err)
		return
	}
	//默认分页
	param.SetDefaults()
	//调用 Service 层获取所有 Endpoint 数据
	endpoints, total, err := e.svc.GetAllEndpoints(c, param)
	if err != nil {
		// 如果查询失败，返回 500 错误
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"data":      endpoints,
		"total":     total,
		"page":      param.Page,
		"page_size": param.PageSize,
	})
}

// Get 获取指定 ID 的 Endpoint
func (e *endpoint) Get(c *gin.Context) {
	idStr := c.Param("id")

	// 将 id 字符串转换为 uint
	ids, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		// 转换失败，返回错误
		handler.Error(c, appError.NewErr("Invalid ID"))
		return
	}
	id := uint(ids)
	// 调用 Service 层获取指定 Endpoint 数据
	endpointInfo, err := e.svc.GetEndpointByID(c, id)
	if err != nil {
		// 如果查询失败，返回 404 错误
		handler.Error(c, err)
		return
	}

	// 查询成功，返回数据
	handler.Success(c, endpointInfo)
}

// Create 创建新的 Endpoint
func (e *endpoint) Create(c *gin.Context) {
	var param api.AddEndpointReq
	// 绑定请求体中的 JSON 数据
	if err := c.ShouldBindJSON(&param); err != nil {
		handler.Error(c, err)
		return
	}

	// 调用 Service 层创建 Endpoint
	if data, err := e.svc.CreateEndpoint(c, &param); err != nil {
		handler.Error(c, err)
		return
	} else {
		handler.Success(c, data)
	}
}

// UpdateEndpoint 更新指定 ID 的 Endpoint
func (e *endpoint) UpdateEndpoint(c *gin.Context) {
	idStr := c.Param("id")
	// 将 id 字符串转换为 uint
	ids, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		// 转换失败，返回错误
		handler.Error(c, appError.NewErr("Invalid ID"))
		return
	}

	var param api.AddEndpointReq

	// 绑定请求体中的 JSON 数据
	if err := c.ShouldBindJSON(&param); err != nil {
		handler.Error(c, err)
		return
	}
	// 设置 ID，确保更新的是指定的 Endpoint
	param.ID = uint(ids)

	// 调用 Service 层更新 Endpoint 数据
	if data, err := e.svc.UpdateEndpoint(c, &param); err != nil {
		handler.Error(c, err)
		return
	} else {
		handler.Success(c, data)
	}
}

// UpdateStatus 更新用户状态
func (e *endpoint) UpdateStatus(c *gin.Context) {
	var req api.UpdateStatusReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	req.ID = cast.ToUint(c.Param("id"))
	if req.ID == 0 {
		handler.Error(c, appError.NewErr("id不能为空"))
		return
	}

	err := e.svc.UpdateStatus(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

// GetTopAgency 获取总代下拉列表
func (e *endpoint) GetTopAgency(c *gin.Context) {
	data, err := e.svc.GetTopAgencies(c)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, data)
}

// GetSecondAgency 获取二级代理下拉列表
func (e *endpoint) GetSecondAgency(c *gin.Context) {
	topAgency := cast.ToUint(c.Query("top_agency"))
	if topAgency == 0 {
		handler.Error(c, appError.NewErr("top_agency不能为空"))
		return
	}
	data, err := e.svc.GetSecondAgencies(c, topAgency)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, data)
}

// GetEndpointType 获取终端设置
func (e *endpoint) GetEndpointType(c *gin.Context) {
	data, err := e.svc.GetEndpointType(c)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, data)
}

// GetDepartmentTree 获取部门树
func (e *endpoint) GetDepartmentTree(c *gin.Context) {
	data, err := e.svc.GetTree(c, true)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, data)
}

// GetAgencyEndpoint 获取终端下拉列表
func (e *endpoint) GetAgencyEndpoint(c *gin.Context) {
	agencyId := cast.ToUint(c.Query("agency_id"))
	if agencyId == 0 {
		handler.Error(c, appError.NewErr("agency_id is required"))
		return
	}

	data, err := e.svc.GetEndpointByAgencyID(c, agencyId)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, data)

}
