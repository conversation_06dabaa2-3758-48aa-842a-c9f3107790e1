package materials

import "time"

type KindAddReq struct {
	ID        int       `json:"id"`
	Title     string    `json:"title" binding:"required"`
	ParentID  int       `json:"parent_id"`
	CreatedAt time.Time `json:"-"`
	UpdatedAt time.Time `json:"-"`
}
type KindSaveReq struct {
	ID    int `json:"id"`
	Order int `json:"order"`
}
type KindList struct {
	ID       int    `json:"id"`
	Title    string `json:"title"`
	ParentID int    `json:"parent_id"`
	Order    int    `json:"order"`
	Num      int    `json:"num"`
}
type Kind struct {
	ID        int    `json:"id"`
	Title     string `json:"title"`
	Parent    string `json:"parent"`
	Order     int    `json:"order"`
	UpdatedAt string `json:"updated_at"`
	CreatedAt string `json:"created_at"`
}
