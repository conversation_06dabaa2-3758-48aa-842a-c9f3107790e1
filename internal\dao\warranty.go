package dao

import (
	stdError "errors"
	"fmt"
	api "marketing/internal/api/warranty"
	"marketing/internal/consts"
	"marketing/internal/model"
	"marketing/internal/pkg/db"
	"marketing/internal/pkg/errors"
	zaplog "marketing/internal/pkg/log"
	"marketing/internal/pkg/types"
	"marketing/internal/pkg/utils"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type InterfaceWarranty interface {
	GetByBarcode(c *gin.Context, barcode string) (*model.Warranty, error)
	GetWarrantiesWithEndpoint(c *gin.Context, params *api.WarrantyInfoReq) ([]*api.WarrantyInfoWithEndpoint, int64, int, int, error)
	HasWarranty(c *gin.Context, barcode string, imei string, phone string) (*api.WarrantyWithMT, error)
	UpdateWarranty(c *gin.Context, params *model.Warranty) (int64, error) // 更新一条已有保单保单‘
	UpdateByID(c *gin.Context, update *model.Warranty) (int64, error)
	AddWarranty(c *gin.Context, params *model.Warranty) (int64, error)
	UpdateAssessment(c *gin.Context, id int, action uint) error // 更新考核状态
	GetOneWarranty(c *gin.Context, barcode string) (*model.Warranty, error)
	ReturnByBarcode(c *gin.Context, barcode string, reason string, warrantyID int, uid uint, endpointID int, returnAt time.Time, overdue bool) (bool, error)
	AddVirtualCard(c *gin.Context, warranty *model.Warranty, ch chan<- error, wg *sync.WaitGroup)
	// DrpMachine 进销存同步
	DrpMachine(c *gin.Context, barcode string, newBarcode string, oType int, ret int, warranty *model.Warranty) error
	// GetWarrantyReturnList 获取退货列表
	GetWarrantyReturnList(c *gin.Context, info *api.ReturnListReq) ([]*api.ReturnListResp, int64, int, int, error)
	EditReturnByReason(c *gin.Context, id int, reason string) error
	ExchangeByUpdate(c *gin.Context, a *api.UpdExchange) error
	ExchangeByCreate(c *gin.Context, a *api.Exchange) error
	GetOneWarrantyByID(c *gin.Context, id int) (*model.Warranty, error)
	GetAllWarrantyByBarcode(c *gin.Context, barcode string) ([]*model.Warranty, error)
	// GetWarrantyExchangeList 获取退货列表
	GetWarrantyExchangeList(c *gin.Context, info *api.ExchangeListReq) ([]*api.ExchangeListResp, int64, int, int, error)
	GetOneExchangeByID(c *gin.Context, id int) (*model.WarrantyExchange, error)
	GetOneExchangeByWarrantyID(c *gin.Context, id int) (*api.WarrantyExchangeInfo, error)
	ExchangeEditByReason(c *gin.Context, id int, reason string) (int64, error)
	GetAllWarrantiesByParam(c *gin.Context, param string) ([]*api.WarrantyDetail, error)
	GetOneReturnByWarrantyID(c *gin.Context, id int) (*api.WarrantyReturnInfo, error)
	GetExportWarranties(c *gin.Context, page int, pageSize int, code string, ids []uint) ([]*api.WarrantiesExportResp, int64, error)
	UpdateOldWithNewOrder(c *gin.Context, a *api.UpdOldWithNewReq) error
	GetActivatedDevices(c *gin.Context, barcode string) ([]*model.AcDevicesUniq, error)
	GetMachinesHistoriesList(c *gin.Context, params *api.MachinesHistoriesReq) ([]*model.DevicesHistory, int64, error)
	GetDevicesHistory(c *gin.Context, barcode string) ([]*model.DevicesHistory, error)
	GetDevicesHistoryByNumberNew(c *gin.Context, numberNew string) (*model.DevicesHistory, error)
	UpsertDevicesHistory(c *gin.Context, history *model.DevicesHistory, params *api.MachineHistoryUpdReq) error
	CreateDevicesHistory(c *gin.Context, create *model.DevicesHistory) error
	UpdateDevicesHistory(c *gin.Context, query clause.Expr, upd map[string]interface{}) error
	GetDevicesLimitAccountList(c *gin.Context, req *api.DevicesLimitAccountReq) ([]*model.DeviceLimitAccount, int64, error)
	EditDevicesLimitAccount(c *gin.Context, query clause.Expr, upd map[string]interface{}) error
	GetDevicesLimitNumber(c *gin.Context, array []string) (map[string]string, error)
	BatchInsertDevicesLimitAccount(c *gin.Context, data []*model.DeviceLimitAccount) error
	GetDevicesLimitAccountByCond(c *gin.Context, cond clause.Expr) (*model.DeviceLimitAccount, error)
	GetPackRecord(c *gin.Context, barcode string) ([]*model.MesPackDevices, error)
	GetK3RecordInStock(c *gin.Context, barcode string) ([]*model.V3Instock, error)
	GetK3RecordOutStock(c *gin.Context, barcode string) ([]*model.V3Outstock, error)
	GetK3RecordReturnStock(c *gin.Context, barcode string) ([]*model.V3Returnstock, error)
	GetK3RecordAllotStock(c *gin.Context, barcode string) ([]*model.V3Allotstock, error)
}

type warranty struct {
	db *gorm.DB
}

func NewWarrantyDao(db *gorm.DB) InterfaceWarranty {
	return &warranty{
		db: db,
	}
}

func (w *warranty) UpdateWarranty(c *gin.Context, wt *model.Warranty) (int64, error) {
	now := time.Now()
	wt.UpdatedAt = types.CustomTime(now)
	createdAtNew := types.CustomTime{}
	wt.CreatedAtNew = &createdAtNew // 指向一个零值的地址使数据库更新为nil
	status := int8(1)
	wt.Status = &status
	res := w.db.WithContext(c).
		Model(&model.Warranty{}).
		Omit("barcode", "model").
		Where("barcode = ? AND status = ?", wt.Barcode, consts.WarrantyStatusVirtual).
		Updates(wt)
	if res.Error != nil {
		return 0, res.Error
	}

	return res.RowsAffected, nil
}

func (w *warranty) UpdateByID(c *gin.Context, update *model.Warranty) (int64, error) {
	now := time.Now()
	update.UpdatedAt = types.CustomTime(now)
	status := int8(1)
	update.Status = &status

	whereCond := model.Warranty{ID: update.ID}
	var record model.Warranty
	if err := w.db.Where(whereCond).First(&record).Error; err != nil {
		if stdError.Is(err, gorm.ErrRecordNotFound) {
			return 0, errors.NewErr("record not found")
		}
		return 0, err
	}

	res := w.db.WithContext(c).Model(&record).Updates(update)
	if res.Error != nil {
		return 0, res.Error
	}
	return res.RowsAffected, nil
}

func (w *warranty) GetByBarcode(c *gin.Context, barcode string) (*model.Warranty, error) {
	var warranty *model.Warranty

	err := w.db.WithContext(c).Table("warranty as w").
		Select("w.id, w.barcode, w.number, w.imei, w.ext_barcode, w.created_at, w.status, w.model, w.model_id, w.customer_phone, w.product_date, mt.category_id, mt.ext_barcode_num, w.state, w.activated_at_old").
		Joins("LEFT JOIN machine_type mt ON w.model_id = mt.model_id").
		Where("(w.barcode = ? OR w.ext_barcode = ?) AND (w.status = ? OR w.status = ?)",
			barcode,
			barcode,
			consts.WarrantyStatusVirtual,
			consts.WarrantyStatusActive).
		First(&warranty).Error

	if err != nil {
		return nil, err
	}

	return warranty, nil
}

func (w *warranty) HasWarranty(c *gin.Context, barcode string, imei string, number string) (*api.WarrantyWithMT, error) {
	var wt api.WarrantyWithMT
	tx := w.db.WithContext(c).Table("warranty as w").
		Select("w.id, w.barcode, w.number, w.imei, w.ext_barcode, w.created_at, w.status, w.model, w.model_id, w.customer_phone, w.product_date, w.state, w.activated_at_old, mt.category_id, mt.ext_barcode_num").
		Joins("LEFT JOIN machine_type mt ON w.model_id = mt.model_id")

	if barcode != "" {
		tx = tx.Where("(w.barcode = ? OR w.ext_barcode = ?) AND (w.status = ? OR w.status = ?)",
			barcode,
			barcode,
			consts.WarrantyStatusVirtual,
			consts.WarrantyStatusActive,
		) // 有主卡或副卡
	} else if imei != "" {
		tx = tx.Where("w.imei = ? AND (w.status = ? OR w.status = ?)",
			imei,
			consts.WarrantyStatusVirtual,
			consts.WarrantyStatusActive,
		)
	} else {
		tx = tx.Where("w.number = ? AND (w.status = ? OR w.status = ?)",
			number,
			consts.WarrantyStatusVirtual,
			consts.WarrantyStatusActive,
		)
	}

	err := tx.First(&wt).Error
	if err != nil {
		if stdError.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &wt, nil
}

func (w *warranty) AddWarranty(c *gin.Context, wt *model.Warranty) (int64, error) {
	res := w.db.WithContext(c).Create(wt)
	if res.Error != nil || res.RowsAffected == 0 {
		return 0, errors.NewErr("insert nothing")
	}
	// TODO: 如果价格为0，发送微信通知
	return res.RowsAffected, nil
}

func (w *warranty) GetWarrantiesWithEndpoint(
	c *gin.Context,
	params *api.WarrantyInfoReq,
) ([]*api.WarrantyInfoWithEndpoint, int64, int, int, error) {
	query := w.db.WithContext(c).Table("warranty as w").
		Select("w.id, w.barcode, w.ext_barcode, w.model, w.salesman, w.buy_date, w.customer_name, w.customer_phone, "+
			"w.customer_addr, w.customer_sex, w.student_name, w.student_school, w.student_birthday, w.student_grade, "+
			"endpoint.name, endpoint.address, endpoint.phone, endpoint.manager, w.assessment").
		Joins("LEFT JOIN endpoint ON w.endpoint = endpoint.id").
		Where("w.status = ?", consts.WarrantyStatusActive)

	if barcode := params.Barcode; barcode != "" {
		query = query.Where("barcode LIKE ?", barcode+"%")
	}
	if customerName := params.CustomerName; customerName != "" {
		query = query.Where("customer_name LIKE ?", "%"+customerName+"%")
	}
	if customerPhone := params.CustomerPhone; customerPhone != "" {
		query = query.Where("customer_phone = ?", customerPhone)
	}
	if begin := params.BuyDateStart; begin != "" {
		b, err := time.Parse("2006-01-02", begin)
		if err != nil {
			return nil, 0, 0, 0, errors.NewErr("非法日期格式")
		}
		query = query.Where("buy_date >= ?", b)
	}
	if end := params.BuyDateEnd; end != "" {
		e, err := time.Parse("2006-01-02", end)
		if err != nil {
			return nil, 0, 0, 0, errors.NewErr("非法日期格式")
		}
		query = query.Where("buy_date <= ?", e)
	}
	if m := params.Model; m != "" {
		query = query.Where("model = ?", m)
	}
	if topAgency := params.Pid; topAgency != 0 {
		query = query.Where("endpoint.top_agency = ?", topAgency)
	}
	if secondaryAgency := params.SecondaryAgency; secondaryAgency != 0 {
		query = query.Where("endpoint.second_agency = ?", secondaryAgency)
	}
	if endpointCode := params.EndpointCode; endpointCode != "" {
		query = query.Where("endpoint.code = ?", endpointCode)
	}

	query = query.Order("w.id desc")
	var total int64
	page := params.Page
	if page <= 0 {
		return nil, 0, 0, 0, errors.NewErr("错误页码")
	}
	pageSize := params.PageSize
	if pageSize <= 0 {
		return nil, 0, 0, 0, errors.NewErr("错误页数量")
	}
	offset := (page - 1) * pageSize

	query.Count(&total)
	var warrantiesWithEndpoint []*api.WarrantyInfoWithEndpoint
	err := query.Offset(offset).Limit(pageSize).Scan(&warrantiesWithEndpoint).Error
	if err != nil {
		return nil, 0, 0, 0, err
	}
	return warrantiesWithEndpoint, total, page, pageSize, nil
}

func (w *warranty) UpdateAssessment(c *gin.Context, id int, action uint) error {
	var wt model.Warranty
	if err := w.db.WithContext(c).First(&wt, id).Error; err != nil {
		return errors.NewErr("warranty not found")
	}

	warrantyID := id
	endpointID := wt.Endpoint

	err := w.db.WithContext(c).
		Transaction(func(tx *gorm.DB) error {
			var wy model.Warranty
			// 锁定 warranty 行
			if err := tx.Clauses(clause.Locking{Strength: "Update"}).First(&wy, "id = ?", warrantyID).Error; err != nil {
				return errors.NewErr("failed to lock warranty")
			}

			newWarrantyUpdateAt := time.Now()
			result := tx.Model(&wy).
				Select("Assessment", "UpdatedAt").
				Updates(
					model.Warranty{
						Assessment: &action,
						UpdatedAt:  types.CustomTime(newWarrantyUpdateAt),
					},
				)
			if result.Error != nil {
				return errors.NewErr("failed to update warranty")
			}
			if result.RowsAffected == 0 {
				return errors.NewErr(fmt.Sprintf("no warranty found with ID: %d", warrantyID))
			}

			// 锁定 Endpoint 行
			var ep model.Endpoint
			if err := tx.Clauses(clause.Locking{Strength: "Update"}).First(&ep, "id = ?", endpointID).Error; err != nil {
				return errors.NewErr("failed to lock endpoint")
			}
			// 4. 更新 Endpoint
			newEndpointActiveAt := time.Now()
			result = tx.Model(&ep).Updates(model.Endpoint{ActiveAt: &newEndpointActiveAt})
			if result.Error != nil {
				return errors.NewErr("failed to update endpoint")
			}
			if result.RowsAffected == 0 {
				return errors.NewErr(fmt.Sprintf("no endpoint found with ID: %d", endpointID))
			}

			return nil
		})

	if err != nil {
		return err
	}

	return nil
}

func (w *warranty) GetOneWarranty(c *gin.Context, barcode string) (*model.Warranty, error) {
	var card model.Warranty
	err := w.db.WithContext(c).Table("warranty").
		Where("barcode = ? AND status = ?", barcode, consts.WarrantyStatusActive).
		First(&card).
		Error
	if err != nil {
		if stdError.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.NewErr("warranty not found")
		}
		return nil, err
	}
	return &card, nil
}

func (w *warranty) GetAllWarrantyByBarcode(c *gin.Context, barcode string) ([]*model.Warranty, error) {
	var res []*model.Warranty
	if err := w.db.WithContext(c).Table("warranty").
		Where("barcode = ?", barcode).
		Scan(&res).Error; err != nil {
		return nil, err
	}
	return res, nil
}

func (w *warranty) ReturnByBarcode(c *gin.Context, barcode string, reason string, warrantyID int, uid uint, endpointID int, returnAt time.Time, overdue bool) (bool, error) {
	tx := w.db.Begin()
	now := time.Now()

	// 标记旧保卡
	if err := tx.WithContext(c).Model(&model.Warranty{}).
		Where("id = ?", warrantyID).
		Updates(map[string]interface{}{
			"status":     3,
			"updated_at": now,
			"deleted_at": now,
		}).Error; err != nil {
		tx.Rollback()
		return false, err
	}

	// 插入退货记录
	wr := model.WarrantyReturn{
		Barcode:    barcode,
		Reason:     reason,
		WarrantyID: warrantyID,
		UID:        int(uid),
		Endpoint:   endpointID,
		ReturnAt:   returnAt,
		IsOverdue:  overdue,
		CreatedAt:  now,
	}
	if err := tx.WithContext(c).Create(&wr).Error; err != nil {
		tx.Rollback()
		return false, err
	}

	if err := tx.Commit().Error; err != nil {
		return false, err
	}

	// TODO: 阿里云发送消息通知

	return true, nil
}

// AddVirtualCard 退货/换货生成虚拟保卡
func (w *warranty) AddVirtualCard(c *gin.Context, wt *model.Warranty, ch chan<- error, wg *sync.WaitGroup) {
	defer wg.Done()
	assessment := uint(0)
	s := int8(0)
	createdAtNew := types.CustomTime(time.Now())
	returnWarranty := &model.Warranty{
		Number:        wt.Number,
		Barcode:       wt.Barcode,
		Imei:          wt.Imei,
		ModelID:       wt.ModelID,
		Model:         wt.Model,
		CustomerPrice: wt.CustomerPrice,
		Status:        &s,
		ProductDate:   wt.ProductDate,
		Assessment:    &assessment,
		CreatedAtNew:  &createdAtNew,
	}
	if err := w.db.WithContext(c).Omit("created_at", "buy_date").Create(returnWarranty).Error; err != nil {
		ch <- errors.NewErr(fmt.Sprintf("add virtual card error: %v", err))
		return
	}
	ch <- nil
}

func (w *warranty) DrpMachine(c *gin.Context, barcode string, newBarcode string, oType int, ret int, warranty *model.Warranty) error {
	now := time.Now()

	tx := w.db.WithContext(c).Begin()
	if tx.Error != nil {
		return tx.Error
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			zaplog.Warn(fmt.Sprintf("Panic occurred, rolling back transaction: %v", r))
		}
		if err := tx.Commit().Error; err != nil {
			zaplog.Error(fmt.Sprintf("Transaction commit failed: %v", err))
			if r := tx.Rollback(); r != nil {
				zaplog.Error(fmt.Sprintf("Transaction rollback failed: %v", r))
			}
		}
	}()

	machine, err := findDrpMachine(tx, barcode)
	if err != nil && !stdError.Is(err, gorm.ErrRecordNotFound) {
		return err
	}

	var newMachine *model.DrpMachine
	if newBarcode != "" {
		newMachine, err = findDrpMachine(tx, newBarcode)
		if err != nil && !stdError.Is(err, gorm.ErrRecordNotFound) {
			return err
		}
	}

	recordID, err := findRecordID(tx, barcode)
	if err != nil {
		return err
	}

	newRecordID, err := findRecordID(tx, newBarcode)
	if err != nil {
		return err
	}

	drpStatus, err := handleMachineStatus(tx, barcode, machine, newBarcode, newMachine, oType, ret, warranty, now)
	if err != nil {
		return err
	}

	if recordID != 0 {
		if err := updateRecordDetail(tx, recordID, barcode, drpStatus); err != nil {
			return err
		}
	}

	if newRecordID != 0 {
		if err := updateRecordDetail(tx, newRecordID, newBarcode, 1); err != nil { // 1 is drpNewStatus
			return err
		}
	}

	return nil
}

func (w *warranty) GetWarrantyReturnList(c *gin.Context, info *api.ReturnListReq) ([]*api.ReturnListResp, int64, int, int, error) {
	query := w.db.WithContext(c).Table("warranty_return as wr").
		Select("wr.id, wr.barcode, w.model, wr.reason, w.customer_name, ep.name, ep.address, ep.phone, ep.manager, wr.return_at").
		Joins("LEFT JOIN warranty w ON w.id = wr.warranty_id").
		Joins("LEFT JOIN endpoint ep ON wr.endpoint = ep.id")

	if info.Barcode != "" {
		query = query.Where("wr.barcode LIKE ?", "%"+info.Barcode+"%")
	}
	if info.ReturnTimeBegin != "" {
		formatTimeBegin, err := time.Parse("2006-01-02", info.ReturnTimeBegin)
		if err != nil {
			return nil, 0, 0, 0, err
		}
		query = query.Where("wr.created_at >= ?", formatTimeBegin)
	}
	if info.ReturnTimeEnd != "" {
		formatTimeEnd, err := time.Parse("2006-01-02", info.ReturnTimeEnd)
		if err != nil {
			return nil, 0, 0, 0, err
		}
		query = query.Where("wr.created_at <= ?", formatTimeEnd)
	}
	query = query.Order("wr.id DESC")
	var total int64
	query.Count(&total)

	page := info.Page
	if page <= 0 {
		return nil, 0, 0, 0, errors.NewErr("错误页码")
	}
	pageSize := info.PageSize
	if pageSize <= 0 {
		return nil, 0, 0, 0, errors.NewErr("错误页数量")
	}
	offset := (page - 1) * pageSize

	var returnList []*api.ReturnListResp
	if err := query.Offset(offset).Limit(pageSize).Scan(&returnList).Error; err != nil {
		return nil, 0, 0, 0, err
	}
	return returnList, total, page, pageSize, nil
}

// EditReturnByReason 编辑退货原因
func (w *warranty) EditReturnByReason(c *gin.Context, id int, reason string) error {
	err := w.db.WithContext(c).Table("warranty_return").
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"reason": reason,
		}).Error
	return err
}

func (w *warranty) GetOneWarrantyByID(c *gin.Context, id int) (*model.Warranty, error) {
	var wt model.Warranty
	if err := w.db.WithContext(c).Where("id = ?", id).First(&wt).Error; err != nil {
		if stdError.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.NewErr("ID not found")
		}
		return nil, err
	}
	return &wt, nil
}

func (w *warranty) ExchangeByUpdate(c *gin.Context, params *api.UpdExchange) error {
	tx := w.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	now := time.Now()
	// 更新虚卡为正常新保卡
	updateData := map[string]interface{}{
		"salesman":         params.Warranty.Salesman,
		"salesman_id":      params.Warranty.SalesmanID,
		"customer_price":   params.Warranty.CustomerPrice,
		"customer_name":    params.Warranty.CustomerName,
		"customer_sex":     params.Warranty.CustomerSex,
		"customer_phone":   params.Warranty.CustomerPhone,
		"customer_addr":    params.Warranty.CustomerAddr,
		"endpoint":         params.EndpointID,
		"buy_date":         params.Warranty.BuyDate,
		"warranty_period":  params.Warranty.WarrantyPeriod,
		"lng":              params.Warranty.Lng,
		"lat":              params.Warranty.Lat,
		"student_uid":      params.Warranty.StudentUID,
		"student_name":     params.Warranty.StudentName,
		"student_sex":      params.Warranty.StudentSex,
		"student_school":   params.Warranty.StudentSchool,
		"student_grade":    params.Warranty.StudentGrade,
		"student_birthday": params.Warranty.StudentBirthday,
		"created_at":       now,
		"updated_at":       now,
		"status":           1, // 正常状态
		"number":           params.Number,
		"imei":             params.Warranty.Imei,
		"ext_barcode":      params.ExtBarcodeNew,
		"purchase_way":     params.Warranty.PurchaseWay,
		"assessment":       params.Warranty.Assessment,
	}
	if err := tx.Model(&model.Warranty{}).Where("barcode = ? AND id = ?", params.BarcodeNew, params.ID).Updates(updateData).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 标记旧保卡
	if err := tx.Model(&model.Warranty{}).Where("id = ?", params.ID).Updates(map[string]interface{}{
		"status":     2,
		"updated_at": now,
		"deleted_at": now,
	}).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 插入换货记录
	exchange := &model.WarrantyExchange{
		Barcode:       params.Barcode,
		BarcodeNew:    params.BarcodeNew,
		Reason:        params.Reason,
		WarrantyID:    params.Warranty.ID,
		WarrantyNewID: params.ID,
		UID:           int(params.Uid),
		Endpoint:      params.EndpointID,
		ExchangedAt:   params.ExchangedAt,
	}
	if err := tx.Create(&exchange).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 删除微信绑定
	var binding *model.PuzcWxbinding
	if err := tx.WithContext(c).Where("barcode = ?", params.Barcode).
		Delete(&binding).
		Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Commit().Error; err != nil {
		return err
	}
	// TODO: 退货换货发送消息队列 （需要go 1.23.0） warrantyReturnOrExchangeProducer()

	return nil
}

func (w *warranty) ExchangeByCreate(c *gin.Context, params *api.Exchange) error {
	tx := w.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()
	now := time.Now()

	// 新保卡数据
	state := int8(1)
	newWarranty := *params.Warranty
	newWarranty.ID = 0
	newWarranty.Barcode = params.BarcodeNew
	newWarranty.ExtBarcode = params.ExtBarcodeNew
	newWarranty.CreatedAt = now
	newWarranty.UpdatedAt = types.CustomTime(now)
	newWarranty.Number = params.Number
	newWarranty.WarrantyPeriod = params.WarrantyPeriod
	newWarranty.State = &state
	newWarranty.Realsale = 0

	// 插入新保卡
	if err := tx.WithContext(c).Create(&newWarranty).Error; err != nil {
		tx.Rollback()
		return err
	}
	warrantyNewID := newWarranty.ID

	// 标记旧保卡
	if err := tx.WithContext(c).Model(&model.Warranty{}).Where("id = ?", params.Warranty.ID).
		Updates(map[string]interface{}{
			"status":     2,
			"updated_at": now,
			"deleted_at": now,
		}).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 插入换货记录
	var exchange *model.WarrantyExchange
	exchange = &model.WarrantyExchange{
		Barcode:       params.Barcode,
		BarcodeNew:    params.BarcodeNew,
		Reason:        params.Reason,
		WarrantyID:    params.Warranty.ID,
		WarrantyNewID: warrantyNewID,
		UID:           int(params.Uid),
		Endpoint:      params.EndpointID,
		ExchangedAt:   params.ExchangedAt,
		CreatedAt:     now,
	}
	if err := tx.WithContext(c).Create(&exchange).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 删除微信绑定
	if err := tx.WithContext(c).Where("barcode = ?", params.Barcode).Delete(&model.PuzcWxbinding{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Commit().Error; err != nil {
		return err
	}
	// TODO: 退货换货发送消息队列 （需要go 1.23.0） warrantyReturnOrExchangeProducer()
	return nil
}

func (w *warranty) GetWarrantyExchangeList(c *gin.Context, info *api.ExchangeListReq) ([]*api.ExchangeListResp, int64, int, int, error) {
	query := w.db.WithContext(c).Table("warranty_exchange").
		Select("warranty_exchange.id, warranty_exchange.barcode, warranty_exchange.barcode_new, warranty_exchange.reason, warranty_exchange.created_at, w1.customer_name, " +
			"w1.model AS model_old, w2.model AS model_new, ep.name AS endpoint_name, ep.address AS endpoint_address, ep.phone AS endpoint_phone, ep.manager AS endpoint_manager").
		Joins("LEFT JOIN warranty AS w1 ON warranty_exchange.warranty_id = w1.id").
		Joins("LEFT JOIN warranty AS w2 ON warranty_exchange.warranty_new_id = w2.id").
		Joins("LEFT JOIN endpoint AS ep ON warranty_exchange.endpoint = ep.id")

	if info.Barcode != "" {
		query = query.Where("warranty_exchange.barcode LIKE ?", "%"+info.Barcode+"%")
	}
	if info.ExchangeTimeBegin != "" {
		formatTimeBegin, err := time.Parse("2006-01-02", info.ExchangeTimeBegin)
		if err != nil {
			return nil, 0, 0, 0, err
		}
		query = query.Where("warranty_exchange.created_at >= ?", formatTimeBegin)
	}
	if info.ExchangeTimeEnd != "" {
		formatTimeEnd, err := time.Parse("2006-01-02", info.ExchangeTimeEnd)
		if err != nil {
			return nil, 0, 0, 0, err
		}
		query = query.Where("warranty_exchange.created_at <= ?", formatTimeEnd)
	}
	query = query.Order("warranty_exchange.id DESC")
	var total int64
	query.Count(&total)

	page := info.Page
	if page <= 0 {
		return nil, 0, 0, 0, errors.NewErr("错误页码")
	}
	pageSize := info.PageSize
	if pageSize <= 0 {
		return nil, 0, 0, 0, errors.NewErr("错误页数量")
	}
	offset := (page - 1) * pageSize

	var exchangeList []*api.ExchangeListResp
	if err := query.Offset(offset).Limit(pageSize).Scan(&exchangeList).Error; err != nil {
		return nil, 0, 0, 0, err
	}
	return exchangeList, total, page, pageSize, nil
}

func (w *warranty) GetOneExchangeByID(c *gin.Context, id int) (*model.WarrantyExchange, error) {
	var ex model.WarrantyExchange
	if err := w.db.WithContext(c).
		Where("id = ?", id).
		First(&ex).Error; err != nil {
		return nil, err
	}
	return &ex, nil
}

func (w *warranty) ExchangeEditByReason(c *gin.Context, id int, reason string) (int64, error) {
	res := w.db.WithContext(c).Table("warranty_exchange").
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"reason": reason,
		})
	return res.RowsAffected, res.Error
}

func (w *warranty) GetAllWarrantiesByParam(c *gin.Context, param string) ([]*api.WarrantyDetail, error) {
	var list []*api.WarrantyDetail
	err := w.db.WithContext(c).Table("warranty as wt").
		Joins("LEFT JOIN endpoint as ep ON ep.id = wt.endpoint").
		Select("wt.id, wt.status, wt.barcode, wt.number, wt.salesman, wt.customer_name, wt.customer_phone, wt.model, "+
			"wt.buy_date, wt.created_at, wt.activated_at, wt.product_date, ep.name, ep.address, ep.manager, ep.phone").
		Where("wt.status != ? and (wt.barcode = ? or wt.customer_phone = ? or wt.number = ?)", 0, param, param, param).
		Scan(&list).Error
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (w *warranty) GetOneExchangeByWarrantyID(c *gin.Context, warrantyID int) (*api.WarrantyExchangeInfo, error) {
	var ex api.WarrantyExchangeInfo
	err := w.db.WithContext(c).Table("warranty_exchange as we").
		Joins("LEFT JOIN endpoint as ep ON we.endpoint = ep.id").
		Select("we.barcode_new, we.reason, we.created_at, ep.name, ep.address, ep.manager, ep.phone").
		Where("we.warranty_id = ?", warrantyID).
		First(&ex).Error
	return &ex, err
}

func (w *warranty) GetOneReturnByWarrantyID(c *gin.Context, id int) (*api.WarrantyReturnInfo, error) {
	var re api.WarrantyReturnInfo
	err := w.db.WithContext(c).Table("warranty_return as wr").
		Joins("LEFT JOIN endpoint as ep ON wr.endpoint = ep.id").
		Select("wr.reason,wr.created_at, ep.name, ep.address, ep.manager, ep.phone").
		Where("wr.warranty_id = ?", id).
		First(&re).Error
	return &re, err
}

func (w *warranty) GetExportWarranties(c *gin.Context, page int, pageSize int, code string, ids []uint) ([]*api.WarrantiesExportResp, int64, error) {
	var warranties []*api.WarrantiesExportResp
	query := w.db.WithContext(c).Table("warranty").
		Joins("LEFT JOIN endpoint ON warranty.endpoint = endpoint.id").
		Select("endpoint.code, endpoint.name as endpoint_name, warranty.model, warranty.barcode, warranty.buy_date,"+
			" warranty.customer_phone, warranty.customer_name, warranty.student_name, warranty.student_school, warranty.student_grade").
		Where("warranty.status = ? AND endpoint.code = ?", consts.WarrantyStatusActive, code).
		Where("warranty.model_id IN ?", ids).
		Order("warranty.buy_date ASC")

	var total int64
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Scan(&warranties).Error; err != nil {
		return nil, 0, err
	}
	return warranties, total, nil
}

func (w *warranty) GetDevicesHistory(c *gin.Context, barcode string) ([]*model.DevicesHistory, error) {
	var devicesHistory []*model.DevicesHistory
	err := w.db.WithContext(c).Model(&model.DevicesHistory{}).
		Where("barcode = ?", barcode).
		Scan(&devicesHistory).Error
	if err != nil {
		return nil, err
	}
	if len(devicesHistory) == 0 {
		err = w.db.WithContext(c).Table("mes_devices as md").
			Select("md.model_id, md.model, md.barcode, md.number").
			Where("md.barcode = ?", barcode).
			Scan(&devicesHistory).Error
		if err != nil {
			return nil, err
		}
	}
	return devicesHistory, nil
}

func (w *warranty) UpdateOldWithNewOrder(c *gin.Context, req *api.UpdOldWithNewReq) (err error) {
	defer func() {
		if err != nil {
			zaplog.Info(fmt.Sprintf("更新老带新订单失败：%v", err.Error()))
		} else {
			zaplog.Info("更新老带新订单成功")
		}
	}()

	return w.db.Transaction(func(tx *gorm.DB) error {
		var orderToUpdate model.OwnOrder
		err = tx.WithContext(c).
			Where("model = ?", req.Model).
			Where("endpoint_id = ?", req.EndpointID).
			Where("mobile = ?", req.Phone).
			Where("warranty_id = ?", 0).
			Order("created_at DESC").
			First(&orderToUpdate).Error
		if err != nil {
			if stdError.Is(err, gorm.ErrRecordNotFound) {
				return errors.NewErr("未找到匹配的待绑定订单")
			}
			return err
		}
		var validWarranty model.Warranty
		err = tx.WithContext(c).
			Where("barcode = ?", req.Barcode).
			Where("status = ?", consts.WarrantyStatusActive).
			First(&validWarranty).Error
		if err != nil {
			if stdError.Is(err, gorm.ErrRecordNotFound) {
				return errors.NewErr("无效或不存在的保卡条码")
			}
			return err
		}
		err = tx.WithContext(c).
			Model(&model.OwnOrder{}).
			Where("id = ?", orderToUpdate.ID).
			Update("warranty_id", validWarranty.ID).Error
		if err != nil {
			return err
		}

		return nil
	})
}

// GetActivatedDevices 查询个人激活记录
func (w *warranty) GetActivatedDevices(c *gin.Context, barcode string) ([]*model.AcDevicesUniq, error) {
	var devices []*model.AcDevicesUniq
	err := db.GetDB("rbcare_data").WithContext(c).Table("ac_devices_uniq as a").
		Select("a.barcode, a.number, a.imei, a.model, a.nav_model, a.origin, a.bindat,"+
			"a.status, a.address, a.location, a.ip, a.createat").
		Where("a.barcode = ?", barcode).
		Scan(&devices).Error
	if len(devices) == 0 {
		return nil, errors.NewErr("无条码个人激活记录")
	}
	return devices, err
}

func (w *warranty) GetMachinesHistoriesList(c *gin.Context, params *api.MachinesHistoriesReq) ([]*model.DevicesHistory, int64, error) {
	var history []*model.DevicesHistory
	query := w.db.WithContext(c).Table("devices_history as dh").
		Select("dh.id, dh.model_id, dh.model, dh.barcode, dh.number, dh.number_new, dh.from, dh.remark, dh.status, dh.add_time, dh.update_time")

	if params.Barcode != "" {
		query = query.Where("dh.barcode = ?", params.Barcode)
	}
	if params.Model != "" {
		query = query.Where("dh.model = ?", params.Model)
	}
	if status := params.Status; status != nil {
		query = query.Where("dh.status = ?", *status)
	}
	if from := params.From; from != nil {
		query = query.Where("dh.from = ?", *from)
	}
	if params.Number != "" {
		query = query.Where("dh.number = ?", params.Number)
	}
	if params.NumberNew != "" {
		query = query.Where("dh.number_new = ?", params.NumberNew)
	}
	query = query.Order("dh.id desc")

	var total int64
	query.Count(&total)
	page, pageSize := params.Page, params.PageSize
	query.Scopes(utils.Paginate(page, pageSize)).Scan(&history)

	return history, total, query.Error
}

func (w *warranty) GetDevicesHistoryByNumberNew(c *gin.Context, numberNew string) (*model.DevicesHistory, error) {
	var devicesHistory *model.DevicesHistory
	err := w.db.WithContext(c).Model(&model.DevicesHistory{}).
		Select("id, model_id, model, barcode, number, number_new").
		Where("number_new = ?", numberNew).
		First(&devicesHistory).Error
	if stdError.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}
	return devicesHistory, err
}

func (w *warranty) UpsertDevicesHistory(c *gin.Context, history *model.DevicesHistory, params *api.MachineHistoryUpdReq) error {
	numberNew := params.NumberNew
	barcode := params.Barcode
	remark := params.Remark
	now := time.Now()
	if history.ID == 0 {
		return w.CreateDevicesHistory(c, &model.DevicesHistory{
			Barcode:    history.Barcode,
			Number:     history.Number,
			NumberNew:  numberNew,
			ModelID:    history.ModelID,
			Model:      history.Model,
			AddTime:    &now,
			UpdateTime: &now,
		},
		)
	} else {
		query := gorm.Expr("barcode = ?", barcode)
		return w.UpdateDevicesHistory(c, query, map[string]interface{}{
			"number_new":  numberNew,
			"remark":      remark,
			"update_time": &now,
		})
	}
}

func (w *warranty) CreateDevicesHistory(c *gin.Context, create *model.DevicesHistory) error {
	res := w.db.WithContext(c).Model(&model.DevicesHistory{}).Create(create)
	return res.Error
}

func (w *warranty) UpdateDevicesHistory(c *gin.Context, query clause.Expr, upd map[string]interface{}) error {
	res := w.db.WithContext(c).Model(&model.DevicesHistory{}).Where(query).Updates(upd)
	if res.RowsAffected == 0 {
		return errors.NewErr("更新记录失败")
	}
	return res.Error
}

func (w *warranty) GetDevicesLimitAccountList(c *gin.Context, req *api.DevicesLimitAccountReq) ([]*model.DeviceLimitAccount, int64, error) {
	query := w.db.WithContext(c).Model(&model.DeviceLimitAccount{}).
		Select("*")
	if req.Barcode != "" {
		query = query.Where("barcode = ?", req.Barcode)
	}
	if req.Number != "" {
		query = query.Where("number = ?", req.Number)
	}
	if req.Phone != "" {
		query = query.Where("phone = ?", req.Phone)
	}
	if req.Model != "" {
		query = query.Where("model = ?", req.Model)
	}
	if status := req.Status; status != nil {
		query = query.Where("status = ?", *status)
	}
	query = query.Order("id desc")

	var list []*model.DeviceLimitAccount
	var total int64
	query.Count(&total)
	page, pageSize := req.Page, req.PageSize
	query.Scopes(utils.Paginate(page, pageSize)).Scan(&list)
	return list, total, query.Error
}

func (w *warranty) EditDevicesLimitAccount(c *gin.Context, query clause.Expr, upd map[string]interface{}) error {
	res := w.db.WithContext(c).Model(&model.DeviceLimitAccount{}).Where(query).Updates(upd)
	if res.RowsAffected == 0 {
		return errors.NewErr("无相应更新记录")
	}
	return res.Error
}

func (w *warranty) GetDevicesLimitNumber(c *gin.Context, array []string) (map[string]string, error) {
	barMap := make(map[string]string)
	var list []*model.DeviceLimitAccount
	err := w.db.WithContext(c).Model(&model.MesDevices{}).
		Select("barcode, number").
		Where("barcode IN (?)", array).
		Scan(&list).Error
	if err != nil {
		return nil, err
	}
	if len(list) == 0 {
		return nil, nil
	}
	for _, devices := range list {
		barMap[devices.Barcode] = devices.Number
	}
	return barMap, nil
}

func (w *warranty) BatchInsertDevicesLimitAccount(c *gin.Context, data []*model.DeviceLimitAccount) error {
	return w.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		return w.db.Model(&model.DeviceLimitAccount{}).Create(&data).Error
	})
}

func (w *warranty) GetDevicesLimitAccountByCond(c *gin.Context, cond clause.Expr) (*model.DeviceLimitAccount, error) {
	var account *model.DeviceLimitAccount
	err := w.db.WithContext(c).Model(&model.DeviceLimitAccount{}).Where(cond).First(&account).Error
	if err != nil && stdError.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}
	return account, err
}

func (w *warranty) GetPackRecord(c *gin.Context, barcode string) ([]*model.MesPackDevices, error) {
	var packRecords []*model.MesPackDevices
	err := db.GetDB("rbcare_data").WithContext(c).Model(&model.MesPackDevices{}).
		Select("model_id, model, barcode, number, imei, pack_time, add_time").
		Where("barcode = ?", barcode).
		Scan(&packRecords).Error
	return packRecords, err
}

func (w *warranty) GetK3RecordInStock(c *gin.Context, barcode string) ([]*model.V3Instock, error) {
	var k3RecordsInStock []*model.V3Instock
	err := db.GetDB("rbcare_data").WithContext(c).Model(&model.V3Instock{}).
		Select("model_id, model, barcode, number, imei, bill_date").
		Where("barcode = ?", barcode).
		Scan(&k3RecordsInStock).Error
	return k3RecordsInStock, err
}

func (w *warranty) GetK3RecordOutStock(c *gin.Context, barcode string) ([]*model.V3Outstock, error) {
	var k3RecordsOutStock []*model.V3Outstock
	err := db.GetDB("rbcare_data").WithContext(c).Model(&model.V3Outstock{}).
		Select("model_id, model, barcode, number, imei, cust_code, cust_name, bill_date").
		Where("barcode = ? AND status = ?", barcode, consts.K3OutStockStatusNormal).
		Scan(&k3RecordsOutStock).Error
	return k3RecordsOutStock, err
}

func (w *warranty) GetK3RecordReturnStock(c *gin.Context, barcode string) ([]*model.V3Returnstock, error) {
	var k3RecordsReturnStock []*model.V3Returnstock
	err := db.GetDB("rbcare_data").WithContext(c).Model(&model.V3Returnstock{}).
		Select("model_id, model, barcode, number, imei, cust_code, cust_name, bill_date").
		Where("barcode = ?", barcode).
		Scan(&k3RecordsReturnStock).Error
	return k3RecordsReturnStock, err
}

func (w *warranty) GetK3RecordAllotStock(c *gin.Context, barcode string) ([]*model.V3Allotstock, error) {
	var k3RecordsAllotStock []*model.V3Allotstock
	err := db.GetDB("rbcare_data").WithContext(c).Model(&model.V3Allotstock{}).
		Select(" model_id, model, barcode, number, imei, cust_code_old, cust_name_old, cust_code, cust_name, bill_date").
		Where("barcode = ?", barcode).
		Scan(&k3RecordsAllotStock).Error
	return k3RecordsAllotStock, err
}

// findDrpMachine 查询 drp_machines 表
func findDrpMachine(tx *gorm.DB, barcode string) (*model.DrpMachine, error) {
	var machine model.DrpMachine
	err := tx.Where("barcode = ? AND status NOT IN (?, ?)", barcode, 0, 2).First(&machine).Error
	if err != nil {
		if stdError.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &machine, nil
}

// findRecordID 查询 drp_records_detail 表，查找盘点异常记录ID
func findRecordID(tx *gorm.DB, barcode string) (int, error) {
	if barcode == "" {
		return 0, nil
	}

	type MachineRecord struct {
		ID      int
		Barcode string
	}
	var recordDetail MachineRecord
	err := tx.Table("drp_records_detail as drd").
		Select("dr.id, drd.barcode").
		Joins("RIGHT JOIN drp_machines as dm ON drd.barcode = dm.barcode").
		Joins("RIGHT JOIN drp_records as dr ON dr.id = drd.operate_id").
		Where("dm.barcode = ? AND drd.type != ? AND drd.new_status = ? AND dr.record_type = ?", barcode, 0, -1, 3).
		Scan(&recordDetail).Error

	if err != nil && !stdError.Is(err, gorm.ErrRecordNotFound) {
		return 0, err
	}

	if recordDetail.ID != 0 && recordDetail.Barcode != "" {
		return recordDetail.ID, nil
	}

	return 0, nil
}

// updateMachineStatus 更新 drp_machines 表的状态
func updateMachineStatus(tx *gorm.DB, barcode string, status int, subStatus int, now time.Time) error {
	updates := map[string]interface{}{
		"status":     status,
		"sub_status": subStatus,
		"updated_at": now,
	}
	err := tx.Model(&model.DrpMachine{}).Where("barcode = ?", barcode).Updates(updates).Error
	return err
}

// handleMachineStatus 处理机器状态变更
func handleMachineStatus(tx *gorm.DB, barcode string, machine *model.DrpMachine, newBarcode string, newMachine *model.DrpMachine, oType int, ret int, warranty *model.Warranty, now time.Time) (int, error) {
	drpStatus := 0
	var err error

	switch oType {
	case 1: // 录保卡
		if machine != nil && (machine.Status == 1 || machine.Status == 4 || machine.Status == 5 || machine.Status == 6) {
			err = updateMachineStatus(tx, barcode, 3, 6, now)
			drpStatus = 3
		}
	case 2, 3: // 换机（换机样机）, 退机（退机样机）
		if machine != nil && machine.Status == 3 {
			newStatus := 1
			if ret != 3 {
				newStatus = 5 // or 6 depending on oType
				if oType == 3 {
					newStatus = 6
				}
			}
			err = updateMachineStatus(tx, barcode, newStatus, 5, now)
			drpStatus = newStatus
		}

		if newMachine != nil && (newMachine.Status == 1 || newMachine.Status == 4 || newMachine.Status == 5 || newMachine.Status == 6) {
			err = updateMachineStatus(tx, newBarcode, 3, 6, now)
		}

		if warranty != nil {
			err = handleWarehouse(tx, barcode, warranty, ret, now)
		}

	case 4: // 4--入样机
		if machine != nil && machine.Status == 1 {
			err = updateMachineStatus(tx, barcode, 4, 5, now)
			drpStatus = 4
		}
	default: // 5--样机正常离库
		if machine != nil && (machine.Status == 4 || machine.Status == 5 || machine.Status == 6) {
			err = updateMachineStatus(tx, barcode, 1, 1, now)
			drpStatus = 1
		}
	}

	return drpStatus, err
}

// handleWarehouse 处理仓库逻辑
func handleWarehouse(tx *gorm.DB, barcode string, warranty *model.Warranty, ret int, now time.Time) error {
	var warehouse model.DrpWarehouse
	if err := tx.Where("endpoint = ?", warranty.Endpoint).First(&warehouse).Error; err != nil {
		if stdError.Is(err, gorm.ErrRecordNotFound) {
			return nil
		}
		return err
	}

	var drpInfo model.DrpMachine
	err := tx.Table("drp_machines").
		Select("drp_machines.status, drp_machines.warehouse_id, drp_machines.id").
		Joins("left join drp_warehouse on drp_machines.warehouse_id = drp_warehouse.id").
		Where("drp_machines.barcode= ? and drp_warehouse.type = ?", barcode, "endpoint").
		Scan(&drpInfo).Error
	if err != nil {
		return err
	}

	if drpInfo.Status == 0 || drpInfo.Status == 2 {
		newStatus := 1
		if ret != 3 {
			newStatus = 5
		}
		updates := map[string]interface{}{
			"warehouse_id": warehouse.ID,
			"status":       newStatus,
			"sub_status":   5,
			"created_at":   now,
			"updated_at":   nil,
		}
		err = tx.Model(&model.DrpMachine{}).Where("barcode = ? AND (status = ? OR status = ?) AND id = ?", barcode, 2, 0, drpInfo.ID).Updates(updates).Error
	}
	return err
}

// updateRecordDetail 更新 drp_records_detail 表
func updateRecordDetail(tx *gorm.DB, recordID int, barcode string, drpStatus int) error {
	err := tx.Model(&model.DrpRecordsDetail{}).Where("operate_id = ? AND barcode = ?", recordID, barcode).Update("new_status", drpStatus).Error
	if err != nil {
		return err
	}

	var count int64
	err = tx.Model(&model.DrpRecordsDetail{}).Where("operate_id = ? AND `type` != ? AND new_status = ?", recordID, 0, -1).Count(&count).Error
	if err != nil {
		return err
	}

	if count == 0 {
		err = tx.Model(&model.DrpRecords{}).Where("id = ?", recordID).Update("status", 1).Error
		if err != nil {
			return err
		}
	}
	return nil
}
