package model

import (
	"gorm.io/gorm"
	"time"
)

// AdminRoleMenuV2 定义与 admin_role_menu_v2 表对应的模型
type AdminRoleMenuV2 struct {
	ID        uint `gorm:"primarykey"`
	RoleID    uint `gorm:"not null" json:"role_id"` // 角色 ID
	MenuID    uint `gorm:"not null" json:"menu_id"` // 菜单 ID
	CreatedAt time.Time
	DeletedAt gorm.DeletedAt `gorm:"index"`
}

// TableName 设置表的别名
func (AdminRoleMenuV2) TableName() string {
	return "admin_role_menus_v2"
}
