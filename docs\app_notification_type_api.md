# 推送消息类型管理 API 文档

## 概述
推送消息类型管理模块提供了对应用推送消息类型的完整管理功能，包括创建、查询、更新和删除操作。

## API 接口列表

### 1. 获取推送消息类型列表

**请求URL:** `/admin/notice/app-notification-type`

**请求方法:** `GET`

**参数:**
| 参数名 | 必选 | 类型 | 说明 |
|--------|------|------|------|
| 无 | - | - | - |

**返回示例:**
```json
{
  "ok": 1,
  "msg": "success",
  "data": [
    {
      "id": 1,
      "name": "系统通知",
      "icon": "/uploads/icons/system.png",
      "slug": "system_notice",
      "msg_type": "文本消息",
      "action": "跳转",
      "popup": "是",
      "banner": "否",
      "created_at": "2024-01-01T10:00:00Z",
      "updated_at": "2024-01-01T10:00:00Z"
    }
  ]
}
```

**返回参数说明:**
| 参数名 | 类型 | 说明 |
|--------|------|------|
| id | int | 消息类型ID |
| name | string | 消息类型名称 |
| icon | string | 图标路径 |
| slug | string | 唯一标识 |
| msg_type | string | 消息类型（文本消息/图文消息） |
| action | string | 消息动作（无/跳转/检查app更新） |
| popup | string | 是否弹窗（是/否） |
| banner | string | 是否显示横幅（是/否） |

### 2. 获取指定推送消息类型

**请求URL:** `/admin/notice/app-notification-type/{id}`

**请求方法:** `GET`

**参数:**
| 参数名 | 必选 | 类型 | 说明 |
|--------|------|------|------|
| id | 是 | int | 消息类型ID |

**返回示例:**
```json
{
  "ok": 1,
  "msg": "success",
  "data": {
    "id": 1,
    "name": "系统通知",
    "icon": "/uploads/icons/system.png",
    "slug": "system_notice",
    "msg_type": "text",
    "media_url": "",
    "action": "forward",
    "url": "/system/notice",
    "action_text": "查看详情",
    "popup": 1,
    "banner": 0,
    "manual": 1,
    "created_at": "2024-01-01T10:00:00Z",
    "updated_at": "2024-01-01T10:00:00Z"
  }
}
```

### 3. 根据slug获取推送消息类型详情

**请求URL:** `/admin/notice/app-notification-type/detail/{slug}`

**请求方法:** `GET`

**参数:**
| 参数名 | 必选 | 类型 | 说明 |
|--------|------|------|------|
| slug | 是 | string | 消息类型唯一标识 |

**返回示例:**
```json
{
  "ok": 1,
  "msg": "success",
  "data": {
    "action": "forward",
    "url": "/system/notice",
    "popup": 1,
    "action_text": "查看详情",
    "banner": 0
  }
}
```

### 4. 创建推送消息类型

**请求URL:** `/admin/notice/app-notification-type`

**请求方法:** `POST`

**参数:**
| 参数名 | 必选 | 类型 | 说明 |
|--------|------|------|------|
| name | 是 | string | 消息类型名称 |
| icon | 是 | string | 图标路径 |
| slug | 是 | string | 唯一标识（创建后不可修改） |
| msg_type | 是 | string | 消息类型（text/image） |
| media_url | 否 | string | 媒体资源地址（图文消息必填） |
| action | 是 | string | 消息动作（none/forward/check_app_upgrade） |
| url | 否 | string | 跳转地址（动作为forward时必填） |
| action_text | 否 | string | 动作提示文字（有动作时必填） |
| popup | 否 | int | 是否弹窗（0/1） |
| banner | 否 | int | 是否显示横幅（0/1） |
| manual | 否 | int | 是否可手动推送（0/1） |

**请求示例:**
```json
{
  "name": "系统通知",
  "icon": "/uploads/icons/system.png",
  "slug": "system_notice",
  "msg_type": "text",
  "action": "forward",
  "url": "/system/notice",
  "action_text": "查看详情",
  "popup": 1,
  "banner": 0,
  "manual": 1
}
```

**返回示例:**
```json
{
  "ok": 1,
  "msg": "success",
  "data": {
    "id": 1
  }
}
```

### 5. 更新推送消息类型

**请求URL:** `/admin/notice/app-notification-type/{id}`

**请求方法:** `PUT`

**参数:**
| 参数名 | 必选 | 类型 | 说明 |
|--------|------|------|------|
| id | 是 | int | 消息类型ID（路径参数） |
| name | 是 | string | 消息类型名称 |
| icon | 是 | string | 图标路径 |
| msg_type | 是 | string | 消息类型（text/image） |
| media_url | 否 | string | 媒体资源地址（图文消息必填） |
| action | 是 | string | 消息动作（none/forward/check_app_upgrade） |
| url | 否 | string | 跳转地址（动作为forward时必填） |
| action_text | 否 | string | 动作提示文字（有动作时必填） |
| popup | 否 | int | 是否弹窗（0/1） |
| banner | 否 | int | 是否显示横幅（0/1） |
| manual | 否 | int | 是否可手动推送（0/1） |

**注意:** 更新时不能修改slug字段

### 6. 删除推送消息类型

**请求URL:** `/admin/notice/app-notification-type/{id}`

**请求方法:** `DELETE`

**参数:**
| 参数名 | 必选 | 类型 | 说明 |
|--------|------|------|------|
| id | 是 | int | 消息类型ID |

**返回示例:**
```json
{
  "ok": 1,
  "msg": "success",
  "data": null
}
```

## 注意事项

1. **slug唯一性**: slug字段必须唯一，创建后不可修改
2. **图文消息**: 当msg_type为"image"时，media_url字段必填
3. **跳转动作**: 当action为"forward"时，url字段必填
4. **动作提示**: 当action不为"none"时，action_text字段必填
5. **权限要求**: 所有接口都需要管理员权限
6. **数据验证**: 所有枚举字段都会进行严格验证

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |
