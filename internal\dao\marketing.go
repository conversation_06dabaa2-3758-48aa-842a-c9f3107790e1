package dao

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"marketing/internal/api/materials"
	"marketing/internal/model"
	"marketing/internal/pkg/utils"
	"strconv"
)

type MarketingDao interface {
	Update(c *gin.Context, param model.MarketingCategory) error
	Create(c *gin.Context, param model.MarketingCategory) (uint, error)
	Save(c *gin.Context, domain []model.MarketingCategory) error
	List(c *gin.Context) ([]materials.MarketingCategoryRes, error)
	Detail(c *gin.Context, id string) (model.MarketingCategory, error)
	Tips(c *gin.Context) ([]materials.Tips, error)
	InfoList(c *gin.Context, req materials.InfoListReq) ([]materials.MarketingDownload, int64, error)
	InfoDelete(c *gin.Context, id string) error
	InfoUpdate(c *gin.Context, id int, status int) error
	Delete(c *gin.Context, id string) error
	HadInfo(c *gin.Context, id string) bool
	Upload(c *gin.Context, id string) error
	AllTips(c *gin.Context) ([]map[string]any, error)
}

type GormMarketingDao struct {
	db *gorm.DB
}

func (g *GormMarketingDao) AllTips(c *gin.Context) ([]map[string]any, error) {
	var tips []map[string]any
	err := g.db.WithContext(c).Model(&model.MarketingCategory{}).Select("id, title, parent_id").Find(&tips).Error
	return tips, err
}

func (g *GormMarketingDao) Upload(c *gin.Context, id string) error {
	//不触发钩子更新时间
	return g.db.Model(&model.MarketingDownload{}).UpdateColumn("download_count", gorm.Expr("download_count + ?", 1)).Error
}

func (g *GormMarketingDao) HadInfo(c *gin.Context, id string) bool {
	return g.db.WithContext(c).Where("category = ?", id).First(&model.MarketingDownload{}).Error == nil
}

func (g *GormMarketingDao) Delete(c *gin.Context, id string) error {
	return g.db.WithContext(c).Where("id = ?", id).Delete(&model.MarketingCategory{}).Error
}

func (g *GormMarketingDao) InfoUpdate(c *gin.Context, id int, status int) error {
	return g.db.WithContext(c).Model(&model.MarketingDownload{}).Where("id = ?", id).Update("status", status).Error
}

func (g *GormMarketingDao) InfoDelete(c *gin.Context, id string) error {
	return g.db.WithContext(c).Where("id = ?", id).Delete(&model.MarketingDownload{}).Error
}

func (g *GormMarketingDao) InfoList(c *gin.Context, req materials.InfoListReq) ([]materials.MarketingDownload, int64, error) {
	curDB := g.db.WithContext(c).Table("marketing_download AS d").
		Joins("LEFT JOIN marketing_download_evaluation AS e ON d.id = e.marketing_download_id").
		Joins("LEFT JOIN marketing_category AS c ON d.category = c.id").
		Select("d.*, IFNULL(AVG(e.score), -1) AS score, COUNT(e.id) AS count,c.title AS kind").
		Group("d.id")
	//条件筛选
	if req.ID != 0 {
		curDB = curDB.Where("d.id = ?", req.ID)
	}
	if req.Status != "" {
		curDB = curDB.Where("d.status = ?", req.Status)
	}
	if req.Kind != 0 {
		curDB = curDB.Where("d.category = ?", req.Kind)
	}
	if req.Label != "" {
		curDB = curDB.Where("d.name like ?", "%"+req.Label+"%").
			Or("d.description like ?", "%"+req.Label+"%")
	}
	//数据查询
	var total int64
	var data []struct {
		model.MarketingDownload
		Score float64 `gorm:"column:score"`
		Count int64   `gorm:"column:count"`
		Kind  string  `gorm:"kind"`
	}
	curDB.Count(&total)
	err := curDB.Order("d.id DESC").Offset((req.Page - 1) * req.PageSize).Limit(req.PageSize).Find(&data).Error
	if err != nil {
		return nil, 0, err
	}
	//数据转换
	res := make([]materials.MarketingDownload, 0, len(data))
	for _, v := range data {
		preview, _ := utils.UrlImageTrans(v.Preview)
		if v.Path != "" {
			if v.CreatedAt.Year() < 2025 {
				v.Path = "https://dt1.readboy.com/" + v.Path
			} else {
				v.Path = "https://static.readboy.com/" + v.Path
			}
		}
		res = append(res, materials.MarketingDownload{
			ID:            v.ID,
			Name:          v.Name,
			Description:   v.Description,
			Preview:       preview,
			Path:          v.Path,
			Category:      v.Kind,
			Status:        v.Status,
			DownloadCount: v.DownloadCount,
			CreatedAt:     v.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt:     v.UpdatedAt.Format("2006-01-02 15:04:05 "),
			Score:         v.Score,
		})
	}
	return res, total, nil
}

func (g *GormMarketingDao) Tips(c *gin.Context) ([]materials.Tips, error) {
	var tips []materials.Tips
	err := g.db.WithContext(c).Table("marketing_category").Where("parent_id=0").Find(&tips).Error
	return tips, err
}

func (g *GormMarketingDao) Detail(c *gin.Context, id string) (model.MarketingCategory, error) {
	var domain model.MarketingCategory
	err := g.db.WithContext(c).Where("id = ?", id).First(&domain).Error
	return domain, err
}

func (g *GormMarketingDao) List(c *gin.Context) ([]materials.MarketingCategoryRes, error) {
	var domain []materials.MarketingCategoryRes
	err := g.db.WithContext(c).Table("marketing_category").
		Joins("LEFT JOIN marketing_download ON marketing_download.category = marketing_category.id").
		Select("marketing_category.*,COUNT(marketing_download.id) AS count").
		Group("marketing_category.id").
		Order("CASE WHEN `order` = 0 THEN 1 ELSE 0 END,`order` ASC").
		Find(&domain).Error
	return domain, err
}

// Save 批量保存
func (g *GormMarketingDao) Save(c *gin.Context, domain []model.MarketingCategory) error {
	for _, item := range domain {
		err := g.db.WithContext(c).Model(&model.MarketingCategory{}).Where("id = ?", item.ID).Updates(item).Error
		if err != nil {
			return err
		}
	}
	return nil
}

// Update 更新
func (g *GormMarketingDao) Update(c *gin.Context, param model.MarketingCategory) error {
	err := g.db.WithContext(c).Model(&model.MarketingCategory{}).Where("id = ?", param.ID).Updates(map[string]any{
		"title":     param.Title,
		"parent_id": param.ParentID,
		"order":     param.Order,
		"route":     param.Route,
	}).Error
	return err
}

// Create 创建
func (g *GormMarketingDao) Create(c *gin.Context, param model.MarketingCategory) (uint, error) {
	err := g.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		err := g.db.WithContext(c).Create(&param).Error
		if err != nil {
			return err
		}
		if param.ParentID != 0 {
			param.Route = strconv.Itoa(int(param.ParentID)) + "," + strconv.Itoa(int(param.ID))
		} else {
			param.Route = strconv.Itoa(int(param.ID))
		}
		err = g.db.WithContext(c).Model(&model.MarketingCategory{}).Where("id = ?", param.ID).Update("route", param.Route).Error
		return err
	})
	return param.ID, err
}

func NewGormMarketingDao(db *gorm.DB) MarketingDao {
	return &GormMarketingDao{db: db}
}
