package endpoint_application

import (
	"marketing/internal/api"
	"marketing/internal/pkg/types"
)

// CreateEndpointPolicyReq 创建终端政策请求
type CreateEndpointPolicyReq struct {
	Name            string `json:"name" form:"name" binding:"required"`
	Description     string `json:"description" form:"description"`
	File            string `json:"file" form:"file"`
	StartDate       string `json:"start_date" form:"start_date" binding:"required"`
	EndDate         string `json:"end_date" form:"end_date" binding:"required"`
	MaterialSupport int    `json:"material_support" form:"material_support"`
	AmountSupport   int    `json:"amount_support" form:"amount_support"`
	Installments    int    `json:"installments" form:"installments"`
	EndpointType    int    `json:"endpoint_type" form:"endpoint_type"`
	Maximum         int    `json:"maximum" form:"maximum"`
	WriteOffTable   string `json:"write_off_table" form:"write_off_table"`
	Template        string `json:"template" form:"template"`
	Enabled         int    `json:"enabled" form:"enabled"`
	States          []int  `json:"states" form:"states"`
}

// UpdateEndpointPolicyReq 更新终端政策请求
type UpdateEndpointPolicyReq struct {
	ID              int    `json:"id" form:"id"`
	Name            string `json:"name" form:"name"`
	Description     string `json:"description" form:"description"`
	File            string `json:"file" form:"file"`
	StartDate       string `json:"start_date" form:"start_date"`
	EndDate         string `json:"end_date" form:"end_date"`
	MaterialSupport int    `json:"material_support" form:"material_support"`
	AmountSupport   int    `json:"amount_support" form:"amount_support"`
	Installments    int    `json:"installments" form:"installments"`
	EndpointType    int    `json:"endpoint_type" form:"endpoint_type"`
	Maximum         int    `json:"maximum" form:"maximum"`
	WriteOffTable   string `json:"write_off_table" form:"write_off_table"`
	Template        string `json:"template" form:"template"`
	Enabled         int    `json:"enabled" form:"enabled"`
	States          []int  `json:"states" form:"states"`
}

// EndpointPolicyListReq 终端政策列表请求
type EndpointPolicyListReq struct {
	api.PaginationParams
	Name    string `json:"name" form:"name"` // 政策名称搜索
	Enabled *int   `json:"enabled" form:"enabled"`
}

// EndpointPolicyResp 终端政策响应
type EndpointPolicyResp struct {
	ID              int              `json:"id"`
	Name            string           `json:"name"`
	Description     string           `json:"description"`
	File            string           `json:"file"`
	StartDate       string           `json:"start_date"`
	EndDate         string           `json:"end_date"`
	CreatedAt       string           `json:"created_at"`
	UpdatedAt       types.CustomTime `json:"updated_at"`
	MaterialSupport int              `json:"material_support" form:"material_support"`
	AmountSupport   int              `json:"amount_support" form:"amount_support"`
	Installments    int              `json:"installments" form:"installments"`
	EndpointType    int              `json:"endpoint_type" form:"endpoint_type"`
	Maximum         int              `json:"maximum" form:"maximum"`
	Template        string           `json:"template" form:"template"`
	Enabled         int              `json:"enabled" form:"enabled"`
}
