package dao

import (
	"encoding/json"
	"fmt"
	"marketing/internal/api/operation"
	"marketing/internal/model"
	"regexp"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

type OpArticleDao interface {
	// 公共
	GetUserRoles(c *gin.Context, uid uint) ([]string, error)
	GetUserPublisherIds(c *gin.Context, uid uint, roles []string) ([]uint, error)

	// 内容
	List(c *gin.Context, req operation.ArticleListReq) ([]operation.ArticleListRspItem, int64, error)
	Tags(c *gin.Context, aids []uint) (map[uint][]operation.ArticleTagsItem, error)
	ShareCount(c *gin.Context, ids []uint) (map[uint]uint, error)
	LikeCount(c *gin.Context, ids []uint, target int) (map[uint]uint, error)
	ViewCount(c *gin.Context, ids []uint) (map[uint]uint, error)
	CommentCount(c *gin.Context, ids []uint) (map[uint]uint, error)
	Creators(c *gin.Context) ([]operation.ArticleCreatorsRspItem, error)
	Detail(c *gin.Context, id uint) (operation.ArticleDetailRsp, error)
	CreateTx(c *gin.Context, req operation.ArticleCreateReq) (uint, error)

	// 类别
	CategoryList(c *gin.Context) ([]operation.ArticleCategoryListRspItem, error)

	// 标签
	TagList(c *gin.Context) ([]operation.ArticleTagListRspItem, error)

	// 评论
	CommentSetting(c *gin.Context, aid uint) (int, error)
	CommentList(c *gin.Context, req operation.ArticleCommentListReq, commentSetting int) ([]operation.ArticleCommentListRspItem, error)
	CommentReplies(c *gin.Context, sourceIds []int, typ string, commentSetting int, creator string, offset, limit int) (map[uint]*operation.CommentReplies, error)
	UserCommentCount(c *gin.Context, userIds []uint) (map[uint]uint, error)
	CreatorInfos(c *gin.Context, userIds []uint, userCommentMap map[uint]uint) (map[uint]*operation.CommentCreator, error)
}

type GormOpArticleDao struct {
	db *gorm.DB
}

func NewGormOpArticleDao(db *gorm.DB) OpArticleDao {
	return &GormOpArticleDao{db: db}
}

// 内容

func (g GormOpArticleDao) List(c *gin.Context, req operation.ArticleListReq) ([]operation.ArticleListRspItem, int64, error) {
	var articles []operation.ArticleListRspItem
	query := g.db.WithContext(c).Table("op_article AS a").Where("a.deleted_at IS NULL")

	if req.CategoryID > 0 {
		descendantIds, err := g.getDescendantAndSelfIds(c, req.CategoryID)
		if err != nil {
			return nil, 0, err
		}
		query.Where("a.category_id IN (?)", descendantIds)
	}
	if req.Keyword != "" {
		query.Where("a.title LIKE ? OR a.content LIKE ? OR a.sleight LIKE ?", "%"+req.Keyword+"%", "%"+req.Keyword+"%", "%"+req.Keyword+"%")
	}
	if req.Enabled != nil {
		query.Where("a.enabled", req.Enabled)
	}
	if req.CreatorID > 0 {
		query.Where("a.created_by", req.CreatorID)
	}

	var total int64
	query.Session(&gorm.Session{}).Count(&total)
	if total == 0 {
		return []operation.ArticleListRspItem{}, 0, nil
	}

	// 统计数量不用排序
	if req.OrderBy != "" {
		query.Order(req.OrderBy + " " + req.Order)
	} else {
		query.Order("created_at Desc")
	}

	var data []struct {
		model.OpArticle
		Creator       string `gorm:"column:creator"`
		CreatorID     uint   `gorm:"column:creator_id"`
		CategoryName  string `gorm:"column:category_name"`
		PublisherName string `gorm:"column:publisher_name"`
	}
	err := query.Joins("LEFT JOIN admin_users AS u ON a.created_by = u.id").
		Joins("LEFT JOIN op_publisher AS p ON a.publisher_id = p.id").
		Joins("LEFT JOIN op_article_category AS c ON a.category_id = c.id").
		Select("a.id, a.title, a.content, a.comment_setting, a.category_id, a.sleight, a.shareable, a.enabled, " +
			"a.attachment_type, a.attachment, a.num_downloads, a.created_at, a.updated_at," +
			" u.name creator, u.id creator_id, c.name category_name, p.name publisher_name").
		Offset((req.Page - 1) * req.PageSize).Limit(req.PageSize).
		Scan(&data).Error
	if err != nil {
		return nil, 0, err
	}

	articles = make([]operation.ArticleListRspItem, len(data))
	for i, item := range data {
		var attachment operation.ArticleAttachment
		json.Unmarshal([]byte(item.Attachment), &attachment)
		var httpPrefixReg = regexp.MustCompile(`(?i)^https?://`)
		for i := range attachment.Files {
			if !httpPrefixReg.MatchString(attachment.Files[i]) {
				attachment.Files[i] = operation.OssBaseUrl + attachment.Files[i]
			}
		}
		for i := range attachment.Covers {
			if !httpPrefixReg.MatchString(attachment.Covers[i]) {
				attachment.Covers[i] = operation.OssBaseUrl + attachment.Covers[i]
			}
		}
		articles[i] = operation.ArticleListRspItem{
			ID:             item.ID,
			Title:          item.Title,
			Content:        item.Content,
			CommentSetting: item.CommentSetting,
			CategoryID:     item.CategoryID,
			CategoryName:   item.CategoryName,
			Sleight:        item.Sleight,
			Shareable:      item.Shareable,
			NumDownloads:   item.NumDownloads,
			Creator:        item.Creator,
			CreatorID:      item.CreatorID,
			PublisherName:  item.PublisherName,
			AttachmentType: item.AttachmentType,
			Attachment:     &attachment,
			Enabled:        item.Enabled,
			CreatedAt:      item.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt:      item.UpdatedAt.Format("2006-01-02 15:04:05"),
		}
	}
	return articles, total, nil
}

func (g GormOpArticleDao) Tags(c *gin.Context, aids []uint) (map[uint][]operation.ArticleTagsItem, error) {
	data := []struct {
		ID       uint   `gorm:"column:article_id"`
		TagIDs   string `gorm:"column:tag_ids"`
		TagNames string `gorm:"column:tag_names"`
	}{}
	err := g.db.WithContext(c).Table("op_article_tag_relation AS r").
		Joins("INNER JOIN op_article_tag AS t ON r.tag_id = t.id").
		Where("r.article_id IN (?)", aids).
		Select(fmt.Sprintf("r.article_id, GROUP_CONCAT(r.tag_id ORDER BY r.tag_id ASC) tag_ids, GROUP_CONCAT(t.name ORDER BY r.tag_id ASC SEPARATOR '%s') tag_names",
			operation.ArticleTagNamesSeparator)).
		Group("article_id").
		Scan(&data).Error
	if err != nil {
		return nil, err
	}

	articleTagMap := map[uint][]operation.ArticleTagsItem{}
	for _, v := range data {
		articleTagMap[v.ID] = []operation.ArticleTagsItem{}
		tids := cast.ToIntSlice(v.TagIDs)
		tnames := cast.ToStringSlice(v.TagNames)
		for i := range tids {
			articleTagMap[v.ID] = append(articleTagMap[v.ID], operation.ArticleTagsItem{
				ID:   uint(tids[i]),
				Name: tnames[i],
			})
		}
	}

	return articleTagMap, nil
}

func (g GormOpArticleDao) ShareCount(c *gin.Context, ids []uint) (map[uint]uint, error) {
	data := []struct {
		ID  uint `gorm:"column:object_id"`
		Num uint `gorm:"column:num"`
	}{}
	err := g.db.WithContext(c).Table("share_info AS s").
		Joins("INNER JOIN share_log AS l ON s.id = l.share_id").
		Where("s.object", operation.ArticleShareSlug).
		Where("s.object_id IN (?)", ids).
		Select("s.object_id, COUNT(*) num").
		Group("s.object_id").
		Scan(&data).Error
	if err != nil {
		return nil, err
	}

	shareMap := map[uint]uint{}
	for _, v := range data {
		shareMap[v.ID] = v.Num
	}

	return shareMap, nil
}

func (g GormOpArticleDao) LikeCount(c *gin.Context, ids []uint, target int) (map[uint]uint, error) {
	data := []struct {
		ID  uint `gorm:"column:target_id"`
		Num uint `gorm:"column:num"`
	}{}
	err := g.db.WithContext(c).Table("op_like").Where("target_id IN (?)", ids).
		Where("target", target).
		Select("target_id, COUNT(*) num").
		Group("target_id").
		Find(&data).Error
	if err != nil {
		return nil, err
	}

	likeMap := map[uint]uint{}
	for _, v := range data {
		likeMap[v.ID] = v.Num
	}

	return likeMap, nil
}

func (g GormOpArticleDao) ViewCount(c *gin.Context, ids []uint) (map[uint]uint, error) {
	data := []struct {
		ID  uint `gorm:"column:target_id"`
		Num uint `gorm:"column:num"`
	}{}
	err := g.db.Table("op_view").Where("target_id IN (?)", ids).
		Where("target", operation.ViewTargetArticle).
		Select("target_id, COUNT(*) num").
		Group("target_id").
		Find(&data).Error
	if err != nil {
		return nil, err
	}

	viewMap := map[uint]uint{}
	for _, v := range data {
		viewMap[v.ID] = v.Num
	}

	return viewMap, nil
}

func (g GormOpArticleDao) CommentCount(c *gin.Context, ids []uint) (map[uint]uint, error) {
	data := []struct {
		ID  uint `gorm:"column:article_id"`
		Num uint `gorm:"column:num"`
	}{}
	err := g.db.WithContext(c).Table("op_article_comment").
		Where("article_id IN (?)", ids).
		Select("article_id, COUNT(*) num").
		Group("article_id").
		Find(&data).Error
	if err != nil {
		return nil, err
	}

	commentMap := map[uint]uint{}
	for _, v := range data {
		commentMap[v.ID] = v.Num
	}

	return commentMap, nil
}

func (g GormOpArticleDao) Creators(c *gin.Context) ([]operation.ArticleCreatorsRspItem, error) {
	var data []operation.ArticleCreatorsRspItem
	err := g.db.WithContext(c).Table("op_article AS a").
		Joins("INNER JOIN admin_users AS u ON a.created_by = u.id").
		Select("u.id, u.name").
		Where("a.deleted_at IS NULL").
		Distinct().
		Order("u.name").
		Find(&data).Error
	if err != nil {
		return nil, err
	}

	return data, nil
}

func (g GormOpArticleDao) Detail(c *gin.Context, id uint) (operation.ArticleDetailRsp, error) {
	var data model.OpArticle
	if err := g.db.WithContext(c).Find(&data, id).Error; err != nil {
		return operation.ArticleDetailRsp{}, err
	}

	var rsp operation.ArticleDetailRsp
	var attachment *operation.ArticleAttachment
	json.Unmarshal([]byte(data.Attachment), &attachment)
	var httpPrefixReg = regexp.MustCompile(`(?i)^https?://`)
	for i := range attachment.Files {
		if !httpPrefixReg.MatchString(attachment.Files[i]) {
			attachment.Files[i] = operation.OssBaseUrl + attachment.Files[i]
		}
	}
	for i := range attachment.Covers {
		if !httpPrefixReg.MatchString(attachment.Covers[i]) {
			attachment.Covers[i] = operation.OssBaseUrl + attachment.Covers[i]
		}
	}
	weworkShareable := uint8(data.WeworkShareable)
	rsp.Article = operation.Article{
		ID:                  data.ID,
		Title:               data.Title,
		Content:             data.Content,
		Sleight:             data.Sleight,
		CategoryID:          data.CategoryID,
		PublisherID:         data.PublisherID,
		Shareable:           &data.Shareable,
		WeWorkShareable:     &weworkShareable,
		CommentSetting:      data.CommentSetting,
		Enabled:             &data.Enabled,
		MarketingDownloadID: data.MarketingDownloadID,
		AttachmentType:      data.AttachmentType,
		Attachment:          attachment,
		CreatedAt:           data.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:           data.UpdatedAt.Format("2006-01-02 15:04:05"),
	}

	tags, err := g.Tags(c, []uint{id})
	if err != nil {
		return operation.ArticleDetailRsp{}, err
	}
	rsp.Tags = tags[id]

	if rsp.MarketingDownloadID > 0 {
		var marketing model.MarketingDownload
		if err := g.db.WithContext(c).Table("marketing_download").Where("id = ?", rsp.MarketingDownloadID).First(&marketing).Error; err != nil {
			return operation.ArticleDetailRsp{}, err
		}
		rsp.MarketingCategoryID = marketing.Category
		rsp.MarketingEnabled = marketing.Status
	}
	return rsp, nil
}

func (g GormOpArticleDao) createMarketingDownload(c *gin.Context, tx *gorm.DB, req operation.ArticleCreateReq) (uint, error) {
	var marketing = model.MarketingDownload{
		Name:          req.Title,
		Description:   req.Content,
		Category:      req.MarketingCategoryId,
		Status:        req.MarketingEnabled,
		SyncOperation: 1,
		OpShareable:   *req.Shareable,
	}

	if req.Attachment != nil {
		if len(req.Attachment.Files) > 0 {
			marketing.Path = req.Attachment.Files[0]
		} else {
			marketing.Path = ""
		}
		if req.AttachmentType == operation.ArticleAttachmentTypeImage {
			preview, _ := json.Marshal(req.Attachment.Files)
			marketing.Preview = string(preview)
		} else {
			preview, _ := json.Marshal(req.Attachment.Covers)
			marketing.Preview = string(preview)
		}
	}

	if err := tx.WithContext(c).Create(&marketing).Error; err != nil {
		return 0, err
	}
	return marketing.ID, nil
}

// 创建事务以处理文章创建
func (g GormOpArticleDao) CreateTx(c *gin.Context, req operation.ArticleCreateReq) (uint, error) {
	var aid uint
	// 开始数据库事务
	err := g.db.Transaction(func(tx *gorm.DB) error {
		var marketingId uint
		var err error

		// 去除文件路径中OSS URL前缀
		for i := range req.Attachment.Files {
			req.Attachment.Files[i], _ = strings.CutPrefix(req.Attachment.Files[i], operation.OssBaseUrl)
		}
		for i := range req.Attachment.Covers {
			req.Attachment.Covers[i], _ = strings.CutPrefix(req.Attachment.Covers[i], operation.OssBaseUrl)
		}

		// 如果需要同步到营销资料，则创建记录
		if req.SyncMarketing == 1 {
			marketingId, err = g.createMarketingDownload(c, tx, req)
			if err != nil {
				return err
			}
		}

		// 检查类别及其祖先是否启用
		categoryEnabled, err := g.isAncestorsAndSelfEnabled(c, req.CategoryID)
		if err != nil {
			return err
		}

		// 将附件信息序列化为JSON格式
		attachment, err := json.Marshal(req.Attachment)
		if err != nil {
			return err
		}

		// 创建文章对象
		var article = model.OpArticle{
			Title:               req.Title,
			Content:             req.Content,
			Sleight:             req.Sleight,
			CategoryID:          req.CategoryID,
			PublisherID:         req.PublisherID,
			Shareable:           *req.Shareable,
			WeworkShareable:     uint(*req.WeWorkShareable),
			CommentSetting:      req.CommentSetting,
			Enabled:             *req.Enabled,
			CreatedBy:           req.CreatedBy,
			AttachmentType:      req.AttachmentType,
			MarketingDownloadID: marketingId,
			Attachment:          string(attachment),
		}

		// 如果类别启用，则设置文章的类别启用标志
		if categoryEnabled {
			article.CategoryEnabled = 1
		}

		// 将文章记录插入数据库
		if err := tx.WithContext(c).Create(&article).Error; err != nil {
			return err
		}

		// 获取新创建的文章ID
		aid = article.ID

		// 更新文章与标签的关系
		err = g.tagRelationUpdate(c, tx, aid, req.TagIds)
		if err != nil {
			return err
		}

		return nil
	})
	// 如果事务出错，返回错误
	if err != nil {
		return 0, err
	}

	// 返回新创建的文章ID
	return aid, nil
}

// 类别

func (g GormOpArticleDao) CategoryList(c *gin.Context) ([]operation.ArticleCategoryListRspItem, error) {
	var data []struct {
		model.OpArticleCategory
		Num uint `gorm:"column:num"`
	}
	err := g.db.WithContext(c).Table("op_article_category AS c").
		Joins("LEFT JOIN op_article AS a ON c.id = a.category_id AND a.deleted_at IS NULL").
		Select("c.id, c.parent_id, c.name, c.enabled, COUNT(a.id) AS num").
		Group("c.id").Order("`order` ASC, id ASC").
		Find(&data).Error
	if err != nil {
		return nil, err
	}

	var categories []operation.ArticleCategoryListRspItem
	for _, v := range data {
		categories = append(categories, operation.ArticleCategoryListRspItem{
			ID:          v.ID,
			ParentID:    v.ParentID,
			Name:        v.Name,
			Enabled:     uint8(v.Enabled),
			NumArticles: v.Num,
		})
	}

	return categories, err
}

func (g GormOpArticleDao) getDescendantAndSelfIds(c *gin.Context, id uint) ([]uint, error) {
	descendantIds := []uint{}
	err := g.db.WithContext(c).Table("op_article_category").Where("FIND_IN_SET(?, ancestor)", id).Pluck("id", &descendantIds).Error
	if err != nil {
		return nil, err
	}
	return append(descendantIds, id), nil
}

func (g GormOpArticleDao) isAncestorsAndSelfEnabled(c *gin.Context, id uint) (bool, error) {
	var category model.OpArticleCategory
	if err := g.db.WithContext(c).Table("op_article_category").Where("id = ?", id).Select("ancestor").First(&category).Error; err != nil {
		return false, err
	}
	ancestors := strings.Split(category.Ancestor, ",")
	ancestors = append(ancestors, strconv.Itoa(int(id)))
	var count int64
	if err := g.db.WithContext(c).Table("op_article_category").Where("id IN (?)", ancestors).Where("enabled", 0).Count(&count).Error; err != nil {
		return false, err
	}
	return count == 0, nil
}

// 标签

func (g GormOpArticleDao) TagList(c *gin.Context) ([]operation.ArticleTagListRspItem, error) {
	var data []struct {
		model.OpArticleTag
		Num uint `gorm:"column:num"`
	}
	err := g.db.WithContext(c).Table("op_article_tag AS t").
		Joins("LEFT JOIN op_article_tag_relation AS r ON t.id = r.tag_id").
		Select("t.id, t.name, COUNT(r.id) num, t.updated_at").
		Group("t.id").Order("t.id DESC").
		Find(&data).Error
	if err != nil {
		return nil, err
	}

	var tags []operation.ArticleTagListRspItem
	for _, v := range data {
		tags = append(tags, operation.ArticleTagListRspItem{
			ID:          v.ID,
			Name:        v.Name,
			NumArticles: v.Num,
			UpdatedAt:   v.UpdatedAt.Format("2006-01-02 15:04:05"),
		})
	}

	return tags, err
}

func (g *GormOpArticleDao) tagRelationUpdate(c *gin.Context, tx *gorm.DB, aid uint, tagIds []uint) error {
	err := tx.WithContext(c).Table("op_article_tag_relation").Where("article_id = ?", aid).Delete(&model.OpArticleTagRelation{}).Error
	if err != nil {
		return err
	}

	var tags []model.OpArticleTagRelation
	for _, tagId := range tagIds {
		tags = append(tags, model.OpArticleTagRelation{
			ArticleID: aid,
			TagID:     tagId,
		})
	}
	err = tx.WithContext(c).Table("op_article_tag_relation").CreateInBatches(tags, 100).Error

	return err
}

// 评论

func (g GormOpArticleDao) CommentSetting(c *gin.Context, aid uint) (int, error) {
	var article model.OpArticle
	if err := g.db.WithContext(c).Find(&article, aid).Error; err != nil {
		return 0, err
	}
	return int(article.CommentSetting), nil
}

// CommentList 方法用于获取文章的评论列表
func (g GormOpArticleDao) CommentList(c *gin.Context, req operation.ArticleCommentListReq, commentSetting int) ([]operation.ArticleCommentListRspItem, error) {
	// 初始化查询，选择评论表中相关字段
	query := g.db.Table("op_article_comment AS c").
		Where("c.article_id = ?", req.ID). // 根据文章ID过滤
		Where("c.source_id = 0")           // 只选择源评论

	// 构建基础字段
	fields := []string{"c.id", "c.content", "c.attachment", "c.created_at", "c.created_by", "c.top", "c.discarded"}

	// 根据评论设置选择不同的字段
	if commentSetting == operation.ArticleCommentSettingNeedAudit {
		fields = append(fields, "c.selected enabled") // 需要审核的评论
	} else if commentSetting == operation.ArticleCommentSettingNoNeedAudit {
		fields = append(fields, "c.visible enabled") // 不需要审核的评论
	}

	// 如果请求中包含创建者信息，则加入模糊查询
	if req.Creator != "" {
		like := fmt.Sprintf("%%%s%%", req.Creator)
		sql := `
			SELECT DISTINCT IF(c.source_id > 0, c.source_id, c.id) id
				FROM op_article_comment c 
				JOIN admin_users u ON c.created_by = u.id
				LEFT JOIN user_extend_details d ON d.user_id = u.id 
				WHERE (d.name LIKE '%s' OR u.name LIKE '%s')
		`
		sql = fmt.Sprintf(sql, like, like)
		query.Joins(fmt.Sprintf("INNER JOIN (%s) AS sub ON sub.id = c.id", sql))
	}

	// 根据请求中的排序字段进行排序
	switch req.OrderBy {
	case "num_likes":
		query.Joins("LEFT JOIN op_like AS l ON c.id = l.target_id AND l.target = 2").
			Group("c.id").
			Order("num_likes " + req.Order)
		fields = append(fields, "COUNT(IF(l.id IS NOT NULL, 1, NULL)) num_likes")
	case "num_comments":
		query.Joins("INNER JOIN op_user AS u ON c.created_by = u.user_id").
			Select("u.num_comments").
			Order("u.num_comments " + req.Order)
		fields = append(fields, "u.num_comments")
	case "created_at":
		query.Order("c.created_at " + req.Order)
		fields = append(fields, "c.created_at")
	default:
	}

	// 根据请求中的类型进行过滤
	switch req.Type {
	case "enabled":
		query.Where("c.discarded = 0")
		if commentSetting == operation.ArticleCommentSettingNeedAudit {
			fields = append(fields, "c.selected")
		} else if commentSetting == operation.ArticleCommentSettingNoNeedAudit {
			fields = append(fields, "c.visible")
		}
	case "top":
		query.Where("c.top = 1 AND c.discarded = 0")
	case "discarded":
		query.Joins("LEFT JOIN op_article_comment AS ch ON c.id = ch.source_id AND ch.discarded = 1").
			Group("c.id").
			Having("num_discadeds > 0 OR c.discarded = 1")
		fields = append(fields, "COUNT(IF(ch.id IS NOT NULL, 1, NULL)) num_discadeds")
	default:
	}

	// 定义数据结构以接收查询结果
	var data []struct {
		model.OpArticleComment
		Enabled     uint `gorm:"column:enabled"`
		NumLikes    uint `gorm:"column:num_likes"`
		NumComments uint `gorm:"column:num_comments"`
	}
	// 设置分页偏移量和限制
	offset := (req.Page - 1) * req.PageSize
	if err := query.Offset(offset).Limit(req.PageSize).Select(fields).Find(&data).Error; err != nil {
		return nil, err
	}

	// 将查询结果转换为响应格式
	list := []operation.ArticleCommentListRspItem{}
	for _, v := range data {
		attachment := []string{}
		_ = json.Unmarshal([]byte(v.Attachment), &attachment)
		var httpPrefixReg = regexp.MustCompile(`(?i)^https?://`)
		for i := range attachment {
			if !httpPrefixReg.MatchString(attachment[i]) {
				attachment[i] = operation.OssBaseUrl + attachment[i]
			}
		}
		list = append(list, operation.ArticleCommentListRspItem{
			Attachment: attachment,
			Content:    v.Content,
			CreatedAt:  v.CreatedAt.Format("2006-01-02 15:04:05"),
			CreatedBy:  int(v.CreatedBy),
			Discarded:  int(v.Discarded),
			Enabled:    int(v.Enabled),
			ID:         int(v.ID),
			NumLikes:   int(v.NumLikes),
			Top:        int(v.Top),
		})
	}

	return list, nil
}

// 获取文章某条评论下的回复
func (g GormOpArticleDao) CommentReplies(c *gin.Context, sourceIds []int, typ string, commentSetting int, creator string, offset, limit int) (map[uint]*operation.CommentReplies, error) {
	// 初始化查询条件
	condition := map[string]interface{}{}

	// 根据评论类型设置查询条件
	if typ == "enabled" {
		condition["c.discarded"] = 0
		if commentSetting == operation.ArticleCommentSettingNeedAudit {
			condition["c.selected"] = 1
		} else if commentSetting == operation.ArticleCommentSettingNoNeedAudit {
			condition["c.visible"] = 1
		}
	} else if typ == "discarded" {
		condition["c.discarded"] = 1
	}

	// 定义用于接收查询结果的数据结构
	type resultData struct {
		model.OpArticleComment
		Enabled int `gorm:"enabled"`
	}
	var results []resultData

	// 构建查询语句
	query := g.db.WithContext(c).Table("op_article_comment AS c").
		Where("c.source_id IN ?", sourceIds).
		Where(condition).
		Order("c.id ASC")

	// 构建基础字段
	fields := []string{"c.id", "c.source_id", "c.parent_id", "c.content", "c.attachment",
		"c.created_at", "c.created_by", "c.top", "c.discarded", "c.reply_to"}

	// 根据评论设置选择不同的字段
	if commentSetting == operation.ArticleCommentSettingNeedAudit {
		fields = append(fields, "c.selected enabled")
	} else if commentSetting == operation.ArticleCommentSettingNoNeedAudit {
		fields = append(fields, "c.visible enabled")
	}

	// 如果指定了创建者，添加模糊查询条件
	if creator != "" {
		like := fmt.Sprintf("%%%s%%", creator)
		query = query.Joins("INNER JOIN admin_users AS u ON c.created_by = u.id").
			Joins("LEFT JOIN user_extend_details AS d ON u.id = d.user_id").
			Where("(u.name LIKE ? OR d.name LIKE ?)", like, like)
	}

	// 执行查询
	if err := query.Select(fields).Find(&results).Error; err != nil {
		return nil, err
	}

	// 如果没有结果，返回空
	if len(results) == 0 {
		return nil, nil
	}

	// 按 parent_id 分组
	replyIds := []uint{}
	replyToUserIds := []uint{}
	groupedResults := make(map[uint][]resultData)
	for _, result := range results {
		groupedResults[result.ParentID] = append(groupedResults[result.ParentID], result)
	}

	// 对每个 parent_id 的评论列表进行分页
	paginatedResults := make(map[uint][]resultData)
	for parentID, comments := range groupedResults {
		start := offset
		end := offset + limit
		if start > len(comments) {
			start = len(comments)
		}
		if end > len(comments) {
			end = len(comments)
		}
		paginatedResults[parentID] = comments[start:end]
		for _, comment := range paginatedResults[parentID] {
			replyIds = append(replyIds, comment.ID)
			replyToUserIds = append(replyToUserIds, comment.ReplyTo)
		}
	}

	// 统计每个评论下的回复条数
	countMap := map[uint]uint{}
	data := []struct {
		Id  uint `gorm:"column:source_id"`
		Num uint `gorm:"column:num"`
	}{}
	query = g.db.WithContext(c).Table("op_article_comment AS c").
		Where("c.source_id IN (?)", replyIds).
		Select("c.source_id, COUNT(*) num").
		Group("c.source_id").
		Where(condition)

	// 如果指定了创建者，添加模糊查询条件
	if creator != "" {
		like := fmt.Sprintf("%%%s%%", creator)
		query.Joins("INNER JOIN admin_users AS u ON c.created_by = u.id").
			Joins("LEFT JOIN user_extend_details AS d ON u.id = d.user_id").
			Where("(u.name LIKE ? OR d.name LIKE ?)", like, like)
	}

	// 执行查询
	if err := query.Find(&data).Error; err != nil {
		return nil, err
	}
	for _, v := range data {
		countMap[v.Id] = v.Num
	}

	// 获取回复到哪个人的姓名
	userNameMap := map[uint]string{}
	data2 := []struct {
		Id   uint   `gorm:"column:id"`
		Name string `gorm:"column:name"`
	}{}
	if err := g.db.WithContext(c).Table("admin_users AS u").
		Joins("LEFT JOIN user_extend_details AS ud ON u.id = ud.user_id").
		Select("u.id, IF(ud.name IS NOT NULL AND ud.name != '', ud.name, u.name) name").
		Where("u.id IN (?)", replyToUserIds).
		Find(&data2).Error; err != nil {
		return nil, err
	}
	for _, v := range data2 {
		userNameMap[v.Id] = v.Name
	}

	// 构造最终的回复结果
	children := make(map[uint]*operation.CommentReplies)
	for parent_id, group := range paginatedResults {
		count := countMap[parent_id]
		list := make([]operation.ArticleCommentListRspItem, len(group))
		for idx, item := range group {
			attachment := []string{}
			_ = json.Unmarshal([]byte(item.Attachment), &attachment)
			var httpPrefixReg = regexp.MustCompile(`(?i)^https?://`)
			for i := range attachment {
				if !httpPrefixReg.MatchString(attachment[i]) {
					attachment[i] = operation.OssBaseUrl + attachment[i]
				}
			}
			list[idx] = operation.ArticleCommentListRspItem{
				ID:         int(item.ID),
				Content:    item.Content,
				ParentID:   int(item.ParentID),
				ReplyTo:    userNameMap[item.ReplyTo],
				Attachment: attachment,
				Enabled:    item.Enabled,
				Top:        int(item.Top),
				Discarded:  int(item.Discarded),
				CreatedAt:  item.CreatedAt.Format("2006-01-02 15:04:05"),
				CreatedBy:  int(item.CreatedBy),
				// Replies:
			}
		}

		children[parent_id] = &operation.CommentReplies{
			Count: int(count),
			List:  list,
		}
	}

	return children, nil
}

func (g GormOpArticleDao) UserCommentCount(c *gin.Context, userIds []uint) (map[uint]uint, error) {
	data := []struct {
		Id  uint `gorm:"column:user_id"`
		Num uint `gorm:"column:num_comments"`
	}{}
	err := g.db.WithContext(c).Table("op_user").
		Where("user_id IN (?)", userIds).
		Select("user_id, num_comments").
		Find(&data).Error
	if err != nil {
		return nil, err
	}

	count := map[uint]uint{}
	for _, v := range data {
		count[v.Id] = v.Num
	}

	return count, nil
}

func (g GormOpArticleDao) CreatorInfos(c *gin.Context, userIds []uint, userCommentMap map[uint]uint) (map[uint]*operation.CommentCreator, error) {
	var createtors []*operation.CommentCreator
	if err := g.db.WithContext(c).Table("admin_users AS u").
		Joins("LEFT JOIN user_extend_details AS ud ON u.id = ud.user_id").
		Select("u.id, IF(ud.name IS NOT NULL AND ud.name != '', ud.name, u.name) name, IF(ud.phone IS NOT NULL AND ud.phone != '', ud.phone, u.phone) phone, u.avatar").
		Where("u.id IN (?)", userIds).
		Find(&createtors).Error; err != nil {
		return nil, err
	}

	creatorMap := map[uint]*operation.CommentCreator{}
	for _, v := range createtors {
		v.NumComments = int(userCommentMap[uint(v.ID)])
		creatorMap[uint(v.ID)] = v
	}

	return creatorMap, nil
}

// 公共

func (g GormOpArticleDao) GetUserRoles(c *gin.Context, uid uint) ([]string, error) {
	var roles []string
	if err := g.db.WithContext(c).Table("admin_role_users AS ru").
		Joins("LEFT JOIN admin_roles AS r ON ru.role_id = r.id").
		Where("ru.user_id = ?", uid).
		Pluck("r.slug", &roles).Error; err != nil {
		return nil, err
	}
	return roles, nil
}

func (g GormOpArticleDao) GetUserPublisherIds(c *gin.Context, uid uint, roles []string) ([]uint, error) {
	var publisherIds []uint

	query := g.db.WithContext(c).Table("op_publisher AS p").
		Select("p.id")

	// 非超管，只能获取与自己账号关联的运营号
	if !lo.Contains(roles, "administrator") {
		query.Joins("INNER JOIN op_publisher_user AS pu ON p.id = pu.publisher_id").
			Where("pu.user_id = ?", uid)
	}

	if err := query.Pluck("p.id", &publisherIds).Error; err != nil {
		return nil, err
	}

	return publisherIds, nil
}
