package system

import (
	"errors"
	"marketing/internal/api"
	"marketing/internal/api/system"
	"marketing/internal/consts"
	"marketing/internal/dao"
	"marketing/internal/dao/admin_user"
	"marketing/internal/model"
	appError "marketing/internal/pkg/errors"
	"marketing/internal/pkg/log"
	"marketing/internal/pkg/types"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-sql-driver/mysql"
	"go.uber.org/zap"
	"golang.org/x/exp/maps"
	"gorm.io/gorm"
)

type AdminRoleInterface interface {
	Add(c *gin.Context, req system.AddRoleReq) error
	Lists(c *gin.Context, param system.AdminRoleReq) (*api.PagedResponse[system.AdminRoleResp], error)
	Update(c *gin.Context, req system.AddRoleReq) error
	Delete(c *gin.Context, id int) error
	UpdatePermission(c *gin.Context, roleID uint, permissions []uint) error
	UpdateMenu(c *gin.Context, roleID uint, menus []uint) error
	UpdateApp(c *gin.Context, roleID uint, appID []uint) error
	ListRolesNoPage(c *gin.Context, roleName string, systemType string) ([]model.AdminRoles, error)
	GetRoleLogs(c *gin.Context, roleID int, page, pageSize int) ([]model.AdminRoleLog, int64, error)
}

// adminRoleSvc 角色管理
type adminRoleSvc struct {
	db                *gorm.DB
	UserDao           admin_user.UserDao
	UserCache         admin_user.UserCacheInterface
	ResourceGroupRepo dao.ResourceGroupRepository
}

// NewAdminRoleSvc 创建 AdminRoleSvc 实例
func NewAdminRoleSvc(db *gorm.DB, userDao admin_user.UserDao, UserCache admin_user.UserCacheInterface) AdminRoleInterface {
	return &adminRoleSvc{
		db:                db,
		UserDao:           userDao,
		UserCache:         UserCache,
		ResourceGroupRepo: dao.NewResourceGroupRepository(db),
	}
}

func (svc *adminRoleSvc) Add(c *gin.Context, req system.AddRoleReq) error {
	//判断是否已经存在
	var existRole model.AdminRoles

	err := svc.db.WithContext(c).Where("slug = ?", req.Slug).First(&existRole).Error

	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}
	if existRole.ID > 0 {
		return appError.NewErr("角色标志：【" + req.Slug + "】已经存在")
	}
	return svc.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		//创建角色
		var roleData = &model.AdminRoles{
			Name:       req.Name,
			Slug:       req.Slug,
			Remark:     req.Description,
			SystemType: req.SystemType,
		}
		//创建角色
		err = tx.WithContext(c).Create(roleData).Error
		if err != nil {
			return err
		}

		//判断资源组是否存在
		if req.ResourceGroupID > 0 { // 0 代表没有资源组
			err := svc.ResourceGroupRepo.AssignToRole(c, roleData.ID, req.ResourceGroupID)
			if err != nil {
				return err
			}
		}
		return nil
	})
}

func (svc *adminRoleSvc) Lists(c *gin.Context, param system.AdminRoleReq) (*api.PagedResponse[system.AdminRoleResp], error) {
	var r api.PagedResponse[system.AdminRoleResp]
	var list *[]model.AdminRoles

	// 1. 分页查询角色
	query := svc.db.WithContext(c).Model(&model.AdminRoles{}).Select("id, name, slug,remark ,system_type, created_at")

	// 条件处理
	if param.SystemType != "" {
		query = query.Where("system_type = ?", param.SystemType)
	}

	if param.Name != "" {
		query = query.Where("name LIKE ?", "%"+param.Name+"%")
	}
	if param.Slug != "" {
		query = query.Where("slug = ?", param.Slug)
	}
	query.Where("status = ?", 1)

	// 统计总数
	err := query.Count(&r.Total).Error
	if err != nil {
		return &r, err
	}

	// 分页查询
	limit, offset := param.PageSize, (param.Page-1)*param.PageSize

	r.PageSize = param.PageSize
	r.Page = param.Page

	err = query.Limit(limit).Offset(offset).Scan(&list).Error
	if err != nil {
		return &r, err
	}
	var roleIDs []uint
	if list != nil {
		for _, role := range *list {
			roleIDs = append(roleIDs, role.ID)
		}
	}

	// 2. 查找角色的关联菜单
	var roleMenus []struct {
		RoleID uint `json:"role_id"`
		MenuID uint `json:"menu_id"`
	}

	if len(roleIDs) > 0 { // Use list instead of roleIDs
		err = svc.db.WithContext(c).
			Model(&model.AdminRoleMenuV2{}).
			Where("role_id IN ?", roleIDs).
			Pluck("role_id, menu_id", &roleMenus).Error
		if err != nil {
			return &r, err
		}
	}
	// 3. 查找角色的关联权限
	var rolePermissions []struct {
		RoleID       uint `json:"role_id"`
		PermissionID uint `json:"permission_id"`
	}
	if len(roleIDs) > 0 { // Use list instead of roleIDs
		err = svc.db.WithContext(c).
			Model(&model.AdminRolePermission{}).
			Where("role_id IN ?", roleIDs).
			Pluck("role_id, permission_id", &rolePermissions).Error
		if err != nil {
			return &r, err
		}
	}
	// 角色关联的APP
	var roleApps []struct {
		RoleID uint `json:"role_id"`
		AppID  uint `json:"app_id"`
	}
	if len(roleIDs) > 0 { // Use list instead of roleIDs
		err = svc.db.WithContext(c).
			Model(&model.AdminRoleAppsV2{}).
			Where("role_id IN ?", roleIDs).
			Pluck("role_id, app_id", &roleApps).Error
		if err != nil {
			return &r, err
		}
	}

	if len(roleIDs) > 0 { // Use list instead of roleIDs
		err = svc.db.WithContext(c).
			Model(&model.AdminRoleMenuV2{}).
			Where("role_id IN ?", roleIDs).
			Pluck("role_id, menu_id", &roleMenus).Error
		if err != nil {
			return &r, err
		}
	}

	// 4. 获取资源组
	resourceGroups, err := svc.ResourceGroupRepo.ListByRole(c, roleIDs)

	// 5. 构建终返回的数据
	if list != nil {
		for _, role := range *list {
			var menus []uint
			var permissions []uint
			var appIds []uint

			// 获取该角色的菜单
			for _, rm := range roleMenus {
				if rm.RoleID == role.ID {
					menus = append(menus, rm.MenuID)
				}
			}

			// 获取该角色的权限
			for _, rp := range rolePermissions {
				if rp.RoleID == role.ID {
					permissions = append(permissions, rp.PermissionID)
				}
			}
			// 获取角色APP
			for _, app := range roleApps {
				if app.RoleID == role.ID {
					appIds = append(appIds, app.AppID)
				}
			}

			// 获取资源组
			var groupID uint
			var groupName string
			for rID, rg := range resourceGroups {
				if rID == role.ID {
					groupID = rg.ID
					groupName = rg.Name
				}
			}
			r.Data = append(r.Data, system.AdminRoleResp{
				ID:                role.ID,
				Name:              role.Name,
				Slug:              role.Slug,
				Description:       role.Remark,
				SystemType:        role.SystemType,
				CreatedAt:         types.CustomTime(role.CreatedAt),
				ResourceGroupID:   groupID,
				ResourceGroupName: groupName,
				MenuIDs:           menus,
				PermissionIDs:     permissions,
				AppsIDs:           appIds,
			})
		}
	}

	return &r, nil
}

func (svc *adminRoleSvc) Update(c *gin.Context, req system.AddRoleReq) error {
	//判断是否已经存在
	var existRole model.AdminRoles

	err := svc.db.WithContext(c).Where("id = ?", req.ID).First(&existRole).Error

	if errors.Is(err, gorm.ErrRecordNotFound) {
		return appError.NewErr("角色不存在")
	}

	if err != nil {
		return err
	}
	return svc.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		existRole.Name = req.Name
		existRole.Slug = req.Slug
		existRole.Remark = req.Description
		existRole.SystemType = req.SystemType
		err = svc.db.WithContext(c).Save(existRole).Error

		var mysqlErr *mysql.MySQLError
		if errors.As(err, &mysqlErr) {
			// 检查错误码,增前先做检查，这样理会导致增id不连续
			if mysqlErr.Number == 1062 {
				// 处理重复键错误
				return appError.NewErr("角色标志已经存在，请检查")
			}
		}
		//需要重新查之前是否有资源组
		resourceGroup, err := svc.ResourceGroupRepo.GetByRole(c, req.ID)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}
		if resourceGroup.ID > 0 && resourceGroup.ID != req.ResourceGroupID {
			err := svc.ResourceGroupRepo.RemoveFromRole(c, req.ID, resourceGroup.ID)
			if err != nil {
				return err
			}
		}
		//判断资源组是否存在
		if req.ResourceGroupID > 0 {
			err := svc.ResourceGroupRepo.AssignToRole(c, req.ID, req.ResourceGroupID)
			if err != nil {
				return err
			}
		}
		return nil
	})

}

func (svc *adminRoleSvc) Delete(c *gin.Context, id int) error {
	//判断是否已经存在
	var existRole model.AdminRoles

	err := svc.db.WithContext(c).Where("id = ?", id).First(&existRole).Error

	if errors.Is(err, gorm.ErrRecordNotFound) {
		return appError.NewErr("角色不存在")
	}

	if err != nil {
		return err
	}
	existRole.Status = 0
	existRole.DeletedAt = gorm.DeletedAt{Time: time.Now(), Valid: true}
	return svc.db.WithContext(c).Save(existRole).Error
}

func (svc *adminRoleSvc) UpdateMenu(c *gin.Context, roleID uint, menus []uint) error {
	// 判断角色是否存在
	var existRole model.AdminRoles
	if err := svc.db.WithContext(c).Where("id = ?", roleID).First(&existRole).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return appError.NewErr("角色不存在")
		}
		return err
	}

	// 获取当前角色的所有菜单
	var existingMenus []model.AdminRoleMenuV2
	if err := svc.db.WithContext(c).Where("role_id = ?", roleID).Find(&existingMenus).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}

	// 创建一个 map 来存储当前菜单 ID
	existingMenuMap := make(map[uint]uint)
	for _, item := range existingMenus {
		existingMenuMap[item.MenuID] = item.MenuID
	}

	// 准备要添加的新菜单
	var menusToAdd []model.AdminRoleMenuV2
	for _, newMenu := range menus {
		if _, exists := existingMenuMap[newMenu]; !exists {
			menusToAdd = append(menusToAdd, model.AdminRoleMenuV2{RoleID: roleID, MenuID: newMenu})
		} else {
			delete(existingMenuMap, newMenu)
		}
	}

	// 获取该角色下的所有用户ID
	var userIDs []uint
	err := svc.db.WithContext(c).Model(&model.AdminRoleUser{}).
		Where("role_id = ?", roleID).
		Pluck("user_id", &userIDs).Error
	if err != nil {
		return err
	}

	// 开启事务
	return svc.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		// 删除不再需要的菜单
		if len(existingMenuMap) > 0 {
			if err := tx.Where("role_id = ? AND menu_id IN ?", roleID, maps.Keys(existingMenuMap)).Delete(&model.AdminRoleMenuV2{}).Error; err != nil {
				return err
			}
		}

		// 添加新菜单
		if len(menusToAdd) > 0 {
			if err := tx.Create(&menusToAdd).Error; err != nil {
				return err
			}
		}

		// 批量删除用户缓存
		if existRole.SystemType == "admin" {

			if err := svc.UserCache.BatchDeleteUserCache(c, userIDs, consts.AdminPrefix); err != nil {
				log.Error("删除用户缓存失败", zap.Error(err))
				// 缓存删除失败不影响业务
			}
		} else if existRole.SystemType == "agency" {
			if err := svc.UserCache.BatchDeleteUserCache(c, userIDs, consts.AgencyPrefix); err != nil {
				log.Error("删除用户缓存失败", zap.Error(err))
				// 缓存删除失败不影响业务
			}
		}

		return nil
	})
}

func (svc *adminRoleSvc) UpdatePermission(c *gin.Context, roleID uint, permissions []uint) error {
	// 判断角色是否存在
	var existRole model.AdminRoles
	if err := svc.db.WithContext(c).Where("id = ?", roleID).First(&existRole).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return appError.NewErr("角色不存在")
		}
		return err
	}

	// 获取当前角色的所有权限
	var existingPermissions []model.AdminRolePermission
	if err := svc.db.WithContext(c).Where("role_id = ?", roleID).Find(&existingPermissions).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}

	// 创建一个 map 来存储当前权限 ID
	existingPermissionMap := make(map[uint]uint)
	for _, item := range existingPermissions {
		existingPermissionMap[item.PermissionID] = item.ID
	}

	// 准备要添加的新权限
	var permissionsToAdd []model.AdminRolePermission
	for _, newPerm := range permissions {
		if _, exists := existingPermissionMap[newPerm]; !exists {
			permissionsToAdd = append(permissionsToAdd, model.AdminRolePermission{RoleID: roleID, PermissionID: newPerm})
		} else {
			delete(existingPermissionMap, newPerm)
		}
	}

	// 获取该角色下的所有用户ID
	var userIDs []uint
	err := svc.db.WithContext(c).Model(&model.AdminRoleUser{}).
		Where("role_id = ?", roleID).
		Pluck("user_id", &userIDs).Error
	if err != nil {
		return err
	}

	// 开启事务
	return svc.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		// 删除不再需要的权限
		if len(existingPermissionMap) > 0 {
			if err := tx.Where("role_id = ? AND permission_id IN ?", roleID, maps.Keys(existingPermissionMap)).Delete(&model.AdminRolePermission{}).Error; err != nil {
				return err
			}
		}

		// 添加新权限
		if len(permissionsToAdd) > 0 {
			if err := tx.Create(&permissionsToAdd).Error; err != nil {
				return err
			}
		}

		// 批量删除用户缓存
		if existRole.SystemType == "admin" {

			if err := svc.UserCache.BatchDeleteUserCache(c, userIDs, consts.AdminPrefix); err != nil {
				log.Error("删除用户缓存失败", zap.Error(err))
				// 缓存删除失败不影响业务
			}
		} else if existRole.SystemType == "agency" {
			if err := svc.UserCache.BatchDeleteUserCache(c, userIDs, consts.AgencyPrefix); err != nil {
				log.Error("删除用户缓存失败", zap.Error(err))
				// 缓存删除失败不影响业务
			}
		}

		return nil
	})
}

func (svc *adminRoleSvc) UpdateApp(c *gin.Context, roleID uint, appID []uint) error {
	// 判断角色是否存在
	var existRole model.AdminRoles
	if err := svc.db.WithContext(c).Where("id = ?", roleID).First(&existRole).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return appError.NewErr("角色不存在")
		}
		return err
	}

	// 获取当前角色的所有应用
	var existingApps []model.AdminRoleAppsV2
	if err := svc.db.WithContext(c).Where("role_id = ?", roleID).Find(&existingApps).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}

	// 创建一个 map 来存储当前应用 ID
	existingAppMap := make(map[uint]uint)
	for _, item := range existingApps {
		existingAppMap[item.AppID] = item.ID
	}

	// 准备要添加的新应用
	var appsToAdd []model.AdminRoleAppsV2
	for _, newApp := range appID {
		if _, exists := existingAppMap[newApp]; !exists {
			appsToAdd = append(appsToAdd, model.AdminRoleAppsV2{RoleID: roleID, AppID: newApp})
		} else {
			delete(existingAppMap, newApp)
		}
	}

	// 获取该角色下的所有用户ID
	var userIDs []uint
	err := svc.db.WithContext(c).Model(&model.AdminRoleUser{}).
		Where("role_id = ?", roleID).
		Pluck("user_id", &userIDs).Error
	if err != nil {
		return err
	}

	// 开启事务
	return svc.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		// 删除不再需要的应用
		if len(existingAppMap) > 0 {
			if err := tx.Where("role_id = ? AND app_id IN ?", roleID, maps.Keys(existingAppMap)).Delete(&model.AdminRoleAppsV2{}).Error; err != nil {
				return err
			}
		}

		// 添加新应用
		if len(appsToAdd) > 0 {
			if err := tx.Create(&appsToAdd).Error; err != nil {
				return err
			}
		}

		// 批量删除用户缓存
		if existRole.SystemType == "admin" {
			if err := svc.UserCache.BatchDeleteUserCache(c, userIDs, consts.AdminPrefix); err != nil {
				log.Error("删除用户缓存失败", zap.Error(err))
				// 缓存删除失败不影响业务
			}
		} else if existRole.SystemType == "agency" {
			if err := svc.UserCache.BatchDeleteUserCache(c, userIDs, consts.AgencyPrefix); err != nil {
				log.Error("删除用户缓存失败", zap.Error(err))
				// 缓存删除失败不影响业务
			}
		}
		return nil
	})
}

// ListRolesNoPage 获取所有角色
func (svc *adminRoleSvc) ListRolesNoPage(c *gin.Context, roleName string, systemType string) ([]model.AdminRoles, error) {
	var roles []model.AdminRoles
	query := svc.db.WithContext(c).Where("status = ?", 1)
	if roleName != "" {
		query = query.Where("name LIKE ?", "%"+roleName+"%")
	}
	if systemType != "" {
		query = query.Where("system_type =?", systemType)
	}
	err := query.Find(&roles).Error
	return roles, err
}

func (svc *adminRoleSvc) GetRoleLogs(c *gin.Context, roleID int, page, pageSize int) ([]model.AdminRoleLog, int64, error) {
	var logs []model.AdminRoleLog
	var total int64

	query := svc.db.Model(&model.AdminRoleLog{}).WithContext(c).Where("role_id =?", roleID)
	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	if err := query.Order("id DESC").
		Offset((page - 1) * pageSize).
		Limit(pageSize).
		Find(&logs).Error; err != nil {
		return nil, 0, err
	}
	// 获取所有操作人的信息
	operatorIDs := make([]uint, len(logs))
	for i, l := range logs {
		operatorIDs[i] = uint(l.Operator)
	}

	operators, err := svc.UserDao.GetByIDs(c, operatorIDs)
	if err != nil {
		return nil, 0, err
	}

	operatorMap := make(map[uint]string)
	for _, operator := range operators {
		operatorMap[operator.ID] = operator.Name
	}

	for i := range logs {
		if name, exists := operatorMap[uint(logs[i].Operator)]; exists {
			logs[i].OperatorName = name
		}
		logs[i].CreatedAtStr = logs[i].CreatedAt.Format(time.DateTime)
	}
	return logs, total, err
}
