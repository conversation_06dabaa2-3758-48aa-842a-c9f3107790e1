package model

import (
	"time"
)

type V3Outstock struct {
	ID          uint       `gorm:"primaryKey;column:id" json:"id"` // Changed to uint
	ModelID     int        `gorm:"column:model_id;comment:机型id" json:"model_id"`
	Model       string     `gorm:"column:model;size:32;index;comment:机型" json:"model"`
	Barcode     string     `gorm:"column:barcode;size:32;not null;index" json:"barcode"`
	Number      string     `gorm:"column:number;size:48;index" json:"number"`
	Imei        string     `gorm:"column:imei;size:16;index" json:"imei"`
	CustCode    string     `gorm:"column:cust_code;size:16;index;comment:客户编码" json:"cust_code"`
	CustName    string     `gorm:"column:cust_name;size:64;comment:客户名称" json:"cust_name"`
	CustCodeNew string     `gorm:"column:cust_code_new;size:16;index:cust_code_new_btree;comment:客户编码（调拨后）" json:"cust_code_new"`
	CustNameNew string     `gorm:"column:cust_name_new;size:64;comment:客户名称（调拨后）" json:"cust_name_new"`
	MatID       int        `gorm:"column:mat_id;comment:物料ID" json:"mat_id"`
	MatCode     string     `gorm:"column:mat_code;size:20;comment:物料编码" json:"mat_code"`
	MatModel    string     `gorm:"column:mat_model;size:64;comment:物料规格" json:"mat_model"`
	BillID      int        `gorm:"column:bill_id;index;comment:金蝶单据ID" json:"bill_id"`
	BillRowID   int        `gorm:"column:bill_row_id;index;comment:金蝶单据行ID，用于更新或删除判定" json:"bill_row_id"`
	BillNo      string     `gorm:"column:bill_no;size:64;comment:金蝶单据编码" json:"bill_no"`
	BillDate    *time.Time `gorm:"column:bill_date;type:date;index;comment:出货日期" json:"bill_date"`
	Settlement  string     `gorm:"column:settlement;size:10;index;comment:结算类型：01-占额度；02-应收款；03-免费；04-不占额度；05-翻新机" json:"settlement"`
	ZCLX        *int8      `gorm:"column:zclx;size:2;default:1;index;comment:支持类型（1正常销售，2特殊支持）" json:"zclx"`        // Use int8 for tinyint
	IsFree      *int8      `gorm:"column:is_free;size:2;default:0;index;comment:是否免费（0收费，1免费）" json:"is_free"`      // Use int8 for tinyint
	Status      *int8      `gorm:"column:status;size:4;not null;default:1;index;comment:状态：1正常，0已删除" json:"status"` // Use int8 for tinyint
	AddTime     *time.Time `gorm:"column:add_time;default:0000-00-00 00:00:00;comment:记录创建时间" json:"add_time"`
	UpdateTime  *time.Time `gorm:"column:update_time;index;comment:记录更新时间" json:"update_time"`
}

func (V3Outstock) TableName() string {
	return "v3_outstock"
}
