package reimbursement

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	api "marketing/internal/api/reimbursement"
	"marketing/internal/model"
	"marketing/internal/pkg/errors"
	"time"
)

type BalanceRepository interface {
	GetBalanceStandard(c *gin.Context, policyID int, companyID int, standardType string) (*model.ReimbursementBalanceStandard, error)
	GetBalanceList(c *gin.Context, req *api.BalanceListReq) ([]*api.ReimbursementBalanceStandardResp, int64, error)
	GetBalanceListStat(c *gin.Context, req *api.BalanceListReq) (*api.BalanceListStatResp, error)
	ImportBalanceStandard(c *gin.Context, data *model.ReimbursementBalanceStandard) error
	ResetBalanceStandardByCompany(c *gin.Context, companyIDs []int, policyID int, standardType string, uid uint) error
	ResetBalanceStandardAll(c *gin.Context, policyID int, standardType string, uid uint) error
}

type balanceRepo struct {
	db *gorm.DB
}

func NewBalanceRepository(db *gorm.DB) BalanceRepository {
	return &balanceRepo{db: db}
}

// GetBalanceStandard 获取余额标准
func (r *balanceRepo) GetBalanceStandard(c *gin.Context, policyID int, companyID int, standardType string) (*model.ReimbursementBalanceStandard, error) {
	var result model.ReimbursementBalanceStandard

	// 使用 GORM 查询
	sql := r.db.WithContext(c).
		Table("reimbursement_balance_import_standard").
		Select("balance")
	if policyID > 0 {
		sql = sql.Where("policy_id = ?", policyID)
	}
	if companyID > 0 {
		sql = sql.Where("company_id = ?", companyID)
	}
	err := sql.Where("standard_type = ?", standardType).Order("created_at DESC").
		Limit(1).
		Scan(&result).Error

	if err != nil {
		return nil, err
	}

	return &result, nil
}

func (r *balanceRepo) GetBalanceList(c *gin.Context, req *api.BalanceListReq) ([]*api.ReimbursementBalanceStandardResp, int64, error) {
	// 构建查询
	query := r.db.WithContext(c).Table("reimbursement_balance_import_standard r1").
		Select(`a.name AS agency_name, r1.company_id, r1.company, r1.code,
            (SELECT rbis.norm_quantity FROM reimbursement_balance_import_standard rbis
             WHERE rbis.code = r1.code AND rbis.company_id = r1.company_id AND rbis.policy_id = r1.policy_id
             AND standard_type = ? AND type = 1 ORDER BY created_at DESC LIMIT 1) AS norm_quantity,
            r1.balance,
            (SELECT rbis.created_at FROM reimbursement_balance_import_standard rbis
             WHERE rbis.code = r1.code AND rbis.company_id = r1.company_id AND rbis.policy_id = r1.policy_id
             AND standard_type = ? AND type = 1 ORDER BY created_at DESC LIMIT 1) AS created_at`,
			req.StandardType, req.StandardType).
		Joins(`RIGHT JOIN (SELECT max(id) id FROM reimbursement_balance_import_standard
             WHERE policy_id = ? AND standard_type = ? GROUP BY company_id, policy_id) r2 ON r1.id = r2.id`,
			req.PolicyID, req.StandardType).
		Joins("LEFT JOIN agency a ON r1.top_agency = a.id")

	// 动态条件
	if req.Code != "" {
		query = query.Where("r1.code = ?", req.Code)
	}
	if req.TopAgency != 0 {
		query = query.Where("r1.top_agency = ?", req.TopAgency)
	}
	if req.CompanyID != 0 {
		query = query.Where("r1.company_id = ?", req.CompanyID)
	}

	// 获取总记录数
	var total int64
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 分页
	offset := (req.Page - 1) * req.PageSize
	query = query.Offset(offset).Limit(req.PageSize)

	// 执行查询
	var results []*api.ReimbursementBalanceStandardResp
	err = query.Scan(&results).Error
	if err != nil {
		return nil, 0, err
	}

	return results, total, nil
}

func (r *balanceRepo) GetBalanceListStat(c *gin.Context, req *api.BalanceListReq) (*api.BalanceListStatResp, error) {
	// 构建查询
	query := r.db.WithContext(c).
		Table("reimbursement_balance_import_standard r1").
		Select(`count(*) AS count,
			SUM((SELECT rbis.norm_quantity FROM reimbursement_balance_import_standard rbis
				WHERE rbis.code = r1.code AND rbis.company_id = r1.company_id AND rbis.policy_id = r1.policy_id
				AND standard_type = ? AND norm_quantity > 0 AND type = 1 ORDER BY created_at DESC LIMIT 1)) AS norm_quantity,
			SUM(r1.balance) AS balance`, req.StandardType).
		Joins(`RIGHT JOIN (SELECT max(id) id FROM reimbursement_balance_import_standard
				WHERE policy_id = ? AND standard_type = ? GROUP BY company_id, policy_id) r2 ON r1.id = r2.id`,
			req.PolicyID, req.StandardType).
		Joins("LEFT JOIN agency a ON r1.top_agency = a.id").
		Where("1 = 1")

	// 动态条件
	if req.Code != "" {
		query = query.Where("r1.code = ?", req.Code)
	}
	if req.TopAgency != 0 {
		query = query.Where("r1.top_agency = ?", req.TopAgency)
	}
	if req.CompanyID != 0 {
		query = query.Where("r1.company_id = ?", req.CompanyID)
	}

	// 执行查询并直接映射到 api.BalanceListStatResp
	var resp api.BalanceListStatResp
	err := query.Scan(&resp).Error
	if err != nil {
		return nil, err
	}

	return &resp, nil
}

// ImportBalanceStandard 导入代理商核销剩余额度
func (r *balanceRepo) ImportBalanceStandard(c *gin.Context, data *model.ReimbursementBalanceStandard) error {
	// 开启事务
	tx := r.db.WithContext(c).Begin()

	var balanceInfo float64

	// 查询最新记录的余额
	err := tx.Table("reimbursement_balance_import_standard").
		Select("balance").
		Where("policy_id = ? AND code = ? AND standard_type = ?", data.PolicyID, data.Code, data.StandardType).
		Order("created_at DESC").
		Limit(1).
		Scan(&balanceInfo).Error

	if err != nil {
		tx.Rollback()
		return err
	}

	// 计算新记录的余额
	balance := data.NormQuantity
	if balanceInfo != 0 {
		balance += balanceInfo
	}

	// 创建新记录
	data.Balance = balance

	if err = tx.Create(&data).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 提交事务
	return tx.Commit().Error
}

// ResetBalanceStandardByCompany 实现重置余额的具体逻辑
func (r *balanceRepo) ResetBalanceStandardByCompany(c *gin.Context, companyIDs []int, policyID int, standardType string, uid uint) error {
	now := time.Now()

	// 开启事务
	tx := r.db.WithContext(c).Begin()
	if tx.Error != nil {
		return tx.Error
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	for _, companyID := range companyIDs {
		var record model.ReimbursementBalanceStandard

		err := tx.Table("reimbursement_balance_import_standard").
			Where("policy_id = ? AND company_id = ?", policyID, companyID).
			Order("created_at DESC").
			Limit(1).
			Scan(&record).Error

		if err != nil {
			tx.Rollback()
			return errors.NewErr("获取余额记录失败")
		}

		if record.CompanyID == 0 {
			tx.Rollback()
			return errors.NewErr("未找到需要清零的记录")
		}

		// 插入清零记录
		newRecord := model.ReimbursementBalanceStandard{
			UID:          uid,
			PolicyID:     policyID,
			CompanyID:    record.CompanyID,
			Code:         record.Code,
			Company:      record.Company,
			TopAgency:    record.TopAgency,
			SecondAgency: record.SecondAgency,
			NormQuantity: -record.Balance, // 负数表示清零
			Balance:      0,               // 清零后余额为0
			Month:        now,
			StandardType: record.StandardType,
			Remark:       "核销标准清零",
			CreatedAt:    now,
			Type:         2, // 清零操作类型
		}

		if err := tx.Create(&newRecord).Error; err != nil {
			tx.Rollback()
			return errors.NewErr("创建清零记录失败: " + err.Error())
		}
	}

	return tx.Commit().Error
}

// ResetBalanceStandardAll 重置所有公司的余额
func (r *balanceRepo) ResetBalanceStandardAll(c *gin.Context, policyID int, standardType string, uid uint) error {
	now := time.Now()

	// 开启事务
	tx := r.db.WithContext(c).Begin()
	if tx.Error != nil {
		return tx.Error
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 直接查询最新记录
	var records []model.ReimbursementBalanceStandard

	err := tx.Table("reimbursement_balance_import_standard r1").
		Select("r1.top_agency, r1.second_agency, r1.company_id, r1.company, r1.code, r1.balance, r1.standard_type").
		Joins("RIGHT JOIN (SELECT max(id) id FROM reimbursement_balance_import_standard "+
			"WHERE policy_id = ? AND standard_type = ? GROUP BY company_id, policy_id) r2 ON r1.id = r2.id",
			policyID, standardType).
		Scan(&records).Error

	if err != nil {
		tx.Rollback()
		return err
	}

	if len(records) == 0 {
		tx.Rollback()
		return errors.NewErr("无可要清零的记录")
	}

	// 批量插入清零记录
	for _, record := range records {
		newRecord := model.ReimbursementBalanceStandard{
			UID:          uid,
			PolicyID:     policyID,
			CompanyID:    record.CompanyID,
			Code:         record.Code,
			Company:      record.Company,
			TopAgency:    record.TopAgency,
			SecondAgency: record.SecondAgency,
			NormQuantity: -record.Balance,
			Balance:      0,
			Month:        now,
			StandardType: record.StandardType,
			Remark:       "核销标准清零",
			CreatedAt:    now,
			Type:         2,
		}

		if err := tx.Create(&newRecord).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	return tx.Commit().Error
}
