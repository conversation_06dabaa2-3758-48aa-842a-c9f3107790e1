package operation

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"marketing/internal/model"
	"marketing/internal/pkg/db"
)

type OpArticleCommentDao interface {
	CreateOpArticleComment(c *gin.Context, comment *model.OpArticleComment) error
	UpdateOpArticleCommentStatus(c *gin.Context, id int, uMap map[string]interface{}) error
	GetOpArticleCommentInfos(c *gin.Context, aids []uint) (list []*model.OpArticleCommentCountInfo)
	GetOpArticleCommentList(c *gin.Context, aid int) (list []*model.OpArticleComment)
	GetOpArticleComment(c *gin.Context, id int) *model.OpArticleComment
	GetByIds(c *gin.Context, ids []uint) (users []model.AdminUsers)
}

// OpArticleCommentDaoImpl 实现 OpArticleCommentDao 接口
type OpArticleCommentDaoImpl struct {
	db *gorm.DB
}

// NewOpArticleCommentDao 创建 OpArticleCommentDao 实例
func NewOpArticleCommentDao() OpArticleCommentDao {
	return &OpArticleCommentDaoImpl{
		db: db.GetDB(""),
	}
}

func (d *OpArticleCommentDaoImpl) CreateOpArticleComment(c *gin.Context, comment *model.OpArticleComment) error {
	return d.db.WithContext(c).Create(comment).Error
}

func (d *OpArticleCommentDaoImpl) UpdateOpArticleCommentStatus(c *gin.Context, id int, uMap map[string]interface{}) error {
	return d.db.WithContext(c).Model(&model.OpArticleComment{}).Where("id = ?", id).Updates(uMap).Error
}

func (d *OpArticleCommentDaoImpl) GetOpArticleCommentInfos(c *gin.Context, aids []uint) (list []*model.OpArticleCommentCountInfo) {
	d.db.WithContext(c).Model(&model.OpArticleComment{}).
		Where("article_id in (?)", aids).
		Select("article_id,count(*) num").
		Group("article_id").
		Find(&list)
	return
}

func (d *OpArticleCommentDaoImpl) GetOpArticleCommentList(c *gin.Context, aid int) (list []*model.OpArticleComment) {
	d.db.WithContext(c).Model(&model.OpArticleComment{}).Where("article_id = ? and deleted_at is null", aid).Find(&list)
	return
}

func (d *OpArticleCommentDaoImpl) GetOpArticleComment(c *gin.Context, id int) *model.OpArticleComment {
	var comment model.OpArticleComment
	err := d.db.WithContext(c).Model(&model.OpArticleComment{}).Where("id = ? and deleted_at is null", id).First(&comment).Error
	if err != nil {
		return nil
	}
	return &comment
}

func (d *OpArticleCommentDaoImpl) GetByIds(c *gin.Context, ids []uint) (users []model.AdminUsers) {
	d.db.WithContext(c).Where("id in ?", ids).Find(&users)
	return
}
