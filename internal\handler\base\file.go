package base

import (
	"marketing/internal/handler"
	"marketing/internal/service"
	"math/rand"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

type HandlerUploadFileV2 struct {
	svc service.UploadService
}

func NewHandlerUploadFileV2(svc service.UploadService) *HandlerUploadFileV2 {
	return &HandlerUploadFileV2{svc: svc}
}

// UploadFile 上传文件
func (u *HandlerUploadFileV2) UploadFile(c *gin.Context) {
	path := c.Param("path")
	file, err := c.FormFile("file")
	md5 := c.Request.FormValue("md5")
	if err != nil {
		handler.Error(c, err)
		return
	}
	url, err := u.svc.UploadFile(c, path, file, md5)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"url": url,
	})
}

// DeleteFile 删除文件
func (u *HandlerUploadFileV2) DeleteFile(c *gin.Context) {
	var url struct {
		Url string `json:"url"`
	}
	err := c.ShouldBindJSON(&url)
	err = u.svc.DeleteFile(c, url.Url)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c)
}

// 随机文件名
func randName(filename string, randomlyRename bool) string {
	name := filename
	if randomlyRename {
		name = strings.ToLower(strconv.FormatInt(time.Now().UnixNano(), 36) + strconv.FormatInt(int64(rand.Intn(2176782335)), 36))
		name = name + filepath.Ext(filename)
	}
	return name
}
