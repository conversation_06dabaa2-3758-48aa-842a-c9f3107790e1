package wecom

import (
	"encoding/json"
	"fmt"
	"marketing/internal/pkg/log"
	"sync"
	"time"

	"github.com/go-resty/resty/v2"
	"go.uber.org/zap"
)

// AccessTokenResp 用于解析获取到的访问令牌响应
type AccessTokenResp struct {
	ErrCode     int    `json:"errcode"`
	ErrMsg      string `json:"errmsg"`
	AccessToken string `json:"access_token"`
	ExpiresIn   int    `json:"expires_in"`
}

// SendMessageReq 用于构造发送消息的请求
type SendMessageReq struct {
	ToUser  string `json:"touser"`
	MsgType string `json:"msgtype"`
	AgentID int    `json:"agentid"`
	Text    struct {
		Content string `json:"content"`
	} `json:"text"`
	Safe int `json:"safe"`
}

// Client WeCom 客户端
type Client struct {
	httpClient  *resty.Client
	corpID      string
	corpSecret  string
	accessToken string
	expiration  time.Time
	mutex       sync.Mutex
	ticketCache *ticketCache
}

type WeChatAPIError struct {
	ErrCode int
	ErrMsg  string
}

// 定义一个全局的映射来存储 WeCom 客户端实例
var weComClients = make(map[string]*Client)
var clientMutex sync.Mutex

func (e *WeChatAPIError) Error() string {
	return fmt.Sprintf("微信请求出错: %s (code: %d)", e.ErrMsg, e.ErrCode)
}

// NewWeComClient 创建 WeCom 客户端实例，并自动获取访问令牌
func NewWeComClient(corpID, corpSecret string) *Client {
	if corpID == "" || corpSecret == "" {
		log.Error(fmt.Sprintf("微信应用实例化出错:corpID或者corpSecret为空"))
		return nil
	}

	// 生成唯一的键
	key := fmt.Sprintf("%s:%s", corpID, corpSecret)

	clientMutex.Lock()
	defer clientMutex.Unlock()

	// 检查实例是否已经存在
	if existingClient, ok := weComClients[key]; ok {
		return existingClient
	}

	client := resty.New().SetTimeout(60 * time.Second)
	c := &Client{
		httpClient: client,
		corpID:     corpID,
		corpSecret: corpSecret,
	}

	// 初始化ticket缓存
	c.initTicketCache()

	//存储新实例
	weComClients[key] = c

	return c
}

// GetAccessToken 获取访问令牌
func (client *Client) GetAccessToken() (string, error) {

	// 如果过期，则重新获取
	if client.accessToken == "" || time.Now().After(client.expiration) {
		if err := client.fetchAccessToken(); err != nil {
			return "", err
		}
	}

	return client.accessToken, nil
}

// fetchAccessToken 获取访问令牌
func (client *Client) fetchAccessToken() error {
	client.mutex.Lock()
	defer client.mutex.Unlock()
	log.Info(fmt.Sprintf("%s 获取微信access_token", client.corpID))

	url := fmt.Sprintf("https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=%s&corpsecret=%s&debug=1", client.corpID, client.corpSecret)
	resp, err := client.httpClient.R().Get(url)

	if err != nil {
		log.Error("获取微信access_token，服务器请求错误：", zap.Error(err))
		return err
	}

	var tokenResp AccessTokenResp
	if err := json.Unmarshal(resp.Body(), &tokenResp); err != nil {
		log.Error("获取微信access_token,解析json：", zap.Error(err))
		return err
	}

	if tokenResp.ErrCode != 0 {
		log.Error("获取微信access_token，微信接口错误：", zap.Int("errcode", tokenResp.ErrCode), zap.String("errmsg", tokenResp.ErrMsg))
		return fmt.Errorf("error getting access token: %s", tokenResp.ErrMsg)
	}

	client.accessToken = tokenResp.AccessToken
	client.expiration = time.Now().Add(time.Duration(tokenResp.ExpiresIn) * time.Second)

	return nil
}
