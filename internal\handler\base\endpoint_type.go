package base

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/handler"
	service "marketing/internal/service/base"
)

type EndpointTypeHandler interface {
	GetEndpointType(c *gin.Context)
}

type endpointTypeHandler struct {
	service service.EndpointTypeService
}

func NewEndpointTypeHandler(service service.EndpointTypeService) EndpointTypeHandler {
	return &endpointTypeHandler{
		service: service,
	}
}

func (h *endpointTypeHandler) GetEndpointType(c *gin.Context) {
	types, err := h.service.GetEndpointType(c)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, types)
}
