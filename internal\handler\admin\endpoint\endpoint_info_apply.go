package endpoint

import (
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	api "marketing/internal/api/endpoint"
	"marketing/internal/consts"
	"marketing/internal/handler"
	"marketing/internal/pkg/errors"
	service "marketing/internal/service/endpoint"
)

type InfoApplyInterface interface {
	Lists(c *gin.Context)
	Audit(c *gin.Context)
}

type endpointInfoApply struct {
	svc service.InfoApplyService
}

func NewEndpointInfoApply(svc service.InfoApplyService) InfoApplyInterface {
	return &endpointInfoApply{
		svc: svc,
	}
}

func (e *endpointInfoApply) Lists(c *gin.Context) {
	var param api.GetEndpointInfoApplyReq
	if err := c.ShouldBind(&param); err != nil {
		handler.Error(c, err)
		return
	}

	if param.Page == 0 {
		param.Page = consts.DefaultPage
	}
	if param.PageSize == 0 {
		param.PageSize = consts.DefaultPageSize
	}

	applies, total, err := e.svc.Lists(c, &param)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, gin.H{
		"list":      applies,
		"total":     total,
		"page":      param.Page,
		"page_size": param.PageSize,
	})
}

func (e *endpointInfoApply) Audit(c *gin.Context) {
	var req api.AuditInfoApplyReq
	if err := c.ShouldBindJSON(&req); err != nil {
		handler.Error(c, err)
		return
	}

	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		handler.Error(c, errors.NewErr("id不能为空"))
		return
	}
	req.ID = id

	err := e.svc.Audit(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c)
}
