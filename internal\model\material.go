package model

import (
	"time"
)

type Material struct {
	Id               int       `json:"id" gorm:"column:id"`                               // 物料id
	Name             string    `json:"name" gorm:"column:name"`                           // 物料名称
	IsPutAway        int       `json:"is_putaway" gorm:"column:is_putaway"`               // 是否上架
	GrantType        string    `json:"grant_type" gorm:"column:grant_type"`               // 发放方式
	Category         int       `json:"category" gorm:"column:category"`                   // 分类id
	ProductionNumber string    `json:"production_number" gorm:"column:production_number"` // 产品编号
	Specification    string    `json:"specification" gorm:"column:specification"`         // 物料规格
	Price            string    `json:"price" gorm:"column:price"`                         // 价格
	Stock            string    `json:"stock" gorm:"column:stock"`                         // 库存
	IsRecommend      int       `json:"is_recommend" gorm:"column:is_recommend"`           // 是否推荐 0:不推荐 1:推荐
	IsNew            int       `json:"is_new" gorm:"column:is_new"`                       // 是否新品 0:旧品 1:新品
	Pic              string    `json:"pic" gorm:"column:pic"`                             // 大图
	Thumbnail        string    `json:"thumbnail" gorm:"column:thumbnail"`                 // 缩略图
	Description      string    `json:"description" gorm:"column:description"`             // 物料描述
	PicOther         string    `json:"pic_other" gorm:"column:pic_other"`                 // 其它图片
	LikeCount        int       `json:"like_count" gorm:"column:like_count"`               // 被赞次数
	Order            int       `json:"order" gorm:"column:order"`                         // 排序
	DislikeCount     int       `json:"dislike_count" gorm:"column:dislike_count"`         // 被踩次数
	Unit             string    `json:"unit" gorm:"column:unit"`                           // 计量单位
	UsedByActivity   int       `json:"used_by_activity" gorm:"column:used_by_activity"`   // 是否可被活动引用做物料
	Video            string    `json:"video" gorm:"column:video"`                         // 介绍视频
	CreatedAt        time.Time `json:"-" gorm:"created_at"`                               // CreatedAt 创建时间
	CreateTime       string    `json:"create_time" gorm:"-"`                              // 创建时间
	UpdatedAt        time.Time `json:"-" gorm:"updated_at"`                               // UpdatedAt 修改时间
	UpdateTime       string    `json:"update_time" gorm:"-"`                              // 修改时间
}

type MaterialCategoryNum struct {
	Category int `json:"category" gorm:"column:category"` // 分类id
	Num      int `json:"num" gorm:"column:num"`           // 数量
}

func (Material) TableName() string {
	return "material"
}

type RepairMaterial struct {
	Id            int     `json:"id" gorm:"column:id"`                       // id
	Code          string  `json:"code" gorm:"column:code"`                   // 物料代码
	Name          string  `json:"name" gorm:"column:name"`                   // 物料名称
	Specification string  `json:"specification" gorm:"column:specification"` // 物料规格
	Price         float64 `json:"price" gorm:"column:price"`                 // 价格
	PriceFirst    float64 `json:"price_first" gorm:"column:price_first"`     // 总代价格
	PriceSecond   float64 `json:"price_second" gorm:"column:price_second"`   // 二代价格
}

func (RepairMaterial) TableName() string {
	return "material"
}
