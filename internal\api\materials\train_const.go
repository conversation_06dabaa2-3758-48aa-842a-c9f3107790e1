package materials

import "strings"

type RoleType string

const (
	Endpoint   RoleType = "endpoint"   //终端店长
	Salesclerk RoleType = "salesclerk" //终端店员
)

// 定义英文角色到中文角色的映射
var roleEnglishToChinese = map[RoleType]string{
	Endpoint:   "终端店长",
	Salesclerk: "终端店员",
}

// StringToChineseRoleTypes 将包含多个角色的字符串转换为中文角色切片
func (roleStr RoleType) StringToChineseRoleTypes() string {
	var builder strings.Builder
	roles := strings.Split(roleStr.String(), ",")
	for _, role := range roles {
		role = strings.TrimSpace(role)
		if roleName, ok := roleEnglishToChinese[RoleType(role)]; ok {
			builder.WriteString(roleName)
		}
	}
	return builder.String()
}

func (roleStr RoleType) String() string {
	return string(roleStr)
}

func (roleStr RoleType) Range() []map[string]interface{} {
	// 初始化一个空的切片
	var rangeSlice []map[string]interface{}

	// 遍历角色类型到中文角色类型的映射
	for english, chinese := range roleEnglishToChinese {
		// 创建一个映射，存储英文角色类型和对应的中文描述
		roleMap := map[string]interface{}{
			"visible_roles": english,
			"name":          chinese,
		}
		// 将映射添加到切片中
		rangeSlice = append(rangeSlice, roleMap)
	}

	return rangeSlice
}

type CourseType string

const (
	CurseTypeLive   CourseType = "live"   //直播
	CurseTypeNormal CourseType = "normal" //普通
)

// 定义英文课程类型到中文课程类型的映射
var curseEnglishToChinese = map[CourseType]string{
	CurseTypeLive:   "直播",
	CurseTypeNormal: "普通",
}

// StringToChineseCurseType 将英文课程类型字符串转换为中文课程类型字符串
func (curseStr CourseType) StringToChineseCurseType() string {
	if chineseName, ok := curseEnglishToChinese[curseStr]; ok {
		return chineseName
	}
	return ""
}

func (curseStr CourseType) String() string {
	return string(curseStr)
}

func (curseStr CourseType) Range() []map[string]interface{} {
	// 初始化一个空的切片
	var rangeSlice []map[string]interface{}
	// 遍历课程类型到中文课程类型的映射
	for english, chinese := range curseEnglishToChinese {
		curseMap := map[string]interface{}{
			"type": english,
			"name": chinese,
		}
		rangeSlice = append(rangeSlice, curseMap)
	}

	return rangeSlice
}
