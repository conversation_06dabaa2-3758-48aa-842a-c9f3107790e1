package agency

import (
	"marketing/internal/handler/agency/reimbursement"

	"github.com/gin-gonic/gin"
)

type ReimbursementRouter struct {
	reimbursementHandler reimbursement.ReimbursementHandler
}

func NewReimbursementRouter() *ReimbursementRouter {
	reimbursementHandler := reimbursement.NewReimbursement()

	return &ReimbursementRouter{
		reimbursementHandler: reimbursementHandler,
	}
}

func (rr *ReimbursementRouter) Register(r *gin.RouterGroup) {
	// 首页
	r.GET("/homepage", rr.reimbursementHandler.GetHomePage)
	// 客户端汇总
	r.GET("/client-summary", rr.reimbursementHandler.GetClientSummary)
	// 所有政策
	r.GET("/policies", rr.reimbursementHandler.GetAllPolicies)
	// 客户端订单
	r.GET("/client-orders", rr.reimbursementHandler.GetClientOrders)
	// 促销品订单详情（未核销）
	r.GET("/promotional-products/detail", rr.reimbursementHandler.GetPromotionalProductsDetail)
	// 广告费用详情（未核销）
	r.GET("/advert-expense/detail", rr.reimbursementHandler.GetAdvertExpenseDetail)
	// 核销订单详情
	r.GET("/client-detail", rr.reimbursementHandler.GetClientDetail)
	// 政策详情
	r.GET("/policy/:id", rr.reimbursementHandler.GetPolicyDetail)
	// 广告费政策申请
	r.POST("/advert-expense/apply", rr.reimbursementHandler.ApplyAdvertExpense)
	//广告费政策修改
	r.PUT("/advert-expense/change", rr.reimbursementHandler.ChangeAdvertExpense)
	// 促销品政策申请
	r.POST("/promotional-products/apply", rr.reimbursementHandler.ApplyPromotionalProducts)
	// 促销品政策修改
	r.PUT("/promotional-products/change", rr.reimbursementHandler.ChangePromotionalProducts)
	// 促销品获取
	r.GET("/promotional-products", rr.reimbursementHandler.GetPromotionalProducts)
	// 促销品收货单上传
	r.PUT("/promotional-products/receipt", rr.reimbursementHandler.UploadReceipt)
	// 回寄（核销金额确认）
	r.PUT("/amount/confirm", rr.reimbursementHandler.AmountConfirm)
	// 创建联系人
	r.POST("/contact", rr.reimbursementHandler.CreateContact)
	// 获取联系人列表
	r.GET("/contacts", rr.reimbursementHandler.GetContacts)
	// 修改联系人
	r.PUT("/contact", rr.reimbursementHandler.UpdateContact)
	// 删除联系人
	r.DELETE("/contact", rr.reimbursementHandler.DeleteContact)
	// 设置默认联系人
	r.PUT("/contact/default", rr.reimbursementHandler.SetDefaultContact)
	// 获取默认联系人
	r.GET("/contact/default", rr.reimbursementHandler.GetDefaultContact)
}
