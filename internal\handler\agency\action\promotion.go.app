package action

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/handler"
	"marketing/internal/service"
	"strconv"
)

type PromotionHandle struct {
	svc service.PromotionService
}

func (h *PromotionHandle) GetPromotionList(c *gin.Context) {
	page, err := strconv.Atoi(<PERSON><PERSON>("page", "1"))
	pageSize, err := strconv.Atoi(<PERSON><PERSON>("page_size", "10"))
	model := c.Query("model_id")
	status := c.Query("status")
	id, err := strconv.Atoi(c.Query("promotion_id"))
	if err != nil {
		handler.Error(c, err)
		return
	}
	list, total, err := h.svc.GetJoinListByEndpoint(c, page, pageSize, model, status, id)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"list":      list,
		"total":     total,
		"page":      page,
		"page_size": pageSize,
	})
}

func (h *PromotionHandle) GetPromotionInfo(c *gin.Context) {
	id := c.Param("promotion_id")
	store, order, err := h.svc.GetJoinInfo(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"store": store,
		"order": order,
	})
}

func (h *PromotionHandle) Receipt(c *gin.Context) {
	param := c.Param("id")
	id, err := strconv.Atoi(param)
	if err != nil {
		handler.Error(c, err)
		return
	}
	receipt := c.Query("receipt")
	uid, ok := c.Get("uid")
	if !ok {
		handler.Error(c, err)
		return
	}
	uidInt, ok := uid.(int)
	if !ok {
		handler.Error(c, err)
		return
	}
	err = h.svc.Receipt(c, id, uidInt, receipt)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

func NewPromotionHandle(svc service.PromotionService) *PromotionHandle {
	return &PromotionHandle{svc: svc}
}
