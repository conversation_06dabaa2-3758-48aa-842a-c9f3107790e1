package system

import (
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"marketing/internal/api/system"
	"marketing/internal/handler"
	"marketing/internal/pkg/errors"
	system2 "marketing/internal/service/system"
)

type AdminMenuInterface interface {
	Add(c *gin.Context)
	Lists(c *gin.Context)
	Update(c *gin.Context)
	Delete(c *gin.Context)
}

type adminMenu struct {
	AdminMenuSvc system2.AdminMenuInterface
}

func NewAdminMenu(adminMenuSvc system2.AdminMenuInterface) AdminMenuInterface {
	return &adminMenu{
		AdminMenuSvc: adminMenuSvc,
	}
}

// Add 新增
func (a *adminMenu) Add(c *gin.Context) {
	var req system.AddMenuReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	err := a.AdminMenuSvc.Add(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

func (a *adminMenu) Lists(c *gin.Context) {
	var req system.AdminMenuReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}

	data, err := a.AdminMenuSvc.Lists(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, data)
}

func (a *adminMenu) Update(c *gin.Context) {
	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		handler.Error(c, errors.NewErr("id不能为空"))
		return
	}
	var req system.AddMenuReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	req.ID = id
	err := a.AdminMenuSvc.Update(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

func (a *adminMenu) Delete(c *gin.Context) {

	id := cast.ToInt(c.Param("id"))
	if id == 0 {
		handler.Error(c, errors.NewErr("id不能为空"))
		return
	}
	err := a.AdminMenuSvc.Delete(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}
