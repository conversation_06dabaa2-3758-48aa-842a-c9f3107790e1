package model

import (
	"time"
)

type CustomerInfo struct {
	GroupId         int    `json:"group_id" gorm:"group_id"`
	GroupParentid   int    `json:"group_parentid" gorm:"group_parentid"`
	GroupCode       string `json:"group_code" gorm:"group_code"`
	GroupName       string `json:"group_name" gorm:"group_name"`
	GroupParentName string `json:"group_parent_name" gorm:"group_parent_name"`
	GroupStatus     int    `json:"group_status" gorm:"group_status"`
	CustId          int    `json:"cust_id" gorm:"cust_id"`
	CustCode        string `json:"cust_code" gorm:"cust_code"`
	CustName        string `json:"cust_name" gorm:"cust_name"`
	CustShortName   string `json:"cust_short_name" gorm:"cust_short_name"`
	CustStatus      int    `json:"cust_status" gorm:"cust_status"`
	Channel         string `json:"channel" gorm:"channel"`
	ChannelName     string `json:"channel_name" gorm:"-"`
}

type V3Customer struct {
	GroupCode         string    `json:"group_code" gorm:"group_code"`
	GroupName         string    `json:"group_name" gorm:"group_name"`
	GroupSort         int       `json:"group_sort" gorm:"group_sort"`
	GroupStatus       int       `json:"group_status" gorm:"group_status"`
	CustomerId        int       `json:"cust_id" gorm:"cust_id"`
	CustomerCode      string    `json:"cust_code" gorm:"cust_code"`
	CustomerName      string    `json:"cust_name" gorm:"cust_name"`
	CustomerShortName string    `json:"cust_short_name" gorm:"cust_short_name"`
	CustomerSort      int       `json:"cust_sort" gorm:"cust_sort"`
	CustomerStatus    int       `json:"cust_status" gorm:"cust_status"`
	Channel           string    `json:"channel" gorm:"channel"`
	UpdateTime        time.Time `json:"update_time" gorm:"update_time"`
}

func (V3Customer) TableName() string {
	return "v3_customer"
}

type V3CustomerGroup struct {
	GroupId       int    `json:"group_id" gorm:"group_id"`
	GroupName     string `json:"group_name" gorm:"group_name"`
	GroupCode     string `json:"group_code" gorm:"group_code"`
	GroupParentid int    `json:"group_parentid" gorm:"group_parentid"`
	GroupSort     int    `json:"group_sort" gorm:"group_sort"`
	GroupStatus   int    `json:"group_status" gorm:"group_status"`
}

func (V3CustomerGroup) TableName() string {
	return "v3_customer_group"
}
