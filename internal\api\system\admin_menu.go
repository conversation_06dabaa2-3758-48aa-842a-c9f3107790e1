package system

// AddMenuReq 表单入参请求
type AddMenuReq struct {
	ID              uint   `json:"id"`                       // 菜单 ID
	ParentID        uint   `json:"parent_id"`                // 父级 ID
	MenuType        int8   `json:"menu_type"`                // 菜单类型
	Title           string `json:"title" binding:"required"` // 菜单标题
	Name            string `json:"name" binding:"required"`  // 菜单标识
	Path            string `json:"path" binding:"required"`  // 菜单路径
	Component       string `json:"component"`                // 组件
	Rank            uint   `json:"rank"`                     // 排名
	Redirect        string `json:"redirect"`                 // 重定向路径
	Icon            string `json:"icon"`                     // 图标
	ExtraIcon       string `json:"extra_icon"`               // 额外图标
	EnterTransition string `json:"enter_transition"`         // 进入过渡效果
	LeaveTransition string `json:"leave_transition"`         // 离开过渡效果
	ActivePath      string `json:"active_path"`              // 活动路径
	Permissions     string `json:"auths"`                    // 权限
	FrameSrc        string `json:"frame_src"`                // iframe 源地址
	FrameLoading    bool   `json:"frame_loading"`            // 是否加载 iframe
	KeepAlive       bool   `json:"keep_alive"`               // 是否保持活动
	HiddenTag       bool   `json:"hidden_tag"`               // 是否隐藏标签
	FixedTag        bool   `json:"fixed_tag"`                // 是否固定标签
	ShowLink        bool   `json:"show_link"`                // 是否显示链接
	ShowParent      bool   `json:"show_parent"`              // 是否显示父级
	SystemType      string `json:"system_type"`
}

// AdminMenuReq 查询入参
type AdminMenuReq struct {
	SystemType string `json:"system_type" form:"system_type" binding:"required,oneof=admin agency endpoint other"`
	Name       string `json:"name" form:"name"`
	Title      string `json:"title" form:"title"`
}

type AdminMenuResp struct {
	Id              int    `json:"id"`
	ParentId        int    `json:"parentId"`
	MenuType        int    `json:"menuType"` // 0: menu, 1: iframe, 2: external link, 3: button
	Title           string `json:"title"`
	Name            string `json:"name"`
	Path            string `json:"path"`
	Component       string `json:"component"`
	Rank            *int   `json:"rank"` // Use pointer to allow null values
	Redirect        string `json:"redirect"`
	Icon            string `json:"icon"`
	ExtraIcon       string `json:"extraIcon"`
	EnterTransition string `json:"enterTransition"`
	LeaveTransition string `json:"leaveTransition"`
	ActivePath      string `json:"activePath"`
	Permissions     string `json:"auths"`
	FrameSrc        string `json:"frameSrc"`
	FrameLoading    bool   `json:"frameLoading"`
	KeepAlive       bool   `json:"keepAlive"`
	HiddenTag       bool   `json:"hiddenTag"`
	FixedTag        bool   `json:"fixedTag"`
	ShowLink        bool   `json:"showLink"`
	ShowParent      bool   `json:"showParent"`
	SystemType      string `json:"system_type"`
}

// MenuItem 定义每个菜单项的结构体
type MenuItem struct {
	ID        uint        `json:"-"`    // 菜单项ID
	ParentID  uint        `json:"-"`    // 父级菜单项ID
	Path      string      `json:"path"` // 路由路径
	Component string      `json:"component"`
	Meta      MetaData    `json:"meta"`               // 元数据
	Children  []*MenuItem `json:"children,omitempty"` // 子菜单项，可以是多个层级
}

// MetaData 定义元数据的结构体
type MetaData struct {
	Icon        string   `json:"icon"`  // 图标
	Title       string   `json:"title"` // 菜单标题
	Rank        uint     `json:"rank"`  // 排名
	KeepAlive   bool     `json:"keepAlive"`
	ShowLink    bool     `json:"showLink"` // 是否显示链接
	Roles       []string `json:"roles,omitempty"`
	Permissions []string `json:"auths,omitempty"`
}
