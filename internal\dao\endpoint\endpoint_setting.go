package endpoint

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	api "marketing/internal/api/endpoint"
	"marketing/internal/model"
)

type SettingDao interface {
	List(c *gin.Context, param api.ListEndpointSettingReq) ([]*api.ListEndpointSettingRes, int64, error)
	GetByID(c *gin.Context, id uint) (*model.EndpointSetting, error)
	Update(c *gin.Context, id uint, updateData map[string]interface{}) error
}

type settingDao struct {
	db *gorm.DB
}

func NewSettingDao(db *gorm.DB) SettingDao {
	return &settingDao{db: db}
}

func (s *settingDao) List(c *gin.Context, param api.ListEndpointSettingReq) ([]*api.ListEndpointSettingRes, int64, error) {
	var list []*api.ListEndpointSettingRes
	var total int64

	query := s.db.WithContext(c).Model(&model.EndpointSetting{}).
		Select(`endpoint_setting.*, e.name as endpoint_name,e.phone,
         ta.name as top_agency_name,COALESCE(sa.name, '直营') as second_agency_name`).
		Joins("INNER JOIN endpoint as e ON e.id = endpoint_setting.endpoint_id").
		Joins("LEFT JOIN agency as ta ON e.top_agency = e.id").
		Joins("LEFT JOIN agency as sa ON e.second_agency = e.id")
	if param.TopAgency != 0 {
		query = query.Where("e.top_agency = ?", param.TopAgency)
	}
	if param.SecondAgency != 0 {
		query = query.Where("e.second_agency = ?", param.SecondAgency)
	}
	if param.Name != "" {
		query = query.Where("e.name LIKE ?", "%"+param.Name+"%")
	}
	if param.Code != "" {
		query = query.Where("e.code = ?", param.Code)
	}
	//total
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}
	//list
	offset := (param.Page - 1) * param.PageSize
	query = query.Offset(offset).Limit(param.PageSize)
	err = query.Find(&list).Error

	if err != nil {
		return nil, 0, err
	}

	return list, total, nil
}

func (s *settingDao) GetByID(c *gin.Context, id uint) (*model.EndpointSetting, error) {
	var setting model.EndpointSetting
	err := s.db.WithContext(c).Where("id =?", id).First(&setting).Error
	if err != nil {
		return nil, err
	}
	return &setting, nil
}

// Update 更新终端设置
func (s *settingDao) Update(c *gin.Context, id uint, updateData map[string]interface{}) error {
	return s.db.WithContext(c).Model(&model.EndpointSetting{}).Where("id =?", id).Updates(updateData).Error
}
