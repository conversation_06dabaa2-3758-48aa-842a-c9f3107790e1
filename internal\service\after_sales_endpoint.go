package service

import (
	"errors"
	"github.com/gin-gonic/gin"
	"marketing/internal/dao"
	"marketing/internal/model"
	"marketing/internal/pkg/utils"
)

type AfterSalesEndpointSvc interface {
	GetAfterSalesEndpointList(c *gin.Context, name string, pageNum, pageSize int) ([]*model.AfterSalesEndpoint, int64)
	GetAfterSalesEndpointByAgency(c *gin.Context, agencyId int) []*model.AfterSalesEndpoint
	GetAfterSalesEndpointById(c *gin.Context, id int) *model.AfterSalesEndpoint
	EditAfterSalesEndpoint(c *gin.Context, id int, name, phone, address string, topAgency, secondAgency, status int) error
	DeleteAfterSalesEndpoint(c *gin.Context, id int) error
}

type AfterSalesEndpointSvcImpl struct {
	agencyRepo   dao.AgencyDao
	endpointRepo dao.AfterSalesEndpointDao
}

func NewAfterSalesEndpointService(agencyRepo dao.AgencyDao, endpointRepo dao.AfterSalesEndpointDao) AfterSalesEndpointSvc {
	return &AfterSalesEndpointSvcImpl{
		agencyRepo:   agencyRepo,
		endpointRepo: endpointRepo,
	}
}

func (s *AfterSalesEndpointSvcImpl) GetAfterSalesEndpointList(c *gin.Context, name string, pageNum, pageSize int) ([]*model.AfterSalesEndpoint, int64) {
	list, total := s.endpointRepo.GetAfterSalesEndpointList(c, name, pageNum, pageSize)

	agencyIds := make([]int, 0)
	for _, l := range list {
		agencyIds = utils.AddIntArrayValue(agencyIds, l.TopAgencyId)
		agencyIds = utils.AddIntArrayValue(agencyIds, l.SecondAgencyId)
	}

	agencyList := s.agencyRepo.GetAgencyByIds(c, agencyIds)
	for _, l := range list {
		for _, agency := range agencyList {
			if len(l.TopAgencyName) > 0 && len(l.SecondAgencyName) > 0 {
				break
			}

			if agency.ID == uint(l.TopAgencyId) {
				l.TopAgencyName = agency.Name
			}

			if agency.ID == uint(l.SecondAgencyId) {
				l.SecondAgencyName = agency.Name
			}
		}
	}

	return list, total
}

func (s *AfterSalesEndpointSvcImpl) GetAfterSalesEndpointByAgency(c *gin.Context, agencyId int) []*model.AfterSalesEndpoint {
	return s.endpointRepo.GetAfterSalesEndpointByAgency(c, agencyId)
}

func (s *AfterSalesEndpointSvcImpl) GetAfterSalesEndpointById(c *gin.Context, id int) *model.AfterSalesEndpoint {
	endpoint := s.endpointRepo.GetAfterSalesEndpointById(c, id)

	// 查询代理列表
	agencyList := s.agencyRepo.GetAgencyByIds(c, []int{endpoint.TopAgencyId, endpoint.SecondAgencyId})
	for _, agency := range agencyList {
		if agency.ID == uint(endpoint.TopAgencyId) {
			endpoint.TopAgencyName = agency.Name
		}

		if agency.ID == uint(endpoint.SecondAgencyId) {
			endpoint.SecondAgencyName = agency.Name
		}
	}

	return endpoint
}

func (s *AfterSalesEndpointSvcImpl) EditAfterSalesEndpoint(c *gin.Context, id int, name, phone, address string, topAgency, secondAgency, status int) error {
	if id == 0 {
		return s.AddAfterSalesEndpoint(c, name, phone, address, topAgency, secondAgency, status)
	}

	endpoint := s.endpointRepo.GetAfterSalesEndpointById(c, id)
	if endpoint == nil {
		return errors.New("编辑售后终端:售后终端不存在")
	}

	agencyId := 0
	uMap := make(map[string]interface{}, 0)

	if topAgency == 0 && secondAgency == 0 {
		return errors.New("编辑售后终端:未选择代理")
	} else if secondAgency > 0 {
		agencyId = secondAgency
		uMap["agency_id"] = secondAgency
		uMap["belong_to_top_agency"] = 0
	} else {
		agencyId = topAgency
		uMap["agency_id"] = topAgency
		uMap["belong_to_top_agency"] = 1
	}

	if s.agencyRepo.GetAgencyById(c, uint(agencyId)) == nil {
		return errors.New("编辑售后终端:代理不存在")
	}

	uMap["name"] = name
	uMap["phone"] = phone
	uMap["address"] = address
	uMap["status"] = status
	uMap["top_agency_id"] = topAgency
	uMap["second_agency_id"] = secondAgency

	return s.endpointRepo.EditAfterSalesEndpoint(c, id, uMap)
}

func (s *AfterSalesEndpointSvcImpl) AddAfterSalesEndpoint(c *gin.Context, name, phone, address string, topAgency, secondAgency, status int) error {
	endpoint := new(model.AfterSalesEndpoint)
	endpoint.Name = name
	endpoint.Phone = phone
	endpoint.Address = address
	endpoint.Status = status
	endpoint.TopAgencyId = topAgency
	endpoint.SecondAgencyId = secondAgency

	if topAgency == 0 && secondAgency == 0 {
		return errors.New("创建售后终端:未选择代理")
	} else if secondAgency > 0 {
		endpoint.AgencyId = secondAgency
		endpoint.BelongToTopAgency = 0
	} else {
		endpoint.AgencyId = topAgency
		endpoint.BelongToTopAgency = 1
	}

	if s.agencyRepo.GetAgencyById(c, uint(endpoint.AgencyId)) == nil {
		return errors.New("创建售后终端:代理不存在")
	}

	return s.endpointRepo.CreateAfterSalesEndpoint(c, endpoint)
}

func (s *AfterSalesEndpointSvcImpl) DeleteAfterSalesEndpoint(c *gin.Context, id int) error {
	return s.endpointRepo.DeleteAfterSalesEndpoint(c, id)
}
