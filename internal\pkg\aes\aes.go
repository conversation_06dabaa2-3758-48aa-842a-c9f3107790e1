package aes

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"errors"
	"fmt"
	"marketing/internal/pkg/log"
	"net/url"
)

// DecryptAESParams AES解密参数
func DecryptAESParams(params url.Values, aesKey string, encryptedFields []string) (map[string]string, error) {
	decryptedParams := make(map[string]string)

	for _, field := range encryptedFields {
		encryptedValue := params.Get(field)

		if encryptedValue == "" {
			continue
		}
		// AES解密
		decryptedValue, err := aesDecrypt(encryptedValue, aesKey)
		if err != nil {
			log.Error(field + "解密失败:" + err.Error())
			return nil, fmt.Errorf("解密参数 %s 失败: %v", field, err)
		}

		decryptedParams[field] = decryptedValue
	}

	return decryptedParams, nil
}

// aesDecrypt AES解密函数（支持PKCS5/PKCS7填充）
func aesDecrypt(encryptedText, key string) (string, error) {
	// 检查密钥长度
	if len(key) != 16 {
		return "", fmt.Errorf("密钥长度必须为16字节")
	}
	// 将Base64编码的密文解码
	cipherBytes, err := base64.StdEncoding.DecodeString(encryptedText)
	if err != nil {
		return "", fmt.Errorf("base64解码失败: %v", err)
	}

	// 创建AES cipher
	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		return "", fmt.Errorf("创建AES cipher失败: %v", err)
	}

	// 初始化向量（IV）
	iv := make([]byte, aes.BlockSize)
	copy(iv, key[:aes.BlockSize]) // 示例中直接使用密钥前16个字节作为IV

	// 创建CBC模式解密器
	mode := cipher.NewCBCDecrypter(block, iv)

	// 解密
	plainBytes := make([]byte, len(cipherBytes))
	mode.CryptBlocks(plainBytes, cipherBytes)

	// 去除PKCS7填充
	plainBytes, err = pkcs7UnPad(plainBytes)
	if err != nil {
		return "", err
	}

	return string(plainBytes), nil
}

// pkcs7Unpad 去除PKCS5/PKCS7填充
func pkcs7UnPad(data []byte) ([]byte, error) {
	length := len(data)
	if length == 0 {
		return nil, errors.New("data is empty")
	}
	unpadding := int(data[length-1])
	if unpadding > length || unpadding == 0 {
		return nil, errors.New("unpadding size is incorrect")
	}

	// 验证填充
	for i := length - unpadding; i < length; i++ {
		if data[i] != byte(unpadding) {
			return nil, errors.New("填充验证失败")
		}
	}

	return data[:(length - unpadding)], nil
}

// AesEncrypt AES加密函数（支持PKCS5/PKCS7填充）
func AesEncrypt(plainText, key string) (string, error) {
	// 检查密钥长度
	if len(key) != 16 {
		return "", fmt.Errorf("密钥长度必须为16字节")
	}

	// 转换明文为字节数组
	plainBytes := []byte(plainText)

	// PKCS7填充
	plainBytes = pkcs7Pad(plainBytes, aes.BlockSize)

	// 创建AES cipher
	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		return "", fmt.Errorf("创建AES cipher失败: %v", err)
	}

	// 初始化向量（IV）
	iv := make([]byte, aes.BlockSize)
	copy(iv, key[:aes.BlockSize]) // 示例中直接使用密钥前16字节作为IV，实际使用中应生成随机IV

	// 创建CBC模式加密器
	mode := cipher.NewCBCEncrypter(block, iv)

	// 加密
	ciphertext := make([]byte, len(plainBytes))
	mode.CryptBlocks(ciphertext, plainBytes)

	// 返回仅包含密文的Base64编码
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// pkcs7Pad PKCS5/PKCS7填充
func pkcs7Pad(data []byte, blockSize int) []byte {
	padding := blockSize - len(data)%blockSize
	padText := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(data, padText...)
}
