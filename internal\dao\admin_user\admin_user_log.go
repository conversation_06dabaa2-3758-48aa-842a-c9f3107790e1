package admin_user

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"marketing/internal/model"
	"marketing/internal/pkg/log"
	"time"
)

type AdminUserLog interface {
	Add(c *gin.Context, uid uint, username, phone, OpType, remark string, data ...string)
	AddLogs(c *gin.Context, logs []model.AdminUserLog)
}
type adminUserLog struct {
	db *gorm.DB
}

func NewAdminUserLog(db *gorm.DB) AdminUserLog {
	return &adminUserLog{
		db: db,
	}
}
func (a *adminUserLog) Add(c *gin.Context, uid uint, username, phone, OpType, remark string, data ...string) {
	logInfo := model.AdminUserLog{
		UID:        int(uid),
		Username:   username,
		OpType:     OpType,
		Phone:      phone,
		Remark:     remark,
		Method:     c.Request.Method,
		Path:       c.<PERSON>(),
		Operator:   int(c.GetUint("uid")),
		OperatorIP: c.<PERSON>lientIP(),
		CreatedAt:  time.Now(),
	}
	if len(data) > 0 {
		logInfo.Request = data[0]
	}
	if len(data) > 1 {
		logInfo.Before = data[1]
	}
	if len(data) > 2 {
		logInfo.After = data[2]
	}
	if err := a.db.WithContext(c).Create(&logInfo).Error; err != nil {
		log.Error("添加用户日志失败", zap.Error(err), zap.Any("logInfo", logInfo))
	}
}

func (a *adminUserLog) AddLogs(c *gin.Context, logs []model.AdminUserLog) {
	for i := range logs {
		logs[i].CreatedAt = time.Now()
		logs[i].Method = c.Request.Method
		logs[i].Path = c.FullPath()
		logs[i].Operator = int(c.GetUint("uid"))
		logs[i].OperatorIP = c.ClientIP()
	}
	if err := a.db.WithContext(c).Create(&logs).Error; err != nil {
		log.Error("批量添加用户日志失败", zap.Error(err), zap.Any("logs", logs))
	}
}
