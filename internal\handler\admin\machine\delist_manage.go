package machine

import (
	"github.com/gin-gonic/gin"
	api "marketing/internal/api/materials"
	"marketing/internal/handler"
	"marketing/internal/pkg/e"
	"marketing/internal/pkg/errors"
	"marketing/internal/service/machine"
	"time"
)

// DelistManage 下市管理处理程序
type DelistManage struct {
	svc machine.DelistManageSvc
}

// NewDelistManage 创建下市管理处理程序
func NewDelistManage(svc machine.DelistManageSvc) *DelistManage {
	return &DelistManage{
		svc: svc,
	}
}

// GetMachineTypeList 获取机型列表
func (m *DelistManage) GetMachineTypeList(c *gin.Context) {
	var req api.DelistSearch
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	req.SetDefaults()
	list, total, err := m.svc.GetMachineTypeList(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, gin.H{
		"list":  list,
		"total": total,
	})
}

// UpdateDelistStatus 更新下市状态
func (m *DelistManage) UpdateDelistStatus(c *gin.Context) {
	type DelistRequest struct {
		ID         int    `json:"id" form:"id" binding:"required"`
		Delist     int    `json:"delist" form:"delist"`
		DelistTime string `json:"delist_time" form:"delist_time" binding:"oneof=0,1"`
	}

	var req DelistRequest
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, errors.NewErr("参数绑定失败"))
		return
	}

	id := req.ID
	delist := req.Delist
	delistTime := req.DelistTime
	// 解析下市时间
	var delistTimeParsed time.Time
	if delist == 1 && delistTime != "" {
		parsedTime, err := time.Parse("2006-01-02", delistTime)
		if err != nil {
			handler.Error(c, errors.NewErr("日期格式错误，请使用YYYY-MM-DD格式"))
			return
		}
		delistTimeParsed = parsedTime
	}

	// 验证输入
	if id <= 0 || (delist != 0 && delist != 1) {
		handler.Error(c, errors.NewErr("参数错误"))
		return
	}

	// 调用服务更新下市状态
	success, err := m.svc.UpdateDelistStatus(c, id, delist, delistTimeParsed)
	if !success {
		handler.Error(c, err)
		return
	}

	handler.Success(c)
}

// UpdateDeclareStatus 更新申报状态
func (m *DelistManage) UpdateDeclareStatus(c *gin.Context) {
	type DeclareRequest struct {
		ID      int `form:"id" json:"id" binding:"required"`
		Declare int `form:"mtr_declare" json:"mtr_declare" binding:"oneof=0,1"`
	}

	var req DeclareRequest
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, errors.NewErr("参数绑定失败"))
		return
	}

	id := req.ID
	declare := req.Declare

	// 验证输入
	if id <= 0 || (declare != 0 && declare != 1) {
		handler.Error(c, errors.NewErr("参数错误"))
		return
	}

	// 调用服务更新申报状态
	success, message := m.svc.UpdateDeclareStatus(c, id, declare)
	if !success {
		handler.Error(c, errors.NewErr(message))
		return
	}

	handler.Success(c)
}

// UpdateStockStatus 更新备货状态
func (m *DelistManage) UpdateStockStatus(c *gin.Context) {
	type DeclareRequest struct {
		ID    int `form:"id" json:"id" binding:"required"`
		Stock int `form:"mtr_stock" json:"mtr_stock" binding:"oneof=0,1"`
	}

	var req DeclareRequest
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, errors.NewErr("参数绑定失败"))
		return
	}

	id := req.ID
	stock := req.Stock

	// 验证输入
	if id <= 0 || (stock != 0 && stock != 1) {
		handler.Error(c, errors.NewErr("参数错误"))
		return
	}

	// 调用服务更新备货状态
	success, message := m.svc.UpdateStockStatus(c, id, stock)
	if !success {
		handler.Error(c, errors.NewErr(message))
		return
	}

	handler.Success(c)
}

// SendNotice 发送下市申报通知
// @Router /api/admin/delist_manage/send_notice [get]
func (m *DelistManage) SendNotice(c *gin.Context) {
	modelName := e.ReqParamStr(c, "model_name")
	if modelName == "" {
		handler.Error(c, errors.NewErr("机型名称不能为空"))
		return
	}

	status, info := m.svc.SendNotice(c, modelName)
	handler.Success(c, gin.H{
		"status": status,
		"info":   info,
	})
}
