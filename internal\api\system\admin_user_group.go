package system

import (
	"marketing/internal/api"
	"marketing/internal/pkg/types"
)

// AddUserGroupReq 添加权限组入参
type AddUserGroupReq struct {
	ID          uint   `json:"id" form:"id"`
	Name        string `json:"name" form:"name" binding:"required"`
	Slug        string `json:"slug" form:"slug" binding:"required"`
	Description string `json:"description" form:"description"`
	ResourceID  uint   `json:"resource_id" form:"resource_id"`
}

// AdminUserGroupReq 查询权限组入参
type AdminUserGroupReq struct {
	api.PaginationParams
	Name       string `json:"name" form:"name"`
	Slug       string `json:"slug" form:"slug"`
	ResourceID uint   `json:"resource_id" form:"resource_id"`
}

// AdminUserGroupResp 权限组
type AdminUserGroupResp struct {
	ID           uint             `json:"id" form:"id"`
	Name         string           `json:"name" form:"name"`
	Slug         string           `json:"slug" form:"slug"`
	Description  string           `json:"description" form:"description"`
	ResourceID   uint             `json:"resource_id" form:"resource_id"`
	ResourceName string           `json:"resource_name" form:"resource_name"`
	CreatedAt    types.CustomTime `json:"created_at"` // 创建时间
}

// AddUserToGroupReq 添加用户到用户组的请求参数
type AddUserToGroupReq struct {
	UserID      uint `json:"user_id" binding:"required"`
	UserGroupID uint `json:"user_group_id" binding:"required"`
}

type AddUsersToGroupReq struct {
	UserIDs     []uint `json:"user_ids" binding:"required"`
	UserGroupID uint   `json:"user_group_id" binding:"required"`
}
