package types

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
)

// JSONMap 自定义 JSON map 类型
type JSONMap map[string]interface{}

func (j JSONMap) Value() (driver.Value, error) {
	if len(j) == 0 {
		return nil, nil
	}
	bytes, err := json.Marshal(j)
	if err != nil {
		return nil, err
	}
	return string(bytes), nil
}

func (j *JSONMap) Scan(value interface{}) error {
	if value == nil {
		*j = JSONMap{}
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(bytes, j)
}

func (j *JSONMap) MarshalJSON() ([]byte, error) {
	if len(*j) == 0 {
		return []byte("{}"), nil
	}
	return json.Marshal(map[string]interface{}(*j))
}

func (j *JSONMap) UnmarshalJSON(data []byte) error {
	if string(data) == "null" {
		*j = make(JSONMap)
		return nil
	}
	var m map[string]interface{}
	if err := json.Unmarshal(data, &m); err != nil {
		return err
	}
	*j = JSONMap(m)
	return nil
}
