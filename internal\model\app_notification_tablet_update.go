package model

import (
	"marketing/internal/pkg/types"
)

// AppNotificationTabletUpdate 平板更新通知模型
type AppNotificationTabletUpdate struct {
	ID        int              `json:"id" gorm:"primaryKey;autoIncrement;column:id"`
	Type      string           `json:"type" gorm:"type:varchar(50);not null;comment:更新类型:sszb_course-双师直播课程更新，msfd_course-名师辅导课程更新，app-软件更新，paper-真题试卷，homework-作业本教辅;column:type"`
	Data      string           `json:"data" gorm:"type:text;comment:接口获取的数据，json格式;column:data"`
	UpdatedAt types.CustomTime `json:"updated_at" gorm:"comment:课程、app等更新时间，不是这条记录的更新时间;column:updated_at"`
	CreatedAt types.CustomTime `json:"created_at" gorm:"column:created_at"`
}

// TableName 设置表名
func (AppNotificationTabletUpdate) TableName() string {
	return "app_notification_tablet_update"
}
