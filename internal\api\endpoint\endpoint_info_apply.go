package endpoint

import (
	"marketing/internal/api"
	"marketing/internal/model"
	"marketing/internal/pkg/types"
)

// GetEndpointInfoApplyReq 查询终端档案申请列表请求
type GetEndpointInfoApplyReq struct {
	api.PaginationParams
	Month        string `json:"month" form:"month" binding:"required"`
	Name         string `json:"name" form:"name"`
	Code         string `json:"code" form:"code"`
	TopAgency    int    `json:"top_agency" form:"top_agency"`       // 一级代理商名称
	SecondAgency int    `json:"second_agency" form:"second_agency"` // 二级代理商名称
	Status       string `json:"status" form:"status"`               // 终端状态，updated 为更新，not_updated 为未更新
	StartTime    string `json:"start_time" form:"start_time"`
	EndTime      string `json:"end_time" form:"end_time"`
	// manual_to_audit: 人工待审, manual_approved: 人工审核通过, manual_rejected: 人工审核不通过, approved: 自动审核通过, rejected: 自动审核不通过
	AuditStatus string `json:"audit_status" form:"audit_status"`
}

// ListEndpointInfoApplyRes 终端档案申请列表响应
type ListEndpointInfoApplyRes struct {
	model.EndpointInfoApply
	Name             string           `json:"name"`
	Code             string           `json:"code"`
	Address          string           `json:"address"`
	TopAgency        int              `json:"top_agency"`    // 一级代理
	SecondAgency     int              `json:"second_agency"` // 二级代理
	TopAgencyName    string           `json:"-"`
	SecondAgencyName string           `json:"-"`
	AgencyName       string           `json:"agency_name"`
	AuditUser        string           `json:"audit_user"`
	EndpointTime     types.CustomTime `json:"endpoint_time"`
	EndpointAddress  string           `json:"endpoint_address"`
}

// AuditInfoApplyReq 审核请求
type AuditInfoApplyReq struct {
	ID           uint   `json:"id" binding:"required,min=1"`
	Status       string `json:"status" binding:"required,oneof=approved rejected"`
	AuditOpinion string `json:"audit_opinion" binding:"required_if=Status rejected,max=100"`
}
