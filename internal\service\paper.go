package service

import (
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"marketing/internal/api/materials"
	"marketing/internal/dao"
	"marketing/internal/model"
	myErrors "marketing/internal/pkg/errors"
	"strconv"
)

type PaperService interface {
	List(c *gin.Context, param materials.PaperListReq) ([]map[string]any, int64, error)
	Detail(c *gin.Context, id string) (map[string]any, error)
	Upsert(c *gin.Context, paper materials.PaperUpsertReq) (int, error)
	Delete(c *gin.Context, id string) error
	Status(c *gin.Context, param materials.PaperStatusReq) error
	QuestionTips(c *gin.Context) ([]materials.QuestionTips, error)
	QuestionList(c *gin.Context, param materials.QuestionListReq) ([]map[string]any, int64, error)
	QuestionDetail(c *gin.Context, id string) (map[string]any, error)
	QuestionDelete(c *gin.Context, id string) error
	QuestionUpsert(c *gin.Context, question materials.QuestionUpsertReq) (int, error)
	QuestionTypeTips(c *gin.Context) ([]materials.QuestionTypeTips, error)
}
type GormPaperService struct {
	dao dao.PaperDao
}

func (g *GormPaperService) QuestionTypeTips(c *gin.Context) ([]materials.QuestionTypeTips, error) {
	return g.dao.QuestionTypeTips(c)
}

func (g *GormPaperService) QuestionUpsert(c *gin.Context, question materials.QuestionUpsertReq) (int, error) {
	var param model.Question
	param.ID = uint(question.ID)
	param.Type = uint8(question.Type)
	param.Description = question.Description
	param.Detail = question.Detail

	switch question.Type {
	case 1:
		param.Answer = question.Answer[0]
		option, _ := MarshalOptions(question.Options)
		param.Options = option
	case 2:
		answers, _ := MarshalOptions(question.Answer)
		param.Answers = answers
		option, _ := MarshalOptions(question.Options)
		param.Options = option
	case 3:
		Blank, _ := MarshalOptions(question.Answer)
		param.Blank = Blank
	case 4:
		answer, err := strconv.Atoi(question.Answer[0])
		if err != nil {
			return 0, myErrors.NewErr("请输入正确的答案")
		}
		judgeValue := int8(answer)
		param.Judge = &judgeValue
	}

	if param.ID == 0 {
		return g.dao.QuestionCreate(c, param)
	}
	return g.dao.QuestionUpdate(c, param)
}

func (g *GormPaperService) QuestionDelete(c *gin.Context, id string) error {
	return g.dao.QuestionDelete(c, id)
}

func (g *GormPaperService) QuestionDetail(c *gin.Context, id string) (map[string]any, error) {
	//问题详情（ 问题内容加已选该题的试卷）
	detail, err := g.dao.QuestionDetail(c, id)
	if err != nil {
		return nil, err
	}
	//问题类型
	questionType, err := g.dao.QuestionType(c, detail.Type)
	if err != nil {
		return nil, err
	}
	//格式转换
	res := make(map[string]interface{})
	options, err := UnmarshalOptions(detail.Options)
	if err != nil {
		return nil, err
	}
	res["id"] = detail.ID
	res["type"] = questionType
	res["detail"] = detail.Detail
	res["options"] = options
	res["description"] = detail.Description
	res["created_at"] = detail.CreatedAt.Format("2006-01-02 15:04:05")
	res["updated_at"] = detail.UpdatedAt.Format("2006-01-02 15:04:05")
	switch questionType {
	case "单选题":
		res["answer"] = detail.Answer
	case "多选题":
		answer, err := UnmarshalOptions(detail.Answers)
		if err != nil {
			return nil, fmt.Errorf("[试卷管理-问题详情]：答案序列化失败: %w", err)
		}
		res["answer"] = answer
	case "判断题":
		res["answer"] = detail.Judge
	case "填空题":
		answer, err := UnmarshalOptions(detail.Blank)
		if err != nil {
			return nil, fmt.Errorf("[试卷管理-问题详情]：填空题答案序列化失败: %w", err)
		}
		res["answer"] = answer
	default:
		return nil, fmt.Errorf("[试卷管理-问题详情]：问题类型错误: %s", questionType)
	}
	res["paper"] = detail.Papers
	return res, nil
}

func (g *GormPaperService) QuestionList(c *gin.Context, param materials.QuestionListReq) ([]map[string]any, int64, error) {
	list, total, err := g.dao.QuestionList(c, param)
	if err != nil {
		return nil, 0, err
	}
	res := make([]map[string]any, len(list))
	for k, v := range list {
		//参数转换
		{
			res[k] = make(map[string]any)
			options, _ := UnmarshalOptions(v.Options)
			res[k]["id"] = v.ID
			res[k]["detail"] = v.Detail
			res[k]["question_type"] = v.QuestionType
			res[k]["description"] = v.Description
			res[k]["updated_at"] = v.UpdatedAt.Format("2006-01-02 15:04:05")
			res[k]["answer_num"] = v.AnswerNum
			res[k]["options"] = options
			if v.QuestionType == "单选题" {
				res[k]["answer"] = v.Answer
			} else if v.QuestionType == "多选题" {
				answer, _ := UnmarshalOptions(v.Answers)
				res[k]["answer"] = answer
			} else if v.QuestionType == "判断题" {
				judge := make([]string, 1)
				if v.Judge != nil {
					judge[0] = strconv.Itoa(int(*v.Judge))
				} else {
					judge[0] = "0"
				}
				res[k]["answer"] = judge
			} else {
				blank, _ := UnmarshalOptions(v.Blank)
				res[k]["answer"] = blank
			}
		}
	}
	return res, total, err
}

func (g *GormPaperService) QuestionTips(c *gin.Context) ([]materials.QuestionTips, error) {
	return g.dao.QuestionTips(c)

}

func (g *GormPaperService) Status(c *gin.Context, param materials.PaperStatusReq) error {
	detail, err := g.dao.Detail(c, strconv.Itoa(param.ID))
	if err != nil {
		return err
	}
	count, err := g.dao.QuestionCount(c, param.ID)
	if err != nil {
		return err
	}
	if detail.Number > count {
		return myErrors.NewErr("题目数不够，禁止发布")
	}
	return g.dao.UpdateStatus(c, param)
}

func (g *GormPaperService) Delete(c *gin.Context, id string) error {
	return g.dao.Delete(c, id)
}

func (g *GormPaperService) Upsert(c *gin.Context, paper materials.PaperUpsertReq) (int, error) {
	//1.题目数不够,不许发布
	if len(paper.Questions) < paper.Paper.Number && paper.Paper.Status == 1 {
		return 0, myErrors.NewErr("题目数不够，禁止发布")
	}
	//2.更新或创建
	if paper.Paper.ID == 0 {
		return g.dao.Create(c, paper)
	}
	return g.dao.Update(c, paper)
}

func (g *GormPaperService) Detail(c *gin.Context, id string) (map[string]any, error) {
	//查询试卷详情
	detail, err := g.dao.Detail(c, id)
	if err != nil {
		return map[string]any{}, err
	}
	//查询试卷下的题目
	questions, err := g.dao.QuestionDetailByPaper(c, id)
	if err != nil {
		return map[string]any{}, err
	}
	detailMap := make(map[string]interface{})
	//参数转换
	{
		detailMap["id"] = detail.ID
		detailMap["name"] = detail.Name
		detailMap["description"] = detail.Description
		detailMap["duration"] = detail.Duration
		detailMap["status"] = detail.Status
		detailMap["creator"] = detail.Creator
		detailMap["start"] = detail.Start.Format("2006-01-02 15:04:05") // 格式化时间
		detailMap["end"] = detail.End.Format("2006-01-02 15:04:05")     // 格式化时间
		detailMap["number"] = detail.Number
		detailMap["created_at"] = detail.CreatedAt.Format("2006-01-02 15:04:05") // 格式化时间
		detailMap["updated_at"] = detail.UpdatedAt.Format("2006-01-02 15:04:05") // 格式化时间
		detailMap["questions"] = questions
	}
	return detailMap, err
}

func (g *GormPaperService) List(c *gin.Context, param materials.PaperListReq) ([]map[string]any, int64, error) {
	list, total, err := g.dao.List(c, param)
	if err != nil {
		return nil, 0, err
	}
	lists := make([]map[string]any, len(list))
	for k, v := range list {
		lists[k] = make(map[string]any)
		//参数转换
		{
			lists[k]["id"] = v.ID
			lists[k]["name"] = v.Name
			lists[k]["description"] = v.Description
			lists[k]["duration"] = v.Duration
			lists[k]["status"] = v.Status
			lists[k]["start"] = v.Start.Format("2006-01-02 15:04:05") // 格式化时间
			lists[k]["end"] = v.End.Format("2006-01-02 15:04:05")     // 格式化时间
			lists[k]["number"] = v.Number
			lists[k]["had_num"] = v.HadNum
			lists[k]["updated_at"] = v.UpdatedAt.Format("2006-01-02 15:04:05") // 格式化时间
			lists[k]["had_endpoint"] = v.HadEndpoint
			lists[k]["had_sales_man"] = v.HadSalesman
		}
	}
	return lists, total, err
}

func NewGormPaperService(dao dao.PaperDao) PaperService {
	return &GormPaperService{dao: dao}
}

func UnmarshalOptions(options string) ([]string, error) {
	var optionsSlice []string
	err := json.Unmarshal([]byte(options), &optionsSlice)
	if err != nil {
		return nil, err
	}
	return optionsSlice, nil
}
func MarshalOptions(options []string) (string, error) {
	data, err := json.Marshal(options)
	if err != nil {
		return "", err
	}
	return string(data), nil
}
