package system

import (
	"github.com/gin-gonic/gin"
	api "marketing/internal/api/system"
	"marketing/internal/dao"
	"marketing/internal/model"
	appError "marketing/internal/pkg/errors"
	"time"
)

type ResourceGroupService interface {
	Create(ctx *gin.Context, group *api.ResourceGroupReq) error
	Update(ctx *gin.Context, group *api.ResourceGroupReq) error
	Delete(ctx *gin.Context, id uint) error
	Get(ctx *gin.Context, id uint) (*model.ResourceGroup, error)
	List(ctx *gin.Context, params api.ListResourceGroupReq) ([]*api.ResourceGroupsResp, int64, error)
	ListResourceGroupsNoPage(c *gin.Context, name string) ([]*model.ResourceGroup, error)
	Sync(ctx *gin.Context, id uint) error
}

type resourceGroupService struct {
	repo     dao.ResourceGroupRepository
	wecomSvc WecomSyncService
}

func NewResourceGroupSvc(repo dao.ResourceGroupRepository, wecomSvc WecomSyncService) ResourceGroupService {
	return &resourceGroupService{
		repo:     repo,
		wecomSvc: wecomSvc,
	}
}

func (s *resourceGroupService) Create(ctx *gin.Context, param *api.ResourceGroupReq) error {
	exists, err := s.repo.ExistsByName(ctx, param.Name)
	if err != nil {
		return err
	}
	if exists {
		return appError.NewErr("资源组已经存在")
	}
	group := &model.ResourceGroup{
		Name:        param.Name,
		Description: param.Description,
	}
	return s.repo.Create(ctx, group)
}

func (s *resourceGroupService) Update(ctx *gin.Context, param *api.ResourceGroupReq) error {
	old, err := s.repo.Get(ctx, param.ID)
	if err != nil {
		return err
	}

	if param.Name != old.Name {
		exists, err := s.repo.ExistsByName(ctx, param.Name)
		if err != nil {
			return err
		}
		if exists {
			return appError.NewErr("资源组已经存在")
		}
	}

	old.Name = param.Name
	old.Description = param.Description

	return s.repo.Update(ctx, old)
}

func (s *resourceGroupService) Delete(ctx *gin.Context, id uint) error {
	return s.repo.Delete(ctx, id)
}

func (s *resourceGroupService) Get(ctx *gin.Context, id uint) (*model.ResourceGroup, error) {
	return s.repo.Get(ctx, id)
}

func (s *resourceGroupService) List(ctx *gin.Context, params api.ListResourceGroupReq) ([]*api.ResourceGroupsResp, int64, error) {
	data, total, err := s.repo.List(ctx, params)
	if err != nil {
		return nil, 0, err
	}
	var resp []*api.ResourceGroupsResp
	for _, v := range data {
		resp = append(resp, &api.ResourceGroupsResp{
			ID:          v.ID,
			Name:        v.Name,
			Description: v.Description,
			QWPartyID:   *v.QWPartyID,
			QWName:      *v.QWName,
			CreatedAt:   v.CreatedAt.Format(time.DateTime),
			UpdatedAt:   v.UpdatedAt.Format(time.DateTime),
		})
	}
	return resp, total, nil
}

func (s *resourceGroupService) ListResourceGroupsNoPage(ctx *gin.Context, name string) ([]*model.ResourceGroup, error) {
	return s.repo.ListResourceGroupsNoPage(ctx, name)

}

func (s *resourceGroupService) Sync(ctx *gin.Context, id uint) error {
	var params api.ListResourceGroupReq
	params.ID = id
	data, _, err := s.repo.List(ctx, params)
	if err != nil {
		return err
	}
	for _, v := range data {
		err = s.wecomSvc.SyncResourceGroupDepartments(ctx, *v.QWName, 1, int(*v.QWPartyID))
		if err != nil {
			return err
		}
	}
	return nil
}
