# 营销中台

营销中台v2

## 项目启动
根目录目录下，执行下面的命令
```
go run ./cmd/server
```
---
## 项目部署
### 测试服
- 代码目前部署到阿里云k8s容器上，测试域名：https://marketing-api-test.readboy.com/
- 代码库在 https://gitlab.readboy.com/readboy/marketing
- 部署用使用gitlab ci流水线自动部署，测试服合并到test分支就会自动打包部署到k8s上

---

## 项目结构
```
.
├── cmd                  # 命令行工具目录
│   ├── docs             # 文档生成相关命令
│   ├── server           # 服务器启动相关命令
│   └── task             # 定时任务相关命令
├── conf                 # 配置文件目录
├── public               # 公共资源目录，如静态文件
└── internal             # 内部包目录，包含业务逻辑
    ├── api              # 对外提供服务（API）的输入/输出数据结构定义
    │   ├── action       # 动作相关的 API
    │   ├── auth         # 认证与授权相关的 API
    │   └── system       # 系统管理相关的 API
    ├── consts           # 常量定义目录
    ├── dao              # 数据访问对象，负责数据库，缓存操作
    ├── global           # 全局变量与设置
    ├── handler          # 接收/解析用户输入参数的入口/接口层
    │   ├── admin        # 【管理系统】相关的处理程序（API入口）
    │   │   └── agency   # 渠道管理相关的处理程序
    │   ├── agency       # 【经销商系统】相关的处理程序（API入口）
    ├── middleware       # 中间件定义
    ├── model            # 模型定义，一般指程序内部用到的数据模型和数据库模型定义
    ├── pkg              # 常用的包跟业务逻辑无关的包
    │
    └── router           # 路由定义目录
        └── admin        # 管理员路由配置，继承admin.go
        ├── router.go    # 负责总体路由初始化
        ├── admin.go     #【管理系统】路由组设置，管理端功能模块较多，可以拆分到admin文件夹里面，按模块细分
        ├── agency.go    #【经销商系统】路由组设置，功能模块不多可以一个文件搞定
    ├── service          # 
```
### 项目架构概述

根据公司内部开发规范 [读书郎开发规范](https://inner-doc.readboy.com/pages/1846ef/#%E5%88%86%E5%B1%82%E6%9E%B6%E6%9E%84 "分层架构") 的要求，项目结构分为四层：

1. **模型层（Models）**
    - **定义**：即实体层（Entities），封装结构及其方法，可供其它层引用。
    - **用途**：
        - 作为数据库模型或 API 请求模型。
        - 若创建资源时的属性、资源保存在数据库中的属性、返回资源的属性三者一致，使用同一个模型：
            - **优点**：代码更简洁、易维护，并提高开发效率。
        - 否则，另建模型适配不同需求。

2. **控制层（Controller）**
    - **在本项目中命名为 `handler`**。
    - **职责**：
        - 接收 HTTP 请求。
        - 进行参数解析与校验。
        - 逻辑分发处理。
        - 请求返回操作。
    - **工作流程**：
        1. 接收来自客户端的请求。
        2. 解析并校验请求参数。
        3. 将请求逻辑分发给业务层处理。
        4. 处理完毕后，将业务层返回的数据进行整合与再加工。
        5. 最终返回响应给客户端。
    - **注意事项**：
        - 实现业务路由功能时，应保持逻辑简单，避免过于复杂。

3. **业务层（Service）**
    - **职责**：
        - 完成所有业务逻辑处理。
        - 处理来自控制层的请求。
        - 根据需要，调用仓库层完成数据的 CRUD 操作。

4. **仓库层（Repository）**
    - **在本项目中命名为 `dao`**。
    - **职责**：
        - 与数据库或第三方服务进行 CRUD 交互。
        - 作为应用程序的数据引擎，负责数据的输入和输出。
        - 实现数据转换：
            - 将来自数据库或微服务的数据转换为业务层和控制层所需的格式。


### 跨层调用要求

- **实例传递**：
    - 调用层必须包含被调用层的实例。
        - **示例**：
            - 创建控制层实例时，传入业务层的实例。
            - 创建业务层实例时，传入仓库层的实例。
- **数据库操作**：
    - 通过仓库层实例的 `db` 字段（`*gorm.DB` 类型），完成数据库的 CRUD 操作。

### 导入关系

项目层级之间的包导入关系如下：

- **模型层（Models）**
    - 可被 **仓库层（dao）**、**业务层（Service）** 和 **控制层（Controller）** 导入。

- **控制层（Controller）**
    - 可导入 **业务层（Service）** 和 **仓库层（dao）** 包。
    - **最佳实践**：
        - 如果没有特殊需求，控制层应避免直接导入仓库层的包。
        - 控制层需要完成的业务功能都通过业务层来实现，使代码逻辑更加清晰、规范。

- **业务层（Service）**
    - 可导入 **仓库层（dao）** 包。

### api层说明
- 在项目结构中还有个api文件夹，也是数据模型定义，跟model 的区别
- 在 api 中定义的数据结构是 对外使用的，与具体的业务场景绑定（如具体的页面交互逻辑、单一的接口功能），数据结构是由上层展示层前置决定的； 
- model 中定义的数据结构是 服务内部公共使用的，并且 model 中的数据结构可以随意在内部修改而并不会影响对外 api 接口的兼容性。
- 注意 model 中的数据结构不应该直接暴露给外部使用，一旦将 model 中的数据结构应用到了 api 层中，内部 model 数据结构的修改会直接影响到 api 接口的兼容性
- 如果两者出现重复的数据结构（甚至常量、枚举类型），建议将数据结构定义到 api 层中。服务内部逻辑可以直接访问 api 层的数据结构。 model 层的数据结构也可以直接引用 api 层的数据结构，但是反之则不行。

### 层与层之间的关系
![img.png](public/img.png)

## 统一响应
- 在handler目录下response.go，写了两个方法,Success 和 Error，在接口返回的可以调用这两个函数，统一的响应格式
- 大道至简，成功 ok 1 ，失败 ok 0，如果想要返回错误码，可以自己再封装一下
- 为了确保系统的安全性和稳定性，防止敏感信息泄露，除了自定义需要展示给用户的错误信息外，所有内部产生的错误都不应该直接返回到前端，在response已经封装了非自定义的错误统一返回未知错误

## 入参校验
- 不要相信用户的输入
- 模型绑定和验证，gin 绑定参数到结构体的 ShouldBind 方法绑定时，在结构体上加上标签 `binding:"required"`
  会自行进行参数校验,文档 [https://gin-gonic.com/zh-cn/docs/examples/binding-and-validation](https://gin-gonic.com/zh-cn/docs/examples/binding-and-validation/)
- 校验规则参考 [https://github.com/go-playground/validator] (https://github.com/go-playground/validator)

## 读书郎开发规范
 - [读书郎开发规范后端](https://inner-doc.readboy.com/pages/04cdf3/#%E4%B8%80%E3%80%81%E9%9C%80%E6%B1%82)
 - 文档账号密码 readboy  readboy13579

# 欢迎您提出宝贵的意见和建议