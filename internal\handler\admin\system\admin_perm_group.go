package system

import (
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"marketing/internal/api/system"
	"marketing/internal/handler"
	"marketing/internal/pkg/errors"
	system2 "marketing/internal/service/system"
)

type AdminPermGroupInterface interface {
	Add(c *gin.Context)
	Lists(c *gin.Context)
	Update(c *gin.Context)
	Delete(c *gin.Context)
}
type adminPermGroup struct {
	AdminPermGroupSvc system2.AdminPermGroupInterface
}

func NewAdminPermGroup(adminPermGroupSvc system2.AdminPermGroupInterface) AdminPermGroupInterface {
	return &adminPermGroup{
		AdminPermGroupSvc: adminPermGroupSvc,
	}
}

// Add 新增
func (a *adminPermGroup) Add(c *gin.Context) {
	var req system.AddPermissionGroupReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	err := a.AdminPermGroupSvc.Add(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

func (a *adminPermGroup) Lists(c *gin.Context) {
	var req system.AdminPermissionReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	req.PaginationParams.SetDefaults()
	data, err := a.AdminPermGroupSvc.Lists(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, data)
}

func (a *adminPermGroup) Update(c *gin.Context) {
	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		handler.Error(c, errors.NewErr("id不能为空"))
		return
	}
	var req system.AddPermissionGroupReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	req.ID = id
	err := a.AdminPermGroupSvc.Update(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

func (a *adminPermGroup) Delete(c *gin.Context) {

	id := cast.ToInt(c.Param("id"))
	if id == 0 {
		handler.Error(c, errors.NewErr("id不能为空"))
		return
	}
	err := a.AdminPermGroupSvc.Delete(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}
