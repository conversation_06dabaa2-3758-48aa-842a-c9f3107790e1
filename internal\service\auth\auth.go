package auth

import (
	"errors"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"marketing/internal/api/auth"
	"marketing/internal/config"
	"marketing/internal/consts"
	"marketing/internal/model"
	appError "marketing/internal/pkg/errors"
	"marketing/internal/pkg/log"
	"marketing/internal/pkg/utils"
	"marketing/internal/pkg/wecom"
)

// ServiceInterface 定义了用户认证的接口
type ServiceInterface interface {
	Login(ctx *gin.Context, req auth.LoginReq) (*auth.TokenUserInfoResp, error)
	WeComLogin(ctx *gin.Context, code string) (*auth.TokenUserInfoResp, error)
	PhoneLogin(ctx *gin.Context, req auth.PhoneLoginReq) (*auth.TokenUserInfoResp, error)
	CheckPermissionByUrlMethod(ctx *gin.Context) bool
	GetUID(ctx *gin.Context) uint
	RefreshToken(ctx *gin.Context, req auth.RefreshTokenReq) (*auth.TokenUserInfoResp, error)
	ValidateToken(c *gin.Context, token string) (*utils.Claims, error)
	GetAppSystemByKey(c *gin.Context, xGateType string) (*model.AppSystemV2, error)
}

type authService struct {
	cfg *config.Config
	*CoreAuthService
}

func NewAuthService(cfg *config.Config,
	coreAuthService *CoreAuthService,
) ServiceInterface {
	return &authService{
		cfg:             cfg,
		CoreAuthService: coreAuthService,
	}
}

func (svc *authService) Login(c *gin.Context, req auth.LoginReq) (*auth.TokenUserInfoResp, error) {
	// 1、检查系统类型
	xGateType, err := svc.checkSystemType(c)
	if err != nil {
		return nil, err
	}
	// 2. 验证用户基本信息
	user, err := svc.validateUser(c, req)
	if err != nil {
		return nil, err
	}
	// 3. 验证用户是否有系统访问权限
	_, err = svc.validateAccessibleSystem(c, xGateType, user)
	if err != nil {
		return nil, err
	}
	// 4. 生成 token
	return svc.generateToken(c, user, xGateType)
}

func (svc *authService) WeComLogin(c *gin.Context, code string) (*auth.TokenUserInfoResp, error) {
	// 1、检查系统类型
	xGateType, err := svc.checkSystemType(c)
	if err != nil {
		return nil, err
	}
	appSystem, err := svc.getAppSystemByKey(c, xGateType)
	if err != nil || appSystem == nil {
		return nil, appError.NewErr("系统不存在")
	}
	//判断是否是子应用
	if appSystem.Parent != "" {
		appSystem, err = svc.getAppSystemByKey(c, appSystem.Parent)
		if err != nil {
			return nil, appError.NewErr("系统不存在")
		}
	}
	// 获取企微客户端
	var weComClient *wecom.Client
	weComClient = wecom.NewWeComClient(appSystem.CorpID, appSystem.CorpSecret)

	//2、根据用户名判断用户是否可用
	qwUserID, err := weComClient.GetUserIDByCode(code)
	if err != nil {
		return nil, err
	}

	var user *model.AdminUsers
	if xGateType == consts.AdminPrefix {
		//根据企微工号查出户，用employ_user表
		user, err = svc.userRepo.GetByJobNumber(c, qwUserID)
	} else {
		user, err = svc.userRepo.GetByQwUserID(c, qwUserID)
	}

	if user == nil || errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, appError.NewErr("用户不存在或者没绑定企微，请在浏览器用手机号登录之后，点开右上角的头像，点击扫码绑定企微，然后再扫码登录1")
	}
	if err != nil {
		return nil, appError.NewErr("用户不存在或者没绑定企微，请在浏览器用手机号登录之后，点开右上角的头像，点击扫码绑定企微，然后再扫码登录2")
	}
	if *user.Status != 1 {
		return nil, appError.NewErr("用户已被禁用")
	}
	// 3. 验证用户是否有系统访问权限
	_, err = svc.validateAccessibleSystem(c, xGateType, user)
	if err != nil {
		return nil, err
	}
	// 4. 生成 token
	return svc.generateToken(c, user, xGateType)
}

func (svc *authService) PhoneLogin(c *gin.Context, req auth.PhoneLoginReq) (*auth.TokenUserInfoResp, error) {
	xGateType, err := svc.checkSystemType(c)
	if err != nil {
		return nil, err
	}

	user, err := svc.validatePhone(c, req)
	if err != nil {
		return nil, err
	}

	_, err = svc.validateAccessibleSystem(c, xGateType, user)
	if err != nil {
		return nil, err
	}

	return svc.generateToken(c, user, xGateType)
}

func (svc *authService) GetUID(c *gin.Context) uint {
	return svc.getUID(c)
}

func (svc *authService) RefreshToken(c *gin.Context, req auth.RefreshTokenReq) (*auth.TokenUserInfoResp, error) {
	systemType, err := svc.checkSystemType(c)
	if err != nil {
		return nil, err
	}
	appSystem, err := svc.getAppSystemByKey(c, systemType)
	if err != nil {
		return nil, appError.NewErr("系统不存在")
	}
	if appSystem.Parent != "" {
		appSystem, err = svc.getAppSystemByKey(c, appSystem.Parent)
		if err != nil {
			return nil, appError.NewErr("系统不存在")
		}
	}
	_, err = utils.ValidateToken(req.RefreshToken, appSystem.JwtKey)
	if err != nil {
		return nil, err
	}
	return svc.refreshToken(c, req.RefreshToken, appSystem.AppKey)
}

func (svc *authService) CheckPermissionByUrlMethod(c *gin.Context) bool {
	token := c.GetString("token")
	// 调用 ValidateToken 来验证 token
	systemType, err := svc.checkSystemType(c)
	appSystem, err := svc.getAppSystemByKey(c, systemType)
	if err != nil {
		log.Error("CheckPermissionByUrlMethod token 验证失败1", zap.Error(err))
		return false
	}
	log.Debug("appSystem-parent", zap.String("appSystem-parent", appSystem.Parent))
	if appSystem.Parent != "" {
		appSystem, err = svc.getAppSystemByKey(c, appSystem.Parent)
		if err != nil {
			log.Error("CheckPermissionByUrlMethod token 验证失败2", zap.Error(err))
			return false
		}
		systemType = appSystem.AppKey // 数据使用父系统的 appKey
	}

	claims, err := utils.ValidateToken(token, appSystem.JwtKey)
	if err != nil || claims == nil {
		log.Error("CheckPermissionByUrlMethod token 验证失败3", zap.Error(err))
		return false
	}
	uid := claims.UserID

	u, err := svc.userCache.GetUserAllData(c, uid, systemType)
	if err != nil {
		log.Error("获取用户数据失败", zap.Error(err))
		return false
	}
	if u == nil {
		u, err = svc.userRepo.GetUserWithAll(c, uid, systemType)
		if err != nil {
			return false
		}
		if err := svc.userCache.SetUser(c, u, consts.UserCacheExpiration, systemType); err != nil {
			log.Error("用户数据写入缓存失败", zap.Error(err))
			// 缓存写入失败不影响返回
		}
	}
	c.Set("resource_group", u.ResourceGroup)
	c.Set("IsSuperAdmin", u.IsSuperAdmin())
	return svc.checkPermissionByUrlMethod(c, u)
}

func (svc *authService) ValidateToken(c *gin.Context, token string) (*utils.Claims, error) {
	systemType, err := svc.checkSystemType(c)
	appSystem, err := svc.getAppSystemByKey(c, systemType)
	if err != nil {
		log.Error("Validate token 验证失败1", zap.Error(err))
		return nil, err
	}
	log.Debug("appSystem-parent", zap.String("appSystem-parent", appSystem.Parent))
	if appSystem.Parent != "" {
		appSystem, err = svc.getAppSystemByKey(c, appSystem.Parent)
		if err != nil {
			log.Error("Validate token 验证失败2", zap.Error(err))
			return nil, err
		}
	}
	claims, err := utils.ValidateToken(token, appSystem.JwtKey)
	if err != nil || claims == nil {
		log.Error("Validate token 验证失败3", zap.Error(err))
		return nil, err
	}

	return claims, nil
}

func (svc *authService) GetAppSystemByKey(c *gin.Context, xGateType string) (*model.AppSystemV2, error) {
	return svc.getAppSystemByKey(c, xGateType)
}

func (svc *authService) checkSystemType(c *gin.Context) (string, error) {
	xGateType := c.GetHeader("x-gate-type")
	if xGateType == "" {
		return "", appError.NewErr("请选择系统类型")
	}
	// 检查系统类型
	appSystem, err := svc.getAppSystemByKey(c, xGateType)
	if err != nil || appSystem == nil {
		return "", appError.NewErr("系统类型错误")
	}

	return xGateType, nil
}
