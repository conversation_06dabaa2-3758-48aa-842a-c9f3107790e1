package prototype

import (
	api "marketing/internal/api/prototype"
	"marketing/internal/consts"
	"marketing/internal/handler"
	appError "marketing/internal/pkg/errors"
	service "marketing/internal/service/prototype"

	"github.com/gin-gonic/gin"
)

type PrototypeHandler interface {
	GetType(c *gin.Context)
	GetPrototype(c *gin.Context)
	PrototypeOut(c *gin.Context)
	PrototypeStat(c *gin.Context)
	ImportRenewData(c *gin.Context)
	GetPrototypeModelList(c *gin.Context)
	GetPrototypeDemoDownList(c *gin.Context)
	AddPrototypeDemoDown(c *gin.Context)
}

type prototypeHandler struct {
	service service.PrototypeService
}

func NewPrototypeHandler(service service.PrototypeService) PrototypeHandler {
	return &prototypeHandler{
		service: service,
	}
}

func (h *prototypeHandler) GetPrototype(c *gin.Context) {
	var req api.PrototypeReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	req.SetDefaults()
	res, err := h.service.GetPrototype(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, res)
}

func (h *prototypeHandler) GetType(c *gin.Context) {
	data := consts.GetPrototypeTypeSlice()
	handler.Success(c, data)
}

func (h *prototypeHandler) GetPrototypeModelList(c *gin.Context) {
	res, err := h.service.GetPrototypeModelList(c)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, res)
}

// PrototypeOut 离库
func (h *prototypeHandler) PrototypeOut(c *gin.Context) {
	var req api.PrototypeOutReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	if req.Barcode == "" {
		handler.Error(c, appError.NewErr("barcode is required"))
		return
	}
	if err := h.service.PrototypeOut(c, req.Barcode); err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

// PrototypeStat 样机统计
func (h *prototypeHandler) PrototypeStat(c *gin.Context) {
	var req api.PrototypeReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	req.SetDefaults()
	res, err := h.service.PrototypeStat(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, res)
}

// ImportRenewData 导入金蝶总代换新样机数量
func (h *prototypeHandler) ImportRenewData(c *gin.Context) {
	file, err := c.FormFile("file")
	if err != nil {
		handler.Error(c, appError.NewErr("获取上传的文件失败"))
		return
	}

	if file.Size < 32 {
		handler.Error(c, appError.NewErr("文件太小了"))
		return
	}

	msg, err := h.service.ImportRenewData(c, file)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, msg)
}

func (h *prototypeHandler) GetPrototypeDemoDownList(c *gin.Context) {
	var req api.PrototypeConfigListSearch
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	req.SetDefaults()
	res, total, err := h.service.GetPrototypeDemoDownList(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"list":  res,
		"total": total,
	})
}

func (h *prototypeHandler) AddPrototypeDemoDown(c *gin.Context) {
	var req api.PrototypeConfigAddReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	if err := h.service.AddPrototypeDemoDown(c, &req); err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}
