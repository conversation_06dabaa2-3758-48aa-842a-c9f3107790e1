package dao

import (
	"gorm.io/gorm"
	"marketing/internal/pkg/db"
	"marketing/internal/pkg/utils"
	"time"
)

// Partition 表示分区的数据结构
type Partition struct {
	ID         uint      `json:"id" gorm:"primaryKey;autoIncrement"`  // ID 是主键
	Name       string    `json:"name" binding:"required" gorm:"name"` // Name 是分区的名称
	CreatedAt  time.Time `json:"-" gorm:"created_at"`                 // CreatedAt 是分区的创建时间
	CreateTime string    `json:"create_time" gorm:"-"`                // 创建时间
	UpdatedAt  time.Time `json:"-" gorm:"updated_at"`                 // UpdatedAt 是分区的修改时间
	UpdateTime string    `json:"update_time" gorm:"-"`                // 修改时间
}

func (Partition) TableName() string {
	return "partition"
}

// PartitionDao 用于访问分区数据的对象
type PartitionDao struct {
	db *gorm.DB // 数据库连接实例
}

// NewPartitionDao 创建并返回一个新的 PartitionDAO 实例
func NewPartitionDao() *PartitionDao {
	return &PartitionDao{db: db.GetDB("rbcare")} // 获取 "rbcare" 数据库连接
}

// FindPartitions 查找符合条件的分区记录
func (dao *PartitionDao) FindPartitions(name string, pageNum, pageSize int) ([]Partition, int64) {
	query := dao.db.Model(&Partition{})
	if name != "" {
		query = query.Where("name LIKE ?", "%"+name+"%")
	}

	var partitions []Partition // 分区结果的切片
	data, total := utils.PaginateQueryV1(query, pageNum, pageSize, &partitions)

	for i, p := range *data {
		(*data)[i].CreateTime = utils.GetTimeStr(p.CreatedAt)
		(*data)[i].UpdateTime = utils.GetTimeStr(p.UpdatedAt)
	}

	return *data, total
}

// CreatePartition 创建新的分区记录
func (dao *PartitionDao) CreatePartition(partition *Partition) error {
	return dao.db.Create(partition).Error
}

// UpdatePartition 更新现有的分区记录
func (dao *PartitionDao) UpdatePartition(id int, name string) error {
	return dao.db.Model(&Partition{}).Where("id = ?", id).Update("name", name).Error
}

// DeletePartition 删除指定 ID 的分区记录
func (dao *PartitionDao) DeletePartition(id int) error {
	var partition Partition
	// 根据 ID 查找分区记录
	if err := dao.db.Model(&Partition{}).First(&partition, id).Error; err != nil {
		return err
	}
	// 删除找到的分区记录
	if err := dao.db.Model(&Partition{}).Delete(&partition).Error; err != nil {
		return err
	}
	return nil
}

func (dao *PartitionDao) GetPartitionByID(id int) (*Partition, error) {
	var partition Partition
	if err := dao.db.Model(&Partition{}).First(&partition, id).Error; err != nil {
		return nil, err
	}
	return &partition, nil
}

func (dao *PartitionDao) GetPartitionByIds(ids []int) (list []*Partition) {
	dao.db.Model(&Partition{}).Where("id in (?)", ids).Find(&list)
	return
}

func (dao *PartitionDao) GetAllPartition() (list []*Partition) {
	dao.db.Model(&Partition{}).Find(&list)
	return
}
