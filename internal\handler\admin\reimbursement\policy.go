package reimbursement

import (
	"encoding/json"
	"github.com/spf13/cast"
	api "marketing/internal/api/reimbursement"
	"marketing/internal/handler"
	"marketing/internal/model"
	"marketing/internal/pkg/errors"
	"marketing/internal/pkg/types"
	service "marketing/internal/service/reimbursement"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

type PolicyHandler interface {
	GetPolicyList(c *gin.Context)
	CreatePolicy(c *gin.Context)
	GetPolicyDetail(c *gin.Context)
	UpdatePolicy(c *gin.Context)
	ArchivePolicy(c *gin.Context)
	GetApproveUserList(c *gin.Context)
}

type policy struct {
	service service.PolicyService
}

func NewPolicy(service service.PolicyService) PolicyHandler {
	return &policy{
		service: service,
	}
}

func (p *policy) GetPolicyList(c *gin.Context) {
	var req api.PolicyListSearch
	err := c.ShouldBind(&req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	policies, total, err := p.service.GetPolicyList(&req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"total": total,
		"data":  policies,
	})
}

func (p *policy) GetPolicyDetail(c *gin.Context) {
	// Get policy ID from URL parameter
	policyID := c.Param("id")
	if policyID == "" {
		handler.Error(c, errors.NewErr("政策ID不能为空"))
		return
	}

	// Call service to get policy detail
	data, err := p.service.GetPolicyDetail(cast.ToInt(policyID))
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, data)
}

func (p *policy) CreatePolicy(c *gin.Context) {
	var req api.PolicyCreateReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}

	// Validate required fields
	if req.Name == "" {
		handler.Error(c, errors.NewErr("政策名不能为空"))
		return
	}
	if req.StartTime == "" || req.EndTime == "" {
		handler.Error(c, errors.NewErr("开始时间或结束时间不能为空"))
		return
	}
	if req.Type == 0 {
		handler.Error(c, errors.NewErr("类型不能为空"))
		return
	}
	if req.Type < 1 || req.Type > 5 {
		handler.Error(c, errors.NewErr("类型错误"))
		return
	}

	// Set policy type and reimbursement type based on type
	var policyType string
	var reimbursementType int
	var standardType string

	if req.Type == 1 || req.Type == 2 {
		policyType = "promotional_products"
	} else if req.Type == 3 {
		policyType = "advert_expense"
	} else {
		policyType = ""
	}

	if req.Type == 1 {
		reimbursementType = 3
		standardType = "standard_amount"
	} else if req.Type == 2 {
		reimbursementType = 1
		standardType = "standard_quantity"
	} else if req.Type == 3 {
		reimbursementType = 4
		standardType = "standard_balance_quota"
	} else {
		reimbursementType = 0
		standardType = ""
	}

	// Validate additional fields for certain types
	if req.Type != 4 {
		if req.UserName == "" {
			handler.Error(c, errors.NewErr("用户名不能为空"))
			return
		}
		if req.Phone == "" {
			handler.Error(c, errors.NewErr("手机号不能为空"))
			return
		}
		if req.Province == "" {
			handler.Error(c, errors.NewErr("省份不能为空"))
			return
		}
		if req.City == "" {
			handler.Error(c, errors.NewErr("城市不能为空"))
			return
		}
		if req.District == "" {
			handler.Error(c, errors.NewErr("地区不能为空"))
			return
		}
		if req.Address == "" {
			handler.Error(c, errors.NewErr("详细地址不能为空"))
			return
		}
		if req.Explain == "" {
			handler.Error(c, errors.NewErr("详细说明不能为空"))
			return
		}
	}

	if req.Declare != 0 && req.Declare != 1 {
		handler.Error(c, errors.NewErr("是否可申报参数错误"))
		return
	}

	// Set default values if not provided
	if req.LookOver == "" {
		req.LookOver = "all"
	}
	if req.Declare == 0 {
		req.Declare = 1
	}

	// Create policy model
	startTime, _ := time.Parse("2006-01-02", req.StartTime)
	endTime, _ := time.Parse("2006-01-02", req.EndTime)

	policy := model.ReimbursementPolicy{
		Name:              req.Name,
		StartTime:         types.DateOnly(startTime),
		EndTime:           types.DateOnly(endTime),
		PolicyType:        policyType,
		ReimbursementType: reimbursementType,
		UserName:          req.UserName,
		Phone:             req.Phone,
		Province:          req.Province,
		City:              req.City,
		District:          req.District,
		Address:           req.Address,
		Explain:           req.Explain,
		StandardType:      standardType,
		Type:              req.Type,
		Content:           req.Content,
		Remark:            req.Remark,
		Declare:           req.Declare,
		LookOver:          req.LookOver,
		ApproveUids:       req.ApproveUids,
		ApproveApplyUids:  req.ApproveApplyUids,
		GiftLimit:         req.GiftLimit,
	}

	// Process products info if provided
	var productsInfo []model.PromotionalProduct
	if len(req.ProductsInfo) > 0 {
		for _, product := range req.ProductsInfo {
			// Validate product fields
			if product.Name == "" {
				handler.Error(c, errors.NewErr("产品名字不能为空"))
				return
			}
			if product.Norm == "" {
				handler.Error(c, errors.NewErr("产品规格不能为空"))
				return
			}
			if product.Unit == "" {
				handler.Error(c, errors.NewErr("产品单位不能为空"))
				return
			}
			if product.IncludeTaxPrice == 0 && product.ExcludeTaxPrice == 0 {
				handler.Error(c, errors.NewErr("顾客价格不能为空"))
				return
			}
			if product.ReimbursementPrice == 0 {
				handler.Error(c, errors.NewErr("核销价格不能为空"))
				return
			}
			if len(product.IncludeTaxAccountInfo) == 0 && len(product.ExcludeTaxAccountInfo) == 0 {
				handler.Error(c, errors.NewErr("账户信息不能为空"))
				return
			}
			if len(product.CommunicationLetter) == 0 {
				handler.Error(c, errors.NewErr("售后交流函不能为空"))
				return
			}
			if len(product.Preview) == 0 {
				handler.Error(c, errors.NewErr("预览图不能为空"))
				return
			}

			// Validate file metadata
			for _, file := range product.IncludeTaxAccountInfo {
				if file.Name == "" || file.URL == "" {
					handler.Error(c, errors.NewErr("含税账户信息文件名或URL不能为空"))
					return
				}
			}
			for _, file := range product.ExcludeTaxAccountInfo {
				if file.Name == "" || file.URL == "" {
					handler.Error(c, errors.NewErr("不含税账户信息文件名或URL不能为空"))
					return
				}
			}
			for _, file := range product.CommunicationLetter {
				if file.Name == "" || file.URL == "" {
					handler.Error(c, errors.NewErr("售后交流函文件名或URL不能为空"))
					return
				}
			}
			for _, file := range product.Preview {
				if file.Name == "" || file.URL == "" {
					handler.Error(c, errors.NewErr("预览图文件名或URL不能为空"))
					return
				}
			}

			// Convert arrays to JSON strings
			includeTaxAccountInfo, _ := json.Marshal(product.IncludeTaxAccountInfo)
			excludeTaxAccountInfo, _ := json.Marshal(product.ExcludeTaxAccountInfo)
			communicationLetter, _ := json.Marshal(product.CommunicationLetter)
			preview, _ := json.Marshal(product.Preview)

			// Create promotional product
			promotionalProduct := model.PromotionalProduct{
				Name:                  product.Name,
				Norm:                  product.Norm,
				Unit:                  product.Unit,
				IncludeTaxPrice:       product.IncludeTaxPrice,
				ExcludeTaxPrice:       product.ExcludeTaxPrice,
				ReimbursementPrice:    product.ReimbursementPrice,
				IncludeTaxAccountInfo: string(includeTaxAccountInfo),
				ExcludeTaxAccountInfo: string(excludeTaxAccountInfo),
				CommunicationLetter:   string(communicationLetter),
				Preview:               string(preview),
			}
			productsInfo = append(productsInfo, promotionalProduct)
		}
	}

	// Call service to create policy
	id, err := p.service.CreatePolicyWithProducts(policy, productsInfo)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, gin.H{
		"id": id,
	})
}

func (p *policy) UpdatePolicy(c *gin.Context) {
	var req api.PolicyUpdateReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}

	// Validate required fields
	if req.ID <= 0 {
		handler.Error(c, errors.NewErr("政策ID不能为空"))
		return
	}
	if req.Name == "" {
		handler.Error(c, errors.NewErr("政策名不能为空"))
		return
	}
	if req.StartTime == "" || req.EndTime == "" {
		handler.Error(c, errors.NewErr("开始时间或结束时间不能为空"))
		return
	}

	// Get existing policy to check type and other fields
	existingPolicyData, err := p.service.GetPolicyDetail(req.ID)
	if err != nil {
		handler.Error(c, err)
		return
	}
	existingPolicy := existingPolicyData.Policy

	// Validate additional fields for certain types
	if existingPolicy.Type != 4 {
		if req.UserName == "" {
			handler.Error(c, errors.NewErr("用户名不能为空"))
			return
		}
		if req.Phone == "" {
			handler.Error(c, errors.NewErr("手机号不能为空"))
			return
		}
		if req.Province == "" {
			handler.Error(c, errors.NewErr("省份不能为空"))
			return
		}
		if req.City == "" {
			handler.Error(c, errors.NewErr("城市不能为空"))
			return
		}
		if req.District == "" {
			handler.Error(c, errors.NewErr("地区不能为空"))
			return
		}
		if req.Address == "" {
			handler.Error(c, errors.NewErr("详细地址不能为空"))
			return
		}
		if req.Explain == "" {
			handler.Error(c, errors.NewErr("详细说明不能为空"))
			return
		}
	}

	if req.Declare != 0 && req.Declare != 1 {
		handler.Error(c, errors.NewErr("是否可申报参数错误"))
		return
	}

	// Set default values if not provided
	if req.LookOver == "" {
		req.LookOver = "all"
	}
	if req.Declare == 0 {
		req.Declare = 1
	}

	// Create policy model for update
	startTime, _ := time.Parse("2006-01-02", req.StartTime)
	endTime, _ := time.Parse("2006-01-02", req.EndTime)

	policy := model.ReimbursementPolicy{
		ID:               req.ID,
		Name:             req.Name,
		StartTime:        types.DateOnly(startTime),
		EndTime:          types.DateOnly(endTime),
		UserName:         req.UserName,
		Phone:            req.Phone,
		Province:         req.Province,
		City:             req.City,
		District:         req.District,
		Address:          req.Address,
		Explain:          req.Explain,
		Content:          req.Content,
		Remark:           req.Remark,
		Declare:          req.Declare,
		LookOver:         req.LookOver,
		ApproveUids:      req.ApproveUids,
		ApproveApplyUids: req.ApproveApplyUids,
		GiftLimit:        req.GiftLimit,
		// Keep existing values for these fields
		PolicyType:        existingPolicy.PolicyType,
		ReimbursementType: existingPolicy.ReimbursementType,
		StandardType:      existingPolicy.StandardType,
		Type:              existingPolicy.Type,
		Archive:           existingPolicy.Archive,
		ArchiveTime:       existingPolicy.ArchiveTime,
	}

	// Process products info if provided and policy type is promotional_products
	var productsInfo []model.PromotionalProduct
	if existingPolicy.PolicyType == "promotional_products" && len(req.ProductsInfo) > 0 {
		for _, product := range req.ProductsInfo {
			// Validate product fields
			if product.Name == "" {
				handler.Error(c, errors.NewErr("产品名字不能为空"))
				return
			}
			if product.Norm == "" {
				handler.Error(c, errors.NewErr("产品规格不能为空"))
				return
			}
			if product.Unit == "" {
				handler.Error(c, errors.NewErr("产品单位不能为空"))
				return
			}
			if product.IncludeTaxPrice == 0 && product.ExcludeTaxPrice == 0 {
				handler.Error(c, errors.NewErr("顾客价格不能为空"))
				return
			}
			if product.ReimbursementPrice == 0 {
				handler.Error(c, errors.NewErr("核销价格不能为空"))
				return
			}
			if len(product.IncludeTaxAccountInfo) == 0 && len(product.ExcludeTaxAccountInfo) == 0 {
				handler.Error(c, errors.NewErr("账户信息不能为空"))
				return
			}
			if len(product.CommunicationLetter) == 0 {
				handler.Error(c, errors.NewErr("售后交流函不能为空"))
				return
			}
			if len(product.Preview) == 0 {
				handler.Error(c, errors.NewErr("预览图不能为空"))
				return
			}

			// Validate file metadata
			for _, file := range product.IncludeTaxAccountInfo {
				if file.Name == "" || file.URL == "" {
					handler.Error(c, errors.NewErr("含税账户信息文件名或URL不能为空"))
					return
				}
			}
			for _, file := range product.ExcludeTaxAccountInfo {
				if file.Name == "" || file.URL == "" {
					handler.Error(c, errors.NewErr("不含税账户信息文件名或URL不能为空"))
					return
				}
			}
			for _, file := range product.CommunicationLetter {
				if file.Name == "" || file.URL == "" {
					handler.Error(c, errors.NewErr("售后交流函文件名或URL不能为空"))
					return
				}
			}
			for _, file := range product.Preview {
				if file.Name == "" || file.URL == "" {
					handler.Error(c, errors.NewErr("预览图文件名或URL不能为空"))
					return
				}
			}

			// Convert arrays to JSON strings
			includeTaxAccountInfo, _ := json.Marshal(product.IncludeTaxAccountInfo)
			excludeTaxAccountInfo, _ := json.Marshal(product.ExcludeTaxAccountInfo)
			communicationLetter, _ := json.Marshal(product.CommunicationLetter)
			preview, _ := json.Marshal(product.Preview)

			// Create promotional product
			promotionalProduct := model.PromotionalProduct{
				Name:                  product.Name,
				Norm:                  product.Norm,
				Unit:                  product.Unit,
				IncludeTaxPrice:       product.IncludeTaxPrice,
				ExcludeTaxPrice:       product.ExcludeTaxPrice,
				ReimbursementPrice:    product.ReimbursementPrice,
				IncludeTaxAccountInfo: string(includeTaxAccountInfo),
				ExcludeTaxAccountInfo: string(excludeTaxAccountInfo),
				CommunicationLetter:   string(communicationLetter),
				Preview:               string(preview),
			}
			productsInfo = append(productsInfo, promotionalProduct)
		}
	}

	// Call service to update policy
	err = p.service.UpdatePolicyWithProducts(policy, productsInfo)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, gin.H{
		"id": req.ID,
	})
}

func (p *policy) ArchivePolicy(c *gin.Context) {
	var req api.PolicyArchiveReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}

	// Validate required fields
	if req.ID <= 0 {
		handler.Error(c, errors.NewErr("政策ID不能为空"))
		return
	}

	// Validate archive value
	if req.Archive != 0 && req.Archive != 1 {
		handler.Error(c, errors.NewErr("归档状态参数错误"))
		return
	}

	// Call service to archive policy
	err := p.service.ArchivePolicy(req.ID, req.Archive)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, gin.H{
		"id": req.ID,
	})
}

// GetApproveUserList 获取有审核权限的用户列表
func (p *policy) GetApproveUserList(c *gin.Context) {
	// 获取类型参数
	typeParam := c.Query("type")
	typeInt := 0
	if typeParam != "" {
		var err error
		typeInt, err = strconv.Atoi(typeParam)
		if err != nil {
			handler.Error(c, errors.NewErr("类型参数错误"))
			return
		}
	}

	// 验证类型参数
	if typeInt != 1 && typeInt != 2 {
		handler.Error(c, errors.NewErr("类型错误"))
		return
	}

	// 根据类型确定权限标识
	var slug string
	if typeInt == 1 {
		slug = "h5-reimbursement-audit" // 核销审核权限
	} else {
		slug = "h5-reimbursement-apply-audit" // 核销申请审核权限
	}

	// 调用服务获取用户列表
	list, err := p.service.GetApproveUserList(slug)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, list)
}
