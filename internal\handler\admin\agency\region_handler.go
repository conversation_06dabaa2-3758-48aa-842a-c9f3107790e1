package agency

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/dao"
	"marketing/internal/handler"
	"marketing/internal/pkg/e"
	"marketing/internal/pkg/errors"
	"marketing/internal/service"
)

// GetRegions 获取行政区域信息
func GetRegions(c *gin.Context) {
	parentId := e.ReqParamInt(c, "parent_id")
	regionType := e.ReqParamInt(c, "region_type")
	regionName := e.ReqParamStr(c, "region_name")
	pageNum := e.ReqParamInt(c, "page_num")
	pageSize := e.ReqParamInt(c, "page_size")

	list, total := service.GetAllRegionTrees(parentId, regionType, regionName, pageNum, pageSize)

	handler.Success(c, gin.H{
		"list":  list,
		"total": total,
	})
}

func EditRegion(c *gin.Context) {
	err := service.EditRegion(&dao.Region{
		RegionId:          e.ReqParamInt(c, "region_id"),
		ParentId:          e.ReqParamInt(c, "parent_id"),
		RegionName:        e.ReqParamStr(c, "region_name"),
		ShortName:         e.ReqParamStr(c, "short_name"),
		RegionLevel:       e.ReqParamStr(c, "region_level"),
		CityLevel:         e.ReqParamInt(c, "city_level"),
		PrimarySchoolsNum: e.ReqParamInt64(c, "primary_schools_num", -1),
		PupilsNum:         e.ReqParamInt64(c, "pupils_num", -1),
		MiddleSchoolsNum:  e.ReqParamInt64(c, "middle_schools_num", -1),
		JuniorsNum:        e.ReqParamInt64(c, "juniors_num", -1),
		Lng:               e.ReqParamStr(c, "lng"),
		Lat:               e.ReqParamStr(c, "lat"),
		Remark:            e.ReqParamStr(c, "remark"),
	})
	if err != nil {
		handler.Error(c, errors.NewErr("编辑区域失败"))
		return
	}

	handler.Success(c, gin.H{})
}

func DeleteRegion(c *gin.Context) {
	err := service.DeleteRegion(e.ReqParamInt(c, "region_id"))
	if err != nil {
		handler.Error(c, errors.NewErr("删除区域失败"))
		return
	}

	handler.Success(c, gin.H{})
}
