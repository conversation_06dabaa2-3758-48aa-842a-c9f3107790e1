package agency

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/handler/agency/mine"
)

type MineRouter struct {
	mineHandler mine.MineInterface
}

func NewMineRouter(mineHandler mine.MineInterface) *MineRouter {
	return &MineRouter{
		mineHandler: mineHandler,
	}
}

func (m *MineRouter) Register(r *gin.RouterGroup) {
	mineRouter := r.Group("mine")
	{
		mineRouter.PUT("", m.mineHandler.Update)
		mineRouter.POST("/reset-password", m.mineHandler.ResetPassword)
	}
}
