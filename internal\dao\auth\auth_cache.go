package auth

import (
	"errors"
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
	v9 "github.com/redis/go-redis/v9"
)

const _TokenMap string = "marketing_token_map:%d" // uid到token的映射

// AuthCacheInterface 定义 AuthCacheInterface 接口
type AuthCacheInterface interface {
	SetToken(ctx *gin.Context, token string, uid int64, expireDuration time.Duration) error
	GetToken(ctx *gin.Context, token string) (int64, error)
	DeleteTokenByUID(ctx *gin.Context, uid int64) error
}

// AuthCache 修改 AuthCache 结构体，新增 prefix 字段
type AuthCache struct {
	redisClient *v9.Client
}

// NewAuthCache 修改 NewAuthCache 构造函数
func NewAuthCache(redisClient *v9.Client) AuthCacheInterface { // 返回接口类型
	return &AuthCache{
		redisClient: redisClient,
	}
}

// SetToken 修改为使用 String 和 Hash 存储
func (a *AuthCache) SetToken(ctx *gin.Context, token string, uid int64, expireDuration time.Duration) error {
	// 清理该用户的过期token
	tokenMapKey := fmt.Sprintf(_TokenMap, uid)
	tokens, err := a.redisClient.HKeys(ctx, tokenMapKey).Result()
	if err == nil && len(tokens) > 0 {
		for _, oldToken := range tokens {
			exists, err := a.redisClient.Exists(ctx, oldToken).Result()
			if err != nil {
				continue
			}
			if exists == 0 {
				// 删除过期的token记录
				a.redisClient.HDel(ctx, tokenMapKey, oldToken)
			}
		}
	}

	// 存储 token -> uid 映射，并设置过期时间
	if err := a.redisClient.Set(ctx, token, uid, expireDuration).Err(); err != nil {
		return err
	}

	// 存储 uid -> token 映射
	if err := a.redisClient.HSet(ctx, tokenMapKey, token, time.Now().Unix()).Err(); err != nil {
		return err
	}

	return nil
}

// GetToken 从 String 中获取
func (a *AuthCache) GetToken(ctx *gin.Context, token string) (int64, error) {
	uid, err := a.redisClient.Get(ctx, token).Int64()
	if errors.Is(err, v9.Nil) {
		return 0, nil
	}
	return uid, err
}

// DeleteTokenByUID 删除指定 uid 的所有 token
func (a *AuthCache) DeleteTokenByUID(ctx *gin.Context, uid int64) error {
	tokenMapKey := fmt.Sprintf(_TokenMap, uid)
	tokens, err := a.redisClient.HKeys(ctx, tokenMapKey).Result()
	if err != nil {
		return err
	}

	for _, token := range tokens {
		if err := a.redisClient.Del(ctx, token).Err(); err != nil {
			return err
		}
	}

	return a.redisClient.Del(ctx, tokenMapKey).Err()
}
