package admin

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/dao"
	"marketing/internal/handler/admin/youke"
	"marketing/internal/pkg/db"
	"marketing/internal/service"
)

type YouKeRouter struct{}

func NewYouKeRouter() *YouKeRouter {
	return &YouKeRouter{}
}

func (y *YouKeRouter) Register(r *gin.RouterGroup) {
	YouKeRouter := r.Group("youke")
	{
		YKDao := dao.NewGormYoukeCourse(db.DB)
		YKService := service.NewGormYkCourseService(YKDao)
		YKController := youke.NewCourseHandle(YKService)
		//优课直播课
		//查询接口
		YouKeRouter.POST("/live/list", YKController.LiveList)
	}
}
