package model

import "gorm.io/gorm"

type AdminMenuV2 struct {
	ID              uint           `gorm:"primaryKey;autoIncrement" json:"id" binding:"required"`                      // 菜单 ID
	ParentID        uint           `gorm:"not null;default:0" json:"parent_id" binding:"required"`                     // 父级 ID
	MenuType        int8           `gorm:"not null" json:"menu_type" binding:"required"`                               // 菜单类型
	Title           string         `gorm:"not null" json:"title" binding:"required"`                                   // 菜单标题
	Name            string         `gorm:"not null;uniqueIndex:admin_menu_name_unique" json:"name" binding:"required"` // 菜单标识
	Path            string         `gorm:"not null" json:"path" binding:"required"`                                    // 菜单路径
	Component       string         `gorm:"type:varchar(255)" json:"component"`                                         // 组件
	Rank            uint           `gorm:"not null" json:"rank" binding:"required"`                                    // 排名
	Redirect        string         `gorm:"type:varchar(255)" json:"redirect"`                                          // 重定向路径
	Icon            string         `gorm:"type:varchar(100)" json:"icon"`                                              // 图标
	ExtraIcon       string         `gorm:"type:varchar(100)" json:"extra_icon"`                                        // 额外图标
	EnterTransition string         `gorm:"type:varchar(100)" json:"enter_transition"`                                  // 进入过渡效果
	LeaveTransition string         `gorm:"type:varchar(100)" json:"leave_transition"`                                  // 离开过渡效果
	ActivePath      string         `gorm:"type:varchar(255)" json:"active_path"`                                       // 活动路径
	Permissions     string         `gorm:"type:varchar(255)" json:"permissions"`                                       // 权限
	FrameSrc        string         `gorm:"type:varchar(255)" json:"frame_src"`                                         // iframe 源地址
	FrameLoading    bool           `gorm:"not null;default:true" json:"frame_loading" binding:"required"`              // 是否加载 iframe
	KeepAlive       bool           `gorm:"not null;default:false" json:"keep_alive"`                                   // 是否保持活动
	HiddenTag       bool           `gorm:"not null;default:false" json:"hidden_tag"`                                   // 是否隐藏标签
	FixedTag        bool           `gorm:"not null;default:false" json:"fixed_tag"`                                    // 是否固定标签
	ShowLink        bool           `gorm:"not null;default:true" json:"show_link"`                                     // 是否显示链接
	ShowParent      bool           `gorm:"not null;default:false" json:"show_parent"`                                  // 是否显示父级
	SystemType      string         `gorm:"column:system_type;type:enum('admin','agency','endpoint');default:'admin';comment:菜单类型，admin为管理端菜单,agency为经销商系统菜单，endpoint为终端菜单" json:"type"`
	CreatedAt       string         `gorm:"type:datetime;not null;default:CURRENT_TIMESTAMP" json:"created_at"`                             // 创建时间
	UpdatedAt       string         `gorm:"type:datetime;not null;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP" json:"updated_at"` // 修改时间
	DeletedAt       gorm.DeletedAt `gorm:"type:datetime" json:"deleted_at,omitempty"`                                                      // 删除时间，可选
}

// TableName 设置表的别名
func (AdminMenuV2) TableName() string {
	return "admin_menus_v2"
}
