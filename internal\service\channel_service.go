package service

import (
	"errors"
	"github.com/gin-gonic/gin"
	"marketing/internal/dao"
	"marketing/internal/model"
)

type ChannelSvc interface {
	GetChannelList(c *gin.Context, code string, pageNum, pageSize int) ([]*model.Channels, int64)
	GetAllChannelList(c *gin.Context) []*model.Channels
	GetChannelById(c *gin.Context, id int) *model.Channels
	EditChannel(c *gin.Context, id int, code string, name string) error
	DeleteChannel(c *gin.Context, id int) error
}

type ChannelSvcImpl struct {
	channelRepo dao.ChannelDao
}

func NewChannelService(channelRepo dao.ChannelDao) ChannelSvc {
	return &ChannelSvcImpl{
		channelRepo: channelRepo,
	}
}

func (s *ChannelSvcImpl) GetChannelList(c *gin.Context, code string, pageNum, pageSize int) ([]*model.Channels, int64) {
	return s.channelRepo.GetChannelList(c, code, pageNum, pageSize)
}

func (s *ChannelSvcImpl) GetAllChannelList(c *gin.Context) []*model.Channels {
	return s.channelRepo.GetAllChannel(c)
}

func (s *ChannelSvcImpl) GetChannelById(c *gin.Context, id int) *model.Channels {
	return s.channelRepo.GetChannelById(c, id)
}

func (s *ChannelSvcImpl) EditChannel(c *gin.Context, id int, code string, name string) error {
	if len(code) == 0 {
		return errors.New("编辑渠道:渠道码为空")
	}

	if len(name) == 0 {
		return errors.New("编辑渠道:渠道名称为空")
	}

	if id == 0 {
		return s.channelRepo.CreateChannel(c, &model.Channels{
			Code: code,
			Name: name,
		})
	}

	channel := s.channelRepo.GetChannelById(c, id)
	if channel == nil {
		return errors.New("编辑渠道:渠道不存在")
	}

	uMap := make(map[string]interface{})
	uMap["code"] = code
	uMap["name"] = name

	return s.channelRepo.UpdateChannel(c, id, uMap)
}

func (s *ChannelSvcImpl) DeleteChannel(c *gin.Context, id int) error {
	return s.channelRepo.DeleteChannel(c, id)
}
