package provider

import (
	"github.com/google/wire"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
	"marketing/internal/config"
	"marketing/internal/pkg/db"
	myRedis "marketing/internal/pkg/redis"
	"marketing/internal/pkg/wecom"
)

type InfrastructureProvider struct {
	DB    *gorm.DB
	DBs   map[string]*gorm.DB
	Redis *redis.Client
}

func NewInfrastructureProvider(config *config.Config, redis *redis.Client) *InfrastructureProvider {
	dbs := make(map[string]*gorm.DB)
	for _, name := range config.MySQL.DSN.Keys {
		dbs[name] = db.GetDB(name)
	}
	newRedis := redis

	return &InfrastructureProvider{
		DB:    db.GetDB(config.MySQL.Default),
		DBs:   dbs,
		Redis: newRedis,
	}
}

// ProvideDefaultDB 提供默认数据库
func ProvideDefaultDB(config *config.Config) *gorm.DB {
	return db.GetDB(config.MySQL.Default)
}

// ProvideDefaultRedis 提供默认数据库
func ProvideDefaultRedis(config *config.Config) *redis.Client {
	return myRedis.NewRedis()
}

// ProvideWeCom 提供企微客户端
func ProvideWeCom(config *config.Config) *wecom.Client {
	return wecom.NewWeComClient(
		config.WeCom.CorpID,
		config.WeCom.CorpSecret,
	)
}

var InfrastructureSet = wire.NewSet(
	ProvideDefaultDB,
	ProvideDefaultRedis,
	NewInfrastructureProvider,
)
