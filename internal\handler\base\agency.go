package base

import (
	"marketing/internal/dao/admin_user"
	"marketing/internal/handler"
	"marketing/internal/service"
	baseSvc "marketing/internal/service/base"

	"github.com/gin-gonic/gin"
)

type AgencyHandler interface {
	ChannelList(c *gin.Context)
	AgencyCompanyList(c *gin.Context)
	AgencyCompany(c *gin.Context)
}

type agencyHandler struct {
	channelSvc       service.ChannelSvc
	agencyCompanySvc baseSvc.AgencyCompanyService
}

func NewAgencyHandler(channelSvc service.ChannelSvc, userDao admin_user.UserDao) AgencyHandler {
	return &agencyHandler{
		channelSvc:       channelSvc,
		agencyCompanySvc: baseSvc.NewAgencyCompanyService(userDao),
	}
}

func (r *agencyHandler) ChannelList(c *gin.Context) {
	data := r.channelSvc.GetAllChannelList(c)
	var res []map[string]interface{}
	for _, channel := range data {
		res = append(res, map[string]interface{}{
			"value": channel.Code,
			"label": channel.Name,
		})
	}
	handler.Success(c, res)
}

// AgencyCompanyList 获取代理公司列表
func (r *agencyHandler) AgencyCompanyList(c *gin.Context) {
	data, err := r.agencyCompanySvc.GetAgencyCompanyList(c)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, data)
}

// AgencyCompany 获取代理公司列表
func (r *agencyHandler) AgencyCompany(c *gin.Context) {
	data, err := r.agencyCompanySvc.GetAgencyCompany(c)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, data)
}
