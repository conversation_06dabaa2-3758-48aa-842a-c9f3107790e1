package wecom

import (
	"encoding/json"
	"fmt"
	"marketing/internal/pkg/log"

	"go.uber.org/zap"
)

// CreateDepartmentReq 用于构造创建部门的请求
type CreateDepartmentReq struct {
	Name     string `json:"name,omitempty"`  // 企微部门的名称，必填，最长32个字符
	ParentID int    `json:"parentid"`        // 父部门id，必填
	ID       int    `json:"id,omitempty"`    // 部门id，非必填，默认自动生成
	Order    int    `json:"order,omitempty"` // 在父部门中的次序，非必填，默认为0
}

// DepartmentResp 用于解析部门相关接口的响应
type DepartmentResp struct {
	ErrCode int    `json:"errcode"`
	ErrMsg  string `json:"errmsg"`
	ID      int    `json:"id"` // 创建的部门id
}

const (
	ErrCodeDepartmentNotFound = 60003
	ErrCodeDepartmentExisted  = 60008
)

// CreateDepartment 创建部门
func (client *Client) CreateDepartment(name string, parentID int, departmentID ...int) (int, error) {
	accessToken, err := client.GetAccessToken()
	if err != nil {
		return 0, err
	}
	url := fmt.Sprintf("https://qyapi.weixin.qq.com/cgi-bin/department/create?access_token=%s", accessToken)

	reqBody := CreateDepartmentReq{
		Name:     name,
		ParentID: parentID,
	}

	// 如果提供了部门ID，则使用提供的ID
	if len(departmentID) > 0 {
		reqBody.ID = departmentID[0]
	}

	resp, err := client.httpClient.R().
		SetBody(reqBody).
		Post(url)
	if err != nil {
		return 0, err
	}

	log.Debug("企微创建部门：", zap.Any("企微创建部门响应", resp), zap.Int("departmentID", reqBody.ID))

	var createDeptResp DepartmentResp
	if err := json.Unmarshal(resp.Body(), &createDeptResp); err != nil {
		log.Error("微信创建部门请求信息错误,json解析错误：", zap.Error(err))
		return 0, err
	}

	if createDeptResp.ErrCode != 0 {
		log.Error("微信创建部门请求信息错误，微信接口错误：",
			zap.Int("errcode", createDeptResp.ErrCode),
			zap.String("errmsg", createDeptResp.ErrMsg),
			zap.Int("departmentID", reqBody.ID),
			zap.String("URL", url))
		return 0, &WeChatAPIError{ErrCode: createDeptResp.ErrCode, ErrMsg: createDeptResp.ErrMsg}
	}

	return createDeptResp.ID, nil
}

// UpdateDepartment 更新部门
func (client *Client) UpdateDepartment(id int, name string, parentID int) error {
	accessToken, err := client.GetAccessToken()
	if err != nil {
		return err
	}
	url := fmt.Sprintf("https://qyapi.weixin.qq.com/cgi-bin/department/update?access_token=%s", accessToken)

	reqBody := CreateDepartmentReq{
		ID:       id,
		Name:     name,
		ParentID: parentID,
	}

	resp, err := client.httpClient.R().
		SetBody(reqBody).
		Post(url)
	if err != nil {
		return err
	}

	log.Debug("企微更新部门：", zap.Any("企微更新部门响应", resp))

	var updateDeptResp DepartmentResp
	if err := json.Unmarshal(resp.Body(), &updateDeptResp); err != nil {
		log.Error("微信更新部门请求信息错误,json解析错误：", zap.Error(err))
		return err
	}

	if updateDeptResp.ErrCode != 0 {
		log.Error("微信更新部门请求信息错误，微信接口错误：",
			zap.Int("errcode", updateDeptResp.ErrCode),
			zap.String("errmsg", updateDeptResp.ErrMsg),
			zap.Int("departmentID", id),
			zap.String("URL", url))
		return &WeChatAPIError{ErrCode: updateDeptResp.ErrCode, ErrMsg: updateDeptResp.ErrMsg}
	}

	return nil
}

func (client *Client) MoveDepartment(id int, parentID int) error {
	accessToken, err := client.GetAccessToken()
	if err != nil {
		return err
	}
	url := fmt.Sprintf("https://qyapi.weixin.qq.com/cgi-bin/department/update?access_token=%s", accessToken)

	reqBody := make(map[string]any)
	reqBody["id"] = id
	reqBody["parentid"] = parentID

	resp, err := client.httpClient.R().
		SetBody(reqBody).
		Post(url)
	if err != nil {
		return err
	}

	log.Debug("企微移动部门：", zap.Any("企微移动部门响应", resp))

	var updateDeptResp DepartmentResp
	if err := json.Unmarshal(resp.Body(), &updateDeptResp); err != nil {
		log.Error("微信移动部门请求信息错误,json解析错误：", zap.Error(err))
		return err
	}

	if updateDeptResp.ErrCode != 0 {
		log.Error("微信移动部门请求信息错误，微信接口错误：",
			zap.Int("errcode", updateDeptResp.ErrCode),
			zap.String("errmsg", updateDeptResp.ErrMsg),
			zap.Int("departmentID", id),
			zap.String("URL", url))
		return &WeChatAPIError{ErrCode: updateDeptResp.ErrCode, ErrMsg: updateDeptResp.ErrMsg}
	}

	return nil
}

// DeleteDepartment 删除部门
func (client *Client) DeleteDepartment(id int) error {
	accessToken, err := client.GetAccessToken()
	if err != nil {
		return err
	}
	url := fmt.Sprintf("https://qyapi.weixin.qq.com/cgi-bin/department/delete?access_token=%s&id=%d", accessToken, id)

	resp, err := client.httpClient.R().Get(url)
	if err != nil {
		return err
	}

	log.Debug("企微删除部门：", zap.Any("企微删除部门响应", resp))

	var deleteDeptResp DepartmentResp
	if err := json.Unmarshal(resp.Body(), &deleteDeptResp); err != nil {
		log.Error("微信删除部门请求信息错误,json解析错误：", zap.Error(err))
		return err
	}

	if deleteDeptResp.ErrCode != 0 {
		log.Error("微信删除部门请求信息错误，微信接口错误：",
			zap.Int("errcode", deleteDeptResp.ErrCode),
			zap.String("errmsg", deleteDeptResp.ErrMsg),
			zap.Int("departmentID", id),
			zap.String("URL", url))
		return &WeChatAPIError{ErrCode: deleteDeptResp.ErrCode, ErrMsg: deleteDeptResp.ErrMsg}
	}

	return nil
}

// DepartmentSimple 部门简单信息
type DepartmentSimple struct {
	ID       int `json:"id"`       // 部门id
	ParentID int `json:"parentid"` // 父部门id
	Order    int `json:"order"`    // 在父部门中的次序值
}

// DepartmentSimpleListResp 获取子部门ID列表的响应
type DepartmentSimpleListResp struct {
	ErrCode    int                `json:"errcode"`
	ErrMsg     string             `json:"errmsg"`
	Department []DepartmentSimple `json:"department_id"`
}

// GetDepartmentSimpleList 获取子部门ID列表
func (client *Client) GetDepartmentSimpleList(departmentID ...int) (*DepartmentSimpleListResp, error) {
	accessToken, err := client.GetAccessToken()
	if err != nil {
		return nil, err
	}

	// 构建请求URL
	url := fmt.Sprintf("https://qyapi.weixin.qq.com/cgi-bin/department/simplelist?access_token=%s", accessToken)

	// 如果指定了部门ID，添加到URL
	if len(departmentID) > 0 && departmentID[0] > 0 {
		url = fmt.Sprintf("%s&id=%d", url, departmentID[0])
	}

	// 发送请求
	resp, err := client.httpClient.R().Get(url)
	if err != nil {
		return nil, err
	}

	log.Debug("企微获取子部门列表：", zap.Any("企微获取子部门列表响应", resp))

	// 解析响应
	var simpleListResp DepartmentSimpleListResp
	if err := json.Unmarshal(resp.Body(), &simpleListResp); err != nil {
		log.Error("微信获取子部门列表请求信息错误,json解析错误：", zap.Error(err))
		return nil, err
	}

	// 检查错误码
	if simpleListResp.ErrCode != 0 {
		log.Error("微信获取子部门列表请求信息错误，微信接口错误：",
			zap.Int("errcode", simpleListResp.ErrCode),
			zap.String("errmsg", simpleListResp.ErrMsg),
			zap.Ints("departmentID", departmentID),
			zap.String("URL", url))
		return nil, &WeChatAPIError{
			ErrCode: simpleListResp.ErrCode,
			ErrMsg:  simpleListResp.ErrMsg,
		}
	}

	return &simpleListResp, nil
}

// GetAllSubDepartmentIDs 获取所有子部门ID（包括子部门的子部门）
func (client *Client) GetAllSubDepartmentIDs(departmentID int) ([]int, error) {
	resp, err := client.GetDepartmentSimpleList(departmentID)
	if err != nil {
		return nil, err
	}

	var departmentIDs []int
	for _, dept := range resp.Department {
		departmentIDs = append(departmentIDs, dept.ID)
	}

	return departmentIDs, nil
}

// GetDepartmentHierarchy 获取部门层级结构
func (client *Client) GetDepartmentHierarchy(departmentID int) (map[int][]int, error) {
	resp, err := client.GetDepartmentSimpleList(departmentID)
	if err != nil {
		return nil, err
	}

	// 构建父子部门映射关系
	hierarchy := make(map[int][]int)
	for _, dept := range resp.Department {
		if _, ok := hierarchy[dept.ParentID]; !ok {
			hierarchy[dept.ParentID] = make([]int, 0)
		}
		hierarchy[dept.ParentID] = append(hierarchy[dept.ParentID], dept.ID)
	}

	return hierarchy, nil
}
