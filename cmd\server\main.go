package main

import (
	"context"
	"errors"
	"fmt"
	"marketing/internal/config"
	"marketing/internal/pkg/db"
	"marketing/internal/pkg/log"
	"marketing/internal/pkg/validator"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/spf13/viper"
	"go.uber.org/zap"
)

func main() {
	// 初始化配置需要在 wire 之前
	cfg := config.NewConfig()
	log.New()
	validator.Init()
	db.Init()

	// 初始化服务器
	r, err := InitializeServer(cfg)
	if err != nil {
		log.Fatal("failed to initialize server: %v", zap.Error(err))
	}

	// 配置路由
	r.InitRoutes()

	// 启动 HTTP 服务器
	server := &http.Server{
		Addr:    ":" + viper.GetString("server.port"),
		Handler: r.Engine(),
	}

	go func() {
		log.Info(fmt.Sprintf("Server listen port: %s", viper.GetString("server.port")))
		if err := server.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			log.Fatal("Server startup failed: %v", zap.Error(err))
		}
	}()

	// 处理关闭信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Info("Server shutting down...")
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		log.Fatal("Could not gracefully shutdown the server: ", zap.Error(err))
	}
	log.Info("Server stopped")
}
