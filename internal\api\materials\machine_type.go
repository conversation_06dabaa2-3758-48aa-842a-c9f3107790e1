package materials

import (
	"marketing/internal/api"
	"marketing/internal/pkg/types"
)

// DelistSearch 下市申报
type DelistSearch struct {
	api.PaginationParams
	Model           string `json:"model" form:"model"`
	Delist          *int   `json:"delist" form:"delist"`
	DelistTimeStart string `json:"delist_time_start" form:"delist_time_start"`
	DelistTimeEnd   string `json:"delist_time_end" form:"delist_time_end"`
}

// DelistListVo 上柜机型
type DelistListVo struct {
	//model.MachineTypeRelation
	ModelID             int            `json:"model_id"`    // 型号ID
	Name                string         `json:"name"`        // 型号
	CategoryID          int            `json:"category_id"` // 分类ID
	Declare             int            `json:"declare"`     // 允许申报 0:不允许 1:允许
	Stock               int            `json:"stock"`       // 允许备货 0:不允许 1:允许
	Delist              int            `json:"delist"`      // 是否下架 0:不下架 1:下架
	DelistTime          types.DateOnly `json:"delist_time"` // 下市时间
	CategoryName        string         `json:"category_name"`
	Inventory           int64          `json:"inventory"`             // 库存
	Prototype           int64          `json:"prototype"`             //样机数量
	NewMachineInventory int64          `json:"new_machine_inventory"` // 新机库存
}
