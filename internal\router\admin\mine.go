package admin

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"marketing/internal/handler/admin/mine"
	"marketing/internal/middleware"
)

type MineRouter struct {
	db          *gorm.DB
	mineHandler mine.MineInterface
}

func NewMineRouter(db *gorm.DB, mineHandler mine.MineInterface) *MineRouter {
	return &MineRouter{
		db:          db,
		mineHandler: mineHandler,
	}
}

func (m *MineRouter) Register(r *gin.RouterGroup) {
	mineRouter := r.Group("mine")
	{
		//用户日志中间件
		mineRouter.Use(middleware.AdminUserLogMiddleware(m.db))
		mineRouter.PUT("", m.mineHandler.Update)
		mineRouter.POST("/reset-password", m.mineHandler.ResetPassword)
		mineRouter.POST("/:id/bind-wecom", m.mineHandler.BindWecom)
	}
}
