package report

type MarginReportReq struct {
	StartTime  string  `json:"start_time" form:"start_time"`
	EndTime    string  `json:"end_time" form:"end_time"`
	ModelID    uint    `json:"model_id" form:"model_id"`
	Model      string  `json:"model" form:"model"`
	Count      int64   `json:"count" form:"count"`
	Amount     float64 `json:"amount" form:"amount"`
	CategoryID int     `json:"category_id" form:"category_id"` // 分类ID
}

type LearningRoomReport struct {
	Amount       float64       `json:"amount"`
	Total        int64         `json:"total"`
	ReturnTotal  int64         `json:"return_total"`
	ReturnAmount float64       `json:"return_amount"`
	ModelReport  []ModelReport `json:"model_report"`
}

type ModelReport struct {
	ModelID uint    `json:"model_id"`
	Model   string  `json:"model"`
	Amount  float64 `json:"amount"`
	Total   int64   `json:"total"`
}
