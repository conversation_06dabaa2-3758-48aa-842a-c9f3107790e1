package model

import (
	"time"

	"github.com/shopspring/decimal"
)

type PrMaterial struct {
	ID                   uint                  `gorm:"primaryKey;autoIncrement;column:id"`
	PrSn                 string                `gorm:"column:pr_sn;type:varchar(50);not null;default:'0';comment:寄修流水号"`
	MatID                uint                  `gorm:"column:mat_id;not null;default:0;comment:机型配件树id"`
	MaterialID           uint                  `gorm:"column:material_id;not null;default:0;comment:物料id"`
	MalfunctionID        uint                  `gorm:"column:malfunction_id;not null;default:0;comment:故障id"`
	IsCharge             uint8                 `gorm:"column:is_charge;not null;default:1;comment:是否收费 0不收费 1收费"`
	ChargeType           uint                  `gorm:"column:charge_type;not null;default:1;comment:收费类型"`
	PriceIn              decimal.Decimal       `gorm:"column:price_in;type:decimal(10,2);not null;default:0.00;comment:配件在本订单的价格"`
	Count                uint                  `gorm:"column:count;not null;default:0;comment:数量"`
	CreatedAt            *time.Time            `gorm:"column:created_at"`
	UpdatedAt            *time.Time            `gorm:"column:updated_at"`
	Material             *Material             `gorm:"foreignKey:MaterialID"`
	MachineAccessoryTree *MachineAccessoryTree `gorm:"foreignKey:MatID"`
}

// TableName 显式指定表名
func (p *PrMaterial) TableName() string {
	return "pr_material"
}
