package endpoint_application

import (
	"marketing/internal/api"
	"marketing/internal/model"
	"marketing/internal/pkg/types"
)

type CreatedEndpointApplyReq struct {
	TopAgency       uint           `json:"top_agency" form:"top_agency" binding:"required"`
	SecondAgency    uint           `json:"second_agency" form:"second_agency"`
	Name            string         `json:"name" form:"name"`
	Type            int            `json:"type" form:"type"`
	Province        int            `json:"province" form:"province"`
	City            int            `json:"city" form:"city"`
	District        int            `json:"district" form:"district"`
	Address         string         `json:"address" form:"address"`
	Lng             string         `json:"lng" form:"lng"` //高德坐标系自己转换
	Lat             string         `json:"lat" form:"lat"`
	ChannelLevel    uint8          `json:"channel_level" form:"channel_level"`
	Blng            string         `json:"blng" form:"blat"`
	Blat            string         `json:"blat" form:"blat"`
	PolicyID        int            `json:"policy_id" form:"policy_id"`
	Phone           string         `json:"phone" form:"phone" binding:"phone"`
	Manager         string         `json:"manager" form:"manager"`
	ApplicationYear int            `json:"application_year"`
	Investor        string         `json:"investor" form:"investor"`                 //投资者
	InvestorPhone   string         `json:"investor_phone" form:"investor_phone"`     //投资者手机号
	Position        string         `json:"position" form:"position"`                 // 所处地段
	EndpointArea    string         `json:"endpoint_area" form:"endpoint_area"`       // 终端面积
	Pics            []string       `json:"pics" form:"pics"`                         // 装修前实景图
	ExpectOpenTime  string         `json:"expect_open_time" form:"expect_open_time"` // 期望开盘时间
	Extend          map[string]any `json:"extend" form:"extend"`
	State           int
}

type EndpointApplicationListReq struct {
	api.PaginationParams
	PolicyID     int    `json:"policy_id" form:"policy_id"`
	State        int    `json:"state" form:"state"`
	Name         string `json:"name" form:"name"`
	Type         int    `json:"type" form:"type"`
	Code         int    `json:"code" form:"code"`
	TopAgency    int    `json:"top_agency" form:"top_agency"`
	SecondAgency int    `json:"second_agency" form:"second_agency"`
}

type EndpointApplicationListResp struct {
	model.EndpointApplication
	PicsArr            []string         `json:"pics"`
	ExtendMap          map[string]any   `json:"extend"`
	Code               int              `json:"code"`
	EndpointID         int              `json:"endpoint_id"`
	EndpointCode       string           `json:"endpoint_code"`
	CreatedAtFormatted types.CustomTime `json:"created_at_formatted"`
	TopAgencyName      string           `json:"top_agency_name"`
	SecondAgencyName   string           `json:"second_agency_name"`
	EndpointTypeName   string           `json:"endpoint_type_name"`
	StateName          string           `json:"state_name"`
	NextStateName      string           `json:"next_state_name"`
}

type AuditApplyReq struct {
	ID     uint           `json:"id" form:"id"`
	State  int            `json:"state" form:"state" binding:"required"`
	Remark string         `json:"remark" form:"remark"`
	Extend map[string]any `json:"extend" form:"extend"`
}

type EndpointMaterialSupport struct {
	ID               uint    `json:"id"`
	Name             string  `json:"name"`
	Pic              string  `json:"pic"`
	Price            float64 `json:"price"`
	ProductionNumber string  `json:"production_number"`
	Thumbnail        string  `json:"thumbnail"`
	Num              int     `json:"num"`
}

type PostbackEndpointApplyReq struct {
	ID                 uint           `json:"id" form:"id"`
	Remark             string         `json:"remark" form:"remark"`
	Extend             map[string]any `json:"extend" form:"extend"`
	WriteOffTable      string         `json:"write_off_table" form:"write_off_table"`
	LeaseContract      string         `json:"lease_contract" form:"lease_contract"`
	AnnualRent         float64        `json:"annual_rent" form:"annual_rent"`
	DesignRenderings   []string       `json:"design_renderings" form:"design_renderings"`
	RenovationPhotos   []string       `json:"renovation_photos" form:"renovation_photos"`
	RenovationVideos   []string       `json:"renovation_videos" form:"renovation_videos"`
	Diploma            []string       `json:"diploma" form:"diploma"`
	EndpointGroupPhoto []string       `json:"endpoint_group_photo" form:"endpoint_group_photo"`
}

type WriteOffReq struct {
	ID     uint           `json:"id" form:"id"`
	Pay    float64        `json:"pay" form:"pay"`
	State  int            `json:"state" form:"state" binding:"required"`
	Remark string         `json:"remark" form:"remark"`
	Extend map[string]any `json:"extend" form:"extend"`
}

type LatestEndpointImageResp struct {
	AutoTime   types.CustomTime `json:"auto_time"`
	ManualTime types.CustomTime `json:"manual_time"`
}
