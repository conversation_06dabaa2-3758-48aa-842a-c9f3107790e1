package endpoint

import (
	"encoding/json"
	"errors"
	api "marketing/internal/api/endpoint"
	"marketing/internal/consts"
	adminUserdao "marketing/internal/dao/admin_user"
	dao "marketing/internal/dao/endpoint"
	appError "marketing/internal/pkg/errors"
	"marketing/internal/pkg/types"
	"marketing/internal/pkg/utils"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

type ImageService interface {
	Lists(c *gin.Context, param *api.GetEndpointImageReq) ([]*api.ListEndpointImageRes, int64, error)
	Audit(c *gin.Context, req api.AuditImageReq) error
}

type image struct {
	db               *gorm.DB
	endpointDao      dao.EndpointDao
	endpointImageDao dao.ImageDao
	adminUserDao     adminUserdao.UserDao
}

func NewEndpointImageService(db *gorm.DB, endpointDao dao.EndpointDao, endpointImageDao dao.ImageDao, adminUserDao adminUserdao.UserDao) ImageService {
	return &image{
		db:               db,
		endpointDao:      endpointDao,
		endpointImageDao: endpointImageDao,
		adminUserDao:     adminUserDao,
	}
}

// Lists 获取形象更新列表
func (i *image) Lists(c *gin.Context, param *api.GetEndpointImageReq) ([]*api.ListEndpointImageRes, int64, error) {
	var list []*api.ListEndpointImageRes
	var total int64

	// 如果 param.Month 为空，设置为当前年月
	if param.Month == "" {
		currentTime := time.Now()
		param.Month = currentTime.Format("2006-01")
	}

	// 构建表连接条件
	joinCondition := "endpoint.id = endpoint_image_apply.endpoint_id"
	if param.Month != "" {
		startDate := param.Month + "-01"
		endDate := time.Date(cast.ToInt(param.Month[:4]), time.Month(cast.ToInt(param.Month[5:7])+1), 1, 0, 0, 0, 0, time.UTC).Format(time.DateOnly)
		joinCondition += " AND endpoint_image_apply.created_at >= '" + startDate + "' AND endpoint_image_apply.created_at < '" + endDate + "'"
	}

	// Set up the query
	query := i.db.WithContext(c).Table("endpoint").
		Select("endpoint_image_apply.*,endpoint.id as endpoint_id, endpoint.name, endpoint.code, endpoint.address as endpoint_address," +
			" endpoint.channel_level, endpoint.top_agency, endpoint.second_agency,endpoint.created_at as endpoint_time").
		Joins("left join endpoint_image_apply on " + joinCondition)

	//默认过滤
	query = query.Where("endpoint.status = ?", 1)
	// Apply filters
	if param.Name != "" {
		query = query.Where("endpoint.name LIKE ?", "%"+param.Name+"%")
	}
	if param.Code != "" {
		query = query.Where("endpoint.code LIKE ?", "%"+param.Code+"%")
	}
	if param.TopAgency != 0 {
		query = query.Where("endpoint.top_agency = ?", param.TopAgency)
	}
	if param.SecondAgency != 0 {
		query = query.Where("endpoint.second_agency = ?", param.SecondAgency)
	}
	if param.Status != "" {
		if param.Status == "updated" {
			query = query.Where("endpoint_image_apply.id is not null")
		} else {
			query = query.Where("endpoint_image_apply.id is null")
		}
	}
	if param.AuditStatus != "" {
		query = query.Where(func(db *gorm.DB) *gorm.DB {
			if param.AuditStatus == "manual_to_audit" {
				return db.Where("endpoint_image_apply.manual_status = ?", "to_audit").
					Where("endpoint_image_apply.status = ?", "approved")
			}
			if strings.HasPrefix(param.AuditStatus, "manual_") {
				return db.Where("endpoint_image_apply.manual_status = ?", strings.TrimPrefix(param.AuditStatus, "manual_"))
			}
			return db.Where("endpoint_image_apply.status = ?", param.AuditStatus)
		})
	}

	// Get the total count
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// Apply pagination
	offset := (param.Page - 1) * param.PageSize
	query = query.Offset(offset).Limit(param.PageSize).Order("endpoint_image_apply.id desc")

	// Execute the query
	err = query.Scan(&list).Error
	if err != nil {
		return nil, 0, err
	}
	// 使用 map 来去重
	agencyIdsMap := make(map[int]struct{})
	auditUserIdsMap := make(map[uint]struct{})
	for _, v := range list {
		agencyIdsMap[v.TopAgency] = struct{}{} // 添加 TopAgency
		if v.SecondAgency > 0 {
			agencyIdsMap[v.SecondAgency] = struct{}{} // 添加 SecondAgency
		}
		if v.AuditUserID > 0 {
			auditUserIdsMap[v.AuditUserID] = struct{}{}
		}
	}

	// 将 map 的键转换为切片
	var agencyIds []int
	for id := range agencyIdsMap {
		agencyIds = append(agencyIds, id)
	}
	var auditUserIds []uint
	for id := range auditUserIdsMap {
		auditUserIds = append(auditUserIds, id)
	}
	// 获取所有代理信息
	agencies, _ := i.endpointDao.GetAgencies(c, agencyIds)
	// 取所有用户信息
	users, _ := i.adminUserDao.GetByIDs(c, auditUserIds)

	// 处理数据
	for _, item := range list {
		secondAgencyName := agencies[uint(item.SecondAgency)]
		if secondAgencyName == "" {
			secondAgencyName = "直营"
		}
		item.AgencyName = agencies[uint(item.TopAgency)] + "-" + secondAgencyName
		//计算距离
		if item.OldLatitude != nil && item.OldLongitude != nil {
			distanceInt64 := cast.ToInt64(utils.Distance(*item.OldLatitude, *item.OldLongitude, item.Latitude, item.Longitude, 1, 1))
			item.Distance = &distanceInt64
		}
		// 处理字段
		item.Storefront.AddPrefix(consts.OssOldPrefix)
		item.ProductImage.AddPrefix(consts.OssOldPrefix)
		item.ProductExperience.AddPrefix(consts.OssOldPrefix)
		item.CultureWall.AddPrefix(consts.OssOldPrefix)
		item.Cashier.AddPrefix(consts.OssOldPrefix)
		item.RestArea.AddPrefix(consts.OssOldPrefix)
		item.SpareParts.AddPrefix(consts.OssOldPrefix)
		item.BooksBorrow.AddPrefix(consts.OssOldPrefix)
		item.TrainingRoom.AddPrefix(consts.OssOldPrefix)
		item.Surroundings.AddPrefix(consts.OssOldPrefix)
		item.Other.AddPrefix(consts.OssOldPrefix)
		//处理审核人
		if item.AuditUserID > 0 {
			for _, v := range users {
				if item.AuditUserID == v.ID {
					item.AuditUser = v.Name
				}
			}

		}
	}

	return list, total, nil
}

func (i *image) Audit(c *gin.Context, req api.AuditImageReq) error {
	//查询id申请是否存在
	existApply, err := i.endpointImageDao.GetByID(c, req.ID)
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return appError.NewErr("申请不存在")
	}
	if err != nil {
		return err
	}
	if existApply.ManualStatus != "to_audit" {
		//return appError.NewErr("形象申请已经审核")
	}
	updateData := map[string]interface{}{
		"manual_status":          req.Status,
		"audit_user_id":          c.GetUint("uid"),
		"manual_audit_time":      time.Now(),
		"manual_rejected_reason": req.AuditOpinion,
	}
	// 事务
	err = i.db.Transaction(func(tx *gorm.DB) error {
		if err := i.endpointImageDao.Update(c, req.ID, updateData); err != nil {
			return err
		}
		if req.Status == "approved" {
			// Update endpoint images if approved
			// Add your logic here to update the endpoint images
			var images []string

			// 直接将所有字段的数组合并
			arrays := []types.JSONStringArray{
				existApply.ProductImage,
				existApply.ProductExperience,
				existApply.CultureWall,
				existApply.Cashier,
				existApply.RestArea,
				existApply.SpareParts,
				existApply.BooksBorrow,
				existApply.TrainingRoom,
				existApply.Surroundings,
				existApply.Other,
			}

			// 处理每个字段
			for _, arr := range arrays {
				images = append(images, []string(arr)...)
			}

			// 如果需要将结果压缩成 JSON
			compressedImages, err := json.Marshal(images)
			if err != nil {
				return err
			}
			//更新终端形象
			existEndpoint, err := i.endpointDao.GetEndpointByID(c, existApply.EndpointID)
			if err != nil {
				return err
			}
			compressedImagesStr := string(compressedImages)
			existEndpoint.Images = &compressedImagesStr
			if _, err = i.endpointDao.UpdateEndpoint(c, existEndpoint); err != nil {
				return err
			}
		}
		return nil
	})
	return err
}
