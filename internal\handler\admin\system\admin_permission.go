package system

import (
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"marketing/internal/api/system"
	"marketing/internal/handler"
	"marketing/internal/pkg/errors"
	system2 "marketing/internal/service/system"
)

type AdminPermissionInterface interface {
	Add(c *gin.Context)
	Lists(c *gin.Context)
	Update(c *gin.Context)
	Delete(c *gin.Context)
	RoutesList(c *gin.Context)
}

type adminPermission struct {
	adminPermissionSvc system2.AdminPermissionInterface
}

// NewAdminPermission 创建 adminPermission 实例
func NewAdminPermission(adminPermissionSvc system2.AdminPermissionInterface) AdminPermissionInterface {
	return &adminPermission{
		adminPermissionSvc: adminPermissionSvc,
	}
}

// Add 新增
func (a *adminPermission) Add(c *gin.Context) {
	var req system.AddPermissionReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	err := a.adminPermissionSvc.Add(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

func (a *adminPermission) Lists(c *gin.Context) {
	var req system.AdminPermissionReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	if req.IsPage {
		//分页处理
		req.PaginationParams.SetDefaults()
		data, err := a.adminPermissionSvc.Lists(c, req)
		if err != nil {
			handler.Error(c, err)
			return
		}
		handler.Success(c, data)
		return
	}
	data, err := a.adminPermissionSvc.ListAll(c, req.SystemType)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, data)
}

// Update 新增
func (a *adminPermission) Update(c *gin.Context) {
	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		handler.Error(c, errors.NewErr("id不能为空"))
		return
	}
	var req system.AddPermissionReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	req.ID = id
	err := a.adminPermissionSvc.Update(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

func (a *adminPermission) Delete(c *gin.Context) {

	id := cast.ToInt(c.Param("id"))
	if id == 0 {
		handler.Error(c, errors.NewErr("id不能为空"))
		return
	}
	err := a.adminPermissionSvc.Delete(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

func (a *adminPermission) RoutesList(c *gin.Context) {
	data, err := a.adminPermissionSvc.AllRoutes(c)
	if err != nil {
		handler.Error(c, err)
	}
	handler.Success(c, data)
}
