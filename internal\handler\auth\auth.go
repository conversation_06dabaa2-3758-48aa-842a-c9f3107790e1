package auth

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/api/auth"
	"marketing/internal/handler"
	service "marketing/internal/service/auth"
	"net/http"
)

// Auth 登录授权
type Auth struct {
	svc service.ServiceInterface
}

// NewAuth new
func NewAuth(svc service.ServiceInterface) *Auth {
	return &Auth{
		svc: svc,
	}
}

// Login 登录
func (a *Auth) Login(c *gin.Context) {
	var param auth.LoginReq
	// 你可以使用显式绑定声明绑定 multipart form：绑定不了json格式
	// c.ShouldBindWith(&form, binding.Form)
	// 或者简单地使用 ShouldBind 方法自动绑定
	// 如果是 `GET` 请求，只使用 `Form` 绑定引擎（`query`）。
	// 如果是 `POST` 请求，首先检查 `content-type` 是否为 `JSON` 或 `XML`，然后再使用 `Form`（`form-data`）。
	if err := c.ShouldBind(&param); err != nil {
		handler.Error(c, err)
		return
	}
	token, err := a.svc.Login(c, param)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, token)
}

// WeComLogin 企业微信登录
func (a *Auth) WeComLogin(c *gin.Context) {
	var param auth.WcLoginReq
	if err := c.ShouldBind(&param); err != nil {
		handler.Error(c, err)
		return
	}

	token, err := a.svc.WeComLogin(c, param.Code)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, token)
}

// RefreshToken 刷新 token
func (a *Auth) RefreshToken(c *gin.Context) {
	var req auth.RefreshTokenReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	token, err := a.svc.RefreshToken(c, req)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "登录已失效"})
		return
	}
	handler.Success(c, token)
}

// PhoneLogin 手机号登录
func (a *Auth) PhoneLogin(c *gin.Context) {
	var param auth.PhoneLoginReq
	if err := c.ShouldBind(&param); err != nil {
		handler.Error(c, err)
		return
	}
	token, err := a.svc.PhoneLogin(c, param)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, token)
}
