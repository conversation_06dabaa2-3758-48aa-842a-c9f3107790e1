package model

import (
	"time"
)

type ReimbursementList struct {
	ID                       int        `gorm:"primaryKey;autoIncrement"`
	SN                       string     `gorm:"type:varchar(100);not null;default:'';comment:'核销单号'"`
	PolicyID                 int        `gorm:"type:tinyint(11);default:0;comment:'政策id'"`
	ReimbursementType        int        `gorm:"type:int(11);not null;comment:'核销类型'"`
	UID                      uint       `gorm:"type:int(11);not null;comment:'用户id,这个id来自admin_users表'"`
	TopAgency                uint       `gorm:"type:int(11);comment:'一级代理id'"`
	SecondAgency             uint       `gorm:"type:int(11);default:0;comment:'二级代理id'"`
	CompanyID                int        `gorm:"type:int(11);not null;comment:'公司id'"`
	Code                     string     `gorm:"type:varchar(10);not null;comment:'客户编码'"`
	Company                  string     `gorm:"type:varchar(100);not null;default:'';comment:'公司名称'"`
	Amount                   float64    `gorm:"type:decimal(10,2);not null;default:0.00;comment:'额度'"`
	ReimbursementApplyAmount float64    `gorm:"type:decimal(10,2);default:0.00;comment:'申请核销金额'"`
	ReimbursementAmount      float64    `gorm:"type:decimal(10,2);default:0.00;comment:'核销金额'"`
	Quantity                 int        `gorm:"type:int(11);not null;default:0;comment:'申请核销总数量'"`
	ReimbursementQuantity    int        `gorm:"type:int(11);not null;default:0;comment:'实际核销总数量'"`
	Status                   int8       `gorm:"type:tinyint(4);not null;default:0;comment:'状态'"`
	CreatedAt                time.Time  `gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:'核销时间'"`
	UpdatedAt                *time.Time `gorm:"type:timestamp;comment:'更新时间'"`
	Remark                   *string    `gorm:"type:varchar(255);comment:'审核不通过原因'"`
	RetrialRemark            *string    `gorm:"type:varchar(255);comment:'审核不通过原因'"`
	AuditMan                 *int       `gorm:"type:int(11);comment:'核销审核人'"`
	AuditTime                *time.Time `gorm:"type:timestamp;comment:'审批时间'"`
	ExpressComeSN            *string    `gorm:"type:varchar(50);comment:'物流单号'"`
	ExpressComeCom           *string    `gorm:"type:varchar(50);comment:'物流公司'"`
	ExpressComeTime          *time.Time `gorm:"type:timestamp;comment:'物流发货时间'"`
	ConfirmStatus            int8       `gorm:"type:tinyint(4);not null;default:0;comment:'确认状态'"`
}

func (ReimbursementList) TableName() string {
	return "reimbursement_list"
}
