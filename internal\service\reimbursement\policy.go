package reimbursement

import (
	"errors"
	"github.com/gin-gonic/gin"
	api "marketing/internal/api/reimbursement"
	dao "marketing/internal/dao/reimbursement"
	"marketing/internal/model"
	"marketing/internal/pkg/types"
	"time"
)

type PolicyService interface {
	// GetPolicyList 获取报销政策列表
	GetPolicyList(req *api.PolicyListSearch) ([]model.ReimbursementPolicy, int64, error)
	// CreatePolicyWithProducts 创建报销政策及其关联的产品
	CreatePolicyWithProducts(policy model.ReimbursementPolicy, products []model.PromotionalProduct) (int, error)
	// GetPolicyDetail 获取报销政策详情
	GetPolicyDetail(policyID int) (*api.PolicyDetailResp, error)
	// GetPolicySummary 获取报销政策摘要
	GetPolicySummary(c *gin.Context, req *api.PolicySummaryReq) ([]api.PolicySummaryItem, int, error)
	// UpdatePolicyWithProducts 更新报销政策及其关联的产品
	UpdatePolicyWithProducts(policy model.ReimbursementPolicy, products []model.PromotionalProduct) error
	// ArchivePolicy 归档/取消归档报销政策
	ArchivePolicy(policyID int, archive int) error
	// GetApproveUserList 获取有审核权限的用户列表
	GetApproveUserList(slug string) ([]map[string]interface{}, error)
	// GetPromotionalProductsHeader 获取推广产品表头
	GetPromotionalProductsHeader(c *gin.Context, policyID int) (map[int]string, error)
	// GetPromotionalProductsList 获取推广产品列表
	GetPromotionalProductsList(c *gin.Context, req *api.PromotionalProductsListReq) (*api.PromotionalProductsListResp, error)
}
type policy struct {
	repo dao.PolicyRepository
}

func NewPolicyService(repo dao.PolicyRepository) PolicyService {
	return &policy{
		repo: repo,
	}
}

func (p *policy) GetPolicyList(req *api.PolicyListSearch) ([]model.ReimbursementPolicy, int64, error) {
	policies, total, err := p.repo.GetPolicyList(req)
	if err != nil {
		return nil, 0, err
	}
	return policies, total, nil
}

func (p *policy) CreatePolicyWithProducts(policy model.ReimbursementPolicy, products []model.PromotionalProduct) (int, error) {
	// Set created_at time
	now := time.Now()
	policy.CreatedAt = types.CustomTime(now)

	// Create policy and get its ID
	id, err := p.repo.CreatePolicyAndGetID(policy)
	if err != nil {
		return 0, err
	}

	// If there are products, create them with the policy ID
	if len(products) > 0 {
		err = p.repo.CreatePromotionalProducts(id, products)
		if err != nil {
			return 0, err
		}
	}

	return id, nil
}

func (p *policy) GetPolicyDetail(policyID int) (*api.PolicyDetailResp, error) {
	policyInfo, err := p.repo.GetPolicyByID(policyID)
	if err != nil {
		return nil, err
	}
	var data api.PolicyDetailResp
	data.Policy = policyInfo

	// If policy type is promotional_products, get products
	var prods []*api.PromotionalProductDetail
	if policyInfo.PolicyType == "promotional_products" {
		prods, err = p.repo.GetPromotionalProductsByPolicyID(policyInfo.ID)
		if err != nil {
			return nil, err
		}
		var processedProducts []api.ProductResp
		for _, product := range prods {

			processedProduct := api.ProductResp{
				ID:                    product.ID,
				PolicyID:              policyID,
				Name:                  product.Name,
				Norm:                  product.Norm,
				Unit:                  product.Unit,
				IncludeTaxPrice:       product.IncludeTaxPrice,
				ExcludeTaxPrice:       product.ExcludeTaxPrice,
				ReimbursementPrice:    product.ReimbursementPrice,
				IncludeTaxAccountInfo: product.IncludeTaxAccountInfo,
				ExcludeTaxAccountInfo: product.ExcludeTaxAccountInfo,
				CommunicationLetter:   product.CommunicationLetter,
				Preview:               product.Preview,
				CreatedAt:             product.CreatedAt,
			}

			processedProducts = append(processedProducts, processedProduct)
		}
		data.Products = processedProducts
	}

	return &data, nil
}

func (p *policy) GetPolicySummary(c *gin.Context, req *api.PolicySummaryReq) ([]api.PolicySummaryItem, int, error) {
	// Get policy summaries from repository with pagination
	return p.repo.GetPolicySummary(c, req)
}

func (p *policy) UpdatePolicyWithProducts(policy model.ReimbursementPolicy, products []model.PromotionalProduct) error {
	// Set updated_at time
	now := time.Now()
	policy.UpdatedAt = types.CustomTime(now)

	// Update policy
	err := p.repo.UpdatePolicy(policy)
	if err != nil {
		return err
	}

	// If policy type is promotional_products and there are products, update them
	if policy.PolicyType == "promotional_products" && len(products) > 0 {
		// Delete existing products
		err = p.repo.DeletePromotionalProductsByPolicyID(policy.ID)
		if err != nil {
			return err
		}

		// Create new products
		err = p.repo.CreatePromotionalProducts(policy.ID, products)
		if err != nil {
			return err
		}
	}

	return nil
}

func (p *policy) ArchivePolicy(policyID int, archive int) error {
	// Get current time for archive_time
	now := time.Now()

	// If archive is 0 (unarchive), set archive_time to zero value
	var archiveTime types.CustomTime
	if archive == 1 {
		archiveTime = types.CustomTime(now)
	}

	// Call repository to archive policy
	return p.repo.ArchivePolicy(policyID, archive, archiveTime)
}

// GetApproveUserList 获取有审核权限的用户列表
func (p *policy) GetApproveUserList(slug string) ([]map[string]interface{}, error) {
	return p.repo.GetApproveUserList(slug)
}

// GetPromotionalProductsHeader 获取推广产品表头
func (p *policy) GetPromotionalProductsHeader(c *gin.Context, policyID int) (map[int]string, error) {
	return p.repo.GetPromotionalProductsHeader(c, policyID)
}

// GetPromotionalProductsList 获取推广产品列表
func (p *policy) GetPromotionalProductsList(c *gin.Context, req *api.PromotionalProductsListReq) (*api.PromotionalProductsListResp, error) {
	// 设置默认值
	req.SetDefaults()

	// 验证时间参数
	if (req.StartTime != "" && req.EndTime == "") || (req.StartTime == "" && req.EndTime != "") {
		return nil, errors.New("时间选择有误")
	}

	// 获取统计信息
	count, err := p.repo.GetPromotionalProductsListCount(c, req)
	if err != nil {
		return nil, err
	}

	// 获取列表数据
	list, err := p.repo.GetPromotionalProductsList(c, req)
	if err != nil {
		return nil, err
	}

	// 获取总计信息
	total, err := p.repo.GetPromotionalProductsTotal(c, req)
	if err != nil {
		return nil, err
	}

	// 组装响应
	resp := &api.PromotionalProductsListResp{
		Total:           count,
		ApplyOrderList:  list,
		ApplyOrderTotal: total,
	}

	return resp, nil
}
