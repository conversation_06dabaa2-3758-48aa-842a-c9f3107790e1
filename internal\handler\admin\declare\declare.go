package declare

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/dao"
	"marketing/internal/handler"
	"marketing/internal/pkg/e"
	"marketing/internal/service"
)

type TDeclare struct {
	svc service.DeclareSvcInterface
}

func NewDeclare(svc service.DeclareSvcInterface) *TDeclare {
	return &TDeclare{
		svc: svc,
	}
}

func (d *TDeclare) GetDeclareList(c *gin.Context) {
	ownInfo, otherInfo := d.svc.GetDeclareList(&dao.GetDeclareParam{
		TopAgency:    e.ReqParamInt(c, "top_agency"),
		SecondAgency: e.ReqParamInt(c, "second_agency"),
		Date:         e.ReqParamStr(c, "date"),
		RegionName:   e.ReqParamStr(c, "region_name"),
		PageNum:      e.ReqParamInt(c, "page_num"),
		PageSize:     e.ReqParamInt(c, "page_size"),
		DeclareType:  e.ReqParamInt(c, "type", -1),
		Sort:         e.ReqParamStr(c, "sort", "desc"),
		DataType:     e.ReqParamStr(c, "data_type", "inventory"),
		Channel:      e.ReqParamStr(c, "channel"),
		CategoryId:   e.ReqParamInt(c, "category_id"),
	})
	handler.Success(c, gin.H{
		"own_warehouse":   ownInfo,
		"other_warehouse": otherInfo,
	})
}
