// Package router 该包主要负责定义和管理与路由相关的逻辑，
// 本文件 admin.go 具体用于配置和注册营销中台管理端（admin）相关的路由组。
// 它通过路由构建器和中间件包装器，灵活地为不同路由配置不同的中间件。
package router

import (
	"marketing/internal/consts"
	"marketing/internal/middleware"
	"marketing/internal/router/admin"
	"marketing/internal/service/auth"

	"github.com/gin-gonic/gin"
)

// NewAdminRouterGroup 创建管理端路由组
func NewAdminRouterGroup(endpoint *admin.EndpointRouter,
	systemRouter *admin.SystemRouter,
	agency *admin.AgencyRouter,
	authService auth.ServiceInterface,
	afterSales *admin.AfterSalesRouter,
	baseRouter *admin.BaseRouter,
	mineRouter *admin.MineRouter,
	reportRouter *admin.ReportRouter) IRouterGroup {

	// 基础中间件 - 仅需要认证
	baseMiddleware := []gin.HandlerFunc{
		middleware.AuthToken(authService),
	}

	// 完整中间件 - 需要权限校验
	fullMiddleware := []gin.HandlerFunc{
		middleware.AuthToken(authService),
		middleware.CheckAdminPermission(authService),
		middleware.CheckTokenSystem(authService, consts.AdminPrefix),
	}

	// 在调试模式下跳过权限校验
	if gin.Mode() == "debug" {
		fullMiddleware = []gin.HandlerFunc{}
		baseMiddleware = []gin.HandlerFunc{}
	}

	// 使用路由构建器
	builder := NewRouterBuilder("admin")

	// 添加仅需基本认证的路由
	builder.AddRouterWithMiddleware(baseRouter, baseMiddleware...)
	builder.AddRouterWithMiddleware(mineRouter, baseMiddleware...)

	// 添加需要完整权限校验的路由
	builder.AddRouterWithMiddleware(systemRouter, fullMiddleware...)
	builder.AddRouterWithMiddleware(endpoint, fullMiddleware...)
	builder.AddRouterWithMiddleware(admin.NewActionRouter(), fullMiddleware...)
	builder.AddRouterWithMiddleware(agency, fullMiddleware...)
	builder.AddRouterWithMiddleware(admin.NewInformationRouter(), fullMiddleware...)
	builder.AddRouterWithMiddleware(admin.NewMaterialRouter(), fullMiddleware...)
	builder.AddRouterWithMiddleware(admin.NewOperationRouter(), fullMiddleware...)
	builder.AddRouterWithMiddleware(admin.NewYouKeRouter(), fullMiddleware...)
	builder.AddRouterWithMiddleware(admin.NewBugFreeLabelRouter(), fullMiddleware...)
	//builder.AddRouterWithMiddleware(admin.NewAgencyRouter(), fullMiddleware...)
	builder.AddRouterWithMiddleware(admin.NewRenewRouter(), fullMiddleware...)
	builder.AddRouterWithMiddleware(admin.NewDeclareRouter(), fullMiddleware...)
	builder.AddRouterWithMiddleware(afterSales, fullMiddleware...)
	// 保单模块
	builder.AddRouterWithMiddleware(admin.NewWarrantyRouter(), fullMiddleware...)
	// 终端政策模块
	builder.AddRouterWithMiddleware(admin.NewEndpointApplicationRouter(), fullMiddleware...)
	// 通知模块
	builder.AddRouterWithPathAndMiddleware(admin.NewNoticeRouter(), "/notice", fullMiddleware...)

	builder.AddRouterWithPathAndMiddleware(admin.NewPrototypeRouter(), "/prototypes", fullMiddleware...)
	//报表模块
	builder.AddRouterWithPathAndMiddleware(reportRouter, "/reports", fullMiddleware...)
	//市场核销
	builder.AddRouterWithPathAndMiddleware(admin.NewReimbursementRouter(), "/reimbursement", fullMiddleware...)

	// 构建并返回路由组
	return builder.Build()
}
