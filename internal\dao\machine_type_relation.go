// internal/dao/machine_type_relation.go
package dao

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	api "marketing/internal/api/materials"
	"marketing/internal/model"
	"time"
)

// MachineTypeRelationDao 机型关联数据访问接口
type MachineTypeRelationDao interface {
	// GetByName 根据机型名称获取关联信息
	GetByName(c *gin.Context, name string) (*model.MachineTypeRelation, error)

	// Create 创建机型关联信息
	Create(c *gin.Context, relation *model.MachineTypeRelation) error

	// Update 更新机型关联信息
	Update(c *gin.Context, name string, updateMap map[string]interface{}) error

	GetDelistList(c *gin.Context, req *api.DelistSearch) ([]*api.DelistListVo, int64, error)
}

// MachineTypeRelationDaoImpl 实现 MachineTypeRelationDao 接口
type machineTypeRelationDaoImpl struct {
	db *gorm.DB
}

// NewMachineTypeRelationDao 创建 MachineTypeRelationDao 实例
func NewMachineTypeRelationDao(db *gorm.DB) MachineTypeRelationDao {
	return &machineTypeRelationDaoImpl{
		db: db,
	}
}

// GetByName 根据机型名称获取关联信息
func (d *machineTypeRelationDaoImpl) GetByName(c *gin.Context, name string) (*model.MachineTypeRelation, error) {
	var relation model.MachineTypeRelation
	err := d.db.WithContext(c).Where("name = ?", name).First(&relation).Error
	if err != nil {
		return nil, err
	}
	return &relation, nil
}

// Create 创建机型关联信息
func (d *machineTypeRelationDaoImpl) Create(c *gin.Context, relation *model.MachineTypeRelation) error {
	relation.CreatedAt = time.Now()
	relation.UpdatedAt = time.Now()
	return d.db.WithContext(c).Create(relation).Error
}

// Update 更新机型关联信息
func (d *machineTypeRelationDaoImpl) Update(c *gin.Context, name string, updateMap map[string]interface{}) error {
	updateMap["updated_at"] = time.Now()
	return d.db.WithContext(c).Model(&model.MachineTypeRelation{}).Where("name = ?", name).Updates(updateMap).Error
}

func (d *machineTypeRelationDaoImpl) GetDelistList(c *gin.Context, req *api.DelistSearch) ([]*api.DelistListVo, int64, error) {
	var list []*api.DelistListVo
	var count int64
	query := d.db.WithContext(c).Model(&model.MachineType{})
	query = query.Joins("left join  machine_type_relation as r on machine_type.name = r.name")
	// 过滤条件
	if req.Model != "" {
		query = query.Where("model LIKE ?", "%"+req.Model+"%")
	}
	if req.Delist != nil {
		if *req.Delist == 0 {
			query = query.Where("r.delist = 0 or r.delist is null")
		} else {
			query = query.Where("r.delist = 1")
		}
	}
	if req.DelistTimeStart != "" {
		query = query.Where("r.delist_time >= ?", req.DelistTimeStart)
	}
	if req.DelistTimeEnd != "" {
		query = query.Where("r.delist_time <= ?", req.DelistTimeEnd)
	}
	query = query.Select([]string{
		"machine_type.model_id",
		"machine_type.name",
		"machine_type.model_name",
		"machine_type.category_id",
		"r.*",
	})

	query = query.Where("machine_type.created_at >= ?", "2021-01-01 00:00:00")
	query = query.Group("machine_type.name")

	err := query.Count(&count).Error
	if err != nil {
		return nil, 0, err
	}
	err = query.Offset((req.Page - 1) * req.PageSize).Limit(req.PageSize).Order("machine_type.model_id desc").Find(&list).Error
	if err != nil {
		return nil, 0, err
	}
	return list, count, nil
}
