package consts

import "time"

const (
	WarrantyNotAssessment int = 0
	WarrantyAssessment    int = 1

	Male   string = "m"
	Female string = "f"
)

const (
	WarrantyStatusVirtual   = 0 // 虚卡
	WarrantyStatusActive    = 1 // 正常
	WarrantyStatusExchanged = 2 // 换货
	WarrantyStatusReturned  = 3 // 退货
	WarrantyStatusOther     = 4 // 其他
	WarrantyStatusRenewed   = 5 // 换新
)

type OrderStatus int

const (
	OrderStatusCancelled        OrderStatus = -900 // 已取消
	OrderStatusAbandoned        OrderStatus = -700 // 已弃修
	OrderStatusSendBackFailed   OrderStatus = -800 // 回寄失败
	OrderStatusShipmentFailed   OrderStatus = -300 // 发货失败
	OrderStatusAuditNotApproved OrderStatus = -200 // 审核不通过

	OrderStatusPlaced        OrderStatus = 100 // 已下单
	OrderStatusAuditApproved OrderStatus = 200 // 审核通过
	OrderStatusUserShipped   OrderStatus = 300 // 用户已发货
	OrderStatusReceived      OrderStatus = 400 // 已收货
	OrderStatusNotified      OrderStatus = 410 // 已知会 (前端显示400)
	OrderStatusPreChecked    OrderStatus = 490 // 已预检测 (前端显示400)
	OrderStatusChecked       OrderStatus = 500 // 已检测
	OrderStatusUserPaid      OrderStatus = 600 // 用户已支付
	OrderStatusRepaired      OrderStatus = 700 // 已维修
	OrderStatusSentBack      OrderStatus = 800 // 已回寄
	OrderStatusCompleted     OrderStatus = 900 // 已完成
)

var ValidHeader = []string{
	"SN码", "机型", "终端账号(必填)", "购买日期(必填)", "顾客电话(必填)",
	"顾客姓名(必填)", "顾客性别", "学生姓名", "学生学校",
	"学生年级", "学生生日(请填上年份)", "学生性别",
}

var ValidDevicesLimitAccountHeader = []string{
	"序号", "经销商（店铺）", "折扣", "型号", "条形码（SN）", "登入个人中心手机号码",
}

const (
	HttpTimeout = 5 * time.Second
)

type OutStockStatus int

const (
	K3OutStockStatusDeleted OutStockStatus = 0
	K3OutStockStatusNormal  OutStockStatus = 1
)
