package service

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/api/materials"
	"marketing/internal/dao"
	"marketing/internal/model"
	myErrors "marketing/internal/pkg/errors"
	"strconv"
)

type MarketingService interface {
	Upsert(c *gin.Context, param materials.MarketingCategory) (uint, error)
	Save(c *gin.Context, param []materials.MarketingCategory) error
	List(c *gin.Context) (data []materials.MarketingCategory, err error)
	Detail(c *gin.Context, id string) (materials.MarketingCategory, error)
	Tips(c *gin.Context) ([]materials.Tips, error)
	InfoList(c *gin.Context, req materials.InfoListReq) (data []materials.MarketingDownload, total int64, err error)
	InfoDelete(c *gin.Context, id string) error
	InfoUpdate(c *gin.Context, id int, status int) error
	Delete(c *gin.Context, id string) error
	Upload(c *gin.Context, id string) error
	AllTips(c *gin.Context) ([]map[string]any, error)
}
type GormMarketingService struct {
	dao dao.MarketingDao
}

func (g *GormMarketingService) AllTips(c *gin.Context) ([]map[string]any, error) {
	tips, err := g.dao.AllTips(c)
	if err != nil {
		return nil, err
	}
	return tips, nil
}

func (g *GormMarketingService) Upload(c *gin.Context, id string) error {
	return g.dao.Upload(c, id)
}

func (g *GormMarketingService) Delete(c *gin.Context, id string) error {
	if g.dao.HadInfo(c, id) {
		return myErrors.NewErr("该类别下有资料，无法删除")
	}
	return g.dao.Delete(c, id)
}

func (g *GormMarketingService) InfoUpdate(c *gin.Context, id int, status int) error {
	return g.dao.InfoUpdate(c, id, status)
}

func (g *GormMarketingService) InfoDelete(c *gin.Context, id string) error {
	return g.dao.InfoDelete(c, id)
}

func (g *GormMarketingService) InfoList(c *gin.Context, req materials.InfoListReq) (data []materials.MarketingDownload, total int64, err error) {
	return g.dao.InfoList(c, req)
}

func (g *GormMarketingService) Tips(c *gin.Context) ([]materials.Tips, error) {
	tips, err := g.dao.Tips(c)
	NewTips := make([]materials.Tips, len(tips)+1)
	NewTips[0] = materials.Tips{
		ID:    0,
		Title: "ROOT",
	}
	for i, v := range tips {
		NewTips[i+1] = v
	}
	return NewTips, err
}

func (g *GormMarketingService) Detail(c *gin.Context, id string) (materials.MarketingCategory, error) {
	detail, err := g.dao.Detail(c, id)
	if err != nil {
		return materials.MarketingCategory{}, err
	}
	info := g.toDTO(detail)
	return info, nil
}

func (g *GormMarketingService) List(c *gin.Context) (data []materials.MarketingCategory, err error) {
	list, err := g.dao.List(c)
	data = make([]materials.MarketingCategory, len(list))
	for k, v := range list {
		data[k].ID = v.ID
		data[k].Title = v.Title
		data[k].ParentID = v.ParentID
		data[k].Route = v.Route
		data[k].Order = v.Order
		data[k].CreatedAt = v.CreatedAt.Format("2006-01-02 15:04:05")
		data[k].UpdatedAt = v.UpdatedAt.Format("2006-01-02 15:04:05")
		data[k].Count = v.Count
	}
	return data, err
}

func (g *GormMarketingService) Save(c *gin.Context, param []materials.MarketingCategory) error {
	domain := make([]model.MarketingCategory, len(param))
	for _, v := range param {
		if v.ParentID != 0 {
			v.Route = strconv.Itoa(int(v.ParentID)) + "," + strconv.Itoa(int(v.ID))
		} else {
			v.Route = strconv.Itoa(int(v.ID))
		}
		domain = append(domain, g.toDomain(v))
	}
	return g.dao.Save(c, domain)
}

// Upsert 插入或更新营销类别信息
func (g *GormMarketingService) Upsert(c *gin.Context, param materials.MarketingCategory) (uint, error) {
	domain := g.toDomain(param)
	if domain.ID != 0 {
		//route 排序
		if domain.ParentID != 0 {
			param.Route = strconv.Itoa(int(domain.ParentID)) + "," + strconv.Itoa(int(domain.ID))
		} else {
			param.Route = strconv.Itoa(int(domain.ID))
		}
		err := g.dao.Update(c, domain)
		return param.ID, err
	}
	return g.dao.Create(c, domain)
}

func (g *GormMarketingService) toDomain(param materials.MarketingCategory) model.MarketingCategory {
	return model.MarketingCategory{
		ID:       param.ID,
		Title:    param.Title,
		ParentID: param.ParentID,
	}
}
func (g *GormMarketingService) toDTO(detail model.MarketingCategory) materials.MarketingCategory {
	var info materials.MarketingCategory
	info.ID = detail.ID
	info.Title = detail.Title
	info.ParentID = detail.ParentID
	info.Route = detail.Route
	info.Order = detail.Order
	info.CreatedAt = detail.CreatedAt.Format("2006-01-02 15:04:05")
	info.UpdatedAt = detail.UpdatedAt.Format("2006-01-02 15:04:05")
	return info
}
func NewGormMarketingService(dao dao.MarketingDao) MarketingService {
	return &GormMarketingService{dao: dao}
}
