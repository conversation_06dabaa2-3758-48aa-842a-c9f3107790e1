package information

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/api/materials"
	"marketing/internal/handler"
	"marketing/internal/service"
)

type MarketingHandle struct {
	svc service.MarketingService
}

func (m *MarketingHandle) Upsert(c *gin.Context) {
	var param materials.MarketingCategory
	if err := c.ShouldBindJSON(&param); err != nil {
		handler.Error(c, err)
		return
	}
	id, err := m.svc.Upsert(c, param)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"id": id,
	})
}

func (m *MarketingHandle) Save(c *gin.Context) {
	var param []materials.MarketingCategory
	if err := c.ShouldBindJSON(&param); err != nil {
		handler.Error(c, err)
		return
	}
	err := m.svc.Save(c, param)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

func (m *MarketingHandle) List(c *gin.Context) {
	list, err := m.svc.List(c)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"list": list,
	})
}

func (m *MarketingHandle) Detail(c *gin.Context) {
	id := c.Param("id")
	detail, err := m.svc.Detail(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"detail": detail,
	})
}

// Tips 父类提示 创建类别需要的
func (m *MarketingHandle) Tips(c *gin.Context) {
	tips, err := m.svc.Tips(c)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"tips": tips,
	})
}

func (m *MarketingHandle) InfoList(c *gin.Context) {
	var param materials.InfoListReq
	if param.PageSize == 0 {
		param.PageSize = 10
	}
	if param.Page == 0 {
		param.Page = 1
	}
	if err := c.ShouldBind(&param); err != nil {
		handler.Error(c, err)
		return
	}
	list, total, err := m.svc.InfoList(c, param)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"list":      list,
		"page":      param.Page,
		"page_size": param.PageSize,
		"total":     total,
	})
}

func (m *MarketingHandle) InfoDelete(c *gin.Context) {
	id := c.Param("id")
	err := m.svc.InfoDelete(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

func (m *MarketingHandle) InfoUpdate(c *gin.Context) {
	var param struct {
		ID     int `json:"id"`
		Status int `json:"status"`
	}
	if err := c.ShouldBindJSON(&param); err != nil {
		handler.Error(c, err)
		return
	}
	err := m.svc.InfoUpdate(c, param.ID, param.Status)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

func (m *MarketingHandle) Delete(c *gin.Context) {
	id := c.Param("id")
	err := m.svc.Delete(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

// Upload 下载量
func (m *MarketingHandle) Upload(c *gin.Context) {
	id := c.Param("id")
	err := m.svc.Upload(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

// AllTips 所有提示
func (m *MarketingHandle) AllTips(c *gin.Context) {
	tips, err := m.svc.AllTips(c)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"tips": tips,
	})
}

func NewMarketingHandle(svc service.MarketingService) *MarketingHandle {
	return &MarketingHandle{svc: svc}
}
