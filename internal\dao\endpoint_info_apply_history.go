package dao

import "marketing/internal/model"

// EndpointInfoDAO handles database operations related to endpoints.
type EndpointInfoDAO struct {
}

type Snapshot struct {
	EndpointName string `json:"endpoint_name"`
	ChannelLevel int    `json:"channel_level"`
	Manager      string `json:"manager"`
	Phone        string `json:"phone"`
	//Coordinate   string `json:"coordinate"`
	Address   string  `json:"address"`
	Latitude  float64 `json:"latitude" gorm:"type:decimal(9,6);not null;default:0.000000;column:latitude;comment:'纬度，高德'"`
	Longitude float64 `json:"longitude" gorm:"type:decimal(9,6);not null;default:0.000000;column:longitude;comment:'经度，高德'"`
}

var EndpointInfoApplyHistory EndpointInfoDAO

// GetEndpointHistory retrieves historical records for a specific endpoint.
func (dao *EndpointInfoDAO) GetEndpointHistory(endpointID int) ([]model.EndpointInfoApply, error) {
	var history []model.EndpointInfoApply
	db := GetDB()
	err := db.Table("endpoint_info_apply as a").
		//Select("a.*, u.code").
		//Joins("LEFT JOIN endpoint as u ON a.enpoint_id = u.id").
		Where("a.endpoint_id = ?", endpointID).
		Order("a.id DESC").
		Find(&history).Error
	return history, err
}

// GetEndpointByID retrieves an endpoint by its ID.

// GetAgencyNames retrieves the names of agencies by their IDs.
func (dao *EndpointInfoDAO) GetAgencyNames(agencyIDs []int) (map[int]string, error) {
	var results []struct {
		ID   int
		Name string
	}
	db := GetDB()
	agencyNames := make(map[int]string)
	err := db.Table("agency").Where("id IN ?", agencyIDs).Select("agency.id,agency.name").Find(&results).Error
	if err != nil {
		return nil, err
	}
	for _, res := range results {
		agencyNames[res.ID] = res.Name
	}
	return agencyNames, nil
}
