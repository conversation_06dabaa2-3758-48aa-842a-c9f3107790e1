package service

import (
	"marketing/internal/dao"
	"time"
)

type DeclareSvcInterface interface {
	GetDeclareList(req *dao.GetDeclareParam) (interface{}, interface{})
}

type DeclareSvcImpl struct {
	declareRepo dao.DeclareDao
}

func NewDeclareSvc(declareRepo dao.DeclareDao) DeclareSvcInterface {
	return &DeclareSvcImpl{
		declareRepo: declareRepo,
	}
}

func (s *DeclareSvcImpl) GetDeclareList(req *dao.GetDeclareParam) (interface{}, interface{}) {
	req.Date = "202109"
	if req.Date == "" {
		start, _ := s.declareRepo.DeclareTimeLimit()
		now := time.Now()
		//如果最后一天  取当前月的数据
		if now.Day() >= start {
			req.Date = now.Format("200601")
		} else { // 如果是1号到start取上个月数据
			req.Date = now.AddDate(0, -1, 0).Format("200601")
		}
	}

	if req.DataType == "" {
		req.DataType = "inventory"
	}

	// 默认第一页,默认一页10条
	if req.PageNum == 0 {
		req.PageNum = 1
	}

	if req.PageSize == 0 {
		req.PageSize = 1
	}

	return s.declareRepo.GetDeclareList(req)

	// return s.declareRepo.ManageDeclareListPage(req.TopAgency, req.SecondAgency, req.Date, req.RegionName, req.PageNum, req.PageSize, req.DeclareType, req.Sort, req.DataType, req.Channel, nil)
}
