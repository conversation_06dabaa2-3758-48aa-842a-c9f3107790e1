package mes

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"github.com/go-resty/resty/v2"
	"marketing/internal/pkg/log"
	"strconv"
	"time"
)

const (
	key           = "Web"
	secret        = "M4S8tUB8OBBvIUN7"
	HttpTimeout   = 10 * time.Second
	serverAddress = "http://api-mes.readboy.com/index.php?s=/Api/Barcode/status.html"
)

type CheckMachineParams struct {
	Barcode string
	Imei    string
	Number  string
}

func generateAuthKey() string {
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	hash := md5.Sum([]byte(key + "-" + timestamp + "-" + secret))
	sn := hex.EncodeToString(hash[:])
	return fmt.Sprintf("%s-%s-%s", key, timestamp, sn)
}

func _request(url string) (map[string]interface{}, error) {
	authKey := generateAuthKey()
	url += "&authKey=" + authKey

	client := resty.New().SetTimeout(HttpTimeout)

	resp, err := client.R().Get(url)
	if err != nil {
		log.Error(fmt.Sprintf("request execution error: url=%s, error=%s\n", url, err))
		return nil, err
	}

	var data map[string]interface{}
	if err := json.Unmarshal(resp.Body(), &data); err != nil {
		log.Error(fmt.Sprintf("json unmarshal error: url=%s, error=%s\n", url, err))
		return nil, err
	}

	if data["errcode"] != "0" {
		log.Error(fmt.Sprintf("api error: url=%s, response=%s\n", url, data))
		return nil, fmt.Errorf(data["errmsg"].(string))
	}

	return data, nil
}

func CheckMachine(req CheckMachineParams) (any, error) {
	var params string
	if req.Barcode != "" {
		params = fmt.Sprintf("barcode=%s", req.Barcode)
	} else if req.Imei != "" {
		params = fmt.Sprintf("imei=%s", req.Imei)
	} else if req.Number != "" {
		params = fmt.Sprintf("number=%s", req.Number)
	} else {
		return nil, nil
	}

	url := fmt.Sprintf("%s&%s", serverAddress, params)
	ret, err := _request(url)
	if err != nil {
		return nil, err
	}

	data := ret["data"].(map[string]interface{})
	if updateTime, ok := data["update_time"].(string); ok {
		data["product_date"] = updateTime
		delete(data, "update_time")

	}
	return data, nil
}
