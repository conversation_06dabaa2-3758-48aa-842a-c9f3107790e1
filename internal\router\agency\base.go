package agency

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/config"
	"marketing/internal/dao"
	userDao "marketing/internal/dao/admin_user"
	"marketing/internal/dao/endpoint"
	baseAgency "marketing/internal/handler/agency/base"
	"marketing/internal/handler/base"
	"marketing/internal/pkg/db"
	"marketing/internal/pkg/oss"
	"marketing/internal/pkg/upload"
	"marketing/internal/provider"
	"marketing/internal/service"
	baseSvc "marketing/internal/service/base"
	endpointService "marketing/internal/service/endpoint"
)

type BaseRouter struct {
	cfg           *config.Config
	svc           *provider.ServiceProvider
	daoProvide    *provider.DaoProvider
	regionHandler base.RegionHandler
}

func NewBaseRouter(cfg *config.Config, svc *provider.ServiceProvider, daoProvide *provider.DaoProvider, regionHandler base.RegionHandler) *BaseRouter {
	return &BaseRouter{
		cfg:           cfg,
		svc:           svc,
		daoProvide:    daoProvide,
		regionHandler: regionHandler,
	}
}

func (b *BaseRouter) Register(r *gin.RouterGroup) {
	//公共模块
	baseRouter := r.Group("/base")
	{
		client := oss.NewClient()
		uploadService := service.NewOssUploadService(upload.NewOssUploadServiceV2(client))
		uploadController := base.NewHandlerUploadFileV2(uploadService)
		baseRouter.POST("/upload_file/:path", uploadController.UploadFile)
		baseRouter.DELETE("/delete_file", uploadController.DeleteFile)

		// 用户终端信息
		var Db = db.GetDB()
		userD := userDao.NewUserDao(Db)
		endpointDao := endpoint.NewEndpointDao(Db)
		endpointSvc := endpointService.NewEndpoint(Db, endpointDao, userD)
		endpointHandler := baseAgency.NewEndpointHandler(endpointSvc)
		baseRouter.GET("/user/second-agency", endpointHandler.GetUserSecondAgency)
		baseRouter.GET("/user/endpoint", endpointHandler.GetAgencyUserEndpoint)

		//oss 临时凭证
		ossService := baseSvc.NewOssService(b.cfg)
		ossHandler := base.NewOssHandler(ossService)
		baseRouter.GET("/oss/sts", ossHandler.GetStsToken)

		// 企业微信相关接口
		wecomService := baseSvc.NewWeComService(b.svc.AppSystemService)
		wecomHandler := base.NewWeComHandler(wecomService)
		baseRouter.GET("/wecom/jsapi_ticket", wecomHandler.GetJsapiTicket)

		// 代理商金蝶账户
		//获取渠道类型
		channelDao := dao.NewChannelDao()
		channelService := service.NewChannelService(channelDao)
		agencyHandler := base.NewAgencyHandler(channelService, b.daoProvide.UserDao)

		//获取代理公司列表
		baseRouter.GET("/agency-company", agencyHandler.AgencyCompany)

		//获取地区
		baseRouter.GET("/region", b.regionHandler.RegionList)

		//获取终端类型
		endpointTypeRepo := dao.NewEndpointTypeRepository(Db)
		endpointTypeService := baseSvc.NewEndpointTypeService(endpointTypeRepo)
		endpointTypeHandler := base.NewEndpointTypeHandler(endpointTypeService)
		baseRouter.GET("/endpoint-type", endpointTypeHandler.GetEndpointType)
	}
}
