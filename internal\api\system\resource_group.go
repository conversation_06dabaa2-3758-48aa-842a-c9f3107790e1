package system

type ResourceGroupReq struct {
	ID          uint   `json:"id"`
	Name        string `json:"name" binding:"required"`
	QWPartyID   int    `json:"qw_partyid"`
	QWName      string `json:"qw_name"`
	Description string `json:"description"`
}

type ListResourceGroupReq struct {
	ID       uint
	Name     string
	Page     int
	PageSize int
}

type ResourceGroupsResp struct {
	ID          uint   `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	QWPartyID   int    `json:"qw_partyid"`
	QWName      string `json:"qw_name"`
	UpdatedAt   string `json:"updated_at"`
	CreatedAt   string `json:"created_at"`
}
