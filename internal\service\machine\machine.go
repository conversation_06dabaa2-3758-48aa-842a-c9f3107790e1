package machine

import (
	"errors"
	"fmt"
	"gorm.io/gorm"
	api "marketing/internal/api/machine"
	"marketing/internal/api/prototype"
	"marketing/internal/dao"
	"marketing/internal/model"
	appError "marketing/internal/pkg/errors"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/xuri/excelize/v2"
)

type TMachineTypeSvc interface {
	GetMachineTypeList(c *gin.Context, name string, pageNum, pageSize int) ([]*model.MachineType, int64)
	EditMachineType(c *gin.Context, m *model.MachineType) error
	GetRepairMachineTypeList(c *gin.Context, modelId string, categoryId, visibility, pageNum, pageSize int) ([]*model.RepairMachineType, int64)
	ExportRepairMachineTypeList(c *gin.Context, modelId string, categoryId, visibility, pageNum, pageSize int) *excelize.File
	GetMachineTypeByModelId(c *gin.Context, modelId int) (*api.MachineTypeInfo, error)
	GetListNoPage(c *gin.Context, req api.MachineTypeReq) ([]map[string]any, error)
	UpdateMarketDate(c *gin.Context, name string, modelId int, marketDate time.Time, price float64) error
	UpdateDiscontinued(ctx *gin.Context, req *prototype.UpdateDiscontinuedReq) error
}

type TMachineTypeSvcImpl struct {
	machineTypeRepo        dao.MachineTypeDao
	categoryRepo           dao.ModelCategoryDao
	machineTypeRelationDao dao.MachineTypeRelationDao
}

func NewMaterialService(machineTypeRepo dao.MachineTypeDao, categoryRepo dao.ModelCategoryDao, machineTypeRelationDao dao.MachineTypeRelationDao) TMachineTypeSvc {
	return &TMachineTypeSvcImpl{
		machineTypeRepo:        machineTypeRepo,
		categoryRepo:           categoryRepo,
		machineTypeRelationDao: machineTypeRelationDao,
	}
}

func (s *TMachineTypeSvcImpl) GetMachineTypeList(c *gin.Context, name string, pageNum, pageSize int) ([]*model.MachineType, int64) {
	list, total := s.machineTypeRepo.GetMachineTypeList(c, name, pageNum, pageSize)

	categoryIds := make([]int, 0)
	for _, l := range list {
		categoryIds = append(categoryIds, l.CategoryId)
	}

	categoryList := s.categoryRepo.GetModelCategoryByIds(c, categoryIds)
	for _, l := range list {
		for _, category := range categoryList {
			if l.CategoryId == int(category.Id) {
				l.CategoryName = category.Name
				break
			}
		}
	}

	return list, total
}

func (s *TMachineTypeSvcImpl) EditMachineType(c *gin.Context, m *model.MachineType) error {
	if s.categoryRepo.GetModelCategoryById(c, m.CategoryId) == nil {
		return errors.New("编辑机器分类:机器标签不存在")
	}

	uMap := make(map[string]interface{})
	uMap["nav_name"] = m.NavName
	uMap["model_name"] = m.ModelName
	uMap["category_id"] = m.CategoryId
	uMap["company_price"] = m.CompanyPrice
	uMap["top_agency_price"] = m.TopAgencyPrice
	uMap["second_agency_price"] = m.SecondAgencyPrice
	uMap["customer_price"] = m.CustomerPrice
	uMap["chart_show"] = m.ChartShow
	uMap["prototype_status"] = m.PrototypeStatus
	uMap["prototype_apk_path"] = m.PrototypeApkPath
	uMap["version_code"] = m.VersionCode
	uMap["ext_barcode_num"] = m.ExtBarcodeNum
	uMap["declare"] = m.Declare
	uMap["stock"] = m.Stock
	uMap["visibility"] = m.Visibility

	return s.machineTypeRepo.EditMachineType(c, m.Id, uMap)
}

func (s *TMachineTypeSvcImpl) GetRepairMachineTypeList(c *gin.Context, modelName string, categoryId, visibility, pageNum, pageSize int) ([]*model.RepairMachineType, int64) {
	return s.machineTypeRepo.GetRepairMachineTypeList(c, modelName, categoryId, visibility, pageNum, pageSize)
}

func (s *TMachineTypeSvcImpl) GetMachineTypeByModelId(c *gin.Context, modelId int) (*api.MachineTypeInfo, error) {
	return s.machineTypeRepo.GetMachineTypeInfo(c, modelId)
}

func (s *TMachineTypeSvcImpl) ExportRepairMachineTypeList(c *gin.Context, modelName string, categoryId, visibility, pageNum, pageSize int) *excelize.File {
	list, _ := s.GetRepairMachineTypeList(c, modelName, categoryId, visibility, pageNum, pageSize)

	sheetName := "机型表"

	// 新建一个excel文件,并添加表
	file := excelize.NewFile()
	_, _ = file.NewSheet(sheetName)

	// 设置所有列居中
	style, _ := file.NewStyle(&excelize.Style{
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
		Font: &excelize.Font{
			Size: 12,
		},
	})

	_ = file.SetCellValue(sheetName, "A1", "Id")
	_ = file.SetCellStyle(sheetName, "A1", "A1", style)
	_ = file.SetColWidth(sheetName, "A", "A", 10.0)
	_ = file.SetCellValue(sheetName, "B1", "型号")
	_ = file.SetCellStyle(sheetName, "B1", "B1", style)
	_ = file.SetColWidth(sheetName, "B", "B", 15.0)
	_ = file.SetCellValue(sheetName, "C1", "所属分类")
	_ = file.SetCellStyle(sheetName, "C1", "C1", style)
	_ = file.SetColWidth(sheetName, "C", "C", 15.0)
	_ = file.SetCellValue(sheetName, "D1", "创建时间")
	_ = file.SetCellStyle(sheetName, "D1", "D1", style)
	_ = file.SetColWidth(sheetName, "D", "D", 25.0)
	_ = file.SetCellValue(sheetName, "E1", "支持寄修")
	_ = file.SetCellStyle(sheetName, "E1", "E1", style)
	_ = file.SetColWidth(sheetName, "E", "E", 15.0)

	for i, l := range list {
		index := i + 2

		v := "否"
		if l.Visibility == 1 {
			v = "是"
		}

		_ = file.SetCellValue(sheetName, fmt.Sprintf("A%d", index), l.Id)
		_ = file.SetCellStyle(sheetName, fmt.Sprintf("A%d", index), fmt.Sprintf("A%d", index), style)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("B%d", index), l.ModelName)
		_ = file.SetCellStyle(sheetName, fmt.Sprintf("B%d", index), fmt.Sprintf("B%d", index), style)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("C%d", index), l.CategoryName)
		_ = file.SetCellStyle(sheetName, fmt.Sprintf("C%d", index), fmt.Sprintf("C%d", index), style)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("D%d", index), l.CreateTime)
		_ = file.SetCellStyle(sheetName, fmt.Sprintf("D%d", index), fmt.Sprintf("D%d", index), style)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("E%d", index), v)
		_ = file.SetCellStyle(sheetName, fmt.Sprintf("E%d", index), fmt.Sprintf("E%d", index), style)
	}

	// 这一步是删除默认创建的Sheet1表
	_ = file.DeleteSheet("Sheet1")

	return file
}
func (s *TMachineTypeSvcImpl) GetListNoPage(c *gin.Context, req api.MachineTypeReq) ([]map[string]any, error) {
	data, err := s.machineTypeRepo.GetListNoPage(c, req)
	if err != nil {
		return nil, err
	}
	//category
	categoryList := s.categoryRepo.GetAllModelCategory(c)
	categoryMap := make(map[int]string)
	for _, v := range categoryList {
		categoryMap[v.Id] = v.Name
	}

	var res []map[string]any
	for _, v := range data {
		// 分类名称
		categoryName := categoryMap[v.CategoryId]
		temp := make(map[string]any)
		temp["value"] = v.ModelId
		temp["label"] = v.Name
		temp["category"] = categoryName
		res = append(res, temp)
	}
	return res, nil
}

// UpdateMarketDate 更新上市日期
func (s *TMachineTypeSvcImpl) UpdateMarketDate(c *gin.Context, name string, modelId int, marketDate time.Time, price float64) error {
	// 获取机型信息
	machineInfo := s.machineTypeRepo.GetMachineTypeByModelId(c, modelId)
	if machineInfo == nil {
		return errors.New("机型不存在")
	}

	// 准备更新数据
	updateData := map[string]interface{}{
		"market_date": marketDate,
		"price":       price,
		"updated_at":  time.Now(),
	}

	// 查询该机型是否已在配置表
	relation, err := s.machineTypeRelationDao.GetByName(c, name)

	if err == nil && relation != nil {
		// 更新现有记录
		return s.machineTypeRelationDao.Update(c, name, updateData)
	} else {
		// 创建新记录
		newRelation := &model.MachineTypeRelation{
			Name:       name,
			CategoryID: machineInfo.CategoryId,
			Declare:    machineInfo.Declare,
			Stock:      machineInfo.Stock,
			Price:      price,
			MarketDate: &marketDate,
			CreatedAt:  time.Now(),
			UpdatedAt:  time.Now(),
		}

		return s.machineTypeRelationDao.Create(c, newRelation)
	}
}

// UpdateDiscontinued 更新机型演示样机下市
func (s *TMachineTypeSvcImpl) UpdateDiscontinued(ctx *gin.Context, req *prototype.UpdateDiscontinuedReq) error {
	// 获取机型信息
	machineInfo := s.machineTypeRepo.GetMachineTypeByModelId(ctx, req.ID)
	if machineInfo == nil || machineInfo.Id == 0 {
		return appError.NewErr("机型不存在")
	}

	// 查询该机型是否已在配置表
	relation, err := s.machineTypeRelationDao.GetByName(ctx, machineInfo.Name)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return appError.NewErr("获取机型关联信息失败: " + err.Error())
	}

	if relation == nil {
		// 如果机型在配置表中不存在，创建
		now := time.Now()
		newRelation := &model.MachineTypeRelation{
			Name:             machineInfo.Name,
			CategoryID:       machineInfo.CategoryId,
			Declare:          machineInfo.Declare,
			Stock:            machineInfo.Stock,
			Discontinued:     *req.Discontinued,
			DiscontinuedDate: &now,
		}
		if err := s.machineTypeRelationDao.Create(ctx, newRelation); err != nil {
			return appError.NewErr("创建机型关联记录失败: " + err.Error())
		}
		return nil
	}

	// 更新下市状态
	updateData := map[string]interface{}{}
	if *req.Discontinued == 1 {
		updateData = map[string]interface{}{
			"discontinued":      req.Discontinued,
			"discontinued_date": time.Now(),
		}
	} else {
		updateData = map[string]interface{}{
			"discontinued":      req.Discontinued,
			"discontinued_date": nil,
		}
	}
	if err := s.machineTypeRelationDao.Update(ctx, machineInfo.Name, updateData); err != nil {
		return appError.NewErr("更新停售状态失败: " + err.Error())
	}

	return nil
}
