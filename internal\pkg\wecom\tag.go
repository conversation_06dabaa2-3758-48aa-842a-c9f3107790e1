package wecom

import (
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"marketing/internal/pkg/log"
)

// CreateTagReq 用于构造创建标签的请求
type CreateTagReq struct {
	TagName string `json:"tagname"`
	TagID   int    `json:"tagid,omitempty"`
}

// UpdateTagReq 用于构造更新标签的请求
type UpdateTagReq struct {
	TagID   int    `json:"tagid"`
	TagName string `json:"tagname"`
}

// AddTagUsersReq 用于构造增加标签成员的请求
type AddTagUsersReq struct {
	TagID     int      `json:"tagid"`
	UserList  []string `json:"userlist,omitempty"`
	PartyList []int    `json:"partylist,omitempty"`
}

// TagResp 用于解析更新标签的响应
type TagResp struct {
	ErrCode int    `json:"errcode"`
	ErrMsg  string `json:"errmsg"`
	TagID   int    `json:"tagid"`
}

// DeleteTagUsersReq 用于构造删除标签成员的请求
type DeleteTagUsersReq struct {
	TagID     int      `json:"tagid"`
	UserList  []string `json:"userlist,omitempty"`
	PartyList []int    `json:"partylist,omitempty"`
}

// TagMembersResp 用于解析获取标签成员的响应
type TagMembersResp struct {
	ErrCode  int    `json:"errcode"`
	ErrMsg   string `json:"errmsg"`
	TagName  string `json:"tagname"`
	UserList []struct {
		UserID string `json:"userid"`
		Name   string `json:"name"`
	} `json:"userlist"`
	PartyList []int `json:"partylist"`
}

// TagListResp 用于解析获取标签列表的响应
type TagListResp struct {
	ErrCode int    `json:"errcode"`
	ErrMsg  string `json:"errmsg"`
	TagList []struct {
		TagID   int    `json:"tagid"`
		TagName string `json:"tagname"`
	} `json:"taglist"`
}

// DeleteTagUsersResp 用于解析删除标签成员的响应
type DeleteTagUsersResp struct {
	ErrCode      int    `json:"errcode"`
	ErrMsg       string `json:"errmsg"`
	InvalidList  string `json:"invalidlist,omitempty"`
	InvalidParty []int  `json:"invalidparty,omitempty"`
}

// CreateTag 创建标签
func (client *Client) CreateTag(tagName string, tagID uint) (int, error) {
	accessToken, err := client.GetAccessToken()
	if err != nil {
		return 0, err
	}
	url := fmt.Sprintf("https://qyapi.weixin.qq.com/cgi-bin/tag/create?access_token=%s", accessToken)

	reqBody := CreateTagReq{
		TagName: tagName,
		TagID:   int(tagID),
	}

	resp, err := client.httpClient.R().
		SetBody(reqBody).
		Post(url)
	if err != nil {
		log.Error("微信创建标签请求信息错误,发送post请求的时候出错：", zap.Error(err))
		return 0, err
	}

	log.Debug("企微创建标签：", zap.Any("企微创建标签响应", resp))

	var createTagResp TagResp
	if err := json.Unmarshal(resp.Body(), &createTagResp); err != nil {
		log.Error("微信创建标签请求信息错误,json解析错误：", zap.Error(err))
		return 0, err
	}

	if createTagResp.ErrCode != 0 {
		log.Error("微信创建标签请求信息错误，微信接口错误：", zap.Int("errcode", createTagResp.ErrCode), zap.String("errmsg", createTagResp.ErrMsg), zap.String("URL", url))
		return 0, &WeChatAPIError{ErrCode: createTagResp.ErrCode, ErrMsg: createTagResp.ErrMsg}
	}

	return createTagResp.TagID, nil
}

// UpdateTag 更新标签
func (client *Client) UpdateTag(tagID int, tagName string) error {
	accessToken, err := client.GetAccessToken()
	if err != nil {
		return err
	}
	url := fmt.Sprintf("https://qyapi.weixin.qq.com/cgi-bin/tag/update?access_token=%s", accessToken)

	reqBody := UpdateTagReq{
		TagID:   tagID,
		TagName: tagName,
	}

	resp, err := client.httpClient.R().
		SetBody(reqBody).
		Post(url)
	if err != nil {
		return err
	}

	log.Debug("企微更新标签：", zap.Any("企微更新标签响应", resp))

	var updateTagResp TagResp
	if err := json.Unmarshal(resp.Body(), &updateTagResp); err != nil {
		log.Error("微信更新标签请求信息错误,json解析错误：", zap.Error(err))
		return err
	}

	if updateTagResp.ErrCode != 0 {
		log.Error("微信更新标签请求信息错误，微信接口错误：", zap.Int("errcode", updateTagResp.ErrCode), zap.String("errmsg", updateTagResp.ErrMsg), zap.String("URL", url))
		return &WeChatAPIError{ErrCode: updateTagResp.ErrCode, ErrMsg: updateTagResp.ErrMsg}
	}

	return nil
}

// DeleteTag 删除标签
func (client *Client) DeleteTag(tagID int) error {
	accessToken, err := client.GetAccessToken()
	if err != nil {
		return err
	}
	url := fmt.Sprintf("https://qyapi.weixin.qq.com/cgi-bin/tag/delete?access_token=%s&tagid=%d", accessToken, tagID)

	resp, err := client.httpClient.R().Get(url)
	if err != nil {
		return err
	}

	log.Debug("企微删除标签：", zap.Any("企微删除标签响应", resp))

	var deleteTagResp TagResp
	if err := json.Unmarshal(resp.Body(), &deleteTagResp); err != nil {
		log.Error("微信删除标签请求信息错误,json解析错误：", zap.Error(err))
		return err
	}

	if deleteTagResp.ErrCode != 0 {
		log.Error("微信删除标签请求信息错误，微信接口错误：", zap.Int("errcode", deleteTagResp.ErrCode), zap.String("errmsg", deleteTagResp.ErrMsg), zap.String("URL", url))
		return &WeChatAPIError{ErrCode: deleteTagResp.ErrCode, ErrMsg: deleteTagResp.ErrMsg}
	}

	return nil
}

// GetTagMembers 获取标签成员
func (client *Client) GetTagMembers(tagID int) (*TagMembersResp, error) {
	accessToken, err := client.GetAccessToken()
	if err != nil {
		return nil, err
	}
	url := fmt.Sprintf("https://qyapi.weixin.qq.com/cgi-bin/tag/get?access_token=%s&tagid=%d", accessToken, tagID)

	resp, err := client.httpClient.R().Get(url)
	if err != nil {
		return nil, err
	}

	log.Debug("企微获取标签成员：", zap.Any("企微获取标签成员响应", resp))

	var tagMembersResp TagMembersResp
	if err := json.Unmarshal(resp.Body(), &tagMembersResp); err != nil {
		log.Error("微信获取标签成员请求信息错误,json解析错误：", zap.Error(err))
		return nil, err
	}

	if tagMembersResp.ErrCode != 0 {
		log.Error("微信获取标签成员请求信息错误，微信接口错误：", zap.Int("errcode", tagMembersResp.ErrCode), zap.String("errmsg", tagMembersResp.ErrMsg), zap.String("URL", url))
		return nil, &WeChatAPIError{ErrCode: tagMembersResp.ErrCode, ErrMsg: tagMembersResp.ErrMsg}
	}

	return &tagMembersResp, nil
}

// AddTagUsers 增加标签成员
func (client *Client) AddTagUsers(tagID int, userList []string, partyList []int) error {
	accessToken, err := client.GetAccessToken()
	if err != nil {
		return err
	}
	url := fmt.Sprintf("https://qyapi.weixin.qq.com/cgi-bin/tag/addtagusers?access_token=%s", accessToken)

	reqBody := AddTagUsersReq{
		TagID:     tagID,
		UserList:  userList,
		PartyList: partyList,
	}

	resp, err := client.httpClient.R().
		SetBody(reqBody).
		Post(url)
	if err != nil {
		return err
	}

	log.Debug("企微增加标签成员：", zap.Any("企微增加标签成员响应", resp))

	var addTagUsersResp TagResp
	if err := json.Unmarshal(resp.Body(), &addTagUsersResp); err != nil {
		log.Error("微信增加标签成员请求信息错误,json解析错误：", zap.Error(err))
		return err
	}
	if addTagUsersResp.ErrCode != 0 {
		log.Error("微信增加标签成员请求信息错误，微信接口错误：", zap.Int("errcode", addTagUsersResp.ErrCode), zap.String("errmsg", addTagUsersResp.ErrMsg), zap.String("URL", url))
		return &WeChatAPIError{ErrCode: addTagUsersResp.ErrCode, ErrMsg: addTagUsersResp.ErrMsg}
	}

	return nil
}

// GetTagList 获取标签列表
func (client *Client) GetTagList() (*TagListResp, error) {
	accessToken, err := client.GetAccessToken()
	if err != nil {
		return nil, err
	}
	url := fmt.Sprintf("https://qyapi.weixin.qq.com/cgi-bin/tag/list?access_token=%s", accessToken)

	resp, err := client.httpClient.R().Get(url)
	if err != nil {
		return nil, err
	}

	log.Debug("企微获取标签列表：", zap.Any("企微获取标签列表响应", resp))

	var tagListResp TagListResp
	if err := json.Unmarshal(resp.Body(), &tagListResp); err != nil {
		log.Error("微信获取标签列表请求信息错误,json解析错误：", zap.Error(err))
		return nil, err
	}

	if tagListResp.ErrCode != 0 {
		log.Error("微信获取标签列表请求信息错误，微信接口错误：", zap.Int("errcode", tagListResp.ErrCode), zap.String("errmsg", tagListResp.ErrMsg), zap.String("URL", url))
		return nil, &WeChatAPIError{ErrCode: tagListResp.ErrCode, ErrMsg: tagListResp.ErrMsg}
	}

	return &tagListResp, nil
}

// DeleteTagUsers 删除标签成员
func (client *Client) DeleteTagUsers(tagID int, userList []string, partyList []int) (*DeleteTagUsersResp, error) {
	accessToken, err := client.GetAccessToken()
	if err != nil {
		return nil, err
	}
	url := fmt.Sprintf("https://qyapi.weixin.qq.com/cgi-bin/tag/deltagusers?access_token=%s", accessToken)

	reqBody := DeleteTagUsersReq{
		TagID:     tagID,
		UserList:  userList,
		PartyList: partyList,
	}

	resp, err := client.httpClient.R().
		SetBody(reqBody).
		Post(url)
	if err != nil {
		log.Error("微信删除标签成员请求信息错误,发送post请求的时候出错：", zap.Error(err))
		return nil, err
	}

	log.Info("企微删除标签成员：", zap.Any("企微删除标签成员响应", resp))

	var deleteTagUsersResp DeleteTagUsersResp
	if err := json.Unmarshal(resp.Body(), &deleteTagUsersResp); err != nil {
		log.Error("微信删除标签成员请求信息错误,json解析错误：", zap.Error(err))
		return nil, err
	}

	if deleteTagUsersResp.ErrCode != 0 {
		log.Error("微信删除标签成员请求信息错误，微信接口错误：", zap.Int("errcode", deleteTagUsersResp.ErrCode), zap.String("errmsg", deleteTagUsersResp.ErrMsg), zap.String("URL", url))
		return nil, &WeChatAPIError{ErrCode: deleteTagUsersResp.ErrCode, ErrMsg: deleteTagUsersResp.ErrMsg}
	}

	return &deleteTagUsersResp, nil
}
