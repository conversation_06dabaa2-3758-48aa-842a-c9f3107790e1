package utils

import (
	"context"
	"crypto/md5"
	"crypto/rand"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"log"
	rand2 "math/rand"
	"reflect"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/redis/go-redis/v9"
)

var (
	inboxCounter   = make(map[string]int) // 按分钟存储计数器
	inboxCounterMu sync.Mutex
)

// StructToJSONString converts a struct to a JSON string.
// If pretty is true, it returns a pretty-printed JSON string.
func StructToJSONString(v any, pretty bool) string {
	var jsonData []byte
	var err error

	if pretty {
		jsonData, err = json.MarshalIndent(v, "", "  ")
	} else {
		jsonData, err = json.Marshal(v)
	}

	if err != nil {
		log.Printf("Error marshaling struct to JSON: %v", err)
		return ""
	}

	return string(jsonData)
}

// ToJSON 将数据转换为JSON字符串
func ToJSON(v interface{}) string {
	if v == nil {
		return ""
	}

	// 使用 json.MarshalIndent 生成格式化的 JSON
	bytes, err := json.Marshal(v)
	if err != nil {
		return ""
	}

	return string(bytes)
}

// JSONMaskFields 对JSON中的敏感字段进行脱敏
func JSONMaskFields(jsonStr string, sensitiveFields []string) string {
	if jsonStr == "" || len(sensitiveFields) == 0 {
		return jsonStr
	}

	var data map[string]interface{}
	if err := json.Unmarshal([]byte(jsonStr), &data); err != nil {
		return jsonStr
	}

	// 递归处理敏感字段
	maskMap(data, sensitiveFields)

	// 重新序列化
	//result, err := json.MarshalIndent(data, "", "    ")
	result, err := json.Marshal(data)
	if err != nil {
		return jsonStr
	}

	return string(result)
}

// maskMap 递归处理 map 中的敏感字段
func maskMap(data map[string]interface{}, sensitiveFields []string) {
	for k, v := range data {
		switch val := v.(type) {
		case map[string]interface{}:
			maskMap(val, sensitiveFields)
		case []interface{}:
			for _, item := range val {
				if m, ok := item.(map[string]interface{}); ok {
					maskMap(m, sensitiveFields)
				}
			}
		default:
			// 检查是否是敏感字段
			for _, field := range sensitiveFields {
				if strings.EqualFold(k, field) {
					data[k] = "******"
					break
				}
			}
		}
	}
}

func GenerateRandomString32() (string, error) {
	// 创建一个字节切片，用于存储随机字节数据
	bytes := make([]byte, 16) // 16 bytes = 32 hex characters
	_, err := rand.Read(bytes)
	if err != nil {
		return "", err
	}

	// 将字节数据编码为十六进制字符串
	return hex.EncodeToString(bytes), nil
}

// IsValidPhone 验证手机号格式
func IsValidPhone(phone string) bool {
	if phone == "" {
		return false
	}
	// 中国大陆手机号的正则表达式
	phoneRegex := regexp.MustCompile(`^1[3-9]\d{9}$`)
	return phoneRegex.MatchString(phone)
}

func MD5(input string) string {
	// 创建新的MD5哈希对象
	hasher := md5.New()

	// 将输入字符串写入哈希对象
	hasher.Write([]byte(input))

	// 计算哈希并返回十六进制表示的字符串
	return hex.EncodeToString(hasher.Sum(nil))
}

// GenerateUniqueSN 生成唯一流水号(格式:YYYYMMDDHHmmss + 8位随机数)，核销订单号
func GenerateUniqueSN() string {
	ts := time.Now().Format("20060102150405")
	randNum := rand2.New(rand2.NewSource(time.Now().UnixNano())).Intn(90000000) + 10000000
	return fmt.Sprintf("%s%d", ts, randNum)
}

func CompareFieldChanges(old, update interface{}, oldData *map[string]any, newData *map[string]any) {
	// 确保映射已初始化
	if *oldData == nil {
		*oldData = make(map[string]any)
	}
	if *newData == nil {
		*newData = make(map[string]any)
	}

	// 解析结构体值（处理指针和值类型）
	oldVal := reflect.Indirect(reflect.ValueOf(old))
	newVal := reflect.Indirect(reflect.ValueOf(update))

	// 检查是否为结构体且类型相同
	if oldVal.Kind() != reflect.Struct || newVal.Kind() != reflect.Struct ||
		oldVal.Type() != newVal.Type() {
		return
	}

	// 遍历所有字段
	for i := 0; i < oldVal.NumField(); i++ {
		fieldType := oldVal.Type().Field(i)
		fieldName := fieldType.Name

		// 跳过非导出字段
		if fieldType.PkgPath != "" {
			continue
		}

		oldField := oldVal.Field(i)
		newField := newVal.Field(i)

		// 比较字段值
		if !reflect.DeepEqual(oldField.Interface(), newField.Interface()) {
			(*oldData)[fieldName] = oldField.Interface()
			(*newData)[fieldName] = newField.Interface()
		}
	}
}

// GenInboxIds 按 PHP genInboxIds 规则生成唯一ID
// 格式: yymmddHHMM + 序号(5位, 左补0)
// 使用Redis保证多实例下的唯一性
func GenInboxIds(volume int) ([]int64, error) {
	if volume <= 0 {
		return []int64{}, nil
	}

	// 当前时间（精确到分钟）
	date := time.Now().Format("0601021504") // Go: 06=yy,01=MM,02=dd,15=HH,04=mm

	// 计数器加锁（本地锁，防止同一实例并发）
	inboxCounterMu.Lock()
	defer inboxCounterMu.Unlock()

	// 自增 volume
	value := inboxCounter[date] + volume
	inboxCounter[date] = value

	// 每分钟最多 99999
	if value > 99999 {
		return nil, fmt.Errorf("调用太频繁了，1分钟内不能超过99999个id")
	}

	// 计算起始值
	startValue := value - volume + 1

	// 拼接 ID（date + 5位序号）
	startStr := date + fmt.Sprintf("%05d", startValue)
	start, _ := strconv.ParseInt(startStr, 10, 64)

	// 返回范围
	ids := make([]int64, volume)
	for i := 0; i < volume; i++ {
		ids[i] = start + int64(i)
	}
	return ids, nil
}

// GenInboxIdsWithRedis 使用Redis保证多实例下的唯一性生成收件箱ID
// 格式: yymmddHHMM + 序号(5位, 左补0)
func GenInboxIdsWithRedis(ctx context.Context, redisClient *redis.Client, volume int) ([]int64, error) {
	if volume <= 0 {
		return []int64{}, nil
	}

	// 当前时间（精确到分钟）
	date := time.Now().Format("0601021504") // Go: 06=yy,01=MM,02=dd,15=HH,04=mm
	redisKey := fmt.Sprintf("inbox_counter:%s", date)

	// 使用Redis原子操作获取序号
	value, err := redisClient.IncrBy(ctx, redisKey, int64(volume)).Result()
	if err != nil {
		return nil, fmt.Errorf("redis操作失败: %w", err)
	}

	// 设置过期时间（2分钟后过期，确保不会无限增长）
	redisClient.Expire(ctx, redisKey, 2*time.Minute)

	// 每分钟最多 99999
	if value > 99999 {
		return nil, fmt.Errorf("调用太频繁了，1分钟内不能超过99999个id")
	}

	// 计算起始值
	startValue := value - int64(volume) + 1

	// 拼接 ID（date + 5位序号）
	startStr := date + fmt.Sprintf("%05d", startValue)
	start, _ := strconv.ParseInt(startStr, 10, 64)

	// 返回范围
	ids := make([]int64, volume)
	for i := 0; i < volume; i++ {
		ids[i] = start + int64(i)
	}
	return ids, nil
}
