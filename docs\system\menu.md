# 菜单管理 API 文档

[TOC]

## 1. 获取菜单列表

##### 简要描述
- 获取系统菜单列表，支持树形结构

##### 请求URL
- `/admin/system/menus`

##### 请求方式
- GET

##### 参数
| 参数名    | 必选 | 类型   | 说明                           |
|-----------|------|--------|--------------------------------|
| title     | 否   | string | 菜单标题，支持模糊搜索         |

##### 返回示例
```json
{
    "ok": 1,
    "msg": "ok",
    "data": [
        {
            "id": 1,
            "parent_id": 0,
            "title": "系统管理",
            "icon": "setting",
            "path": "/system",
            "component": "Layout",
            "sort": 1,
            "status": 1,
            "children": [
                {
                    "id": 2,
                    "parent_id": 1,
                    "title": "用户管理",
                    "icon": "user",
                    "path": "/system/users",
                    "component": "system/user/index",
                    "sort": 1,
                    "status": 1
                }
            ],
            "created_at": "2023-01-01 00:00:00",
            "updated_at": "2023-01-01 00:00:00"
        }
    ]
}
```

## 2. 新增菜单

##### 简要描述
- 新增系统菜单

##### 请求URL
- `/admin/system/menus`

##### 请求方式
- POST

##### 参数
```json
{
    "parent_id": 1,
    "title": "用户管理",
    "icon": "user",
    "path": "/system/users",
    "component": "system/user/index",
    "sort": 1,
    "status": 1
}
```

| 参数名    | 必选 | 类型   | 说明                           |
|-----------|------|--------|--------------------------------|
| parent_id | 否   | int    | 父菜单ID，0表示顶级菜单        |
| title     | 是   | string | 菜单标题                       |
| icon      | 否   | string | 菜单图标                       |
| path      | 是   | string | 路由路径                       |
| component | 是   | string | 组件路径                       |
| sort      | 否   | int    | 排序，越小越靠前               |
| status    | 否   | int    | 状态：0-禁用，1-启用           |

##### 返回示例
```json
{
    "ok": 1,
    "msg": "ok"
}
```

## 3. 更新菜单

##### 简要描述
- 更新菜单信息

##### 请求URL
- `/admin/system/menus/:id`

##### 请求方式
- PUT

##### 参数
```json
{
    "parent_id": 1,
    "title": "用户管理",
    "icon": "user",
    "path": "/system/users",
    "component": "system/user/index",
    "sort": 1,
    "status": 1
}
```

| 参数名    | 必选 | 类型   | 说明                           |
|-----------|------|--------|--------------------------------|
| parent_id | 否   | int    | 父菜单ID，0表示顶级菜单        |
| title     | 是   | string | 菜单标题                       |
| icon      | 否   | string | 菜单图标                       |
| path      | 是   | string | 路由路径                       |
| component | 是   | string | 组件路径                       |
| sort      | 否   | int    | 排序，越小越靠前               |
| status    | 否   | int    | 状态：0-禁用，1-启用           |

##### 返回示例
```json
{
    "ok": 1,
    "msg": "ok"
}
```

## 4. 删除菜单

##### 简要描述
- 删除系统菜单

##### 请求URL
- `/admin/system/menus/:id`

##### 请求方式
- DELETE

##### 返回示例
```json
{
    "ok": 1,
    "msg": "ok"
}
```

##### 备注
- 如果菜单下有子菜单，则不能删除
- 如果菜单已被角色使用，则不能删除
- 所有接口都需要登录认证
- 所有接口都需要相应的权限
- 返回的 ok 为 1 表示成功，为 0 表示失败
 