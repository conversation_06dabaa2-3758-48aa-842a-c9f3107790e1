package aftersales

import (
	"errors"
	"github.com/gin-gonic/gin"
	"marketing/internal/dao"
	"marketing/internal/model"
	"marketing/internal/pkg/utils"
	"sort"
	"sync"
)

type TMachineMalfunctionInfo struct {
	Id          int                       `json:"id" gorm:"id"`                   // id
	Title       string                    `json:"title" gorm:"title"`             // 标题
	Description string                    `json:"description" gorm:"description"` // 描述
	Order       int                       `json:"order"`                          // 排序
	ChildList   []TMachineMalfunctionInfo `json:"child_list"`                     // 子标签列表
}

var (
	machineMalfunctionArray      []TMachineMalfunctionInfo
	machineMalfunctionArrayMutex sync.RWMutex
	machineMalfunctionArrayOnce  sync.Once // 单例锁
	machineMalfunctionVersion    int       // 版本号
)

type TMachineMalfunctionSvcInterface interface {
	GetAllMachineMalfunction(c *gin.Context) (infos []TMachineMalfunctionInfo)
	RefreshMachineMalfunction(c *gin.Context) (infos []TMachineMalfunctionInfo)
	EditMachineMalfunction(c *gin.Context, id, pId int, title, description string, order int) error
	AddMachineMalfunction(c *gin.Context, pId int, title, description string, order int) error
	DelMachineMalfunction(c *gin.Context, id int) error
}

type TMachineMalfunctionSvcImpl struct {
	machineMalfunctionRepo dao.MachineMalfunctionDao
}

func NewMachineMalfunctionService(machineMalfunctionRepo dao.MachineMalfunctionDao) TMachineMalfunctionSvcInterface {
	svc := &TMachineMalfunctionSvcImpl{
		machineMalfunctionRepo: machineMalfunctionRepo,
	}

	machineMalfunctionArrayOnce.Do(func() {
		svc.RefreshMachineMalfunction(new(gin.Context))
	})

	return svc
}

func (s *TMachineMalfunctionSvcImpl) GetAllMachineMalfunction(c *gin.Context) (infos []TMachineMalfunctionInfo) {
	if machineMalfunctionVersion != dao.GetCacheVersion(dao.CvMachineMalfunction) {
		return s.RefreshMachineMalfunction(c)
	}

	return machineMalfunctionArray
}

func (s *TMachineMalfunctionSvcImpl) RefreshMachineMalfunction(c *gin.Context) (infos []TMachineMalfunctionInfo) {
	machineMalfunctionArrayMutex.Lock()
	defer machineMalfunctionArrayMutex.Unlock()

	machineMalfunctionArray = make([]TMachineMalfunctionInfo, 0)
	malfunctionList := s.machineMalfunctionRepo.GetAllMachineMalfunction(c)
	for _, malfunction := range malfunctionList {
		parentIds := make([]int, 0)

		if malfunction.ParentId != 0 {
			parentIds = s.GetMachineMalfunctionParentIds(malfunction.ParentId, malfunctionList)
			if len(parentIds) == 0 {
				continue
			}
		}

		s.addMalfunction(parentIds, &machineMalfunctionArray, TMachineMalfunctionInfo{
			Id:          malfunction.Id,
			Title:       malfunction.Title,
			Description: malfunction.Description,
			Order:       malfunction.Order,
			ChildList:   make([]TMachineMalfunctionInfo, 0),
		})
	}

	// 修改版本号
	machineMalfunctionVersion = dao.GetCacheVersion(dao.CvMachineMalfunction)

	return machineMalfunctionArray
}

func (s *TMachineMalfunctionSvcImpl) EditMachineMalfunction(c *gin.Context, id, pId int, title, description string, order int) error {
	machineMalfunctionArrayMutex.Lock()
	defer machineMalfunctionArrayMutex.Unlock()

	if len(title) == 0 {
		return errors.New("编辑标签:名称为空")
	}

	malfunction := s.machineMalfunctionRepo.GetMachineMalfunctionById(c, id)
	if malfunction == nil {
		return errors.New("编辑标签:标签不存在")
	}

	oldPid := malfunction.ParentId
	childList := make([]TMachineMalfunctionInfo, 0)
	uMap := make(map[string]interface{})
	uMap["title"] = title
	uMap["description"] = description
	uMap["order"] = order

	if pId != oldPid {
		if pId != 0 && s.machineMalfunctionRepo.GetMachineMalfunctionById(c, pId) == nil {
			return errors.New("编辑标签:父标签不存在")
		}

		uMap["parent_id"] = pId
	}

	// db中修改故障标签
	dbErr := s.machineMalfunctionRepo.UpdateMachineMalfunction(c, malfunction.Id, uMap)
	if dbErr != nil {
		return nil
	}

	// 删除旧的节点
	if pId != oldPid {
		// 获取旧的父id
		oldsParentIds, err := s.GetMachineMalfunctionParentIdsFromDb(oldPid)
		if err != nil {
			return errors.New("编辑标签:获取旧的父标签id异常")
		}

		// 获取故障标签的子列表
		childList = s.getChildMalfunction(oldsParentIds, &machineMalfunctionArray, malfunction.Id)

		// 缓存中删除故障标签
		s.delMalfunction(oldsParentIds, &machineMalfunctionArray, malfunction.Id)
	}

	// 获取新的父id
	parentIds, err := s.GetMachineMalfunctionParentIdsFromDb(pId)
	if err != nil {
		return errors.New("编辑标签:获取新的父标签id异常")
	}

	// 缓存中新增故障标签
	s.addMalfunction(parentIds, &machineMalfunctionArray, TMachineMalfunctionInfo{
		Id:          malfunction.Id,
		Title:       title,
		Description: description,
		Order:       order,
		ChildList:   childList,
	})

	// 增加版本号
	s.addVersionNum()

	return nil
}

func (s *TMachineMalfunctionSvcImpl) AddMachineMalfunction(c *gin.Context, pId int, title, description string, order int) error {
	machineMalfunctionArrayMutex.Lock()
	defer machineMalfunctionArrayMutex.Unlock()

	if len(title) == 0 {
		return errors.New("添加标签:名称为空")
	}

	malfunction := &model.MachineMalfunction{
		Title:       title,
		Description: description,
		ParentId:    pId,
		Order:       order,
	}

	if pId != 0 && s.machineMalfunctionRepo.GetMachineMalfunctionById(c, pId) == nil {
		return errors.New("添加标签:父标签不存在")
	}

	// db中创建故障标签
	dbErr := s.machineMalfunctionRepo.CreateMachineMalfunction(c, malfunction)
	if dbErr != nil {
		return dbErr
	}

	// 获取父id
	parentIds, err := s.GetMachineMalfunctionParentIdsFromDb(malfunction.Id)
	if err != nil {
		return errors.New("添加标签:获取新的父标签id异常")
	}

	// 缓存中新增故障标签
	s.addMalfunction(parentIds, &machineMalfunctionArray, TMachineMalfunctionInfo{
		Id:          malfunction.Id,
		Title:       malfunction.Title,
		Description: malfunction.Description,
		ChildList:   make([]TMachineMalfunctionInfo, 0),
	})

	// 增加版本号
	s.addVersionNum()

	return nil
}

func (s *TMachineMalfunctionSvcImpl) DelMachineMalfunction(c *gin.Context, id int) error {
	machineMalfunctionArrayMutex.Lock()
	defer machineMalfunctionArrayMutex.Unlock()

	// 查询故障标签
	malfunction := s.machineMalfunctionRepo.GetMachineMalfunctionById(c, id)
	if malfunction == nil {
		return errors.New("删除标签:标签不存在")
	}

	// db中删除故障标签
	dbErr := s.machineMalfunctionRepo.DeleteMachineMalfunction(c, id)
	if dbErr != nil {
		return dbErr
	}

	// 获取父id
	parentIds, err := s.GetMachineMalfunctionParentIdsFromDb(malfunction.Id)
	if err != nil {
		return errors.New("删除标签:获取新的父标签id异常")
	}

	// 缓存中删除故障标签
	s.delMalfunction(parentIds, &machineMalfunctionArray, malfunction.Id)

	// 增加版本号
	s.addVersionNum()

	return nil
}

func (s *TMachineMalfunctionSvcImpl) addMalfunction(parentIds []int, malfunctionArray *[]TMachineMalfunctionInfo, malfunction TMachineMalfunctionInfo) {
	if len(parentIds) > 0 {
		for i, c := range *malfunctionArray {
			if c.Id == parentIds[0] {
				s.addMalfunction(parentIds[1:], &(*malfunctionArray)[i].ChildList, malfunction)
				return
			}
		}

		parentMalfunction := TMachineMalfunctionInfo{
			Id:        parentIds[0],
			ChildList: make([]TMachineMalfunctionInfo, 0),
		}

		// 加入子标签
		s.addMalfunction(parentIds[1:], &parentMalfunction.ChildList, malfunction)
		// 加入父标签
		*malfunctionArray = append(*malfunctionArray, parentMalfunction)

		return
	}

	noExist := true

	for i, c := range *malfunctionArray {
		if c.Id == malfunction.Id {
			(*malfunctionArray)[i].Title = malfunction.Title
			(*malfunctionArray)[i].Description = malfunction.Description
			(*malfunctionArray)[i].Order = malfunction.Order
			noExist = false
			break
		}
	}

	if noExist {
		*malfunctionArray = append(*malfunctionArray, malfunction)
	}

	// 排序
	sort.Sort(utils.TagSort{
		Data:   *malfunctionArray,
		Length: len(*malfunctionArray),
	})

	return
}

func (s *TMachineMalfunctionSvcImpl) delMalfunction(parentIds []int, malfunctionArray *[]TMachineMalfunctionInfo, id int) {
	if len(parentIds) > 0 {
		for i, c := range *malfunctionArray {
			if c.Id == parentIds[0] {
				s.delMalfunction(parentIds[1:], &(*malfunctionArray)[i].ChildList, id)
			}
		}
	}

	childList := make([]TMachineMalfunctionInfo, 0)
	for _, a := range *malfunctionArray {
		if a.Id != id {
			childList = append(childList, a)
		}
	}

	*malfunctionArray = childList

	return
}

func (s *TMachineMalfunctionSvcImpl) getChildMalfunction(parentIds []int, malfunctionArray *[]TMachineMalfunctionInfo, id int) []TMachineMalfunctionInfo {
	if len(parentIds) > 0 {
		for i, c := range *malfunctionArray {
			if c.Id == parentIds[0] {
				return s.getChildMalfunction(parentIds[1:], &(*malfunctionArray)[i].ChildList, id)
			}
		}
	}

	for _, c := range *malfunctionArray {
		if c.Id == id {
			return c.ChildList
		}
	}

	return make([]TMachineMalfunctionInfo, 0)
}

func (s *TMachineMalfunctionSvcImpl) GetMachineMalfunctionParentIds(pid int, malfunctionList []*model.MachineMalfunction) []int {
	parentIds := make([]int, 0)

	// 获取父类id
	s.getMachineMalfunctionParentIds(pid, malfunctionList, &parentIds)
	if len(parentIds) == 0 {
		return parentIds
	}

	// 因为获取的父id是从下到上,所以需要倒序一次
	utils.ReverseIntArray(&parentIds)

	return parentIds
}

func (s *TMachineMalfunctionSvcImpl) getMachineMalfunctionParentIds(pid int, malfunctionList []*model.MachineMalfunction, parentIds *[]int) {
	for _, malfunction := range malfunctionList {
		if malfunction.Id == pid {
			// 检查父标签id是否存在回路
			if utils.IntHas(malfunction.Id, *parentIds) {
				*parentIds = make([]int, 0)
				return
			}

			*parentIds = append(*parentIds, malfunction.Id)

			if malfunction.ParentId != 0 {
				s.getMachineMalfunctionParentIds(malfunction.ParentId, malfunctionList, parentIds)
			}

			return
		}
	}

	*parentIds = make([]int, 0)

	return
}

func (s *TMachineMalfunctionSvcImpl) GetMachineMalfunctionParentIdsFromDb(id int) ([]int, error) {
	if id == 0 {
		return nil, nil
	}

	parentIds := make([]int, 0)

	// 获取父类id
	err := s.getMachineMalfunctionParentIdsFromDb(id, &parentIds)
	if err != nil {
		return nil, err
	}

	// 因为获取的父id是从下到上,所以需要倒序一次
	utils.ReverseIntArray(&parentIds)

	return parentIds, nil
}

func (s *TMachineMalfunctionSvcImpl) getMachineMalfunctionParentIdsFromDb(id int, parentIds *[]int) error {
	malfunction := s.machineMalfunctionRepo.GetMachineMalfunctionById(new(gin.Context), id)
	if malfunction == nil {
		*parentIds = make([]int, 0)
		return errors.New("标签不存在")
	}

	// 检查父标签id是否存在回路
	if utils.IntHas(malfunction.Id, *parentIds) {
		*parentIds = make([]int, 0)
		return errors.New("父标签异常")
	}

	*parentIds = append(*parentIds, malfunction.Id)

	if malfunction.ParentId != 0 {
		err := s.getMachineMalfunctionParentIdsFromDb(malfunction.ParentId, parentIds)
		if err != nil {
			*parentIds = make([]int, 0)
			return err
		}
	}

	return nil
}

func (s *TMachineMalfunctionSvcImpl) addVersionNum() {
	// 修改db中的版本号
	if dao.AddCacheVersion(dao.CvMachineMalfunction) == nil {
		machineMalfunctionVersion += 1
	}
}
