package agency

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/dao"
	"marketing/internal/handler/agency/action"
	"marketing/internal/pkg/db"
	"marketing/internal/pkg/oss"
	"marketing/internal/pkg/redis"
	"marketing/internal/pkg/upload"
	"marketing/internal/service"
	"marketing/internal/service/cache"
	service2 "marketing/internal/service/system"
)

type ActionRouter struct {
	userService service2.AdminUserInterface
}

func NewActionRouter(userService service2.AdminUserInterface) *ActionRouter {
	return &ActionRouter{
		userService: userService,
	}
}

func (a *ActionRouter) Register(r *gin.RouterGroup) {
	//活动管理模块
	activityRouter := r.Group("/activity")
	{
		client := oss.NewClient()
		uploadService := upload.NewOssUploadServiceV2(client)
		actionDao := dao.NewActionTypeGorm()
		activityDao := dao.NewActionGorm(uploadService)
		redisCache := cache.NewActionRedisCache(redis.NewRedis())
		commonDao := dao.NewCommonGorm(db.DB)
		activityService := service.NewGormActionService(activityDao, actionDao, redisCache, commonDao)
		activityController := action.NewHandleActionAgency(activityService)
		activityRouter.GET("/type/drop_down", activityController.DropDown)
		//操作
		activityRouter.POST("/action/apply", activityController.ApplyAction)
		activityRouter.DELETE("/action/delete/:id", activityController.DeleteAction)
		activityRouter.POST("/action/finish", activityController.FinishAction)
		activityRouter.POST("/action/update", activityController.UpdateAction)
		//输入提示信息
		activityRouter.GET("/action/tips/type", activityController.GetTypeTips)
		activityRouter.GET("/action/tips/partition", activityController.GetPartitionTips)
		activityRouter.GET("/action/tips/top_agency", activityController.GetTopAgencyTips)
		activityRouter.GET("/action/tips/second_agency", activityController.GetSecondAgencyTips)
		activityRouter.GET("/action/tips/endpoint", activityController.GetEndpointTips)
		// 查询接口
		activityRouter.GET("/action/list", activityController.GetActionList)
		activityRouter.GET("/action/apply_info/:id", activityController.GetActionApplyInfo)
		activityRouter.GET("/action/finish_info/:id", activityController.GetActionFinishInfo)
		activityRouter.GET("/action/audit_info/:id", activityController.GetActionAuditInfo)
		activityRouter.GET("/action/verify_info/:id", activityController.GetActionVerifyInfo)
		activityRouter.GET("/action/recorded_info/:id", activityController.GetActionRecordedInfo)
	}
	PromotionRouter := r.Group("/promotions")
	{
		PromotionDao := dao.NewGormPromotionDao(db.DB)
		CommonGorm := dao.NewCommonGorm(db.DB)
		PromotionService := service.NewGormPromotionService(PromotionDao, CommonGorm)
		promotionController := action.NewPromotionHandle(PromotionService, a.userService)
		PromotionRouter.GET("/tips/type", promotionController.GetPromotionType)
		PromotionRouter.GET("/:type", promotionController.GetPromotion)
		PromotionRouter.GET("/info/:id", promotionController.GetInfo)
		PromotionRouter.GET("/list", promotionController.GetJoinList)
		PromotionRouter.POST("/list/:id/receipt", promotionController.Receipt)
		PromotionRouter.GET("/list/info/:id", promotionController.GetJoinInfo)
		PromotionRouter.GET("/list/export", promotionController.Export)
	}
}
