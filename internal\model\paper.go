package model

import (
	"gorm.io/gorm"
	"time"
)

type Paper struct {
	ID          uint      `gorm:"column:id;type:int(10) unsigned;primaryKey;autoIncrement;comment:id"`
	Name        string    `gorm:"column:name;type:varchar(50);not null;default:'';comment:名称"`
	Description string    `gorm:"column:description;type:varchar(255);not null;default:'';comment:描述"`
	Duration    uint      `gorm:"column:duration;type:int(10) unsigned;not null;default:0;comment:考试时长"`
	Status      uint8     `gorm:"column:status;type:tinyint(3) unsigned;not null;default:0;comment:是否发布，0为未发布，1为发布"`
	Creator     string    `gorm:"column:creator;type:varchar(50);not null;default:'';comment:命题人"`
	Start       time.Time `gorm:"column:start;type:datetime;comment:开始时间"`
	End         time.Time `gorm:"column:end;type:datetime;comment:结束时间"`
	Number      int       `gorm:"column:number;type:int(11);not null;default:0;comment:出题数量"`
	Attribution string    `gorm:"column:attribution;type:enum('exam','train');default:'exam';comment:归属，exam-终端考试，train-培训"`
	PassMark    uint8     `gorm:"column:pass_mark;type:tinyint(3) unsigned;not null;default:80;comment:及格分数，不能超过100"`
	UpdatedBy   uint      `gorm:"column:updated_by;type:int(10) unsigned;not null;default:0"`
	CreatedAt   time.Time `gorm:"column:created_at;type:timestamp;default:CURRENT_TIMESTAMP;comment:创建时间"`
	UpdatedAt   time.Time `gorm:"column:updated_at;type:timestamp;comment:更新时间"`
	DeletedAt   gorm.DeletedAt
}
type PaperQuestion struct {
	ID         uint      `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	PaperID    uint      `gorm:"column:paper_id;not null" json:"paper_id"`
	QuestionID uint      `gorm:"column:question_id;not null" json:"question_id"`
	CreatedAt  time.Time `gorm:"column:created_at;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt  time.Time `gorm:"column:updated_at" json:"updated_at"`
}
type Question struct {
	ID                   uint           `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Type                 uint8          `gorm:"column:type" json:"type"`
	Detail               string         `gorm:"column:detail;type:text" json:"detail"`
	Options              string         `gorm:"column:options;type:text" json:"options"`
	Answer               string         `gorm:"column:answer;type:varchar(50)" json:"answer"`
	Answers              string         `gorm:"column:answers;type:varchar(50)" json:"answers"`
	Judge                *int8          `gorm:"column:judge" json:"judge"`
	Blank                string         `gorm:"column:blank;type:text" json:"blank"`
	Description          string         `gorm:"column:description;type:varchar(500)" json:"description"`
	SelectTimes          uint           `gorm:"column:select_times" json:"select_times"`
	CorrectTimes         uint           `gorm:"column:correct_times" json:"correct_times"`
	CreatedAt            time.Time      `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt            time.Time      `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
	Attribution          string         `gorm:"column:attribution;type:enum('exam','train');default:'exam'" json:"attribution"`
	DeletedAt            gorm.DeletedAt `gorm:"column:deleted_at" json:"deleted_at"`
	AnswerOrderDependent uint8          `gorm:"column:answer_order_dependent" json:"answer_order_dependent"`
}

// TableName 指定表名
func (Paper) TableName() string {
	return "paper"
}

func (PaperQuestion) TableName() string {
	return "paper_question"
}
func (Question) TableName() string {
	return "question"
}
