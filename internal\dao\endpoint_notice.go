package dao

import (
	"errors"
	"marketing/internal/model"
	"marketing/internal/pkg/utils"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type EndpointNoticeDao interface {
	GetEndpointNoticeList(c *gin.Context, title, author string, pageNum, pageSize int) ([]*model.EndpointNotice, int64, error)
	GetEndpointNoticeById(c *gin.Context, id int) (*model.EndpointNotice, error)
	CreateEndpointNotice(c *gin.Context, notice *model.EndpointNotice) error
	UpdateEndpointNotice(c *gin.Context, id int, updateMap map[string]interface{}) error
	DeleteEndpointNotice(c *gin.Context, id int) error
}

type endpointNoticeDao struct {
	db *gorm.DB
}

func NewEndpointNoticeDao(db *gorm.DB) EndpointNoticeDao {
	return &endpointNoticeDao{
		db: db,
	}
}

// GetEndpointNoticeList 获取终端通知列表
func (d *endpointNoticeDao) GetEndpointNoticeList(c *gin.Context, title, author string, pageNum, pageSize int) ([]*model.EndpointNotice, int64, error) {
	var notices []*model.EndpointNotice
	query := d.db.WithContext(c).Model(&model.EndpointNotice{})

	// 添加过滤条件
	if title != "" {
		query = query.Where("title LIKE ?", "%"+title+"%")
	}
	if author != "" {
		query = query.Where("author LIKE ?", "%"+author+"%")
	}

	// 按创建时间倒序排列
	query = query.Order("id DESC")

	// 分页查询
	list, total := utils.PaginateQueryV1(query, pageNum, pageSize, &notices)
	return *list, total, nil
}

// GetEndpointNoticeById 根据ID获取终端通知
func (d *endpointNoticeDao) GetEndpointNoticeById(c *gin.Context, id int) (*model.EndpointNotice, error) {
	var notice model.EndpointNotice
	err := d.db.WithContext(c).Where("id = ?", id).First(&notice).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &notice, nil
}

// CreateEndpointNotice 创建终端通知
func (d *endpointNoticeDao) CreateEndpointNotice(c *gin.Context, notice *model.EndpointNotice) error {
	return d.db.WithContext(c).Create(notice).Error
}

// UpdateEndpointNotice 更新终端通知
func (d *endpointNoticeDao) UpdateEndpointNotice(c *gin.Context, id int, updateMap map[string]interface{}) error {
	return d.db.WithContext(c).Model(&model.EndpointNotice{}).Where("id = ?", id).Updates(updateMap).Error
}

// DeleteEndpointNotice 删除终端通知
func (d *endpointNoticeDao) DeleteEndpointNotice(c *gin.Context, id int) error {
	return d.db.WithContext(c).Where("id = ?", id).Delete(&model.EndpointNotice{}).Error
}
