package endpoint

import (
	"marketing/internal/api"
	"marketing/internal/model"
)

type ListEndpointSettingReq struct {
	api.PaginationParams
	TopAgency    uint   `json:"top_agency" form:"top_agency"`       // 一级代理商名称
	SecondAgency uint   `json:"second_agency" form:"secode_agency"` // 二级代理商名称
	Name         string `json:"name" form:"name"`
	Code         string `json:"code"`
}

// ListEndpointSettingRes 终端设置列表响应
type ListEndpointSettingRes struct {
	model.EndpointSetting
	TopAgencyName    string `json:"top_agency"`    // 一级代理
	SecondAgencyName string `json:"second_agency"` // 二级代理
	EndpointName     string `json:"endpoint_name"`
}

type UpdateEndpointSettingReq struct {
	ID                      uint  `json:"id" form:"id"`
	PrototypeLimit          int8  `json:"prototype_limit" form:"prototype_limit"`
	PrototypeFrequencyLimit uint8 `json:"prototype_frequency_limit" form:"prototype_frequency_limit"`
}
