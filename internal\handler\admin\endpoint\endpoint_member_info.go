// controller/member_controller.go
package endpoint

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/dao"
	"marketing/internal/handler"
	"marketing/internal/service"
	"net/http"
	"strconv"
)

// 定义 MemberInfoController
type MemberInfoController struct {
	Service *service.AdminUsersService
}

// 新建 EndpointAuditController
func NewEndpointMemberInfoController(service *service.AdminUsersService) MemberInfoController {
	return MemberInfoController{Service: service}
}

// QueryMemberbyCondition 根据查询条件获取成员信息
func (ctrl *MemberInfoController) QueryMemberbyConditions(c *gin.Context) {
	// 获取查询参数并转换为合适的数据类型
	var params dao.AdminUserQueryParams
	params.Role = c.DefaultQuery("role", "")
	params.EndpointName = c.DefaultQuery("endpoint_name", "")
	params.TopAgencyName = c.DefaultQuery("top_agency_name", "")
	params.SecondAgencyName = c.DefaultQuery("second_agency_name", "")
	params.Name = c.DefaultQuery("name", "")
	params.Phone = c.DefaultQuery("phone", "")
	params.Status = c.DefaultQuery("status", "")
	params.IsSameWithWechat = c.DefaultQuery("is_same_with_wechat", "")
	// 调用 service 获取数据
	members, total, page, pageSize, err := ctrl.Service.GetMembersbyCondition(params, c)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	handler.Success(c, gin.H{
		"data":     members,
		"total":    total,
		"page":     page,
		"pageSize": pageSize,
	})
}

// 创建成员
func (ctrl *MemberInfoController) CreateMember(c *gin.Context) {
	var member dao.AdminUsers
	if err := c.ShouldBindJSON(&member); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := ctrl.Service.CreateMember(&member); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	handler.Success(c, gin.H{"message": "Member created successfully", "data": member})
}

// 获取成员信息
func (ctrl *MemberInfoController) GetMemberByID(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid member ID"})
		return
	}

	member, err := ctrl.Service.GetMemberByID(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Member not found"})
		return
	}
	handler.Success(c, gin.H{"data": member})
}

// 获取所有成员
func (ctrl *MemberInfoController) GetAllMembers(c *gin.Context) {
	members, count, page, pagesize, err := ctrl.Service.GetAllMembers(c)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	handler.Success(c, gin.H{
		"data":     members,
		"count":    count,
		"page":     page,
		"pagesize": pagesize,
	})
}

// 更新成员
func (ctrl *MemberInfoController) UpdateMember(c *gin.Context) {
	var member dao.AdminUsers
	if err := c.ShouldBindJSON(&member); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := ctrl.Service.UpdateMember(&member); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	handler.Success(c, gin.H{"message": "Member updated successfully", "data": member})
}

// 删除成员
func (ctrl *MemberInfoController) DeleteMember(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid member ID"})
		return
	}

	if err := ctrl.Service.SoftDeleteMember(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	handler.Success(c, gin.H{"message": "Member deleted successfully", "data": nil})
}
