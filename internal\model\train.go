package model

import (
	"time"
)

type TrainType struct {
	ID        uint      `gorm:"column:id;type:int(11) unsigned;primaryKey;autoIncrement;comment:主键" json:"id"`
	Title     string    `gorm:"column:title;type:varchar(50);not null;default:'';comment:名称" json:"title"`
	ParentID  uint      `gorm:"column:parent_id;type:int(11) unsigned;not null;default:0;comment:父种类id" json:"parent_id"`
	Order     uint      `gorm:"column:order;type:int(11) unsigned;not null;default:0;comment:排序" json:"order"`
	CreatedAt time.Time `gorm:"column:created_at;type:timestamp;default:null;comment:创建时间" json:"created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at;type:timestamp;not null;default:0000-00-00 00:00:00;comment:更新时间" json:"updated_at"`
}
type Train struct {
	ID                 uint       `gorm:"column:id;primaryKey;autoIncrement;comment:主键" json:"id"`
	Name               string     `gorm:"column:name;type:varchar(50);not null;default:'';comment:名称" json:"name"`
	Description        string     `gorm:"column:description;type:text;not null;comment:描述" json:"description"`
	Preview            string     `gorm:"column:preview;type:text;not null;comment:预览图" json:"preview"`
	Path               string     `gorm:"column:path;type:text;not null;comment:下载路径" json:"path"`
	ArticleLink        string     `gorm:"column:article_link;type:varchar(500);comment:文章链接地址" json:"article_link"`
	Type               uint       `gorm:"column:type;not null;default:0;comment:大分类" json:"type"`
	Category           uint       `gorm:"column:category;not null;default:0;comment:小分类" json:"category"`
	Status             uint8      `gorm:"column:status;not null;default:0;comment:状态，1为通过，0为未通过" json:"status"`
	Top                uint8      `gorm:"column:top;not null;default:0;comment:是否置顶，1为置顶，0为未置顶" json:"top"`
	Share              uint8      `gorm:"column:share;not null;default:0;comment:是否可以分享，1为可以分享" json:"share"`
	Star               uint8      `gorm:"column:star;not null;default:0;comment:是否重要，1为重要，0为未重要" json:"star"`
	DownloadCount      uint       `gorm:"column:download_count;not null;default:0;comment:下载量" json:"download_count"`
	Increment          uint       `gorm:"column:increment;not null;default:0;comment:下载增量" json:"increment"`
	CreatedAt          time.Time  `gorm:"column:created_at;not null;default:'0000-00-00 00:00:00';comment:创建时间" json:"created_at"`
	UpdatedAt          time.Time  `gorm:"column:updated_at;not null;default:'0000-00-00 00:00:00';comment:更新时间" json:"updated_at"`
	Recommend          uint8      `gorm:"column:recommend;not null;default:0;comment:是否加入推荐" json:"recommend"`
	DailyCount         uint       `gorm:"column:daily_count;not null;default:0;comment:日点击量" json:"daily_count"`
	MonthCount         uint       `gorm:"column:month_count;not null;default:0;comment:30天点击量" json:"month_count"`
	LastUpdatedAt      *time.Time `gorm:"column:last_updated_at;comment:每日最后更新时间" json:"last_updated_at"`
	MonthText          string     `gorm:"column:month_text;type:text;not null;comment:30天内每天的点击记录， json" json:"month_text"`
	Banner             uint8      `gorm:"column:banner;not null;default:0;comment:是否作为banner" json:"banner"`
	BannerImage        string     `gorm:"column:banner_image;type:varchar(100);not null;default:'';comment:banner图片" json:"banner_image"`
	Credit             float64    `gorm:"column:credit;type:decimal(5,2);not null;default:0.00;comment:学分" json:"credit"`
	CreditLearningTime uint16     `gorm:"column:credit_learning_time;not null;default:0;comment:获得学分所需学习时长，秒" json:"credit_learning_time"`
	CreatedBy          uint       `gorm:"column:created_by;not null;default:0;comment:创建人" json:"created_by"`
}
type TrainCategoryRelation struct {
	ID         uint `gorm:"column:id;primaryKey;autoIncrement;type:int(10) unsigned"`
	TrainID    uint `gorm:"column:train_id;not null;type:int(10) unsigned"`
	CategoryID uint `gorm:"column:category_id;not null;type:int(10) unsigned"`
}
type TrainVideos struct {
	ID          uint      `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Title       string    `gorm:"column:title;type:varchar(50);not null" json:"title"`
	Preview     string    `gorm:"column:preview;type:text;not null" json:"preview"`
	Path        string    `gorm:"column:path;type:text;not null" json:"path"`
	Description string    `gorm:"column:description;type:text;not null" json:"description"`
	Status      uint8     `gorm:"column:status;type:smallint(3) unsigned;not null;default:0" json:"status"`
	CreatedAt   time.Time `gorm:"column:created_at;type:timestamp;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt   time.Time `gorm:"column:updated_at;type:timestamp;default:0000-00-00 00:00:00" json:"updated_at"`
}

func (t *TrainType) TableName() string {
	return "train"
}

func (Train) TableName() string {
	return "train"
}

func (TrainCategoryRelation) TableName() string {
	return "train_category_relation"
}
