package service

/*import (
	"fmt"
	"marketing/internal/model"
	"math"
)

// LocationService 用于处理位置相关的计算
type LocationService struct{}

var LS LocationService

// NewLocationService 创建新的 LocationService 实例
func NewLocationService() *LocationService {
	return &LocationService{}
}

// GetCoordinate 获取经纬度，并计算相关信息
func (ls *LocationService) GetCoordinate(record model.EndpointImageApply) string {
	if record.Longitude == 0 {
		return "0"
	}
	distance := "0"
	if record.OldLongitude != 0 {
		// 计算距离
		distance = LS.GetDistance(record.OldLongitude, record.OldLatitude, record.Longitude, record.Latitude, 1, 0)
	}
	return fmt.Sprintf("%f, %f %s", record.Longitude, record.Latitude, distance)
}

// GetDistance 计算两点之间的距离，单位米
func (ls *LocationService) GetDistance(lon1, lat1, lon2, lat2 float64, unit int, decimals int) string {
	// 使用 Haversine 公式计算两点之间的距离
	radius := 6371000 // 地球半径，单位米
	phi1 := lat1 * math.Pi / 180
	phi2 := lat2 * math.Pi / 180
	deltaPhi := (lat2 - lat1) * math.Pi / 180
	deltaLambda := (lon2 - lon1) * math.Pi / 180

	a := math.Sin(deltaPhi/2)*math.Sin(deltaPhi/2) + math.Cos(phi1)*math.Cos(phi2)*math.Sin(deltaLambda/2)*math.Sin(deltaLambda/2)
	c := 2 * math.Atan2(math.Sqrt(a), math.Sqrt(1-a))

	distance := float64(radius) * c

	return fmt.Sprintf("%.2f 米", distance)
}*/
