package router

import (
	"marketing/internal/config"
	"marketing/internal/dao/app_auth"
	"marketing/internal/dao/auth"
	"marketing/internal/middleware"
	"marketing/internal/pkg/db"
	myRedis "marketing/internal/pkg/redis"
	"marketing/internal/router/third_party"
	service "marketing/internal/service/third_party"
)

// NewThirdPartyRoutes 设置第三方接口路由
func NewThirdPartyRoutes(cfg *config.Config) IRouterGroup {
	// 使用路由构建器构建路由组
	builder := NewRouterBuilder("third_party")

	// 第三方认证相关组件
	Db := db.GetDB()
	redisClient := myRedis.NewRedis() // 使用Redis包创建客户端
	appAuthDao := app_auth.NewAppAuthDao(Db)
	appAuthCache := app_auth.NewAppAuthCache(redisClient)           // 使用专门的应用认证缓存
	thirdPartyAuthCache := auth.NewThirdPartyAuthCache(redisClient) // 使用专门的第三方认证缓存

	// 第三方认证接口
	builder.AddRouterWithMiddleware(third_party.NewAuthRouter(Db, appAuthDao, appAuthCache, thirdPartyAuthCache))

	// 400客服系统系统 来电弹屏（合力亿捷）
	authService := service.NewThirdPartyAuthService(Db, appAuthDao, appAuthCache, thirdPartyAuthCache)
	hollycrmMiddleware := middleware.ThirdPartyAuthMiddleware(authService)
	builder.AddRouterWithMiddleware(third_party.NewWarrantyRouter(Db), hollycrmMiddleware)

	// 400客服系统系统 ivr发短信（合力亿捷）
	builder.AddRouterWithMiddleware(third_party.NewCustomerServiceRouter(Db, cfg, authService))
	// 构建并返回路由组
	return builder.Build()
}
