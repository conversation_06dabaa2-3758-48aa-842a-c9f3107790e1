package agency

import (
	"fmt"
	"marketing/internal/api/system"
	"marketing/internal/consts"
	"marketing/internal/handler"
	"marketing/internal/pkg/errors"
	service "marketing/internal/service/endpoint"
	system2 "marketing/internal/service/system"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/xuri/excelize/v2"
)

// UserHandler 总代用户接口
type UserHandler interface {
	Lists(c *gin.Context)
	Add(c *gin.Context)
	Delete(c *gin.Context)
	GetRoleDropdown(c *gin.Context)
	GetDepartmentTree(c *gin.Context)
	SyncUser(c *gin.Context)
	ExportUser(c *gin.Context)
	Update(c *gin.Context)
	ImportUsers(c *gin.Context)
}

type agencyUser struct {
	adminUserSvc system2.AdminUserInterface
	adminRoleSvc system2.AdminRoleInterface
	endpointSvc  service.Endpoint
}

// NewAgencyUser 创建 adminUser 实例
func NewAgencyUser(
	adminUserSvc system2.AdminUserInterface,
	adminRoleSvc system2.AdminRoleInterface,
	endpointSvc service.Endpoint,
) UserHandler {
	return &agencyUser{
		adminUserSvc: adminUserSvc,
		adminRoleSvc: adminRoleSvc,
		endpointSvc:  endpointSvc,
	}
}

// Lists 获取用户列表
func (a *agencyUser) Lists(c *gin.Context) {
	var req system.AdminUserReq
	// 你可以使用显式绑定声明绑定 multipart form：绑定不了json格式
	// c.ShouldBindWith(&form, binding.Form)
	// 或者简单地使用 ShouldBind 方法自动绑定
	// 如果是 `GET` 请求，只使用 `Form` 绑定引擎（`query`）。
	// 如果是 `POST` 请求，首先检查 `content-type` 是否为 `JSON` 或 `XML`，然后再使用 `Form`（`form-data`）。
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	//分页处理
	req.PaginationParams.SetDefaults()
	// 用户组来区分是否是代理商
	req.Type = "agency"
	data, err := a.adminUserSvc.Lists(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, data)
}

// Add 新增用户
func (a *agencyUser) Add(c *gin.Context) {
	var req system.AddUserReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	err := a.adminUserSvc.AddAgencyUser(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

// GetRoleDropdown 获取用户角色列表
func (a *agencyUser) GetRoleDropdown(c *gin.Context) {
	roleName := c.Query("roleName")
	roles, err := a.adminRoleSvc.ListRolesNoPage(c, roleName, "agency")
	if err != nil {
		handler.Error(c, err)
		return
	}
	var data []map[string]interface{}
	for _, role := range roles {
		data = append(data, map[string]interface{}{
			"id":   role.ID,
			"slug": role.Slug,
			"name": role.Name,
		})
	}

	handler.Success(c, data)
}

// GetDepartmentTree 获取经销商树所有数据
func (a *agencyUser) GetDepartmentTree(c *gin.Context) {
	data, err := a.endpointSvc.GetTree(c, false)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, data)
}

// Delete 删除用户
func (a *agencyUser) Delete(c *gin.Context) {
	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		handler.Error(c, errors.NewErr("id不能为空"))
		return
	}
	err := a.adminUserSvc.Delete(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

// SyncUser 同步用户到微信
func (a *agencyUser) SyncUser(c *gin.Context) {
	type Request struct {
		IDs    []int  `json:"ids"`
		TagIDs []uint `json:"tags"`
	}
	var req Request
	// 解析 JSON 数据
	if err := c.BindJSON(&req); err != nil {
		handler.Error(c, errors.NewErr("请求数据格式错误，不是有效的 JSON 格式"))
		return
	}
	ids := req.IDs
	if len(ids) == 0 {
		handler.Error(c, errors.NewErr("ids不能为空"))
		return
	}
	err := a.adminUserSvc.SyncUser(c, ids, req.TagIDs)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

// ExportUser 导出用户
func (a *agencyUser) ExportUser(c *gin.Context) {
	var req system.AdminUserReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	req.SetDefaults()
	err := a.adminUserSvc.ExportUser(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
}

// Update 更新用户信息
func (a *agencyUser) Update(c *gin.Context) {
	var req system.AddUserReq
	if err := c.ShouldBind(&req); err != nil {
		err = errors.NewErr("参数错误" + err.Error())
		handler.Error(c, err)
		return
	}
	req.ID = cast.ToUint(c.Param("id"))
	if req.ID == 0 {
		handler.Error(c, errors.NewErr("id不能为空"))
		return
	}
	req.UpdateType = consts.AgencyPrefix
	err := a.adminUserSvc.Update(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

// ImportUsers 导入用户
func (a *agencyUser) ImportUsers(c *gin.Context) {
	// 从表单中获取上传的文件
	file, err := c.FormFile("file")
	if err != nil {
		handler.Error(c, errors.NewErr("获取上传的文件失败"))
		return
	}

	// 检查文件大小是否为 0
	if file.Size == 0 {
		handler.Error(c, errors.NewErr("上传的 Excel 文件不能为空"))
		return
	}

	// 打开上传的文件
	src, err := file.Open()
	if err != nil {
		handler.Error(c, errors.NewErr("打开文件失败: "+err.Error()))
		return
	}
	defer src.Close()

	// 读取excel文件
	f, err := excelize.OpenReader(src)
	if err != nil {
		handler.Error(c, errors.NewErr("解析Excel文件失败: "+err.Error()))
		return
	}
	defer f.Close()

	// 获取第一个工作表
	sheetName := f.GetSheetName(0)
	rows, err := f.GetRows(sheetName)
	if err != nil {
		handler.Error(c, errors.NewErr("读取工作表失败: "+err.Error()))
		return
	}

	// 检查是否有数据
	if len(rows) < 2 {
		handler.Error(c, errors.NewErr("Excel 文件中没有数据"))
		return
	}

	// 检查表头是否符合要求
	expectedHeaders := []string{"手机号", "昵称", "代理", "角色", "用户组", "企微标签"}
	if len(rows[0]) < 4 { // 前4列是必须的，后面的是可选的
		handler.Error(c, errors.NewErr("Excel 文件格式不正确，请确保至少包含手机号、昵称、代理和角色列"))
		return
	}

	// 验证必填表头
	for i, header := range expectedHeaders[:4] {
		if i < len(rows[0]) && rows[0][i] != header {
			handler.Error(c, errors.NewErr(fmt.Sprintf("Excel 文件表头不正确，第 %d 列应为 %s", i+1, header)))
			return
		}
	}

	// 检查可选表头
	for i := 4; i < len(expectedHeaders) && i < len(rows[0]); i++ {
		if rows[0][i] != expectedHeaders[i] {
			handler.Error(c, errors.NewErr(fmt.Sprintf("Excel 文件表头不正确，第 %d 列应为 %s", i+1, expectedHeaders[i])))
			return
		}
	}

	// 处理数据
	successCount, failedCount, results, err := a.adminUserSvc.ImportUsers(c, rows[1:])
	if err != nil {
		handler.Error(c, err)
		return
	}

	// 计算成功率
	totalRows := successCount + failedCount
	var successRate float64
	if totalRows > 0 {
		successRate = float64(successCount) / float64(totalRows) * 100
	}

	// 构建格式化的消息
	message := fmt.Sprintf("成功导入 %d 条数据 (%.2f%%)，失败 %d 条",
		successCount,
		successRate,
		failedCount)

	handler.Success(c, gin.H{
		"success_count": successCount,
		"failed_count":  failedCount,
		"success_rate":  fmt.Sprintf("%.2f%%", successRate),
		"message":       message,
		"results":       results, // 返回每行的导入结果
	})
}
