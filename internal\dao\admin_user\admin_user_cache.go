package admin_user

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"marketing/internal/model"
	"time"

	"github.com/redis/go-redis/v9"
)

const (
	cacheKey = "-user-data"
)

// UserCacheInterface 定义 UserCacheInterface 接口
type UserCacheInterface interface {
	GetUser(ctx context.Context, id uint, prefix string) (*model.AdminUsers, error)
	SetUser(ctx context.Context, user *model.AdminUsers, expiration time.Duration, prefix string) error
	GetUserAllData(ctx context.Context, id uint, prefix string) (*model.AdminUsers, error)
	DeleteUserCache(ctx context.Context, userID uint, prefix string) error
	BatchDeleteUserCache(ctx context.Context, userIDs []uint, prefix string) error
}

// UserCache 修改 UserCache 结构体，新增接口实现
type userCache struct {
	client *redis.Client
}

// NewUserCache 修改 NewUserCache 构造函数，返回接口类型
func NewUserCache(client *redis.Client) UserCacheInterface {
	return &userCache{
		client: client,
	}
}

func (c *userCache) GetUser(ctx context.Context, id uint, prefix string) (*model.AdminUsers, error) {
	key := fmt.Sprintf("%s%s:%d", prefix, cacheKey, id)
	data, err := c.client.Get(ctx, key).Bytes()
	if err != nil {
		return nil, err
	}

	var user model.AdminUsers
	if err := json.Unmarshal(data, &user); err != nil {
		return nil, err
	}
	return &user, nil
}

func (c *userCache) SetUser(ctx context.Context, user *model.AdminUsers, expiration time.Duration, prefix string) error {
	key := fmt.Sprintf("%s%s:%d", prefix, cacheKey, user.ID)
	data, err := json.Marshal(user)
	if err != nil {
		return err
	}
	return c.client.Set(ctx, key, data, expiration).Err()
}

// GetUserAllData 获取用户所有数据(先查缓存，没有则查库并缓存查库操作移动到service层)
func (c *userCache) GetUserAllData(ctx context.Context, id uint, prefix string) (*model.AdminUsers, error) {
	// 先从缓存获取
	user, err := c.GetUser(ctx, id, prefix)
	if err != nil && errors.Is(err, redis.Nil) {
		return nil, nil
	}

	return user, err
}

// DeleteUserCache 删除单个用户缓存
func (c *userCache) DeleteUserCache(ctx context.Context, userID uint, prefix string) error {
	key := fmt.Sprintf("%s%s:%d", prefix, cacheKey, userID)
	return c.client.Del(ctx, key).Err()
}

// BatchDeleteUserCache 批量删除用户缓存
func (c *userCache) BatchDeleteUserCache(ctx context.Context, userIDs []uint, prefix string) error {
	if len(userIDs) == 0 {
		return nil
	}

	// 构建所有要删除的key
	keys := make([]string, len(userIDs))
	for i, id := range userIDs {
		keys[i] = fmt.Sprintf("%s%s:%d", prefix, cacheKey, id)
	}

	// 批量删除
	return c.client.Del(ctx, keys...).Err()
}
