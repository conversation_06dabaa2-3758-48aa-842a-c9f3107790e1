package bug

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/handler"
	"marketing/internal/pkg/e"
	"marketing/internal/pkg/errors"
	"marketing/internal/service"
)

type TBugFreeLabel struct {
	svc service.BugFreeLabelSvcInterface
}

func NewMachineAccessoryRelation(svc service.BugFreeLabelSvcInterface) *TBugFreeLabel {
	return &TBugFreeLabel{
		svc: svc,
	}
}

func (b *TBugFreeLabel) BugFreeLabelList(c *gin.Context) {
	labelType := e.ReqParamInt(c, "type")
	category := e.ReqParamInt(c, "category")

	list := b.svc.GetBugFreeLabelList(c, labelType, category)

	handler.Success(c, gin.H{
		"list": list,
	})
}

func (b *TBugFreeLabel) EditBugFreeLabel(c *gin.Context) {
	id := e.ReqParamStr(c, "id")
	name := e.ReqParamStr(c, "name")
	labelType := e.ReqParamInt(c, "type")
	category := e.ReqParamInt(c, "category")
	categoryKey := e.ReqParamStr(c, "category_key")
	order := e.ReqParamInt(c, "order")
	visibility := e.ReqParamInt(c, "visibility", 1)

	err := b.svc.EditBugFreeLabel(c, id, name, labelType, category, categoryKey, order, visibility)
	if err != nil {
		handler.Error(c, errors.NewErr("编辑问题标签错误:"+err.Error()))
		return
	}

	handler.Success(c, gin.H{})
}

func (b *TBugFreeLabel) DeleteFreeLabel(c *gin.Context) {
	id := e.ReqParamStr(c, "id")

	err := b.svc.DeleteBugFreeLabel(c, id)
	if err != nil {
		handler.Error(c, errors.NewErr("删除问题标签错误:"+err.Error()))
		return
	}

	handler.Success(c, gin.H{})
}
