package system

import (
	"marketing/internal/api"
	"marketing/internal/api/system"
	"marketing/internal/handler"
	"marketing/internal/pkg/errors"
	system2 "marketing/internal/service/system"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

type AdminRoleInterface interface {
	Add(c *gin.Context)
	Lists(c *gin.Context)
	Update(c *gin.Context)
	Delete(c *gin.Context)
	UpdatePermission(c *gin.Context)
	UpdateMenu(c *gin.Context)
	UpdateApp(c *gin.Context)
	GetRoleLogs(c *gin.Context)
	GetAppDropDown(c *gin.Context)
}

type adminRole struct {
	adminRoleSvc     system2.AdminRoleInterface
	resourceGroupSvc system2.ResourceGroupService
	appSystemSvc     system2.AppSystemSvc
}

// NewAdminRole 创建 adminRole 实例
func NewAdminRole(adminRoleSvc system2.AdminRoleInterface, resourceGroupSvc system2.ResourceGroupService, appSystemSvc system2.AppSystemSvc) AdminRoleInterface {
	return &adminRole{
		adminRoleSvc:     adminRoleSvc,
		resourceGroupSvc: resourceGroupSvc,
		appSystemSvc:     appSystemSvc,
	}
}

// Add 新增角色
func (a *adminRole) Add(c *gin.Context) {
	var req system.AddRoleReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	err := a.adminRoleSvc.Add(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

func (a *adminRole) Lists(c *gin.Context) {
	var req system.AdminRoleReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	//分页处理
	req.PaginationParams.SetDefaults()
	data, err := a.adminRoleSvc.Lists(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, data)
}

// Update 更新角色
func (a *adminRole) Update(c *gin.Context) {
	var req system.AddRoleReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	req.ID = cast.ToUint(c.Param("id"))
	if req.ID == 0 {
		handler.Error(c, errors.NewErr("id不能为空"))
		return
	}

	if err := a.adminRoleSvc.Update(c, req); err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

func (a *adminRole) Delete(c *gin.Context) {

	id := cast.ToInt(c.Param("id"))
	if id == 0 {
		handler.Error(c, errors.NewErr("id不能为空"))
		return
	}
	err := a.adminRoleSvc.Delete(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

// UpdatePermission 更新角色权限
func (a *adminRole) UpdatePermission(c *gin.Context) {
	var req system.UpdateRolePermissionReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}

	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		handler.Error(c, errors.NewErr("角色ID不能为空"))
		return
	}

	err := a.adminRoleSvc.UpdatePermission(c, id, req.Permissions)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

// UpdateMenu 更新角色菜单
func (a *adminRole) UpdateMenu(c *gin.Context) {
	var req system.UpdateRoleMenuReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}

	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		handler.Error(c, errors.NewErr("角色ID不能为空"))
		return
	}

	err := a.adminRoleSvc.UpdateMenu(c, id, req.Menus)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

// UpdateApp 更新角色菜单
func (a *adminRole) UpdateApp(c *gin.Context) {
	var req system.UpdateRoleAppReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}

	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		handler.Error(c, errors.NewErr("角色ID不能为空"))
		return
	}

	err := a.adminRoleSvc.UpdateApp(c, id, req.Apps)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

// GetRoleLogs 获取角色日志
func (a *adminRole) GetRoleLogs(c *gin.Context) {
	var req system.GetUserLogsReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	req.PaginationParams.SetDefaults()
	ID := cast.ToInt(c.Param("id"))
	data, total, err := a.adminRoleSvc.GetRoleLogs(c, ID, req.Page, req.PageSize)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, &api.PaginationListResp{Data: data, Total: total, Page: req.Page, PageSize: req.PageSize})
}

func (a *adminRole) GetAppDropDown(c *gin.Context) {
	var req system.AppSystemReq

	req.PaginationParams.SetDefaults()
	req.PageSize = 10000
	appSystemList, _, err := a.appSystemSvc.GetAppSystemList(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	// 转换数据格式
	var result []*system.AppSystemDropDownResp
	for _, app := range appSystemList {
		item := &system.AppSystemDropDownResp{
			ID:     app.ID,
			AppKey: app.AppKey,
			Name:   app.Name,
		}
		if len(app.Children) > 0 {
			for _, child := range app.Children {
				item.Children = append(item.Children, &system.AppSystemDropDownResp{
					ID:     child.ID,
					AppKey: child.AppKey,
					Name:   child.Name,
				})
			}
		}
		result = append(result, item)
	}

	handler.Success(c, result)
}
