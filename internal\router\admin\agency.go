package admin

import (
	"marketing/internal/dao"
	"marketing/internal/handler/admin/agency"
	"marketing/internal/handler/admin/system"
	"marketing/internal/middleware"
	"marketing/internal/provider"
	"marketing/internal/service"
	endpointSvc "marketing/internal/service/endpoint"

	"github.com/gin-gonic/gin"
)

type AgencyRouter struct {
	svc         *provider.ServiceProvider
	infra       *provider.InfrastructureProvider
	endpointSvc endpointSvc.Endpoint
}

func NewAgencyRouter(svc *provider.ServiceProvider, infra *provider.InfrastructureProvider, endpointSvc endpointSvc.Endpoint) *AgencyRouter {
	return &AgencyRouter{
		svc:         svc,
		infra:       infra,
		endpointSvc: endpointSvc,
	}
}

func (a *AgencyRouter) Register(r *gin.RouterGroup) {
	// 渠道管理模块
	agencyRouter := r.Group("/agency")
	{
		// 初始化 DAO, Service, and Controller
		partitionDAO := dao.NewPartitionDao()
		partitionService := service.NewPartitionService(partitionDAO)
		partitionController := agency.NewPartitionHandler(partitionService)
		// 关于大区管的增删改查方法
		agencyRouter.GET("/partitions", partitionController.GetPartitions)
		agencyRouter.GET("/partition", partitionController.GetPartition)
		agencyRouter.POST("/partition/create", partitionController.CreatePartition)
		agencyRouter.PUT("/partition/update", partitionController.UpdatePartition)
		agencyRouter.DELETE("/partition/delete", partitionController.DeletePartition)

		// 关于channel的增删改查方法
		channelDao := dao.NewChannelDao()
		channelService := service.NewChannelService(channelDao)
		chanelController := agency.NewChannel(channelService)
		agencyRouter.GET("/channel/list", chanelController.GetChannelList)
		agencyRouter.GET("/channel/all", chanelController.GetAllChannel)
		agencyRouter.GET("/channel/get", chanelController.GetChannelById)
		agencyRouter.POST("/channel/edit", chanelController.EditChannel)
		agencyRouter.DELETE("/channel/delete", chanelController.DeleteChannel)

		// 关于代理列表的获取方法
		agencyRouter.GET("/agency-get", agency.GetAgencyData)
		agencyDao := dao.NewAgencyDao()
		partitionDao := dao.NewPartitionDao()
		kingDeeDao := dao.NewKingDeeAgencyDao()
		agencyService := service.NewAgencyService(agencyDao, partitionDao, kingDeeDao, channelDao)
		agencyController := agency.NewAgency(agencyService)
		agencyRouter.GET("/list", agencyController.AgencyList)
		agencyRouter.POST("/edit", agencyController.EditAgency)
		agencyRouter.DELETE("/delete", agencyController.DeleteAgency)

		// 获取代理用户(依赖注入是个灾难，自己被绕晕了，有空再改改吧，不过看来是没空了)
		agencyUserGroup := agencyRouter.Group("/users")
		{
			agencyUserGroup.Use(middleware.AdminUserLogMiddleware(a.infra.DB))
			agencyUserHandler := agency.NewAgencyUser(a.svc.AdminUserService, a.svc.AdminRoleService, a.endpointSvc)
			adminUserHandler := system.NewAdminUser(a.svc.AdminUserService,
				a.svc.AdminRoleService,
				a.svc.AdminUserGroupService,
				a.svc.WecomTageService)
			agencyUserGroup.GET("", agencyUserHandler.Lists)
			agencyUserGroup.POST("", agencyUserHandler.Add)
			agencyUserGroup.DELETE("/:id", agencyUserHandler.Delete)
			agencyUserGroup.GET("/role-dropdown", agencyUserHandler.GetRoleDropdown)
			agencyUserGroup.GET("/department-tree", agencyUserHandler.GetDepartmentTree)
			agencyUserGroup.PUT("/sync", agencyUserHandler.SyncUser)
			agencyUserGroup.GET("/export", agencyUserHandler.ExportUser)
			// 直接用原来的权限不好管控
			agencyUserGroup.PUT("/:id", agencyUserHandler.Update)
			agencyUserGroup.POST("/:id/reset-password", adminUserHandler.ResetPassword)
			agencyUserGroup.POST("/:id/status", adminUserHandler.UpdateStatus)
			agencyUserGroup.PUT("/:id/wecom-tags", adminUserHandler.UpdateWecomTag)
			agencyUserGroup.POST("/add-group", adminUserHandler.UserAddToGroup)
			agencyUserGroup.POST("/import", agencyUserHandler.ImportUsers)
		}

		// 初始化department的 DAO, Service, and Controller
		departmentDAO := dao.NewDepartmentDAO()
		departmentService := service.NewDepartmentService(departmentDAO)
		departmentController := agency.NewDepartmentHandler(departmentService)

		// 关于部门管理的增删改查方法
		agencyRouter.GET("/departments", departmentController.GetDepartments)

		// 关于地区管理的增删改查方法
		agencyRouter.GET("/regions", agency.GetRegions)
		agencyRouter.POST("/region/edit", agency.EditRegion)
		agencyRouter.DELETE("/region/delete", agency.DeleteRegion)

		// 关于金蝶用户的查询
		agencyRouter.GET("/kingdee-list-getting", agency.Kingdee.GetKingdees)
		agencyRouter.PUT("/kingdee-update", agency.Kingdee.UpdateKingdee)

		v3CustomerDao := dao.NewV3CustomerDao()
		v3CustomerService := service.NewV3CustomerService(v3CustomerDao, channelDao)
		v3CustomerController := agency.NewV3Customer(v3CustomerService)
		agencyRouter.GET("/v3_customer/list", v3CustomerController.GetCustomerList)
		agencyRouter.GET("/v3_customer/get", v3CustomerController.GetCustomerById)
		agencyRouter.POST("/v3_customer/edit", v3CustomerController.EditCustomer)
	}
}
