package reimbursement

import (
	"github.com/gin-gonic/gin"
	api "marketing/internal/api/reimbursement"
	"marketing/internal/handler"
	"marketing/internal/pkg/errors"
	service "marketing/internal/service/reimbursement"
)

// ReimbursementHandler handles reimbursement application related requests
type ReimbursementHandler interface {
	// GetPolicySummary returns a summary of reimbursement policies with application statistics
	GetPolicySummary(c *gin.Context)
	// MaterialSignIn handles material sign in
	MaterialSignIn(c *gin.Context)
	// GetSummaryShortcutStat returns statistics for summary shortcuts
	GetSummaryShortcutStat(c *gin.Context)
	//GetSummaryList returns a list of summary shortcuts
	GetSummaryList(c *gin.Context)
	// ReimbursementInvalid handles invalidation of apply order summaries
	ReimbursementInvalid(c *gin.Context)
	//ReimbursementSubmit handles submission of apply order summaries
	ReimbursementSubmit(c *gin.Context)
	// ReimbursementRetrial handles retrial of apply order summaries
	ReimbursementRetrial(c *gin.Context)
	// ReimbursementAudit 核销入账
	ReimbursementAudit(c *gin.Context)
	// ReimbursementPolicySummary 核销单汇总
	ReimbursementPolicySummary(c *gin.Context)
	//ReimbursementCompanySummary 核销单按公司汇总
	ReimbursementCompanySummary(c *gin.Context)
}

type reimbursement struct {
	policyService        service.PolicyService
	reimbursementService service.ReimbursementService
}

// NewReimbursement creates a new ApplyHandler
func NewReimbursement(policyService service.PolicyService, reimbursementService service.ReimbursementService) ReimbursementHandler {
	return &reimbursement{
		policyService:        policyService,
		reimbursementService: reimbursementService,
	}
}

// GetPolicySummary returns a summary of reimbursement policies with application statistics
func (a *reimbursement) GetPolicySummary(c *gin.Context) {
	// Parse pagination parameters
	var req api.PolicySummaryReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}

	// Set default values if not provided
	req.SetDefaults()

	// Call service to get policy summary with pagination
	summaries, total, err := a.policyService.GetPolicySummary(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	// Return policy summaries with pagination
	handler.Success(c, gin.H{
		"total": total,
		"data":  summaries,
	})
}

// ReimbursementPolicySummary returns a summary of reimbursement policies with application statistics
func (a *reimbursement) ReimbursementPolicySummary(c *gin.Context) {
	// Parse pagination parameters
	var req api.PolicySummaryReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}

	// Set default values if not provided
	req.SetDefaults()

	// Call service to get policy summary with pagination
	summaries, total, err := a.reimbursementService.ReimbursementPolicySummary(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	// Return policy summaries with pagination
	handler.Success(c, gin.H{
		"total": total,
		"data":  summaries,
	})
}

// ReimbursementCompanySummary returns a summary of reimbursement policies grouped by company
func (a *reimbursement) ReimbursementCompanySummary(c *gin.Context) {
	// Parse pagination parameters
	var req api.CompanySummaryReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}

	// Set default values if not provided
	req.SetDefaults()

	// Call service to get policy summary with pagination
	stat, summaries, total, err := a.reimbursementService.ReimbursementCompanySummary(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	// Return policy summaries with pagination
	handler.Success(c, gin.H{
		"sum_stat": stat,
		"total":    total,
		"data":     summaries,
	})
}

// MaterialSignIn 材料签收
func (a *reimbursement) MaterialSignIn(c *gin.Context) {
	var req api.MaterialSignInReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, errors.NewErr("参数错误"))
		return
	}

	// 验证申请单类型
	if req.ApplyOrderType != "promotional_products" && req.ApplyOrderType != "advert_expense" {
		handler.Error(c, errors.NewErr("申请单类型有误"))
		return
	}

	// 执行材料签收
	success, err := a.reimbursementService.MaterialSignIn(c, req.ID, req.ApplyOrderType)
	if err != nil {
		handler.Error(c, err)
		return
	}

	if success {
		handler.Success(c)
	} else {
		handler.Error(c, errors.NewErr("系统出错"))
	}
}

// GetSummaryShortcutStat 核销小单列表快捷键
func (a *reimbursement) GetSummaryShortcutStat(c *gin.Context) {
	var req api.SummaryShortcutStatReq
	if err := c.ShouldBindQuery(&req); err != nil {
		handler.Error(c, errors.NewErr("参数错误"))
		return
	}
	// 调用服务获取统计数据
	stats, err := a.reimbursementService.GetSummaryShortcutStat(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, stats)
}

// GetSummaryList 核销小单列表
func (a *reimbursement) GetSummaryList(c *gin.Context) {
	var req api.SummaryShortcutStatReq
	if err := c.ShouldBindQuery(&req); err != nil {
		handler.Error(c, errors.NewErr("参数错误: "+err.Error()))
		return
	}
	req.SetDefaults()
	// 调用服务获取核销小单列表
	summaries, total, err := a.reimbursementService.GetSummaryList(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	// 返回核销小单列表和总数
	handler.Success(c, gin.H{
		"total": total,
		"data":  summaries,
	})

}

// ReimbursementInvalid 核销小单作废
func (a *reimbursement) ReimbursementInvalid(c *gin.Context) {
	var req api.ApplyOrderSummaryInvalidReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, errors.NewErr("参数错误: "+err.Error()))
		return
	}
	err := a.reimbursementService.ApplyOrderSummaryInvalid(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c)
}

// ReimbursementSubmit 核销提交
func (a *reimbursement) ReimbursementSubmit(c *gin.Context) {
	var req api.ApplyOrderSummarySubmitReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, errors.NewErr("参数错误: "+err.Error()))
		return
	}
	if len(req.IDs) == 0 {
		handler.Error(c, errors.NewErr("请选择需要提交的核销小单"))
		return
	}
	if len(req.IDs) > 100 {
		handler.Error(c, errors.NewErr("最多只能提交100个核销小单"))
		return
	}

	err := a.reimbursementService.ReimbursementSubmit(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c)
}

// ReimbursementRetrial 核销反审
func (a *reimbursement) ReimbursementRetrial(c *gin.Context) {
	var req api.ReimbursementRetrialReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, errors.NewErr("参数错误: "+err.Error()))
		return
	}

	err := a.reimbursementService.ReimbursementRetrial(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c)
}

// ReimbursementAudit 核销入账
func (a *reimbursement) ReimbursementAudit(c *gin.Context) {
	var req api.ReimbursementAuditReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, errors.NewErr("参数错误: "+err.Error()))
		return
	}

	err := a.reimbursementService.ReimbursementAudit(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c)
}
