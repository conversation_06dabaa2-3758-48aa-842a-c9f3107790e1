package service

import (
	"errors"
	"marketing/internal/dao"
)

type PartitionService struct {
	dao *dao.PartitionDao
}

func NewPartitionService(dao *dao.PartitionDao) *PartitionService {
	return &PartitionService{dao: dao}
}

func (s *PartitionService) GetPartitions(name string, pageNum, pageSize int) ([]dao.Partition, int64) {
	return s.dao.FindPartitions(name, pageNum, pageSize)
}

func (s *PartitionService) CreatePartition(name string) error {
	if len(name) == 0 {
		return errors.New("创建大区:大区名称为空")
	}

	return s.dao.CreatePartition(&dao.Partition{
		Name: name,
	})
}

func (s *PartitionService) UpdatePartition(id int, name string) error {
	if len(name) == 0 {
		return errors.New("编辑大区:大区名称为空")
	}

	return s.dao.UpdatePartition(id, name)
}

func (s *PartitionService) DeletePartition(id int) error {
	return s.dao.DeletePartition(id)
}

func (s *PartitionService) GetPartitionByID(id int) (*dao.Partition, error) {
	return s.dao.GetPartitionByID(id)
}
