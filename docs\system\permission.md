# 权限管理 API 文档

[TOC]

## 1. 获取权限列表

##### 简要描述
- 获取系统权限列表，支持分页和条件查询

##### 请求URL
- `/admin/system/permissions`

##### 请求方式
- GET

##### 参数
| 参数名    | 必选 | 类型   | 说明          |
|-----------|------|--------|-------------|
| is_page   | 否   | bool    | 是否分页        |
| page      | 否   | int    | 页码，默认 1     |
| page_size | 否   | int    | 每页数量，默认 20  |
| name      | 否   | string | 权限名称，支持模糊搜索 |
| group_id  | 否   | int    | 权限组ID       |

##### 返回示例（有分页）
```json
{
    "ok": 1,
    "msg": "ok",
    "data": {
        "total": 100,
        "page": 1,
        "page_size": 20,
        "data": [
            {
                "id": 1,
                "name": "用户管理",
                "slug": "user-manage",
                "http_method": "POST",
                "http_path": "/admin/system/users",
                "group_id": 1,
                "group_name": "系统管理",
                "created_at": "2023-01-01 00:00:00",
                "updated_at": "2023-01-01 00:00:00"
            }
        ]
    }
}
```

##### 返回示例（无分页）
```json
{
    "ok": 1,
    "msg": "ok",
    "data": [
        {
            "id": 1,
            "name": "用户管理",
            "slug": "user-manage",
            "http_method": "POST",
            "http_path": "/admin/system/users",
            "group_id": 1,
            "group_name": "系统管理",
            "created_at": "2023-01-01 00:00:00",
            "updated_at": "2023-01-01 00:00:00"
        }
    ]
}
```

## 2. 新增权限

##### 简要描述
- 新增系统权限

##### 请求URL
- `/admin/system/permissions`

##### 请求方式
- POST

##### 参数
```json
{
    "name": "新增用户",
    "slug": "user-create",
    "http_method": "POST",
    "http_path": "/admin/system/users",
    "group_id": 1
}
```

| 参数名      | 必选 | 类型   | 说明                |
|------------|------|--------|-------------------|
| name       | 是   | string | 权限名称            |
| slug       | 是   | string | 权限标识            |
| http_method| 是   | string | HTTP请求方法        |
| http_path  | 是   | string | HTTP请求路径        |
| group_id   | 是   | uint   | 所属权限组ID        |

##### 返回示例
```json
{
    "ok": 1,
    "msg": "ok"
}
```

##### 备注
- slug 必须唯一
- slug 只能包含字母、数字和中划线

## 3. 更新权限

##### 简要描述
- 更新权限信息

##### 请求URL
- `/admin/system/permissions/:id`

##### 请求方式
- PUT

##### 参数
```json
{
    "name": "新增用户",
    "slug": "user-create",
    "http_method": "POST",
    "http_path": "/admin/system/users",
    "group_id": 1
}
```

| 参数名      | 必选 | 类型   | 说明                |
|------------|------|--------|-------------------|
| name       | 是   | string | 权限名称            |
| slug       | 是   | string | 权限标识            |
| http_method| 是   | string | HTTP请求方法        |
| http_path  | 是   | string | HTTP请求路径        |
| group_id   | 是   | uint   | 所属权限组ID        |

##### 返回示例
```json
{
    "ok": 1,
    "msg": "ok"
}
```

## 4. 删除权限

##### 简要描述
- 删除系统权限

##### 请求URL
- `/admin/system/permissions/:id`

##### 请求方式
- DELETE

##### 返回示例
```json
{
    "ok": 1,
    "msg": "ok"
}
```

##### 备注
- 如果权限已被角色使用，则不能删除

## 5. 获取路由列表

##### 简要描述
- 获取系统所有路由列表，用于权限配置

##### 请求URL
- `/admin/system/permissions/routes`

##### 请求方式
- GET

##### 返回示例
```json
{
    "ok": 1,
    "msg": "ok",
    "data": [
        {
            "path": "/admin/system/users",
            "method": "POST"
        },
        {
            "path": "/admin/system/users",
            "method": "GET"
        },
        {
            "path": "/admin/system/users/:id",
            "method": "PUT"
        }
    ]
}
```

##### 返回参数说明
| 参数名      | 类型   | 说明                     |
|------------|--------|------------------------|
| path       | string | 路由路径                |
| method     | string | HTTP请求方法            |
| handler    | string | 处理函数说明             |
| middleware | array  | 中间件列表               |

##### 备注
- 所有接口都需要登录认证
- 所有接口都需要相应的权限
- 返回的 ok 为 1 表示成功，为 0 表示失败

 