package dao

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"marketing/internal/model"
	"marketing/internal/pkg/db"
	"marketing/internal/pkg/utils"
)

type AfterSalesEndpointDao interface {
	CreateAfterSalesEndpoint(c *gin.Context, endpoint *model.AfterSalesEndpoint) error
	DeleteAfterSalesEndpoint(c *gin.Context, id int) error
	EditAfterSalesEndpoint(c *gin.Context, id int, uMap map[string]interface{}) error
	GetAfterSalesEndpointList(c *gin.Context, name string, pageNum, pageSize int) ([]*model.AfterSalesEndpoint, int64)
	GetAfterSalesEndpointByAgency(c *gin.Context, agencyId int) []*model.AfterSalesEndpoint
	GetAfterSalesEndpointById(c *gin.Context, id int) *model.AfterSalesEndpoint
	GetAfterSalesEndpointByIds(c *gin.Context, ids []int) (list []*model.AfterSalesEndpoint)
}

// AfterSalesEndpointDaoImpl 实现 AfterSalesEndpointDao 接口
type AfterSalesEndpointDaoImpl struct {
	db *gorm.DB
}

// NewAfterSalesEndpointDao 创建 AfterSalesEndpointDao 实例
func NewAfterSalesEndpointDao() AfterSalesEndpointDao {
	return &AfterSalesEndpointDaoImpl{
		db: db.GetDB(""),
	}
}

func (d *AfterSalesEndpointDaoImpl) CreateAfterSalesEndpoint(c *gin.Context, endpoint *model.AfterSalesEndpoint) error {
	return d.db.WithContext(c).Create(endpoint).Error
}

func (d *AfterSalesEndpointDaoImpl) DeleteAfterSalesEndpoint(c *gin.Context, id int) error {
	return d.db.WithContext(c).Delete(&model.AfterSalesEndpoint{}, "id = ?", id).Error
}

func (d *AfterSalesEndpointDaoImpl) EditAfterSalesEndpoint(c *gin.Context, id int, uMap map[string]interface{}) error {
	return d.db.WithContext(c).Model(&model.AfterSalesEndpoint{}).Where("id = ?", id).Updates(uMap).Error
}

func (d *AfterSalesEndpointDaoImpl) GetAfterSalesEndpointList(c *gin.Context, name string, pageNum, pageSize int) ([]*model.AfterSalesEndpoint, int64) {
	query := d.db.WithContext(c).Model(&model.AfterSalesEndpoint{})

	if len(name) > 0 {
		query = query.Where("name = ?", name)
	}

	data, total := utils.PaginateQueryV1(query, pageNum, pageSize, new([]*model.AfterSalesEndpoint))
	for i, p := range *data {
		(*data)[i].CreateTime = utils.GetTimeStr(p.CreatedAt)
		(*data)[i].UpdateTime = utils.GetTimeStr(p.UpdatedAt)
	}

	return *data, total
}

func (d *AfterSalesEndpointDaoImpl) GetAfterSalesEndpointByAgency(c *gin.Context, agencyId int) (list []*model.AfterSalesEndpoint) {
	d.db.WithContext(c).Model(&model.AfterSalesEndpoint{}).Select("id,name").Where("second_agency_id = ?", agencyId).Find(&list)
	return
}

func (d *AfterSalesEndpointDaoImpl) GetAfterSalesEndpointById(c *gin.Context, id int) *model.AfterSalesEndpoint {
	var endpoint model.AfterSalesEndpoint
	err := d.db.WithContext(c).Model(&model.AfterSalesEndpoint{}).Where("id = ?", id).First(&endpoint).Error
	if err != nil {
		return nil
	}

	endpoint.CreateTime = utils.GetTimeStr(endpoint.CreatedAt)
	endpoint.UpdateTime = utils.GetTimeStr(endpoint.UpdatedAt)

	return &endpoint
}

func (d *AfterSalesEndpointDaoImpl) GetAfterSalesEndpointByIds(c *gin.Context, ids []int) (list []*model.AfterSalesEndpoint) {
	d.db.WithContext(c).Model(&model.AfterSalesEndpoint{}).Where("id in (?)", ids).Find(&list)

	for _, l := range list {
		l.CreateTime = utils.GetTimeStr(l.CreatedAt)
		l.UpdateTime = utils.GetTimeStr(l.UpdatedAt)
	}

	return
}
