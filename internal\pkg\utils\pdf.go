package utils

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
)

func ConvertDocxToPDF(docxData []byte) ([]byte, error) {
	// 创建临时目录
	tmpDir, err := os.MkdirTemp("", "docx2pdf_*")
	if err != nil {
		return nil, fmt.Errorf("error creating temp dir: %v", err)
	}
	defer os.RemoveAll(tmpDir) // 函数结束清理

	inputPath := filepath.Join(tmpDir, "input.docx")
	outputPath := filepath.Join(tmpDir, "input.pdf")

	// 写入 DOCX
	if err := os.WriteFile(inputPath, docxData, 0644); err != nil {
		return nil, fmt.Errorf("error writing DOCX: %v", err)
	}

	// 调用 LibreOffice
	cmd := exec.Command("soffice",
		"--headless",
		"--convert-to", "pdf",
		"--outdir", tmpDir,
		inputPath,
	)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return nil, fmt.Errorf("error converting DOCX to PDF: %v, Output: %s", err, string(output))
	}

	// 读取 PDF
	pdfData, err := os.ReadFile(outputPath)
	if err != nil {
		return nil, fmt.Errorf("error reading PDF: %v", err)
	}

	return pdfData, nil
}
