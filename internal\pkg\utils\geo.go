package utils

import "math"

// Haversine 计算 Haversine 值
func Haversine(theta float64) float64 {
	return math.Pow(math.Sin(theta/2), 2)
}

// Distance calculates the distance between two points on the Earth
// unit: 1 for meters, 2 for kilometers
func Distance(lat1, lon1, lat2, lon2 float64, unit int, decimal int) float64 {
	// convert to radians
	// must cast radius as float to multiply later
	var la1, lo1, la2, lo2, r float64
	la1 = lat1 * math.Pi / 180
	lo1 = lon1 * math.Pi / 180
	la2 = lat2 * math.Pi / 180
	lo2 = lon2 * math.Pi / 180

	r = 6371000 // Earth radius in METERS

	// calculate
	h := Haversine(la2-la1) + math.Cos(la1)*math.Cos(la2)*Haversine(lo2-lo1)

	distance := 2 * r * math.Asin(math.Sqrt(h))

	if unit == 2 {
		distance /= 1000
	}

	// 精度处理
	factor := math.Pow(10, float64(decimal))
	return math.Round(distance*factor) / factor
}
