package material

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/handler"
	"marketing/internal/pkg/e"
	"marketing/internal/pkg/errors"
	"marketing/internal/service"
)

type Category struct {
	svc service.MaterialCategorySvcInterface
}

func NewMaterialCategory(svc service.MaterialCategorySvcInterface) *Category {
	return &Category{
		svc: svc,
	}
}

func (m *Category) List(c *gin.Context) {
	list := m.svc.GetAllMaterialCategory(c)
	handler.Success(c, gin.H{
		"list": list,
	})
}

func (m *Category) RefreshMaterialCategory(c *gin.Context) {
	list := m.svc.RefreshMaterialCategory(c)
	handler.Success(c, gin.H{
		"list": list,
	})
}

func (m *Category) EditMaterialCategory(c *gin.Context) {
	id := e.ReqParamInt(c, "id", -1)
	pid := e.ReqParamInt(c, "pid")
	name := e.ReqParamStr(c, "name")

	if id != -1 {
		err := m.svc.EditMaterialCategory(c, id, pid, name)
		if err != nil {
			handler.Error(c, errors.NewErr(err.Error()))
			return
		}
	} else {
		err := m.svc.AddMaterialCategory(c, pid, name)
		if err != nil {
			handler.Error(c, errors.NewErr(err.Error()))
			return
		}
	}

	handler.Success(c, gin.H{})
}

func (m *Category) DeleteMaterialCategory(c *gin.Context) {
	id := e.ReqParamInt(c, "id")
	err := m.svc.DelMaterialCategory(c, id)
	if err != nil {
		handler.Error(c, errors.NewErr("删除物料标签失败"))
		return
	}

	handler.Success(c, gin.H{})
}
