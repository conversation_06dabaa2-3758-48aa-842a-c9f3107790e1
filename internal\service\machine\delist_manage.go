package machine

import (
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	api "marketing/internal/api/materials"
	"marketing/internal/dao"
	"marketing/internal/model"
	appError "marketing/internal/pkg/errors"
	"marketing/internal/pkg/notification"
	"time"
)

type DelistManageSvc interface {
	GetMachineTypeList(c *gin.Context, search *api.DelistSearch) ([]*api.DelistListVo, int64, error)
	UpdateDelistStatus(c *gin.Context, id int, delist int, delistTime time.Time) (bool, error)
	UpdateDeclareStatus(c *gin.Context, id int, declare int) (bool, string)
	UpdateStockStatus(c *gin.Context, id int, stock int) (bool, string)
	SendNotice(c *gin.Context, modelName string) (int, string)
}

type DelistManageSvcImpl struct {
	db                     *gorm.DB
	categoryRepo           dao.ModelCategoryDao
	machineTypeRepo        dao.MachineTypeDao
	machineTypeRelationDao dao.MachineTypeRelationDao
	notificationService    notification.NotificationService
}

func NewDelistManageService(
	db *gorm.DB,
	categoryRepo dao.ModelCategoryDao,
	machineTypeRepo dao.MachineTypeDao,
	machineTypeRelationDao dao.MachineTypeRelationDao,
	notificationService notification.NotificationService) DelistManageSvc {
	return &DelistManageSvcImpl{
		db:                     db,
		categoryRepo:           categoryRepo,
		machineTypeRepo:        machineTypeRepo,
		machineTypeRelationDao: machineTypeRelationDao,
		notificationService:    notificationService,
	}
}

func (s *DelistManageSvcImpl) GetMachineTypeList(c *gin.Context, search *api.DelistSearch) ([]*api.DelistListVo, int64, error) {
	// 获取机型列表
	list, total, err := s.machineTypeRelationDao.GetDelistList(c, search)
	if err != nil {
		return nil, 0, err
	}

	// 获取分类信息
	categoryList := s.categoryRepo.GetAllModelCategory(c)
	categoryMap := make(map[int]string)
	for _, v := range categoryList {
		categoryMap[v.Id] = v.Name
	}

	// 获取所有机型名称
	var modelNames []string
	for _, item := range list {
		modelNames = append(modelNames, item.Name)
	}
	// 查询库存数据
	for i, item := range list {
		name := item.Name
		// 查询期初库存
		var inventoryInfo struct {
			SumInventory int64
		}
		s.db.WithContext(c).Table("drp_delist").
			Select("SUM(inventory) as sum_inventory").
			Where("model_name", name).
			Group("model_name").
			Scan(&inventoryInfo)
		list[i].Inventory = inventoryInfo.SumInventory

		// 查询样机
		var prototypeInfo struct {
			SumPrototype int64
		}
		s.db.WithContext(c).Table("drp_delist").
			Select("SUM(prototype) as sum_prototype").
			Where("model_name", name).
			Group("model_name").
			Scan(&prototypeInfo)
		list[i].Prototype = prototypeInfo.SumPrototype

		// 查询新机库存
		var newMachineInfo struct {
			SumNewMachine int64
		}
		s.db.WithContext(c).Table("drp_delist").
			Select("SUM(new_machine) as sum_new_machine").
			Where("model_name", name).
			Group("model_name").
			Scan(&newMachineInfo)
		list[i].NewMachineInventory = newMachineInfo.SumNewMachine
	}

	return list, total, nil
}

func (s *DelistManageSvcImpl) UpdateDelistStatus(c *gin.Context, id int, delist int, delistTime time.Time) (bool, error) {
	// 获取机型信息
	machineInfo := s.machineTypeRepo.GetMachineTypeByModelId(c, id)
	if machineInfo == nil {
		return false, appError.NewErr("机型不存在")
	}

	// 准备更新数据
	now := time.Now()

	// 查询该机型是否已在配置表
	relation, err := s.machineTypeRelationDao.GetByName(c, machineInfo.Name)

	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		return false, appError.NewErr("机型查询失败: " + err.Error())
	}
	// 改变machine_type表所有相同机型名称的下市状态
	if delist == 1 {
		if relation.ID > 0 {
			updateData := map[string]interface{}{
				"delist":             delist,
				"updated_at":         now,
				"delist_on_off_time": now,
				"delist_time":        delistTime,
			}
			err = s.machineTypeRepo.UpdateByName(c, machineInfo.Name, updateData)
			if err != nil {
				return false, appError.NewErr("更新machine_type表失败: " + err.Error())
			}
		} else {
			//创建
			newRelation := &model.MachineTypeRelation{
				Name:            machineInfo.Name,
				CategoryID:      machineInfo.CategoryId,
				Declare:         machineInfo.Declare,
				Stock:           machineInfo.Stock,
				Delist:          delist,
				DelistTime:      &delistTime,
				DelistOnOffTime: &now,
				CreatedAt:       now,
				UpdatedAt:       now,
			}
			err = s.machineTypeRelationDao.Create(c, newRelation)
		}
		return true, err
	}

	// 更新现有记录(如果是1改成下市，记录已经存在)
	updateData := map[string]interface{}{
		"delist":     delist,
		"updated_at": now,
	}
	err = s.machineTypeRepo.UpdateByName(c, machineInfo.Name, updateData)
	if err != nil {
		return false, appError.NewErr("更新machine_type表失败: " + err.Error())
	}
	return true, nil
}

func (s *DelistManageSvcImpl) UpdateDeclareStatus(c *gin.Context, id int, declare int) (bool, string) {
	// 获取机型信息
	machineInfo := s.machineTypeRepo.GetMachineTypeByModelId(c, id)
	if machineInfo == nil {
		return false, "机型不存在"
	}

	// 准备更新数据
	updateData := map[string]interface{}{
		"declare":    declare,
		"updated_at": time.Now(),
	}

	// 改变machine_type表所有相同机型名称的申报状态
	uMap := map[string]interface{}{
		"declare": declare,
	}
	err := s.machineTypeRepo.UpdateByName(c, machineInfo.Name, uMap)
	if err != nil {
		return false, "更新machine_type表失败"
	}

	// 查询该机型是否已在配置表
	relation, err := s.machineTypeRelationDao.GetByName(c, machineInfo.Name)

	if err == nil && relation != nil {
		// 更新现有记录
		err = s.machineTypeRelationDao.Update(c, machineInfo.Name, updateData)
		if err != nil {
			return false, "更新失败"
		}
		return true, "更新成功"
	} else {
		// 创建新记录
		newRelation := &model.MachineTypeRelation{
			Name:       machineInfo.Name,
			CategoryID: machineInfo.CategoryId,
			Declare:    declare,
			CreatedAt:  time.Now(),
			UpdatedAt:  time.Now(),
		}

		err = s.machineTypeRelationDao.Create(c, newRelation)
		if err != nil {
			return false, "创建记录失败"
		}
		return true, "更新成功"
	}
}

func (s *DelistManageSvcImpl) UpdateStockStatus(c *gin.Context, id int, stock int) (bool, string) {
	// 获取机型信息
	machineInfo := s.machineTypeRepo.GetMachineTypeByModelId(c, id)
	if machineInfo == nil {
		return false, "机型不存在"
	}

	// 准备更新数据
	updateData := map[string]interface{}{
		"stock":      stock,
		"updated_at": time.Now(),
	}

	// 改变machine_type表所有相同机型名称的备货状态
	uMap := map[string]interface{}{
		"stock": stock,
	}
	err := s.machineTypeRepo.UpdateByName(c, machineInfo.Name, uMap)
	if err != nil {
		return false, "更新machine_type表失败"
	}

	// 查询该机型是否已在配置表
	relation, err := s.machineTypeRelationDao.GetByName(c, machineInfo.Name)

	if err == nil && relation != nil {
		// 更新现有记录
		err = s.machineTypeRelationDao.Update(c, machineInfo.Name, updateData)
		if err != nil {
			return false, "更新失败"
		}
		return true, "更新成功"
	} else {
		// 创建新记录
		newRelation := &model.MachineTypeRelation{
			Name:       machineInfo.Name,
			CategoryID: machineInfo.CategoryId,
			Stock:      stock,
			CreatedAt:  time.Now(),
			UpdatedAt:  time.Now(),
		}

		err = s.machineTypeRelationDao.Create(c, newRelation)
		if err != nil {
			return false, "创建记录失败"
		}
		return true, "更新成功"
	}
}

func (s *DelistManageSvcImpl) SendNotice(c *gin.Context, modelName string) (int, string) {
	// 获取通知类型
	notificationType, err := s.notificationService.GetNotificationTypeBySlug(c, "delist")
	if err != nil {
		return 0, fmt.Sprintf("获取通知类型失败: %s", err.Error())
	}

	// 设置接收者
	audience := map[string]interface{}{
		"roles": []string{"topAgency"},
	}

	// 发送通知
	err = s.notificationService.PushNotification(
		"下市申报通知",
		modelName+"已开启下市申报",
		[]string{"ios", "android"},
		audience,
		"delist",
		notificationType.Action,
		notificationType.ActionText,
		notificationType.URL,
		notificationType.Popup,
		notificationType.Banner,
		"",
		"",
	)

	if err != nil {
		return 0, err.Error()
	}

	return 1, "发送成功"
}
