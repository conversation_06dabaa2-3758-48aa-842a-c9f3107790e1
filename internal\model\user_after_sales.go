package model

import (
	"time"
)

type UserAfterSalesDetails struct {
	UserAfterSales
	Name             string `json:"name" gorm:"name"`            // 用户名称
	Password         string `json:"password" gorm:"password"`    // 用户密码
	TopAgencyName    string `json:"top_agency_name" gorm:"-"`    // 一级代理名称(来源于代理表:agency)
	SecondAgencyName string `json:"second_agency_name" gorm:"-"` // 二级代理名称(来源于代理表:agency)
	EndpointName     string `json:"endpoint_name" gorm:"-"`      // 终端名称(来源于售后终端表:after_sales_endpoint)
	Province         int    `json:"province" gorm:"province"`    // 省(来源于寄修地址表:after_sales_delivery_address)
	City             int    `json:"city" gorm:"city"`            // 市(来源于寄修地址表:after_sales_delivery_address)
	District         int    `json:"district" gorm:"district"`    // 区(来源于寄修地址表:after_sales_delivery_address)
	Address          string `json:"address" gorm:"address"`      // 地址(来源于寄修地址表:after_sales_delivery_address)
}

type UserAfterSales struct {
	Id               int       `json:"id" gorm:"id"`                             // id
	UserId           int       `json:"user_id" gorm:"user_id"`                   // 用户id
	EndpointId       int       `json:"endpoint_id" gorm:"endpoint_id"`           // 终端id
	Status           int       `json:"status" gorm:"status"`                     // 是否启用 0:未启用 1:启用
	AgencyType       string    `json:"agency_type" gorm:"agency_type"`           // 代理商维修工程师类型
	CompanyType      string    `json:"company_type" gorm:"company_type"`         // 公司维修工程师类型
	Phone            string    `json:"phone" gorm:"-"`                           // 维修人员电话
	PhoneCode        string    `json:"-" gorm:"phone_code"`                      // 维修人员电话(加密数据)
	IdentityCard     string    `json:"identity_card" gorm:"-"`                   // 维修人员身份证号
	IdentityCardCode string    `json:"-" gorm:"identity_card_code"`              // 维修人员身份证号(加密数据)
	ThroughTraining  int       `json:"through_training" gorm:"through_training"` // 是否通过培训
	Education        string    `json:"education" gorm:"education"`               // 学历
	EntryTime        time.Time `json:"-" gorm:"entry_time"`                      // 入职时间
	EntryTimeStr     string    `json:"entry_time" gorm:"-"`                      // 入职时间
	Remark           string    `json:"remark" gorm:"remark"`                     // 备注
	Deliverable      int       `json:"deliverable" gorm:"deliverable"`           // 是否支持寄修
	Corporation      string    `json:"corporation" gorm:"corporation"`           // 公司名称
	CreatedAt        time.Time `json:"-" gorm:"created_at"`                      // 创建时间
	CreateTime       string    `json:"create_time" gorm:"-"`                     // 创建时间
	UpdatedAt        time.Time `json:"-" gorm:"updated_at"`                      // 修改时间
	UpdateTime       string    `json:"update_time" gorm:"-"`                     // 修改时间
}

func (UserAfterSales) TableName() string {
	return "user_after_sales"
}

type AfterSalesDeliveryAddress struct {
	Id           int    `json:"id" gorm:"id"`                         // id
	AfterSalesId int    `json:"after_sales_id" gorm:"after_sales_id"` // 售后id
	Province     int    `json:"province" gorm:"province"`             // 省
	City         int    `json:"city" gorm:"city"`                     // 市
	District     int    `json:"district" gorm:"district"`             // 区
	Address      string `json:"address" gorm:"address"`               // 地址
}

func (AfterSalesDeliveryAddress) TableName() string {
	return "after_sales_delivery_address"
}
