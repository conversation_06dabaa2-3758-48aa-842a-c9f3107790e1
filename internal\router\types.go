package router

import "github.com/gin-gonic/gin"

// IRouter 路由注册接口
// 所有需要注册路由的模块都需要实现这个接口
type IRouter interface {
	// Register 注册路由方法
	// 参数 r 是 gin 的路由组,用于注册具体的路由
	Register(r *gin.RouterGroup)
}

// IMiddlewareProvider 中间件提供接口
// 允许路由提供自己的中间件
type IMiddlewareProvider interface {
	// Middlewares 返回该路由需要使用的中间件列表
	Middlewares() []gin.HandlerFunc
}

// IRouterWithMiddleware 带中间件的路由接口
// 同时实现路由注册和中间件提供功能
type IRouterWithMiddleware interface {
	IRouter
	IMiddlewareProvider
}

// IRouterGroup 路由组接口
// 用于管理一组相关的路由,比如 admin 模块、agency 模块
type IRouterGroup interface {
	IRouter                         // 继承 IRouter 接口
	Prefix() string                 // 返回路由组的前缀,如 "/admin"、"/agency"
	Middlewares() []gin.HandlerFunc // 返回该路由组需要使用的中间件列表
}

// BaseRouterGroup 基础路由组
// 提供路由组的基本实现,其他路由组可以嵌入此结构体
type BaseRouterGroup struct {
	prefix      string            // 路由前缀
	middlewares []gin.HandlerFunc // 中间件列表
}

// NewBaseRouterGroup 创建基础路由组
func NewBaseRouterGroup(prefix string, middlewares ...gin.HandlerFunc) *BaseRouterGroup {
	return &BaseRouterGroup{
		prefix:      prefix,
		middlewares: middlewares,
	}
}

// Prefix 实现 IRouterGroup 接口
func (b *BaseRouterGroup) Prefix() string {
	return b.prefix
}

// Middlewares 实现 IRouterGroup 接口
func (b *BaseRouterGroup) Middlewares() []gin.HandlerFunc {
	return b.middlewares
}

// MiddlewareWrapper 中间件包装器
// 为普通路由添加中间件能力
type MiddlewareWrapper struct {
	router      IRouter
	middlewares []gin.HandlerFunc
	pathPrefix  string
}

// NewMiddlewareWrapper 创建中间件包装器
func NewMiddlewareWrapper(router IRouter, middlewares ...gin.HandlerFunc) *MiddlewareWrapper {
	return &MiddlewareWrapper{
		router:      router,
		middlewares: middlewares,
	}
}

// WithPathPrefix 设置路径前缀
func (m *MiddlewareWrapper) WithPathPrefix(prefix string) *MiddlewareWrapper {
	m.pathPrefix = prefix
	return m
}

// Register 实现 IRouter 接口
func (m *MiddlewareWrapper) Register(r *gin.RouterGroup) {
	// 创建带特定中间件的子路由组
	var subGroup *gin.RouterGroup
	if m.pathPrefix != "" {
		subGroup = r.Group(m.pathPrefix)
	} else {
		subGroup = r.Group("")
	}
	subGroup.Use(m.middlewares...)
	m.router.Register(subGroup)
}

// Middlewares 实现 IMiddlewareProvider 接口
func (m *MiddlewareWrapper) Middlewares() []gin.HandlerFunc {
	return m.middlewares
}
