package model

import (
	"time"
)

type BugFreeLabel struct {
	Id           int       `json:"id" gorm:"column:id"`                       // id
	Name         string    `json:"name" gorm:"column:name"`                   // 名称
	Type         int       `json:"type" gorm:"column:type"`                   // 类型
	Category     int       `json:"category" gorm:"column:category"`           // 父id
	CategoryKey  string    `json:"category_key" gorm:"column:category_key"`   // 标签key
	CategoryName string    `json:"category_name" gorm:"column:category_name"` // 标签名称
	Order        int       `json:"order" gorm:"column:order"`                 // 排序
	Visibility   int       `json:"visibility" gorm:"column:visibility"`       // 是否显示 0:不显示 1:显示
	CreatedAt    time.Time `json:"-" gorm:"created_at"`                       // CreatedAt 创建时间
	CreateTime   string    `json:"create_time" gorm:"-"`                      // 创建时间
	UpdatedAt    time.Time `json:"-" gorm:"updated_at"`                       // UpdatedAt 修改时间
	UpdateTime   string    `json:"update_time" gorm:"-"`                      // 修改时间
}

func (BugFreeLabel) TableName() string {
	return "bugfree_label"
}

type BugFreeLabelCategory struct {
	Id         int       `json:"id" gorm:"column:id"`                 // id
	Name       string    `json:"name" gorm:"column:name"`             // 名称
	Key        string    `json:"key" gorm:"column:key"`               // key
	Order      int       `json:"order" gorm:"column:order"`           // 排序
	Visibility int       `json:"visibility" gorm:"column:visibility"` // 是否显示 0:不显示 1:显示
	CreatedAt  time.Time `json:"-" gorm:"created_at"`                 // CreatedAt 创建时间
	CreateTime string    `json:"create_time" gorm:"-"`                // 创建时间
	UpdatedAt  time.Time `json:"-" gorm:"updated_at"`                 // UpdatedAt 修改时间
	UpdateTime string    `json:"update_time" gorm:"-"`                // 修改时间
}

func (BugFreeLabelCategory) TableName() string {
	return "bugfree_label_category"
}
