package endpoint

import (
	"errors"
	"marketing/internal/model"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type ImageDao interface {
	GetByID(c *gin.Context, id uint) (*model.EndpointImageApply, error)
	Update(c *gin.Context, id uint, updateData map[string]interface{}) error
	GetLatest(c *gin.Context, id int) (*model.EndpointImageApply, error)
}

type endpointImage struct {
	db *gorm.DB
}

func NewEndpointImageDao(db *gorm.DB) ImageDao {
	return &endpointImage{db: db}
}

func (dao *endpointImage) GetByID(c *gin.Context, id uint) (*model.EndpointImageApply, error) {
	var apply model.EndpointImageApply
	err := dao.db.WithContext(c).Where("id = ?", id).First(&apply).Error
	if err != nil {
		return nil, err
	}
	return &apply, nil
}

func (dao *endpointImage) Update(c *gin.Context, id uint, updateData map[string]interface{}) error {
	return dao.db.WithContext(c).Model(&model.EndpointImageApply{}).Where("id = ?", id).Updates(updateData).Error
}

func (dao *endpointImage) GetLatest(c *gin.Context, id int) (*model.EndpointImageApply, error) {
	var apply model.EndpointImageApply
	err := dao.db.WithContext(c).Where("endpoint_id = ?", id).Order("id desc").First(&apply).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}
	if err != nil {
		return nil, err
	}
	return &apply, nil
}
