package admin

import (
	"marketing/internal/handler/admin/system"
	"marketing/internal/middleware"
	"marketing/internal/provider"

	"github.com/gin-gonic/gin"
)

type SystemRouter struct {
	svc   *provider.ServiceProvider
	infra *provider.InfrastructureProvider
}

func NewSystemRouter(svc *provider.ServiceProvider, infra *provider.InfrastructureProvider) *SystemRouter {
	return &SystemRouter{
		svc:   svc,
		infra: infra,
	}
}

// Register RegisterSystemRoutes 注册系统相关的路由(用户管理、角色管理、权限管理、菜单管理、权限组管理)
// @Summary 正常情况下，我们会将handler注册到对应的 providerHandler 中，但是在这里我们将路由注册到 router 中 ，有空再改
func (s SystemRouter) Register(r *gin.RouterGroup) {
	systemRouter := r.Group("system")
	{
		// 用户管理
		adminUser := systemRouter.Group("/users")
		{
			//用户日志中间件
			adminUser.Use(middleware.AdminUserLogMiddleware(s.infra.DB))

			adminUserHandler := system.NewAdminUser(s.svc.AdminUserService,
				s.svc.AdminRoleService,
				s.svc.AdminUserGroupService,
				s.svc.WecomTageService)

			adminUser.POST("", adminUserHandler.Add)
			adminUser.GET("", adminUserHandler.Lists)
			adminUser.GET("/:id", adminUserHandler.Get)
			adminUser.PUT("/:id", adminUserHandler.Update)
			adminUser.POST("/:id/reset-password", adminUserHandler.ResetPassword)
			adminUser.POST("/:id/status", adminUserHandler.UpdateStatus)
			adminUser.GET("/roles/dropdown", adminUserHandler.GetRoleDropdown)
			adminUser.GET("/groups/dropdown", adminUserHandler.GetGroupDropdown)
			adminUser.GET("/wecom-tags/dropdown", adminUserHandler.GetWecomTagDropdown)
			adminUser.GET("/:id/logs", adminUserHandler.GetUserLogs)
			adminUser.POST("/:id/wecom-tags", adminUserHandler.UpdateWecomTag)
			adminUser.POST("/add-group", adminUserHandler.UserAddToGroup)
			//同步用户到企业微信
			adminUser.PUT("/sync", adminUserHandler.SyncUser)
			//注销用户（修改状态、清理phone、用户名占用等）
			adminUser.DELETE("/:id", adminUserHandler.Delete)
		}

		// 角色管理
		adminRole := systemRouter.Group("/roles")
		{
			//角色日志中间件
			adminRole.Use(middleware.AdminRoleLogMiddleware(s.infra.DB))
			adminRoleHandler := system.NewAdminRole(s.svc.AdminRoleService, s.svc.ResourceGroupService, s.svc.AppSystemService)
			adminRole.POST("", adminRoleHandler.Add)
			adminRole.GET("", adminRoleHandler.Lists)
			adminRole.PUT("/:id", adminRoleHandler.Update)
			adminRole.DELETE("/:id", adminRoleHandler.Delete)
			adminRole.POST("/:id/permission", adminRoleHandler.UpdatePermission)
			adminRole.POST("/:id/menu", adminRoleHandler.UpdateMenu)
			adminRole.POST("/:id/apps", adminRoleHandler.UpdateApp)
			adminRole.GET("/:id/logs", adminRoleHandler.GetRoleLogs)
			adminRole.GET("/app-dropdown", adminRoleHandler.GetAppDropDown)
		}

		// 权限管理
		permission := systemRouter.Group("/permissions")
		{
			permHandler := system.NewAdminPermission(s.svc.AdminPermissionService)
			permission.POST("", permHandler.Add)
			permission.GET("", permHandler.Lists)
			permission.PUT("/:id", permHandler.Update)
			permission.DELETE("/:id", permHandler.Delete)
			permission.GET("/routes", permHandler.RoutesList)
		}

		// 菜单管理
		menu := systemRouter.Group("/menus")
		{
			menuHandler := system.NewAdminMenu(s.svc.AdminMenuService)
			menu.POST("", menuHandler.Add)
			menu.GET("", menuHandler.Lists)
			menu.PUT("/:id", menuHandler.Update)
			menu.DELETE("/:id", menuHandler.Delete)
		}

		// 权限组管理
		permGroup := systemRouter.Group("/permission-groups")
		{
			permGroupHand1er := system.NewAdminPermGroup(s.svc.AdminPermGroupService)
			permGroup.POST("", permGroupHand1er.Add)
			permGroup.GET("", permGroupHand1er.Lists)
			permGroup.PUT("/:id", permGroupHand1er.Update)
			permGroup.DELETE("/:id", permGroupHand1er.Delete)
		}

		userGroup := systemRouter.Group("/user-groups")
		{
			adminUserGroupHandler := system.NewAdminUserGroup(s.svc.AdminUserGroupService)
			userGroup.GET("", adminUserGroupHandler.List)
			userGroup.POST("", adminUserGroupHandler.Add)
			userGroup.PUT("/:id", adminUserGroupHandler.Update)
			userGroup.DELETE("/:id", adminUserGroupHandler.Delete)
			userGroup.POST("/add-user", adminUserGroupHandler.AddUserToGroup)
		}

		// WeCom Tag Management
		wecomTag := systemRouter.Group("/wecom-tags")
		{
			wecomTagHandler := system.NewWecomTag(s.svc.WecomTageService)
			wecomTag.GET("", wecomTagHandler.Lists)
			wecomTag.POST("", wecomTagHandler.Add)
			wecomTag.PUT("/:id", wecomTagHandler.Update)
			wecomTag.DELETE("/:id", wecomTagHandler.Delete)
			wecomTag.GET("/sync-from-wecom", wecomTagHandler.SyncTagsFormWecom)
			wecomTag.GET("/move-department", wecomTagHandler.MoveDepartment)
		}

		// 资源组路由
		resourceGroups := systemRouter.Group("/resource-groups")
		{
			groupHandler := system.NewResourceGroupHandler(s.svc.ResourceGroupService)
			resourceGroups.POST("", groupHandler.Create)
			resourceGroups.PUT("/:id", groupHandler.Update)
			resourceGroups.DELETE("/:id", groupHandler.Delete)
			resourceGroups.GET("", groupHandler.List)
			resourceGroups.GET("/dropdown", groupHandler.GetResourceGroupsDropdown)
			resourceGroups.GET("/:id/sync_wecom", groupHandler.Sync)
		}
		//应用系统
		appSystem := systemRouter.Group("/app-systems")
		{
			appSystemHandler := system.NewAppSystemHandler(s.svc.AppSystemService)
			appSystem.POST("", appSystemHandler.Add)
			appSystem.PUT("/:id", appSystemHandler.Update)
			appSystem.DELETE("/:id", appSystemHandler.Delete)
			appSystem.GET("", appSystemHandler.GetAppSystemList)
			appSystem.GET("/parent-dropdown", appSystemHandler.GetParentAppSystem)
		}
	}
}
