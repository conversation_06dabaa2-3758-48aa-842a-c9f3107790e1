package system

import (
	"errors"
	api2 "marketing/internal/api"
	api "marketing/internal/api/system"
	"marketing/internal/model"
	appError "marketing/internal/pkg/errors"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type AdminPermGroupInterface interface {
	Add(c *gin.Context, req api.AddPermissionGroupReq) error
	Lists(c *gin.Context, param api.AdminPermissionReq) (*api2.PagedResponse[api.AddPermissionGroupResp], error)
	Update(c *gin.Context, req api.AddPermissionGroupReq) error
	Delete(c *gin.Context, id int) error
}

type adminPermGroupSvc struct {
	db *gorm.DB
}

// NewAdminPermGroupSvc 创建 NewAdminPermissionGroupSvc 实例
func NewAdminPermGroupSvc(db *gorm.DB) AdminPermGroupInterface {
	return &adminPermGroupSvc{
		db: db,
	}
}

func (svc *adminPermGroupSvc) Add(c *gin.Context, req api.AddPermissionGroupReq) error {
	//判断是否存在
	var existGroup model.AdminPermissionGroup

	err := svc.db.WithContext(c).Where("name = ?", req.Name).First(&existGroup).Error

	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}
	if existGroup.ID > 0 {
		return appError.NewErr("权限组：【" + req.Name + "】已经存在")
	}
	//创建用户
	var data = &model.AdminPermissionGroup{
		Name:        req.Name,
		Description: req.Description,
		SystemType:  req.SystemType,
	}
	return svc.db.WithContext(c).Create(data).Error
}

func (svc *adminPermGroupSvc) Lists(c *gin.Context, param api.AdminPermissionReq) (*api2.PagedResponse[api.AddPermissionGroupResp], error) {

	var r api2.PagedResponse[api.AddPermissionGroupResp]
	var list []api.AddPermissionGroupResp

	query := svc.db.WithContext(c).Model(&model.AdminPermissionGroup{})

	//条件处理
	if param.Name != "" {
		query = query.Where("name like ?", "%"+param.Name+"%")
	}
	if param.SystemType != "" {
		query = query.Where("system_type =?", param.SystemType)
	}

	// 总数
	err := query.Count(&r.Total).Error
	if err != nil {
		return &r, err
	}

	// 分页查询
	limit, offset := param.PageSize, (param.Page-1)*param.PageSize
	err = query.Limit(limit).Offset(offset).Scan(&list).Error
	if err != nil {
		return &r, err
	}

	r.Data, r.PageSize, r.Page = list, limit, param.Page

	return &r, nil
}

func (svc *adminPermGroupSvc) Update(c *gin.Context, req api.AddPermissionGroupReq) error {
	//判断是否存在
	var existGroup model.AdminPermissionGroup

	err := svc.db.WithContext(c).Where("id = ?", req.ID).First(&existGroup).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return appError.NewErr("权限组不存在")
	} else if err != nil {
		return err
	}

	//修改菜单
	existGroup.Name = req.Name
	existGroup.Description = req.Description
	existGroup.SystemType = req.SystemType

	return svc.db.WithContext(c).Save(existGroup).Error
}

func (svc *adminPermGroupSvc) Delete(c *gin.Context, id int) error {
	//判断是否已经存在
	var existGroup model.AdminPermissionGroup

	err := svc.db.WithContext(c).Where("id = ?", id).First(&existGroup).Error

	if errors.Is(err, gorm.ErrRecordNotFound) {
		return appError.NewErr("权限组不存在")
	}

	if err != nil {
		return err
	}
	return svc.db.WithContext(c).Delete(&existGroup).Error
}
