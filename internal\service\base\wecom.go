package base

import (
	appError "marketing/internal/pkg/errors"
	"marketing/internal/pkg/wecom"
	"marketing/internal/service/system"

	"github.com/gin-gonic/gin"
)

type WeComService interface {
	GetJsapiTicket(c *gin.Context) (any, error)
}

type weComService struct {
	appSystemSvc system.AppSystemSvc
}

func NewWeComService(appSystemSvc system.AppSystemSvc) WeComService {

	return &weComService{
		appSystemSvc: appSystemSvc,
	}
}

func (s *weComService) GetJsapiTicket(c *gin.Context) (any, error) {
	//获取系统类型
	systemType := c.GetString("system_type")
	if systemType == "" {
		systemType = c.GetHeader("X-Gate-Type")
	}

	//获取应用系统
	appSystem, err := s.appSystemSvc.GetAppSystemByKey(c, systemType)
	if err != nil {
		return nil, appError.NewErr("系统不存在")
	}
	//判断是否是子应用
	if appSystem.Parent != "" {
		appSystem, err = s.appSystemSvc.GetAppSystemByKey(c, appSystem.Parent)
		if err != nil {
			return nil, appError.NewErr("系统父应用不存在")
		}
	}
	// 获取企微客户端
	var weComClient *wecom.Client
	weComClient = wecom.NewWeComClient(appSystem.CorpID, appSystem.CorpSecret)
	ticket, err := weComClient.GetJsapiTicket()
	if err != nil {
		return nil, err
	}
	data := gin.H{
		"corpId":  appSystem.CorpID,
		"ticket":  ticket,
		"agentId": appSystem.AgentID,
	}

	return data, nil
}
