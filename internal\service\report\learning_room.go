package report

import (
	api "marketing/internal/api/report"
	"marketing/internal/dao"
	"marketing/internal/dao/warranty"
	appErr "marketing/internal/pkg/errors"

	"github.com/gin-gonic/gin"
)

type LearningRoomService interface {
	GetLearningRoomReport(c *gin.Context, req *api.MarginReportReq) (api.LearningRoomReport, error)
}

type learningRoom struct {
	warrantyDao    warranty.InterfaceWarranty
	machineTypeDao dao.MachineTypeDao
}

func NewLearningRoomService(warrantyDao warranty.InterfaceWarranty) LearningRoomService {
	machineTypeDao := dao.NewMachineTypeDao()
	return &learningRoom{
		warrantyDao:    warrantyDao,
		machineTypeDao: machineTypeDao,
	}
}

func (l *learningRoom) GetLearningRoomReport(c *gin.Context, req *api.MarginReportReq) (api.LearningRoomReport, error) {
	var result api.LearningRoomReport
	param := &warranty.StatParams{}
	// 查出分类所有的型号
	modelIDs, err := l.machineTypeDao.GetModelIDsByCategoryID(c, req.CategoryID)
	if err != nil {
		return result, err
	}
	if len(modelIDs) == 0 {
		return result, appErr.NewErr("相关分类型号不存在")
	}
	param.ModelID = modelIDs

	//统计数据过滤
	realSale := 1
	param.Realsale = &realSale
	status := 1
	param.Status = &status
	param.StartTime = req.StartTime
	param.EndTime = req.EndTime

	// 统计数据
	dataTotal, err := l.warrantyDao.StatCountAndAmount(c, param)
	if err != nil {
		return result, err
	}
	result.Total = dataTotal.Count
	result.Amount = dataTotal.Amount
	// 退机数据
	dataReturn, err := l.warrantyDao.StatReturnCountAndAmount(c, param)
	if err != nil {
		return result, err
	}
	result.ReturnTotal = dataReturn.Count
	result.ReturnAmount = dataReturn.Amount
	// 型号数据
	dataModel, err := l.warrantyDao.StatModelCountAndAmount(c, param)
	if err != nil {
		return result, err
	}
	for _, v := range dataModel {
		result.ModelReport = append(result.ModelReport, api.ModelReport{
			ModelID: v.ModelID,
			Model:   v.Model,
			Amount:  v.Amount,
			Total:   v.Count,
		})
	}

	return result, nil
}
