package model

import (
	"time"
)

// PrototypeUpCounter 样机上柜审核信息
type PrototypeUpCounter struct {
	ID          int       `gorm:"column:id;primaryKey;autoIncrement"`
	Model       string    `gorm:"column:model;type:varchar(20);not null;comment:机型名称"`
	EndpointID  int       `gorm:"column:endpoint_id;not null;default:0;comment:终端ID"`
	UID         int       `gorm:"column:uid;not null;default:0;comment:提交人UID"`
	Photo       string    `gorm:"column:photo;type:text;comment:照片地址URL"`
	Status      int       `gorm:"column:status;type:tinyint(4);not null;default:0;comment:状态:0.无; 1.待审核; 2.审核通过; -2.审核不通过"`
	Longitude   string    `gorm:"column:longitude;type:varchar(50);comment:提交照片经度"`
	Latitude    string    `gorm:"column:latitude;type:varchar(50);comment:提交照片纬度"`
	Address     string    `gorm:"column:address;type:varchar(255);comment:提交照片地址"`
	CreatedAt   time.Time `gorm:"column:created_at;comment:创建时间"`
	UpdatedAt   time.Time `gorm:"column:updated_at;comment:更新时间"`
	ApproveTime time.Time `gorm:"column:approve_time;comment:审核时间"`
	Barcode     string    `gorm:"column:barcode;type:varchar(32);comment:条码"`
	Reason      string    `gorm:"column:reason;type:varchar(100);comment:审核不通过原因"`
}

// TableName 表名
func (PrototypeUpCounter) TableName() string {
	return "prototype_up_counter"
}
