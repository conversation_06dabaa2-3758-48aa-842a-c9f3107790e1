package agency

import (
	"errors"
	"fmt"
	"marketing/internal/handler"
	"marketing/internal/pkg/db"
	"marketing/internal/pkg/utils"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type kingdee struct {
	ID           int64     `json:"cust_id" gorm:"column:id"`
	Channel      string    `json:"channel" gorm:"channel"`
	CustomerCode string    `json:"code" gorm:"column:code"`
	Name         string    `json:"short_name" gorm:"column:name"`
	ShortName    string    `json:"name" gorm:"column:short_name"`
	Enabled      int8      `json:"enabled" gorm:"enabled"`
	Group        string    `json:"group_name" gorm:"group"`
	UpdatedAt    time.Time `json:"updated_at" gorm:"column:updated_at"`
}

type agencyKingdee struct {
	KingdeeID int64 `json:"kingdee_id" gorm:"column:kingdee_id" `
	AgencyID  int64 `json:"agency_id" gorm:"column:agency_id"`
}

var Kingdee kingdee

// 获取金蝶用户信息
func (k *kingdee) GetKingdees(c *gin.Context) {
	// 从数据库连接池中获取名为 "rbcare" 的数据库连接
	db := db.GetDB("rbcare")
	// 获取查询参数
	name := c.Query("name")
	group := c.Query("group")
	agency_id := c.Query("agency_id")
	channel_id := c.Query("channel_id")
	enabled_id := c.Query("enabled")

	// 构建查询条件
	query := db.Table("kingdee_agency")

	if name != "" {
		query = query.Where("kingdee_agency.name LIKE?", "%"+name+"%")
	}
	if group != "" {
		query = query.Where("kingdee_agency.group LIKE?", "%"+group+"%")
	}
	if agency_id != "" {
		query = query.Where("kingdee_agency.agency_id =?", agency_id)
	}
	if channel_id != "" {
		query = query.Where("kingdee_agency.channel LIKE?", "%"+channel_id+"%")
	}
	if enabled_id != "" {
		query = query.Where("kingdee_agency.enabled =?", enabled_id)
	}

	var kingdees []kingdee

	// 调用 utils.PaginateQuery 函数进行分页查询
	data, total, page, pageSize, err := utils.PaginateQuery[kingdee](query, c, &kingdees)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, gin.H{
		"data":     data,
		"total":    total,
		"page":     page,
		"pageSize": pageSize,
	})
}

// 更新金蝶用户信息
func (k *kingdee) UpdateKingdee(c *gin.Context) {
	// 从数据库连接池中获取名为 "rbcare" 的数据库连接
	db := db.GetDB("rbcare")

	// 获取请求中的新增数据
	var inputData struct {
		ID        int64  `json:"id" gorm:"column:id"`
		Channel   string `json:"channel" gorm:"column:channel"`
		Name      string `json:"short_name" gorm:"column:short_name"`
		ShortName string `json:"name" gorm:"column:name"`
		Enabled   int8   `json:"enabled" gorm:"column:enabled"`
		Group     string `json:"group" gorm:"column:group"`
		Code      string `json:"code" gorm:"column:code"`
	}

	if err := c.ShouldBindJSON(&inputData); err != nil {
		handler.Error(c, fmt.Errorf("无效的请求数据: %w", err))
		return
	}

	// 开启一个事务以确保数据一致性
	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 根据ID在数据库中查询对应的记录
	var existingRecord kingdee
	if err := tx.Table("kingdee_agency").First(&existingRecord, inputData.ID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// c.JSON(http.StatusNotFound, gin.H{"error": "记录未找到"})
			handler.Error(c, fmt.Errorf("记录未找到失败: %w", err))
			tx.Rollback()
			return
		}
		// c.JSON(http.StatusInternalServerError, gin.H{"error": "数据库查询失败"})
		handler.Error(c, fmt.Errorf("数据库查询失败: %w", err))
		tx.Rollback()
		return
	}

	// 更新现有记录的数据
	existingRecord.Channel = inputData.Channel
	existingRecord.Name = inputData.Name
	existingRecord.ShortName = inputData.ShortName
	existingRecord.Enabled = inputData.Enabled
	existingRecord.Group = inputData.Group
	existingRecord.CustomerCode = inputData.Code
	existingRecord.UpdatedAt = time.Now()

	// 执行更新操作，确保使用正确的表名
	if err := tx.Table("kingdee_agency").Save(&existingRecord).Error; err != nil {
		handler.Error(c, fmt.Errorf("数据更新失败: %w", err))
		tx.Rollback()
		return
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		handler.Error(c, fmt.Errorf("提交事务失败: %w", err))
		return
	}

	// 返回成功响应
	handler.Success(c, gin.H{"message": "更新成功", "data": existingRecord})
}

func (k *kingdee) GetKingIDsByAgencyID(agencyID int64) ([]kingdee, []string, []string, error) {
	// 从数据库连接池中获取名为 "rbcare" 的数据库连接
	database := db.GetDB("rbcare")
	// 查询数据库，获取所有分区记录，并将结果存储在 kingdee 切片中
	// 如果查询过程中发生错误，将错误信息返回给客户端，并返回 500 状态码
	var agencyKingdees []agencyKingdee
	result := database.Table("agency_kingdee").Where("agency_id = ?", agencyID).Find(&agencyKingdees)

	if result.Error != nil {
		return nil, nil, nil, result.Error
	}

	var detailedKings []kingdee
	var names []string
	var shortNames []string

	for _, agencyKing := range agencyKingdees {
		detailedKing, err := (&agencyKing).GetKingNameByKingID(agencyKing.KingdeeID)
		if err != nil {
			// 处理错误，可以选择记录日志或忽略
			fmt.Printf("Failed to get detailed kingdee for ID %d: %v\n", agencyKing.KingdeeID, err)
			continue
		}
		detailedKings = append(detailedKings, *detailedKing)
		names = append(names, detailedKing.Name)
		shortNames = append(shortNames, detailedKing.ShortName)
	}

	return detailedKings, names, shortNames, nil
}

// 这里得用agency和kingdee之间的中间表来查询，因为kingdee表中没有agency_id字段
func (k *agencyKingdee) GetKingNameByKingID(id int64) (*kingdee, error) {
	database := db.GetDB("rbcare")
	var king kingdee
	result := database.Table("kingdee_agency").Where("id = ?", id).Find(&king)
	if result.Error != nil {
		// 如果查询不到记录，返回一个带有默认名称 "未知" 的 department 对象
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			fmt.Printf("Kingdee not found for ID: %d, setting default name '未知'\n", id)
			return &kingdee{Name: "未知"}, nil
		}
		// 其他错误
		fmt.Printf("Database query error: %v\n", result.Error)
		return nil, result.Error
	}
	return &king, nil
}
