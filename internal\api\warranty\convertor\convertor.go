package convertor

import (
	api "marketing/internal/api/warranty"
	"marketing/internal/model"
)

type ActivatedDeviceInfoConvertor struct{}
type PackInfoConvertor struct{}
type K3InStockConvertor struct{}
type K3OutStockConvertor struct{}
type K3ReturnStockConvertor struct{}
type K3AllotStockConvertor struct{}

func (a *ActivatedDeviceInfoConvertor) ModelToResponse(in *model.AcDevicesUniq) *api.ActivatedDeviceInfo {
	if ok := a.check(in); !ok {
		return nil
	}
	return &api.ActivatedDeviceInfo{
		Model:     in.Model,
		Imei:      in.Imei,
		Number:    in.Number,
		Barcode:   in.Barcode,
		NavModel:  in.NavModel,
		Origin:    in.Origin,
		Status:    in.Status,
		BindAt:    in.Bindat,
		Location:  in.Location,
		IP:        in.Ip,
		CreatedAt: in.Createat,
		Address:   in.Address,
	}
}

func (a *ActivatedDeviceInfoConvertor) ModelsToResponse(ins []*model.AcDevicesUniq) []*api.ActivatedDeviceInfo {
	var rets []*api.ActivatedDeviceInfo
	for _, in := range ins {
		rets = append(rets, a.ModelToResponse(in))
	}
	return rets
}

func (a *ActivatedDeviceInfoConvertor) check(in interface{}) bool {
	return in != nil
}

func (p *PackInfoConvertor) ModelToResponse(in *model.MesPackDevices) *api.MesPackInfo {
	if ok := p.check(in); !ok {
		return nil
	}
	packTime := in.PackTime.String()
	addTime := in.AddTime.String()
	return &api.MesPackInfo{
		Model:    in.Model,
		Imei:     in.Imei,
		Number:   in.Number,
		Barcode:  in.Barcode,
		PackTime: packTime,
		AddTime:  addTime,
	}
}

func (p *PackInfoConvertor) ModelsToResponse(ins []*model.MesPackDevices) []*api.MesPackInfo {
	var rets []*api.MesPackInfo
	for _, in := range ins {
		rets = append(rets, p.ModelToResponse(in))
	}
	return rets
}

func (p *PackInfoConvertor) check(in interface{}) bool {
	return in != nil
}

func (k *K3InStockConvertor) ModelToResponse(in *model.V3Instock) *api.K3InStock {
	if ok := k.check(in); !ok {
		return nil
	}
	billDate := in.BillDate.String()
	return &api.K3InStock{
		Model:    in.Model,
		Barcode:  in.Barcode,
		Number:   in.Number,
		Imei:     in.Imei,
		BillDate: billDate,
	}
}

func (k *K3InStockConvertor) ModelsToResponse(ins []*model.V3Instock) []*api.K3InStock {
	if ok := k.check(ins); !ok {
		return nil
	}
	var rets []*api.K3InStock
	for _, in := range ins {
		rets = append(rets, k.ModelToResponse(in))
	}
	return rets
}

func (k *K3InStockConvertor) check(in interface{}) bool {
	return in != nil
}

func (k *K3OutStockConvertor) ModelToResponse(in *model.V3Outstock) *api.K3OutStock {
	if ok := k.check(in); !ok {
		return nil
	}
	billDate := in.BillDate.String()
	return &api.K3OutStock{
		Model:    in.Model,
		Barcode:  in.Barcode,
		Number:   in.Number,
		Imei:     in.Imei,
		BillDate: billDate,
		CustName: in.CustName,
		CustCode: in.CustCode,
	}
}

func (k *K3OutStockConvertor) ModelsToResponse(ins []*model.V3Outstock) []*api.K3OutStock {
	if ok := k.check(ins); !ok {
		return nil
	}
	var rets []*api.K3OutStock
	for _, in := range ins {
		rets = append(rets, k.ModelToResponse(in))
	}
	return rets
}

func (k *K3OutStockConvertor) check(in interface{}) bool {
	return in != nil
}

func (k *K3ReturnStockConvertor) ModelToResponse(in *model.V3Returnstock) *api.K3Returnstock {
	if ok := k.check(in); !ok {
		return nil
	}
	billDate := in.BillDate.String()
	return &api.K3Returnstock{
		Model:    in.Model,
		Barcode:  in.Barcode,
		Number:   in.Number,
		CustName: in.CustName,
		CustCode: in.CustCode,
		Imei:     in.Imei,
		BillDate: billDate,
	}
}

func (k *K3ReturnStockConvertor) ModelsToResponse(ins []*model.V3Returnstock) []*api.K3Returnstock {
	if ok := k.check(ins); !ok {
		return nil
	}
	var rets []*api.K3Returnstock
	for _, in := range ins {
		rets = append(rets, k.ModelToResponse(in))
	}
	return rets
}

func (k *K3ReturnStockConvertor) check(in interface{}) bool {
	return in != nil
}

func (k *K3AllotStockConvertor) ModelToResponse(in *model.V3Allotstock) *api.K3Allotstock {
	if ok := k.check(in); !ok {
		return nil
	}
	billDate := in.BillDate.String()
	return &api.K3Allotstock{
		Model:       in.Model,
		Barcode:     in.Barcode,
		Number:      in.Number,
		CustNameOld: in.CustNameOld,
		CustCodeOld: in.CustCodeOld,
		CustName:    in.CustName,
		CustCode:    in.CustCode,
		Imei:        in.Imei,
		BillDate:    billDate,
	}
}

func (k *K3AllotStockConvertor) ModelsToResponse(ins []*model.V3Allotstock) []*api.K3Allotstock {
	if ok := k.check(ins); !ok {
		return nil
	}
	var rets []*api.K3Allotstock
	for _, in := range ins {
		rets = append(rets, k.ModelToResponse(in))
	}
	return rets
}

func (k *K3AllotStockConvertor) check(in interface{}) bool {
	return in != nil
}
