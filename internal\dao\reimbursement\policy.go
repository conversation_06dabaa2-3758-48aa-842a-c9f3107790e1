package reimbursement

import (
	"errors"
	"github.com/gin-gonic/gin"
	api "marketing/internal/api/reimbursement"
	"marketing/internal/model"
	"marketing/internal/pkg/log"
	"marketing/internal/pkg/types"
	"time"

	"gorm.io/gorm"
)

type PolicyRepository interface {
	GetPolicyList(req *api.PolicyListSearch) ([]model.ReimbursementPolicy, int64, error)
	GetAllPolicy() ([]*model.ReimbursementPolicy, error)
	CreatePolicyAndGetID(policy model.ReimbursementPolicy) (int, error)
	CreatePromotionalProducts(policyID int, products []model.PromotionalProduct) error
	GetPolicyByID(policyID int) (*model.ReimbursementPolicy, error)
	GetPromotionalProductsByPolicyID(policyID int) ([]*api.PromotionalProductDetail, error)
	GetPolicySummary(c *gin.Context, req *api.PolicySummaryReq) ([]api.PolicySummaryItem, int, error)
	UpdatePolicy(policy model.ReimbursementPolicy) error
	DeletePromotionalProductsByPolicyID(policyID int) error
	ArchivePolicy(policyID int, archive int, archiveTime types.CustomTime) error
	GetApproveUserList(slug string) ([]map[string]interface{}, error)
	GetPromotionalProductsHeader(c *gin.Context, policyID int) (map[int]string, error)
	GetPromotionalProductsListCount(c *gin.Context, req *api.PromotionalProductsListReq) (int64, error)
	GetPromotionalProductsList(c *gin.Context, req *api.PromotionalProductsListReq) ([]api.PromotionalProductsListItem, error)
	GetPromotionalProductsTotal(c *gin.Context, req *api.PromotionalProductsListReq) (*api.PromotionalProductsTotal, error)
}

type policyRepo struct {
	db *gorm.DB // Assumes a SQL database connection
}

func NewPolicyRepository(db *gorm.DB) PolicyRepository {
	return &policyRepo{db: db}
}

type PromotionalProductsSummary struct {
	Count       int64
	UnderAudit  int64
	AuditFinish int64
	AuditFail   int64
}

func (r *policyRepo) GetPolicyList(req *api.PolicyListSearch) ([]model.ReimbursementPolicy, int64, error) {
	// Implementation for fetching policy list from the database
	var policies []model.ReimbursementPolicy
	var total int64
	query := r.db.Model(&model.ReimbursementPolicy{}).Where("archive = ?", req.Archive)

	if req.Name != "" {
		query = query.Where("name LIKE ?", "%"+req.Name+"%")
	}
	if req.Type != 0 {
		query = query.Where("type = ?", req.Type)
	}

	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}
	offset := (req.Page - 1) * req.PageSize
	err = query.Limit(req.PageSize).Offset(offset).Find(&policies).Error
	if err != nil {
		return nil, 0, err
	}
	return policies, total, nil
}

func (r *policyRepo) GetAllPolicy() ([]*model.ReimbursementPolicy, error) {
	var policies []*model.ReimbursementPolicy
	query := r.db.Model(&model.ReimbursementPolicy{}).Order("id DESC")

	err := query.Find(&policies).Error
	if err != nil {
		return nil, err
	}
	return policies, nil
}

func (r *policyRepo) CreatePolicyAndGetID(policy model.ReimbursementPolicy) (int, error) {
	// Implementation for creating a new policy in the database and returning its ID
	tx := r.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	if err := tx.Error; err != nil {
		return 0, err
	}

	if err := tx.Create(&policy).Error; err != nil {
		tx.Rollback()
		return 0, err
	}

	if err := tx.Commit().Error; err != nil {
		return 0, err
	}

	return policy.ID, nil
}

func (r *policyRepo) CreatePromotionalProducts(policyID int, products []model.PromotionalProduct) error {
	// Implementation for creating promotional products for a policy
	tx := r.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	if err := tx.Error; err != nil {
		return err
	}

	now := time.Now()
	for _, product := range products {
		// Set policy ID and created_at time
		product.PolicyID = policyID
		product.CreatedAt = types.CustomTime(now)

		if err := tx.Table("reimbursement_promotional_products").Create(&product).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	if err := tx.Commit().Error; err != nil {
		return err
	}

	return nil
}

func (r *policyRepo) GetPolicyByID(policyID int) (*model.ReimbursementPolicy, error) {
	var policy model.ReimbursementPolicy
	err := r.db.Where("id = ?", policyID).First(&policy).Error
	if err != nil {
		if errors.Is(gorm.ErrRecordNotFound, err) {
			return nil, errors.New("政策不存在")
		}
		return nil, err
	}
	return &policy, nil
}

func (r *policyRepo) GetPromotionalProductsByPolicyID(policyID int) ([]*api.PromotionalProductDetail, error) {
	var products []*model.PromotionalProduct
	err := r.db.Where("policy_id = ?", policyID).Find(&products).Error
	if err != nil {
		return nil, err
	}
	var results []*api.PromotionalProductDetail
	for _, product := range products {
		result := api.PromotionalProductDetail{
			ID:                 product.ID,
			Name:               product.Name,
			Norm:               product.Norm,
			Unit:               product.Unit,
			IncludeTaxPrice:    product.IncludeTaxPrice,
			ExcludeTaxPrice:    product.ExcludeTaxPrice,
			ReimbursementPrice: product.ReimbursementPrice,
			CreatedAt:          product.CreatedAt,
		}

		// 解析JSON字符串为FileMetadata数组
		result.IncludeTaxAccountInfo = loadFiles(product.IncludeTaxAccountInfo)
		result.ExcludeTaxAccountInfo = loadFiles(product.ExcludeTaxAccountInfo)
		result.CommunicationLetter = loadFiles(product.CommunicationLetter)
		result.Preview = loadFiles(product.Preview)

		results = append(results, &result)
	}
	return results, nil
}

func (r *policyRepo) UpdatePolicy(policy model.ReimbursementPolicy) error {
	// Update policy in the database
	return r.db.Model(&model.ReimbursementPolicy{}).Where("id = ?", policy.ID).Updates(policy).Error
}

func (r *policyRepo) DeletePromotionalProductsByPolicyID(policyID int) error {
	// Delete all promotional products for a policy
	return r.db.Where("policy_id = ?", policyID).Delete(&model.PromotionalProduct{}).Error
}

func (r *policyRepo) ArchivePolicy(policyID int, archive int, archiveTime types.CustomTime) error {
	// Update archive status and archive_time
	updates := map[string]interface{}{
		"archive":      archive,
		"archive_time": archiveTime,
	}
	return r.db.Model(&model.ReimbursementPolicy{}).Where("id = ?", policyID).Updates(updates).Error
}

func (r *policyRepo) GetPolicySummary(c *gin.Context, req *api.PolicySummaryReq) ([]api.PolicySummaryItem, int, error) {
	// Query for policies that are not type 4 (申报预估政策)
	var policies []model.ReimbursementPolicy
	var total int64

	// Count total policies
	err := r.db.WithContext(c).Model(&model.ReimbursementPolicy{}).Where("archive = ?", req.Archive).
		Where("type != ?", 4).Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// Get paginated policies
	offset := (req.Page - 1) * req.PageSize
	err = r.db.WithContext(c).Where("archive =?", req.Archive).Where("type != ?", 4).
		Order("id DESC").
		Limit(req.PageSize).
		Offset(offset).
		Find(&policies).Error
	if err != nil {
		return nil, 0, err
	}

	// Create summary items
	var summaries []api.PolicySummaryItem
	for _, policy := range policies {

		// Format dates
		startTime := time.Time(policy.StartTime).Format("2006-01-02")
		endTime := time.Time(policy.EndTime).Format("2006-01-02")
		archiveTime := ""
		if !time.Time(policy.ArchiveTime).IsZero() {
			archiveTime = time.Time(policy.ArchiveTime).Format("2006-01-02")
		}

		// Create summary item
		summary := api.PolicySummaryItem{
			ID:          policy.ID,
			Name:        policy.Name,
			StartTime:   startTime,
			EndTime:     endTime,
			PolicyType:  policy.PolicyType,
			Archive:     policy.Archive,
			ArchiveTime: archiveTime,
			Count:       0,
			UnderAudit:  0,
			AuditFinish: 0,
			AuditFail:   0,
		}

		var summaryCount PromotionalProductsSummary
		// Get application counts based on policy type
		if policy.PolicyType == "promotional_products" {
			// Total count
			if err = r.db.WithContext(c).Model(&model.ReimbursementPromotionalProductsList{}).
				Select("count(1) as count",
					"SUM(status = 0 OR status = 1 OR status = 2) as under_audit",
					"SUM(status = 3) as audit_finish",
					"SUM(status = -1 OR status = -3) as audit_fail").
				Where("policy_id = ?", policy.ID).
				Scan(&summaryCount).Error; err != nil {
				log.Error("核销申请统计查询失败" + err.Error())
			}

		} else {
			if err = r.db.WithContext(c).
				Model(&model.ReimbursementAdvertExpenseList{}).
				Select("count(1) as count",
					"SUM(status = 0 AND rollback = 0) as under_audit",
					"SUM(status = 1 OR status = 2) as audit_finish",
					"SUM(status = -1) as audit_fail").
				Where("policy_id = ?", policy.ID).
				Scan(&summaryCount).Error; err != nil {
				log.Error("核销申请统计查询失败" + err.Error())
			}
		}
		summary.Count = int(summaryCount.Count)
		summary.UnderAudit = int(summaryCount.UnderAudit)
		summary.AuditFinish = int(summaryCount.AuditFinish)
		summary.AuditFail = int(summaryCount.AuditFail)

		summaries = append(summaries, summary)
	}

	return summaries, int(total), nil
}

// GetApproveUserList 获取有审核权限的用户列表
func (r *policyRepo) GetApproveUserList(slug string) ([]map[string]interface{}, error) {
	// 1. 获取权限ID
	var permissionInfo map[string]interface{}
	err := r.db.Table("admin_permissions").
		Where("slug = ?", slug).
		Select("id").
		Take(&permissionInfo).Error
	if err != nil {
		return nil, err
	}

	// 2. 获取拥有该权限的用户ID列表
	var userPermissions []map[string]interface{}
	err = r.db.Table("admin_role_permissions rp").
		Select("DISTINCT ru.user_id").
		Joins("JOIN admin_role_users ru ON rp.role_id = ru.role_id").
		Where("rp.permission_id = ?", permissionInfo["id"]).
		Find(&userPermissions).Error
	if err != nil {
		return nil, err
	}

	// 如果没有用户有该权限，返回空列表
	if len(userPermissions) == 0 {
		return []map[string]interface{}{}, nil
	}

	// 3. 提取用户ID列表
	var userIDs []interface{}
	for _, userPerm := range userPermissions {
		userIDs = append(userIDs, userPerm["user_id"])
	}

	// 4. 获取用户信息
	var users []map[string]interface{}
	err = r.db.Table("admin_users").
		Select("id, name").
		Where("id IN ? AND status = 1", userIDs).
		Find(&users).Error
	if err != nil {
		return nil, err
	}

	return users, nil
}

// GetPromotionalProductsHeader 获取推广产品表头
func (r *policyRepo) GetPromotionalProductsHeader(c *gin.Context, policyID int) (map[int]string, error) {
	var products []model.PromotionalProduct
	err := r.db.WithContext(c).Select("id, name").Where("policy_id = ?", policyID).Find(&products).Error
	if err != nil {
		return nil, err
	}

	result := make(map[int]string)
	for _, product := range products {
		result[product.ID] = product.Name
	}

	return result, nil
}

// GetPromotionalProductsListCount 获取推广产品列表统计
func (r *policyRepo) GetPromotionalProductsListCount(c *gin.Context, req *api.PromotionalProductsListReq) (int64, error) {
	query := r.db.WithContext(c).Table("reimbursement_promotional_products_list rpp")

	// 根据报销状态决定是否需要JOIN
	if req.ReimbursementStatus != nil {
		query = query.Joins("LEFT JOIN reimbursement_apply_order_summary raos ON rpp.id = raos.apply_order_id").
			Where("raos.status = ? AND raos.apply_order_type = ? AND rpp.policy_id = ?",
				*req.ReimbursementStatus, "promotional_products", req.PolicyID)
	} else {
		query = query.Where("rpp.policy_id = ?", req.PolicyID)
	}

	// 添加其他过滤条件
	query = r.addPromotionalProductsFilters(query, req)

	var count int64
	err := query.Count(&count).Error
	return count, err
}

// addPromotionalProductsFilters 添加推广产品过滤条件
func (r *policyRepo) addPromotionalProductsFilters(query *gorm.DB, req *api.PromotionalProductsListReq) *gorm.DB {
	if req.ID != nil {
		query = query.Where("rpp.id = ?", *req.ID)
	}
	if req.TopAgency != nil {
		query = query.Where("rpp.top_agency = ?", *req.TopAgency)
	}
	if req.StartTime != "" && req.EndTime != "" {
		query = query.Where("rpp.created_at BETWEEN ? AND ?", req.StartTime, req.EndTime)
	}
	if req.Status != nil {
		query = query.Where("rpp.status = ?", *req.Status)
		if *req.Status == 0 {
			query = query.Where("rpp.voucher_audit_rollback = 0")
		}
	}
	if req.CompanyID > 0 {
		query = query.Where("rpp.company_id = ?", req.CompanyID)
	}
	if req.MaterialReturnStatus != nil {
		query = query.Where("rpp.material_return_status = ?", *req.MaterialReturnStatus)
	}
	if req.ExpressComeSn != "" {
		query = query.Where("rpp.express_come_sn = ?", req.ExpressComeSn)
	}
	if req.CompletionStatus != nil {
		query = query.Where("rpp.completion_status = ?", *req.CompletionStatus)
	}
	return query
}

// GetPromotionalProductsList 获取推广产品列表
func (r *policyRepo) GetPromotionalProductsList(c *gin.Context, req *api.PromotionalProductsListReq) ([]api.PromotionalProductsListItem, error) {
	offset := (req.Page - 1) * req.PageSize
	if offset < 0 {
		offset = 0
	}

	query := r.db.WithContext(c).Table("reimbursement_promotional_products_list rpp").
		Select(`DISTINCT rpp.id, rp.name, rpp.top_agency, rpp.amount, rpp.actual_amount,
				rpp.reimbursement_apply_amount, rpp.created_at, rpp.status, a.name AS agency_name,
				rpp.company_id, rpp.company, rpp.code, rpp.express_come_sn, rpp.express_come_com,
				rpp.express_come_time, rpp.material_return_status, rpp.completion_status,
				rpp.voucher_audit_rollback, rpp.data_audit_rollback,
				(SELECT GROUP_CONCAT(raos.status) FROM reimbursement_apply_order_summary raos
				 WHERE raos.apply_order_id = rpp.id AND raos.apply_order_type = ?) AS reimbursement_status`,
			"promotional_products").
		Joins("LEFT JOIN reimbursement_policy rp ON rpp.policy_id = rp.id").
		Joins("LEFT JOIN agency a ON rpp.top_agency = a.id")

	// 根据报销状态决定是否需要额外的JOIN
	if req.ReimbursementStatus != nil {
		query = query.Joins("LEFT JOIN reimbursement_apply_order_summary raos ON rpp.id = raos.apply_order_id").
			Where("raos.status = ? AND raos.apply_order_type = ? AND rpp.policy_id = ?",
				*req.ReimbursementStatus, "promotional_products", req.PolicyID)
	} else {
		query = query.Where("rpp.policy_id = ?", req.PolicyID)
	}

	// 添加过滤条件
	query = r.addPromotionalProductsFilters(query, req)

	// 排序和分页
	query = query.Order("rpp.created_at DESC").Limit(req.PageSize).Offset(offset)

	var items []api.PromotionalProductsListItem
	err := query.Scan(&items).Error
	if err != nil {
		return nil, err
	}

	// 为每个项目获取产品信息
	for i := range items {
		products, err := r.getPromotionalProductsForOrder(items[i].ID)
		if err != nil {
			return nil, err
		}
		items[i].Products = products
	}

	return items, nil
}

// getPromotionalProductsForOrder 获取订单的产品信息
func (r *policyRepo) getPromotionalProductsForOrder(orderID int) (map[int]float64, error) {
	type ProductInfo struct {
		ID     int     `json:"id"`
		Number float64 `json:"number"`
	}

	var products []ProductInfo
	err := r.db.Table("reimbursement_promotional_products_relation rppr").
		Select("rpp.id, rppr.quantity * rppr.norm AS number").
		Joins("LEFT JOIN reimbursement_promotional_products rpp ON rppr.product_id = rpp.id").
		Where("rppr.reimbursement_id = ?", orderID).
		Scan(&products).Error

	if err != nil {
		return nil, err
	}

	result := make(map[int]float64)
	for _, product := range products {
		if existing, exists := result[product.ID]; exists {
			result[product.ID] = existing + product.Number
		} else {
			result[product.ID] = product.Number
		}
	}

	return result, nil
}

// GetPromotionalProductsTotal 获取推广产品总计
func (r *policyRepo) GetPromotionalProductsTotal(c *gin.Context, req *api.PromotionalProductsListReq) (*api.PromotionalProductsTotal, error) {
	// 构建产品总计查询
	productQuery := r.buildProductTotalQuery(req)

	type ProductTotal struct {
		ProductID int     `json:"product_id"`
		Total     float64 `json:"total"`
	}

	var productTotals []ProductTotal
	err := productQuery.Scan(&productTotals).Error
	if err != nil {
		return nil, err
	}

	// 构建金额总计查询
	amountQuery := r.buildAmountTotalQuery(req)

	type AmountTotal struct {
		AmountTotal float64 `json:"amount_total"`
	}

	var amountTotal AmountTotal
	err = amountQuery.Scan(&amountTotal).Error
	if err != nil {
		return nil, err
	}

	// 组装结果
	result := &api.PromotionalProductsTotal{
		ProductTotals: make(map[int]float64),
		AmountTotal:   amountTotal.AmountTotal,
	}

	var quantityTotal float64
	for _, pt := range productTotals {
		result.ProductTotals[pt.ProductID] = pt.Total
		quantityTotal += pt.Total
	}
	result.QuantityTotal = quantityTotal

	return result, nil
}

// buildProductTotalQuery 构建产品总计查询
func (r *policyRepo) buildProductTotalQuery(req *api.PromotionalProductsListReq) *gorm.DB {
	var query *gorm.DB

	if req.ReimbursementStatus != nil {
		// 有报销状态过滤的查询
		query = r.db.Table("(?) rppr", r.db.Table("reimbursement_promotional_products_relation rppr").
			Select("DISTINCT rppl.id, rppr.product_id, rppr.norm, rppr.quantity").
			Joins("LEFT JOIN reimbursement_promotional_products_list rppl ON rppr.reimbursement_id = rppl.id").
			Joins("LEFT JOIN reimbursement_apply_order_summary raos ON rppl.id = raos.apply_order_id").
			Where("raos.status = ? AND raos.apply_order_type = ? AND rppl.policy_id = ?",
				*req.ReimbursementStatus, "promotional_products", req.PolicyID)).
			Select("rppr.product_id, SUM(rppr.norm * rppr.quantity) as total").
			Group("rppr.product_id")
	} else {
		// 无报销状态过滤的查询
		query = r.db.Table("reimbursement_promotional_products_relation rppr").
			Select("rppr.product_id, SUM(rppr.norm * rppr.quantity) as total").
			Joins("LEFT JOIN reimbursement_promotional_products_list rppl ON rppr.reimbursement_id = rppl.id").
			Where("rppl.policy_id = ?", req.PolicyID).
			Group("rppr.product_id")
	}

	return r.addTotalFilters(query, req)
}

// buildAmountTotalQuery 构建金额总计查询
func (r *policyRepo) buildAmountTotalQuery(req *api.PromotionalProductsListReq) *gorm.DB {
	var query *gorm.DB

	if req.ReimbursementStatus != nil {
		// 有报销状态过滤的查询
		query = r.db.Table("(?) rppl", r.db.Table("reimbursement_promotional_products_list rppl").
			Select("DISTINCT rppl.id, rppl.amount").
			Joins("LEFT JOIN reimbursement_apply_order_summary raos ON rppl.id = raos.apply_order_id").
			Where("raos.status = ? AND raos.apply_order_type = ? AND rppl.policy_id = ?",
				*req.ReimbursementStatus, "promotional_products", req.PolicyID)).
			Select("SUM(rppl.amount) as amount_total")
	} else {
		// 无报销状态过滤的查询
		query = r.db.Table("reimbursement_promotional_products_list rppl").
			Select("SUM(rppl.amount) as amount_total").
			Where("rppl.policy_id = ?", req.PolicyID)
	}

	return r.addTotalFilters(query, req)
}

// addTotalFilters 为总计查询添加过滤条件
func (r *policyRepo) addTotalFilters(query *gorm.DB, req *api.PromotionalProductsListReq) *gorm.DB {
	if req.ID != nil {
		query = query.Where("rppl.id = ?", *req.ID)
	}
	if req.TopAgency != nil {
		query = query.Where("rppl.top_agency = ?", *req.TopAgency)
	}
	if req.StartTime != "" && req.EndTime != "" {
		query = query.Where("rppl.created_at BETWEEN ? AND ?", req.StartTime, req.EndTime)
	}
	if req.Status != nil {
		query = query.Where("rppl.status = ?", *req.Status)
		if *req.Status == 0 {
			query = query.Where("rppl.voucher_audit_rollback = 0")
		}
	}
	// 添加状态不等于-100的条件（除非明确指定）
	if req.Status == nil || *req.Status != -100 {
		query = query.Where("rppl.status != -100")
	}
	if req.CompanyID > 0 {
		query = query.Where("rppl.company_id = ?", req.CompanyID)
	}
	if req.MaterialReturnStatus != nil {
		query = query.Where("rppl.material_return_status = ?", *req.MaterialReturnStatus)
	}
	if req.ExpressComeSn != "" {
		query = query.Where("rppl.express_come_sn = ?", req.ExpressComeSn)
	}
	if req.CompletionStatus != nil {
		query = query.Where("rppl.completion_status = ?", *req.CompletionStatus)
	}
	return query
}
