package operation

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"marketing/internal/api/operation"
	"marketing/internal/model"
	"marketing/internal/pkg/db"
)

type OpPublisherDao interface {
	CreateOpPublisher(c *gin.Context, name, avatar string, userIds []int) error
	DeleteOpPublisher(c *gin.Context, id int) error
	EditOpPublisher(c *gin.Context, id int, name, avatar string, userIds []int) error
	GetOpPublisherList(c *gin.Context, pageNum, pageSize int) (list []*model.OpPublisher, total int64)
	CheckAdminUserIs(c *gin.Context, userIds []int) (hasUserIds []int)
	GetOpPublisherUser(c *gin.Context, pIds []uint) (list []operation.OpPublisherUserInfo)
	GetOpPublisherArticleInfo(c *gin.Context, pIds []uint) (list []model.OpPublisherArticleCountInfo)
}

// OpPublisherDaoImpl 实现 OpPublisherDao 接口
type OpPublisherDaoImpl struct {
	db *gorm.DB
}

// NewOpPublisherDao 创建 OpPublisherDao 实例
func NewOpPublisherDao() OpPublisherDao {
	return &OpPublisherDaoImpl{
		db: db.GetDB(""),
	}
}

func (d *OpPublisherDaoImpl) CreateOpPublisher(c *gin.Context, name, avatar string, userIds []int) error {
	tx := d.db.WithContext(c).Begin()

	publisher := model.OpPublisher{
		Name:   name,
		Avatar: avatar,
	}

	if dbErr := tx.Create(&publisher).Error; dbErr != nil {
		tx.Rollback()
		return dbErr
	}

	dbErr := d.updatePublisherUserRelation(tx, int(publisher.ID), userIds)
	if dbErr != nil {
		return dbErr
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}

	return nil
}

func (d *OpPublisherDaoImpl) DeleteOpPublisher(c *gin.Context, id int) error {
	tx := d.db.WithContext(c).Begin()

	if dbErr := tx.Delete(&model.OpPublisher{}, "id = ?", id).Error; dbErr != nil {
		tx.Rollback()
		return dbErr
	}

	if dbErr := tx.Delete(&model.OpPublisherUser{}, "publisher_id = ?", id).Error; dbErr != nil {
		tx.Rollback()
		return dbErr
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}

	return nil
}

func (d *OpPublisherDaoImpl) EditOpPublisher(c *gin.Context, id int, name, avatar string, userIds []int) error {
	tx := d.db.WithContext(c).Begin()

	uMap := make(map[string]interface{})
	uMap["name"] = name
	uMap["avatar"] = avatar

	if dbErr := tx.Model(&model.OpPublisher{}).Where("id = ?", id).Updates(uMap).Error; dbErr != nil {
		tx.Rollback()
		return dbErr
	}

	dbErr := d.updatePublisherUserRelation(tx, id, userIds)
	if dbErr != nil {
		return dbErr
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}

	return nil
}

func (d *OpPublisherDaoImpl) CheckAdminUserIs(c *gin.Context, userIds []int) (hasUserIds []int) {
	var list []struct {
		Id int `gorm:"column:id"`
	}

	d.db.WithContext(c).Table("admin_users").Where("id in (?) and status = 1", userIds).Find(&list)

	for _, l := range list {
		hasUserIds = append(hasUserIds, l.Id)
	}

	return
}

func (d *OpPublisherDaoImpl) GetOpPublisherList(c *gin.Context, pageNum, pageSize int) (list []*model.OpPublisher, total int64) {
	query := d.db.WithContext(c).Model(&model.OpPublisher{}).Where("deleted_at is null")

	if pageNum < 1 {
		pageNum = 1
	}

	if pageSize < 1 {
		pageSize = 20
	}

	query.Count(&total)
	query.Offset((pageNum - 1) * pageSize).Limit(pageSize).Order("op_publisher.id desc").Find(&list)

	//for _, l := range list {
	//	l.Avatar = addOssBaseUrl(l.Avatar)
	//}

	return
}

func (d *OpPublisherDaoImpl) GetOpPublisherUser(c *gin.Context, pIds []uint) (list []operation.OpPublisherUserInfo) {
	d.db.WithContext(c).Model(&model.OpPublisherUser{}).
		Select("publisher_id,admin_users.id,admin_users.username name").
		Joins("join admin_users on op_publisher_user.user_id = admin_users.id").Where("publisher_id in (?) and status = 1", pIds).
		Find(&list)
	return
}

func (d *OpPublisherDaoImpl) GetOpPublisherArticleInfo(c *gin.Context, pIds []uint) (list []model.OpPublisherArticleCountInfo) {
	d.db.WithContext(c).Model(&model.OpArticle{}).
		Where("publisher_id in (?) and deleted_at is null", pIds).
		Select("publisher_id,count(*) num").
		Group("publisher_id").
		Find(&list)
	return
}

func (d *OpPublisherDaoImpl) updatePublisherUserRelation(tx *gorm.DB, id int, userIds []int) error {
	if dbErr := tx.Delete(&model.OpPublisherUser{}, "publisher_id = ?", id).Error; dbErr != nil {
		tx.Rollback()
		return dbErr
	}

	for _, userId := range userIds {
		if dbErr := tx.Create(&model.OpPublisherUser{
			UserId:      userId,
			PublisherId: id,
		}).Error; dbErr != nil {
			tx.Rollback()
			return dbErr
		}
	}

	return nil
}
