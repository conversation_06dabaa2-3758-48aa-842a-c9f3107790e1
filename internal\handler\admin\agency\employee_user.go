package agency

import (
	"time"
)

type employeeUser struct {
	// ID           int64     `json:"id" gorm:"column:id"`
	UID          int64     `json:"uid" gorm:"column:uid"`
	Name         string    `json:"name" gorm:"column:name"`
	UserName     string    `json:"username" gorm:"column:username"`
	RoleIds      []int64   `json:"role_id"`
	RoleNames    []string  `json:"role_name"`
	CreateTime   time.Time `json:"created_at" gorm:"column:created_at"`
	Phone        string    `json:"phone" gorm:"column:phone"`
	Partition    int64     `json:"partition" gorm:"column:partition"`
	Status       int64     `json:"status" gorm:"column:status"`
	DepartmentId int64     `json:"department_id"`
	Department   string    `json:"department_name"`
}

type employeeUserForQuery struct {
	// ID           int64     `json:"id" gorm:"column:id"`
	UID          int64     `json:"uid" gorm:"column:uid"`
	Name         string    `json:"name" gorm:"column:name"`
	UserName     string    `json:"username" gorm:"column:username"`
	CreateTime   time.Time `json:"created_at" gorm:"column:created_at"`
	Phone        string    `json:"phone" gorm:"column:phone"`
	Partition    int64     `json:"partition" gorm:"column:partition"`
	Status       int64     `json:"status" gorm:"column:status"`
	DepartmentId int64     `json:"department_id"`
	Department   string    `json:"department_name"`
}

type EmployeeAgency struct {
	ID         int64 `gorm:"column:id"`
	EmployeeID int64 `gorm:"column:employee_id"`
	AgencyID   int64 `gorm:"column:agency_id"`
}

type EmployeeRoleUser struct {
	UserID     int64     `gorm:"column:user_id"`
	RoleID     int64     `gorm:"column:role_id"`
	UpdateTime time.Time `gorm:"column:updated_at"`
}

var EmployeeUser employeeUser
