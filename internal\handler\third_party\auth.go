package third_party

import (
	"encoding/json"
	"io/ioutil"
	"marketing/internal/api/third_party"
	"marketing/internal/handler"
	"marketing/internal/pkg/errors"
	thirdPartyService "marketing/internal/service/third_party"

	"github.com/gin-gonic/gin"
)

// ThirdPartyAuthHandler 第三方认证处理器接口
type ThirdPartyAuthHandler interface {
	// Authenticate 第三方认证接口
	Authenticate(c *gin.Context)
	// AuthenticateV2 第三方认证接口（合力亿捷）
	AuthenticateV2(c *gin.Context)
}

// thirdPartyAuthHandler 第三方认证处理器实现
type thirdPartyAuthHandler struct {
	authService thirdPartyService.ThirdPartyAuthServiceInterface
}

// NewThirdPartyAuthHandler 创建第三方认证处理器实例
func NewThirdPartyAuthHandler(authService thirdPartyService.ThirdPartyAuthServiceInterface) ThirdPartyAuthHandler {
	return &thirdPartyAuthHandler{
		authService: authService,
	}
}

// Authenticate 第三方认证接口
// 支持GET和POST两种请求方式
// GET: /third_party/auth?appld=test&flag=hollycrm&timestampKey=123456&encryptionToken=md5(123456+encryption配置的值)
// POST: /third_party/auth {"appld":"test","flag":"hollycrm","timestampKey":"123456","encryptionToken":"md5(123456+encryption配置的值)"}
// @Router /third_party/auth [get,post]
func (h *thirdPartyAuthHandler) Authenticate(c *gin.Context) {
	var req third_party.ThirdPartyAuthReq

	// 根据请求方法绑定参数
	if c.Request.Method == "GET" {
		if err := c.ShouldBindQuery(&req); err != nil {
			handler.Error(c, errors.NewErr("参数错误: "+err.Error()))
			return
		}
	} else {
		if err := c.ShouldBind(&req); err != nil {
			handler.Error(c, errors.NewErr("参数错误: "+err.Error()))
			return
		}
	}

	// 调用认证服务
	resp, err := h.authService.Authenticate(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	// 根据响应格式返回结果
	// 可以返回JSON格式: {"token":"hollycrmToken"}
	// 也可以返回字符串格式: hollycrmToken
	if c.Request.Method == "GET" {
		// 返回字符串格式
		c.Set("responseBody", resp.Token)
		c.String(200, resp.Token)
	} else {
		// 返回JSON格式（默认）
		c.Set("responseBody", resp)
		c.JSON(200, resp)
	}
}

func (h *thirdPartyAuthHandler) AuthenticateV2(c *gin.Context) {
	var req third_party.ThirdPartyAuthReq

	// Read the raw data from the request body
	rawData, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		handler.Error(c, errors.NewErr("Failed to read request body: "+err.Error()))
		return
	}

	// Bind the raw JSON data into the `req` structure
	if err := json.Unmarshal(rawData, &req); err != nil {
		handler.Error(c, errors.NewErr("Invalid JSON format: "+err.Error()))
		return
	}

	// 调用认证服务
	resp, err := h.authService.AuthenticateV2(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	// 返回字符串格式
	c.Set("responseBody", resp)
	c.JSON(200, resp)
}
