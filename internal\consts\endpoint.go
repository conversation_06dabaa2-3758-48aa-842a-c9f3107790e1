package consts

type EndpointType int

const (
	EndpointTypeCounter         EndpointType = iota + 1 // 专柜
	EndpointTypeOperatorChannel              = 2        // 运营商渠道
	EndpointTypeExclusiveStore               = 3        // 专卖店
	EndpointTypeCityComplex                  = 4        // 城市综合体
	EndpointTypeSupermarket                  = 5        // 商超
	EndpointTypeSmartStudyRoom               = 6        // 智习室
	EndpointTypeFortressStore                = 7        // 城市综合体堡垒店
)

var endpointTypeToPrefixMap = map[EndpointType]string{
	EndpointTypeCounter:         "C",
	EndpointTypeOperatorChannel: "D",
	EndpointTypeExclusiveStore:  "A",
	EndpointTypeCityComplex:     "B",
	EndpointTypeSupermarket:     "E",
	EndpointTypeFortressStore:   "Z",
	EndpointTypeSmartStudyRoom:  "M",
}

var endpointTypeNameMap = map[EndpointType]string{
	EndpointTypeCounter:         "专柜",
	EndpointTypeOperatorChannel: "运营商渠道",
	EndpointTypeExclusiveStore:  "专卖店",
	EndpointTypeCityComplex:     "城市综合体",
	EndpointTypeSupermarket:     "商超",
	EndpointTypeFortressStore:   "智习室",
	EndpointTypeSmartStudyRoom:  "城市综合体堡垒店",
}

func GetEndpointTypePrefix() map[EndpointType]string {
	// 创建一个新的 map 来存储副本
	copyMap := make(map[EndpointType]string)
	// 复制原始 map 的键值对到新的 map 中
	for key, value := range endpointTypeToPrefixMap {
		copyMap[key] = value
	}
	return copyMap
}

func GetEndpointNameMap() map[EndpointType]string {
	copyMap := make(map[EndpointType]string)
	for key, value := range endpointTypeNameMap {
		copyMap[key] = value
	}
	return copyMap
}
