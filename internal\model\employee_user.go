package model

import "time"

type EmployeeUser struct {
	ID         uint   `gorm:"primaryKey;autoIncrement"`
	UID        uint   `gorm:"uniqueIndex;not null"` // 确保有唯一索引
	Department uint8  `gorm:"not null;default:0;comment:'部门归属，1渠道一部，2渠道二部，3推培部，0未知'"`
	WeixinID   string `gorm:"type:varchar(255);default:null;comment:'微信昵称'"`
	Remark     string `gorm:"type:text;not null;comment:'备注'"`
	Email      string `gorm:"type:varchar(60);not null;comment:'邮箱'"`
	JobNumber  string `gorm:"type:varchar(20);default:null;comment:'企业微信工号'"`
	UpdatedAt  *time.Time
	CreatedAt  *time.Time
}

func (EmployeeUser) TableName() string {
	return "employee_user"
}
