package service

import (
	"encoding/json"
	"fmt"
	"mime/multipart"
	"reflect"
	"strconv"
	"strings"
	"time"

	"marketing/internal/api/notice"
	"marketing/internal/dao"
	"marketing/internal/model"
	"marketing/internal/pkg/errors"
	"marketing/internal/pkg/types"

	"github.com/gin-gonic/gin"
	"github.com/xuri/excelize/v2"
)

type TiKuSvc interface {
	// UploadPaperAndHomework 上传试卷和作业本Excel文件
	UploadPaperAndHomework(c *gin.Context, file *multipart.FileHeader) (int64, error)
	// GetTextBookOptions 获取教材类型选项
	GetTextBookOptions(c *gin.Context) ([]*notice.TextBookOptionsRes, error)
	// GetTextBookHistories 获取教材历史记录
	GetTextBookHistories(c *gin.Context, req *notice.TextBookHistoriesReq) ([]*notice.TextBookHistoriesGroupItem, error)
}

type tiKuSvc struct {
	dao dao.AppNotificationTabletUpdateDao
}

func NewTiKuSvc(dao dao.AppNotificationTabletUpdateDao) TiKuSvc {
	return &tiKuSvc{
		dao: dao,
	}
}

var (
	// ExcelHeaders Excel表头定义
	ExcelHeaders = [][]string{
		{"序号", "试卷名称", "版本", "年级", "课程", "省", "市", "区/县", "学校", "类型", "上线日期"},
		{"序号", "教辅书名", "课程", "版本", "年级", "系列", "出版社", "出版次数", "印刷次数", "条形码", "地区", "上线日期"},
	}
)

// UploadPaperAndHomework 上传试卷和作业本Excel文件
func (s *tiKuSvc) UploadPaperAndHomework(c *gin.Context, file *multipart.FileHeader) (int64, error) {
	if file == nil {
		return 0, errors.NewErr("上传文件不能为空")
	}

	reader, err := file.Open()
	if err != nil {
		return 0, errors.NewErr("打开文件失败: " + err.Error())
	}
	defer reader.Close()

	excelFile, err := excelize.OpenReader(reader)
	if err != nil {
		return 0, errors.NewErr("解析Excel文件失败: " + err.Error())
	}
	defer excelFile.Close()

	// 检查Excel格式
	sheetNames, err := s.checkTiKuExcelFormat(excelFile)
	if err != nil {
		return 0, err
	}

	dataList := make([]*model.AppNotificationTabletUpdate, 0, 800)

	for si, sheetName := range sheetNames {
		rows, err := excelFile.Rows(sheetName)
		if err != nil {
			return 0, errors.NewErr("读取工作表失败: " + err.Error())
		}

		ri := 0
		for rows.Next() {
			if ri > 0 { // 跳过表头
				row, err := rows.Columns(excelize.Options{RawCellValue: true})
				if err != nil {
					return 0, errors.NewErr("读取行数据失败: " + err.Error())
				}

				if len(row) != len(ExcelHeaders[si]) {
					continue
				}

				item := &model.AppNotificationTabletUpdate{}
				var data map[string]string

				if si == 0 { // 试卷
					item.Type = "paper"
					updatedAt, err := s.convertExcelTime(row[10], sheetName, ri, 10)
					if err != nil {
						return 0, err
					}
					item.UpdatedAt = types.CustomTime(updatedAt)
					data = map[string]string{
						"name": row[1],
					}
				} else { // 作业本
					item.Type = "homework"
					updatedAt, err := s.convertExcelTime(row[11], sheetName, ri, 11)
					if err != nil {
						return 0, err
					}
					item.UpdatedAt = types.CustomTime(updatedAt)
					data = map[string]string{
						"name":       row[1],
						"press_date": row[7],
						"print_date": row[8],
						"isbn":       row[9],
					}
				}

				js, _ := json.Marshal(data)
				item.Data = string(js)
				dataList = append(dataList, item)
			}
			ri++
		}
	}

	// 批量插入数据库
	err = s.dao.BatchInsert(c, dataList)
	if err != nil {
		return 0, errors.NewErr("保存数据失败: " + err.Error())
	}

	return int64(len(dataList)), nil
}

// checkTiKuExcelFormat 检查题库Excel格式
func (s *tiKuSvc) checkTiKuExcelFormat(f *excelize.File) ([]string, error) {
	sheetNames := f.GetSheetList()
	if len(sheetNames) != 2 {
		return nil, errors.NewErr("文件应有两个工作表")
	}

	for i, sheetName := range sheetNames {
		rows, err := f.Rows(sheetName)
		if err != nil {
			return nil, errors.NewErr("读取工作表失败: " + err.Error())
		}

		for rows.Next() {
			row, err := rows.Columns()
			if err != nil {
				return nil, errors.NewErr("读取表头失败: " + err.Error())
			}

			// 清理空格
			for j, column := range row {
				row[j] = strings.TrimSpace(column)
			}

			if !reflect.DeepEqual(row, ExcelHeaders[i]) {
				return nil, errors.NewErr(fmt.Sprintf("第%d个工作表表头应该为%s", i+1, strings.Join(ExcelHeaders[i], "、")))
			}
			break
		}
	}

	return sheetNames, nil
}

// convertExcelTime 转换Excel时间
func (s *tiKuSvc) convertExcelTime(val, sheetName string, row, col int) (time.Time, error) {
	dateFloat, err := strconv.ParseFloat(val, 64)
	if err != nil {
		cellName, err := excelize.CoordinatesToCellName(col+1, row+1)
		if err != nil {
			return time.Time{}, errors.NewErr("获取单元格名称失败")
		}
		return time.Time{}, errors.NewErr(fmt.Sprintf("工作表[%s]中%s单元格日期格式有误", sheetName, cellName))
	}

	t, err := excelize.ExcelDateToTime(dateFloat, false)
	if err != nil {
		cellName, err := excelize.CoordinatesToCellName(col+1, row+1)
		if err != nil {
			return time.Time{}, errors.NewErr("获取单元格名称失败")
		}
		return time.Time{}, errors.NewErr(fmt.Sprintf("工作表[%s]中%s单元格日期转换失败", sheetName, cellName))
	}

	return t, nil
}

// GetTextBookOptions 获取教材类型选项
func (s *tiKuSvc) GetTextBookOptions(c *gin.Context) ([]*notice.TextBookOptionsRes, error) {
	options := []*notice.TextBookOptionsRes{
		{Name: "双师直播课", Value: "sszb_course"},
		{Name: "名师辅导班", Value: "msfd_course"},
		{Name: "真题试卷", Value: "paper"},
		{Name: "作业本教辅", Value: "homework"},
	}
	return options, nil
}

// GetTextBookHistories 获取教材历史记录
func (s *tiKuSvc) GetTextBookHistories(c *gin.Context, req *notice.TextBookHistoriesReq) ([]*notice.TextBookHistoriesGroupItem, error) {
	// 获取不重复的日期列表
	dates, err := s.dao.GetDistinctDates(c, req.Type, req.Page, req.PageSize)
	if err != nil {
		return nil, errors.NewErr("获取日期列表失败: " + err.Error())
	}

	if len(dates) == 0 {
		return []*notice.TextBookHistoriesGroupItem{}, nil
	}

	// 获取日期范围内的所有记录
	startDate := dates[len(dates)-1]
	endDate := dates[0] + " 23:59:59"

	records, err := s.dao.GetByTypeAndDateRange(c, req.Type, startDate, endDate)
	if err != nil {
		return nil, errors.NewErr("获取历史记录失败: " + err.Error())
	}

	// 按日期分组
	groups := make(map[string][]*model.AppNotificationTabletUpdate)
	for _, record := range records {
		updateTime := time.Time(record.UpdatedAt)
		dateStr := updateTime.Format(time.DateOnly)
		groups[dateStr] = append(groups[dateStr], record)
	}

	// 构建响应数据
	histories := make([]*notice.TextBookHistoriesGroupItem, len(dates))
	for i, date := range dates {
		histories[i] = &notice.TextBookHistoriesGroupItem{
			UpdatedAt: date,
			Items:     make([]*notice.TextBookHistoryItem, 0),
		}

		if records, exists := groups[date]; exists {
			for _, record := range records {
				item, err := s.parseRecordToHistoryItem(record, req.Type)
				if err != nil {
					continue // 跳过解析失败的记录
				}
				histories[i].Items = append(histories[i].Items, item)
			}
		}
	}

	return histories, nil
}

// parseRecordToHistoryItem 解析记录为历史项
func (s *tiKuSvc) parseRecordToHistoryItem(record *model.AppNotificationTabletUpdate, recordType string) (*notice.TextBookHistoryItem, error) {
	var data map[string]interface{}
	if err := json.Unmarshal([]byte(record.Data), &data); err != nil {
		return nil, err
	}

	item := &notice.TextBookHistoryItem{}

	switch recordType {
	case "sszb_course":
		item.Subject = getStringFromMap(data, "subject_name")
		item.Grades = data["grade_names"]
		item.Name = getStringFromMap(data, "course_name")
	case "msfd_course":
		item.Subject = getStringFromMap(data, "subject_name")
		item.Grades = data["grade_name"]
		item.Name = getStringFromMap(data, "book_name")
	case "paper":
		item.Subject = ""
		item.Grades = ""
		item.Name = getStringFromMap(data, "name")
	case "homework":
		item.Subject = ""
		item.Grades = ""
		item.Name = fmt.Sprintf("%s[%s_%s]ISBN:%s",
			getStringFromMap(data, "name"),
			getStringFromMap(data, "press_date"),
			getStringFromMap(data, "print_date"),
			getStringFromMap(data, "isbn"))
	}

	return item, nil
}

// getStringFromMap 从map中获取字符串值
func getStringFromMap(data map[string]interface{}, key string) string {
	if val, exists := data[key]; exists {
		if str, ok := val.(string); ok {
			return str
		}
	}
	return ""
}
