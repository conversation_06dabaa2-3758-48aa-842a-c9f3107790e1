package warranty

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/xuri/excelize/v2"
	api "marketing/internal/api/warranty"
	"marketing/internal/consts"
	"marketing/internal/dao/strategy"
	"marketing/internal/handler"
	"marketing/internal/pkg/db"
	"marketing/internal/pkg/errors"
	"marketing/internal/service"
	"mime/multipart"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
)

type WarrantyHandler interface {
	Create(c *gin.Context)
	List(c *gin.Context)
	Endpoints(c *gin.Context)
	GetOneWarranty(c *gin.Context)
	UpdateAssessment(c *gin.Context)
	Update(c *gin.Context)
	GroupImport(c *gin.Context)
	Export(c *gin.Context)

	Return(c *gin.Context)
	ReturnList(c *gin.Context)
	ReturnEdit(c *gin.Context)

	Exchange(c *gin.Context)
	ExchangeList(c *gin.Context)
	ExchangeGet(c *gin.Context)
	ExchangeEdit(c *gin.Context)

	SNList(c *gin.Context)
	SNBatchGet(c *gin.Context)

	MachineTrack(c *gin.Context)
	MachinesHistoriesList(c *gin.Context)
	MachineHistory(c *gin.Context)
	MachineHistoryCreate(c *gin.Context)

	DevicesLimitAccountList(c *gin.Context)
	DevicesLimitAccountEdit(c *gin.Context)
	DevicesLimitAccountImport(c *gin.Context)
}

type warrantyHandler struct {
	warrantyService service.InterfaceWarranty
}

func NewWarrantyHandler(svc service.InterfaceWarranty) WarrantyHandler {
	return &warrantyHandler{
		warrantyService: svc,
	}
}

func (w *warrantyHandler) Return(c *gin.Context) {
	var err error
	var successMsg string
	defer func() {
		if err != nil {
			handler.Success(c, gin.H{
				"error_msg": err.Error(),
			})
		} else {
			handler.Success(c, successMsg)
		}
	}()

	var returnReq *api.ReturnReq
	err = c.ShouldBind(&returnReq)
	if err != nil {
		return
	}
	err = w.warrantyService.ReturnWarranty(c, returnReq)
	if err != nil {
		return
	}
	successMsg = fmt.Sprintf("退货成功")
}

func (w *warrantyHandler) ReturnList(c *gin.Context) {
	var err error
	var resp gin.H
	defer func() {
		if err != nil {
			handler.Success(c, gin.H{
				"error_msg": err.Error(),
			})
		} else {
			handler.Success(c, resp)
		}
	}()

	var returnListInfo *api.ReturnListReq
	if err = c.ShouldBind(&returnListInfo); err != nil {
		return
	}
	resp, err = w.warrantyService.GetReturnList(c, returnListInfo)
	if err != nil {
		return
	}
}

func (w *warrantyHandler) ReturnEdit(c *gin.Context) {
	var err error
	defer func() {
		if err != nil {
			handler.Success(c, gin.H{
				"error_msg": err.Error(),
			})
		} else {
			handler.Success(c, nil)
		}
	}()

	var id int
	id, err = strconv.Atoi(c.Param("id")) // 退货id
	reason := c.PostForm("reason")
	if err != nil || reason == "" {
		err = errors.NewErr("输入参数有误")
		return
	}
	err = w.warrantyService.ReturnEdit(c, id, reason)
	if err != nil {
		return
	}
}

func (w *warrantyHandler) Exchange(c *gin.Context) {
	var err error
	var successMsg string
	defer func() {
		if err != nil {
			handler.Success(c, gin.H{
				"error_msg": err.Error(),
			})
		} else {
			handler.Success(c, successMsg)
		}
	}()

	var req api.ExchangeReq
	if err = c.ShouldBind(&req); err != nil {
		return
	}
	if err = w.warrantyService.Exchange(c, &req); err != nil {
		return
	}
	successMsg = fmt.Sprintf("换货操作成功")
}

func (w *warrantyHandler) ExchangeList(c *gin.Context) {
	var err error
	var resp gin.H
	defer func() {
		if err != nil {
			handler.Success(c, gin.H{
				"error_msg": err.Error(),
			})
		} else {
			handler.Success(c, resp)
		}
	}()

	var exchangeListInfo *api.ExchangeListReq
	if err = c.ShouldBind(&exchangeListInfo); err != nil {
		return
	}
	resp, err = w.warrantyService.GetExchangeList(c, exchangeListInfo)
	if err != nil {
		return
	}
}

func (w *warrantyHandler) ExchangeGet(c *gin.Context) {
	var err error
	var resp gin.H
	defer func() {
		if err != nil {
			handler.Success(c, gin.H{
				"error_msg": err.Error(),
			})
		} else {
			handler.Success(c, resp)
		}
	}()

	var id int
	id, err = strconv.Atoi(c.Param("id"))
	if err != nil {
		err = errors.NewErr("id参数错误")
		return
	}
	resp, err = w.warrantyService.ExchangeGetOneWarranty(c, id)
	if err != nil {
		return
	}
}

func (w *warrantyHandler) Endpoints(c *gin.Context) {
	var err error
	var endpoints []*api.GetEndpointResp
	defer func() {
		if err != nil {
			handler.Success(c, gin.H{
				"error_msg": err.Error(),
			})
		} else {
			handler.Success(c, endpoints)
		}
	}()

	q := c.Query("q")
	endpoints = w.warrantyService.GetEndpoints(c, q)
	if len(endpoints) == 0 {
		err = errors.NewErr("无搜索结果")
		return
	}
}

func (w *warrantyHandler) Create(c *gin.Context) {
	var err error
	var data map[string]interface{}
	defer func() {
		if err != nil {
			handler.Success(c, gin.H{
				"error_msg": err.Error(),
			})
		} else {
			handler.Success(c, data)
		}
	}()

	var req *api.CreateWarrantyReq
	if err = c.ShouldBind(&req); err != nil {
		err = errors.NewErr("输入参数有误")
		return
	}
	cnt, _ := strconv.Atoi(c.DefaultQuery("contact", "1"))
	salesmanID, _ := strconv.Atoi(c.DefaultQuery("salesman_id", "0"))
	req.Contact = cnt
	data, err = w.warrantyService.Create(c, uint(salesmanID), req)
	if err != nil {
		return
	}
}

func (w *warrantyHandler) List(c *gin.Context) {
	var err error
	var resp gin.H
	defer func() {
		if err != nil {
			handler.Success(c, gin.H{
				"error_msg": err.Error(),
			})
		} else {
			handler.Success(c, resp)
		}
	}()

	var warrantyInfo *api.WarrantyInfoReq
	if err = c.ShouldBind(&warrantyInfo); err != nil {
		return
	}
	resp, err = w.warrantyService.GetInfo(c, warrantyInfo)
	if err != nil {
		return
	}
}

func (w *warrantyHandler) UpdateAssessment(c *gin.Context) {
	var err error
	defer func() {
		if err != nil {
			handler.Success(c, gin.H{
				"error_msg": err.Error(),
			})
		} else {
			handler.Success(c, nil)
		}
	}()

	var id int
	id, err = strconv.Atoi(c.Param("id"))
	if err != nil {
		return
	}
	action, err := strconv.Atoi(c.Param("assessment"))
	if err != nil || (action != consts.WarrantyAssessment && action != consts.WarrantyNotAssessment) {
		err = errors.NewErr("参数错误")
		return
	}
	err = w.warrantyService.UpdateAssessment(c, id, uint(action))
	if err != nil {
		return
	}
}

func (w *warrantyHandler) Update(c *gin.Context) {
	var err error
	defer func() {
		if err != nil {
			handler.Success(c, gin.H{
				"error_msg": err.Error(),
			})
		} else {
			handler.Success(c, nil)
		}
	}()

	var id int
	id, err = strconv.Atoi(c.Param("id"))
	if err != nil {
		err = errors.NewErr("id参数错误")
		return
	}
	var updateReq *api.EditReq
	if err = c.ShouldBind(&updateReq); err != nil {
		return
	}
	var rowsAffected int64
	rowsAffected, err = w.warrantyService.Update(c, id, updateReq)
	if err != nil || rowsAffected == 0 {
		return
	}
}

func (w *warrantyHandler) GroupImport(c *gin.Context) {
	// TODO: 仅Administrator和管理员可以批量导入保卡
	var err error
	var msg *api.ImportResp
	defer func() {
		if err != nil {
			handler.Success(c, gin.H{
				"error_msg": err.Error(),
			})
		} else {
			handler.Success(c, msg)
		}
	}()

	file, err := c.FormFile("file")
	if err != nil {
		err = errors.NewErr("请选择要上传的文件")
		return
	}
	if file.Size > 2*1024*1024 {
		err = errors.NewErr("文件超过了最大允许上传大小 2MB")
		return
	}
	ext := strings.ToLower(filepath.Ext(file.Filename))
	if ext != ".xls" && ext != ".xlsx" {
		err = errors.NewErr("只允许上传 .xls 和 .xlsx 文件")
		return
	}
	src, err := file.Open()
	if err != nil {
		err = errors.NewErr("无法打开文件")
		return
	}
	defer func(s multipart.File) {
		_ = s.Close()
	}(src)
	f, err := excelize.OpenReader(src)
	if err != nil {
		err = errors.NewErr(fmt.Sprintf("无法读取Excel文件: %s", err.Error()))
		return
	}
	sheetName := f.GetSheetName(0)
	rows, err := f.GetRows(sheetName)
	if err != nil {
		err = errors.NewErr(fmt.Sprintf("无法获取工作表数据: %s", err.Error()))
		return
	}
	if len(rows) < 2 {
		err = errors.NewErr("Excel文件中没有数据或只有表头")
		return
	}
	header := rows[0]
	if len(header) < len(consts.ValidHeader) {
		err = errors.NewErr(fmt.Sprintf("数据列数不足，至少应有 %d 列", len(consts.ValidHeader)))
		return
	}
	for i, v := range consts.ValidHeader {
		if strings.TrimSpace(header[i]) != v {
			err = errors.NewErr(fmt.Sprintf("表头不匹配: 第 %c 列应为“%s”，实际为“%s”", 'A'+i, v, header[i]))
			return
		}
	}
	msg = w.warrantyService.GroupImportWarranty(c, rows, f, sheetName)
}

func (w *warrantyHandler) Export(c *gin.Context) {
	var err error
	var successMsg gin.H
	defer func() {
		if err != nil {
			handler.Success(c, gin.H{
				"error_msg": err.Error(),
			})
		} else {
			handler.Success(c, successMsg)
		}
	}()

	endpointCode := c.Query("code")
	page, _ := strconv.Atoi(c.Query("page"))
	pageSize, _ := strconv.Atoi(c.Query("page_size"))
	if endpointCode == "" {
		err = errors.NewErr("终端编码不能为空")
		return
	}
	// TODO: 权限校验（目前默认为管理员）
	// user.InRoles([]string{"manager", "administrator", "warrantyExport"})
	resp, total, err := w.warrantyService.ExportWarranties(c, page, pageSize, endpointCode)
	if err != nil {
		return
	}
	successMsg = gin.H{
		"total":    total,
		"page":     page,
		"pageSize": pageSize,
		"list":     resp,
	}
}

func (w *warrantyHandler) GetOneWarranty(c *gin.Context) {
	var err error
	var resp gin.H
	defer func() {
		if err != nil {
			handler.Success(c, gin.H{
				"error_msg": err.Error(),
			})
		} else {
			handler.Success(c, resp)
		}
	}()

	var id int
	id, err = strconv.Atoi(c.Param("id"))
	if err != nil && id != 0 {
		err = errors.NewErr("id参数错误")
		return
	}
	resp, err = w.warrantyService.GetOneWarranty(c, id)
	if err != nil {
		return
	}
}

func (w *warrantyHandler) ExchangeEdit(c *gin.Context) {
	var err error
	var successMsg string
	defer func() {
		if err != nil {
			handler.Success(c, gin.H{
				"error_msg": err.Error(),
			})
		} else {
			handler.Success(c, successMsg)
		}
	}()

	var id int
	id, err = strconv.Atoi(c.Param("id"))
	if err != nil && id != 0 {
		err = errors.NewErr("id参数错误")
		return
	}
	reason := c.PostForm("reason")
	if reason == "" {
		err = errors.NewErr("换货原因不能为空")
		return
	}
	err = w.warrantyService.ExchangeEdit(c, id, reason)
	if err != nil {
		return
	}
	successMsg = fmt.Sprintf("编辑成功")
}

func (w *warrantyHandler) SNList(c *gin.Context) {
	var err error
	var resp gin.H
	defer func() {
		if err != nil {
			handler.Success(c, gin.H{
				"error_msg": err.Error(),
			})
		} else {
			handler.Success(c, resp)
		}
	}()

	param := c.Query("sn_number_phone")
	SNMatched, _ := regexp.MatchString(`^[A-Za-z-0-9]{6,14}$`, param)
	NumberMatched, _ := regexp.MatchString(`^[A-Za-z-0-9]{8,64}$`, param)
	PhoneMatched, _ := regexp.MatchString(`^1[0-9]{10}$`, param)
	if param == "" || (!SNMatched && !NumberMatched && !PhoneMatched) {
		err = errors.NewErr("非法查询参数")
		return
	}
	paramStrategy := strategy.NewStrategyForParamGetWarranty(db.GetDB(), param)
	warranties, err := w.warrantyService.SNGetWarranties(c, paramStrategy)
	if err != nil {
		return
	}
	resp = gin.H{
		"list": warranties,
	}
}

func (w *warrantyHandler) SNBatchGet(c *gin.Context) {
	encodeSNCodes := c.Query("encode_sn_codes")
	snCodes := strings.Split(encodeSNCodes, "/")
	resp := w.warrantyService.BatchGetWarranties(c, snCodes)
	handler.Success(c, resp)
}

func (w *warrantyHandler) MachineTrack(c *gin.Context) {
	var err error
	var resp *api.MachineLifeResp
	defer func() {
		if err != nil {
			handler.Success(c, gin.H{
				"error_msg": err.Error(),
			})
		} else {
			handler.Success(c, resp)
		}
	}()

	var barcode string
	barcode = c.Query("barcode")
	if err != nil && barcode != "" {
		err = errors.NewErr("输入参数有误")
		return
	}
	resp, err = w.warrantyService.TrackMachineLife(c, barcode)
}

func (w *warrantyHandler) MachinesHistoriesList(c *gin.Context) {
	var err error
	var resp *api.MachinesHistoriesResp
	defer func() {
		if err != nil {
			handler.Success(c, gin.H{
				"error_msg": err.Error(),
			})
		} else {
			handler.Success(c, resp)
		}
	}()

	var params api.MachinesHistoriesReq
	if err = c.ShouldBind(&params); err != nil {
		err = errors.NewErr("输入参数有误")
		return
	}
	resp, err = w.warrantyService.MachinesHistoriesList(c, &params)
}

func (w *warrantyHandler) MachineHistory(c *gin.Context) {
	var err error
	var resp []*api.MachineHistoryResp
	defer func() {
		if err != nil {
			handler.Success(c, gin.H{
				"error_msg": err.Error(),
			})
		} else {
			handler.Success(c, resp)
		}
	}()

	var barcode string
	barcode = c.Param("barcode")
	if barcode == "" {
		err = errors.NewErr("SN码不能为空")
		return
	}
	resp, err = w.warrantyService.MachineHistory(c, barcode)
}

func (w *warrantyHandler) MachineHistoryCreate(c *gin.Context) {
	var err error
	var resp gin.H
	defer func() {
		if err != nil {
			handler.Success(c, gin.H{
				"error_msg": err.Error(),
			})
		} else {
			handler.Success(c, resp)
		}
	}()

	var req api.MachineHistoryUpdReq
	var msg string
	err = c.ShouldBind(&req)
	if err != nil {
		return
	}
	msg, err = w.warrantyService.MachineHistoryCreate(c, &req)
	resp = gin.H{
		"operation_msg": msg,
	}
}

func (w *warrantyHandler) DevicesLimitAccountList(c *gin.Context) {
	var err error
	var resp gin.H
	defer func() {
		if err != nil {
			handler.Success(c, gin.H{
				"error_msg": err.Error(),
			})
		} else {
			handler.Success(c, resp)
		}
	}()

	var req *api.DevicesLimitAccountReq
	if err = c.ShouldBindJSON(&req); err != nil {
		err = c.ShouldBind(&req)
		if err != nil {
			return
		}
	}
	var list []*api.DevicesLimitAccount
	var total int64
	list, total, err = w.warrantyService.DevicesLimitAccountList(c, req)
	resp = gin.H{
		"page":      req.Page,
		"page_size": req.PageSize,
		"total":     total,
		"list":      list,
	}
}

func (w *warrantyHandler) DevicesLimitAccountEdit(c *gin.Context) {
	var err error
	var updMsg string
	defer func() {
		if err != nil {
			handler.Success(c, gin.H{
				"error_msg": err.Error(),
			})
		} else {
			handler.Success(c, updMsg)
		}
	}()

	id, _ := strconv.Atoi(c.Param("id"))
	phone := c.Query("phone")
	status, _ := strconv.Atoi(c.Query("status"))

	req := &api.DevicesLimitAccountUpdReq{
		ID:     id,
		Phone:  phone,
		Status: &status,
	}
	updMsg, err = w.warrantyService.DevicesLimitAccountEdit(c, req)
}

func (w *warrantyHandler) DevicesLimitAccountImport(c *gin.Context) {
	var err error
	var msg string
	defer func() {
		if err != nil {
			handler.Success(c, gin.H{
				"import_msg": msg,
				"error_msg":  err.Error(),
			})
		} else {
			handler.Success(c, msg)
		}
	}()

	file, err := c.FormFile("file")
	if err != nil {
		err = errors.NewErr("请选择要上传的文件")
		return
	}
	fileName := file.Filename
	if file.Size > 2*1024*1024 {
		err = errors.NewErr("文件超过了最大允许上传大小 2MB")
		return
	}
	ext := strings.ToLower(filepath.Ext(file.Filename))
	if ext != ".xls" && ext != ".xlsx" {
		err = errors.NewErr("只允许上传 .xls 和 .xlsx 文件")
		return
	}
	src, err := file.Open()
	if err != nil {
		err = errors.NewErr("无法打开文件")
		return
	}
	defer func(s multipart.File) {
		_ = s.Close()
	}(src)
	f, err := excelize.OpenReader(src)
	if err != nil {
		err = errors.NewErr(fmt.Sprintf("无法读取Excel文件: %s", err.Error()))
		return
	}
	sheetName := f.GetSheetName(0)
	rows, err := f.GetRows(sheetName)
	if err != nil {
		err = errors.NewErr(fmt.Sprintf("无法获取工作表数据: %s", err.Error()))
		return
	}
	if len(rows) < 2 {
		err = errors.NewErr("Excel文件中没有数据或只有表头")
		return
	}
	header := rows[0]
	if len(header) < len(consts.ValidDevicesLimitAccountHeader) {
		err = errors.NewErr(fmt.Sprintf("数据列数不足，至少应有 %d 列", len(consts.ValidDevicesLimitAccountHeader)))
		return
	}
	for i, v := range consts.ValidDevicesLimitAccountHeader {
		if strings.TrimSpace(header[i]) != v {
			err = errors.NewErr(fmt.Sprintf("表头不匹配: 第 %c 列应为“%s”，实际为“%s”", 'A'+i, v, header[i]))
			return
		}
	}
	msg, err = w.warrantyService.DevicesLimitAccountImport(c, f, fileName, sheetName)
}
