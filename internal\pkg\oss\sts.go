package oss

import (
	"fmt"
	openapi "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	sts20150401 "github.com/alibabacloud-go/sts-20150401/v2/client"
	util "github.com/alibabacloud-go/tea-utils/v2/service"
	"github.com/alibabacloud-go/tea/tea"
	"marketing/internal/pkg/log"
	"os"
	"sync"
	"time"
)

const expirationTime = 1800

type STSClient struct {
	c           *sts20150401.Client
	credentials *sts20150401.AssumeRoleResponseBodyCredentials
	expiration  time.Time
	mutex       sync.RWMutex
}

// NewSTSClient 创建阿里云STS客户端
func NewSTSClient() *STSClient {
	// 从环境变量获取AccessKey
	id := os.Getenv("OSS_ACCESS_KEY_ID")
	secret := os.Getenv("OSS_ACCESS_KEY_SECRET")

	config := &openapi.Config{
		// 必填，您的 AccessKey ID
		AccessKeyId: tea.String(id),
		// 必填，您的 AccessKey Secret
		AccessKeySecret: tea.String(secret),
	}

	// 从配置文件获取Endpoint
	endpoint := "sts.cn-shenzhen.aliyuncs.com"
	config.Endpoint = &endpoint
	_result, _err := sts20150401.NewClient(config)
	if _err != nil {
		log.Error(fmt.Sprintf("OSS STS应用实例化失败1: %s", _err.Error()))
	}

	return &STSClient{
		c: _result,
	}
}

// AssumeRole 获取临时访问凭证
func (s *STSClient) AssumeRole() {
	acsRam := os.Getenv("OSS_ACS_RAM")
	assumeRoleRequest := &sts20150401.AssumeRoleRequest{
		DurationSeconds: tea.Int64(expirationTime),
		RoleArn:         tea.String(acsRam),
		RoleSessionName: tea.String("marketing-oss"),
	}
	res, err := s.c.AssumeRoleWithOptions(assumeRoleRequest, &util.RuntimeOptions{})
	if err != nil {
		log.Error(fmt.Sprintf("OSS STS创建临时访问凭证失败2: %s", err.Error()))
	}
	if res == nil {
		log.Error("OSS STS创建临时访问凭证失败: nil response")
	} else {
		credentials := res.Body.Credentials
		s.credentials = credentials
		// 设置过期时间，预留5分钟的缓冲时间
		expireTime, err := time.Parse("2006-01-02T15:04:05Z", tea.StringValue(res.Body.Credentials.Expiration))
		if err != nil {
			log.Error(fmt.Sprintf("解析过期时间失败: %s", err.Error()))
			// 如果解析失败，可以选择设置一个默认过期时间或直接返回
			s.expiration = time.Now().Add(-1 * time.Minute) // 设置为当前时间减1分钟，避免逻辑错误
			return
		}
		s.expiration = expireTime.Add(-1 * time.Minute) // 提前5分钟刷新
	}
}

// GetCredentials 获取临时访问凭证
func (s *STSClient) GetCredentials() *sts20150401.AssumeRoleResponseBodyCredentials {
	s.mutex.RLock()
	if s.credentials != nil && time.Now().Before(s.expiration) {
		defer s.mutex.RUnlock()
		return s.credentials
	}
	s.mutex.RUnlock()

	s.mutex.Lock()
	defer s.mutex.Unlock()
	// 双重检查，避免多个goroutine同时刷新
	if s.credentials != nil && time.Now().Before(s.expiration) {
		return s.credentials
	}

	s.AssumeRole()
	return s.credentials
}
