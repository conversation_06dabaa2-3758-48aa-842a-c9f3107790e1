package system

import (
	"marketing/internal/api"
	"marketing/internal/pkg/types"
)

// AddPermissionReq 添加入参
type AddPermissionReq struct {
	ID         uint   `json:"id" form:"id"`
	GroupID    uint   `json:"group_id" form:"group_id" binding:"required"`
	Name       string `json:"name" form:"name" binding:"required"`
	Slug       string `json:"slug" form:"slug" binding:"required"`
	HttpMethod string `json:"http_method" form:"http_method"`
	HttpPath   string `json:"http_path" form:"http_path"`
	SystemType string `json:"system_type" form:"system_type"`
}

// AdminPermissionReq 查询入参
type AdminPermissionReq struct {
	api.PaginationParams
	IsPage     bool   `json:"is_page" form:"is_page"`
	Name       string `json:"name" form:"name"`
	Slug       string `json:"slug" form:"slug"`
	SystemType string `json:"system_type" form:"system_type"`
}

// AdminPermissionResp 响应出参
type AdminPermissionResp struct {
	ID         uint             `json:"id"`
	GroupID    uint             `json:"group_id"`
	GroupName  string           `json:"group_name"`
	Name       string           `json:"name"`
	Slug       string           `json:"slug"`
	HttpMethod string           `json:"http_method" `
	HttpPath   string           `json:"http_path"`
	SystemType string           `json:"system_type"`
	CreatedAt  types.CustomTime `json:"created_at"` // 创建时间
}

// AddPermissionGroupReq 添加权限组入参
type AddPermissionGroupReq struct {
	ID          uint   `json:"id" form:"id"`
	Name        string `json:"name" form:"name" binding:"required"`
	Description string `json:"description" form:"description"`
	SystemType  string `json:"system_type" form:"system_type" binding:"required"`
}

// AddPermissionGroupResp 权限组
type AddPermissionGroupResp struct {
	ID          uint             `json:"id" form:"id"`
	Name        string           `json:"name" form:"name"`
	Description string           `json:"description" form:"description"`
	SystemType  string           `json:"system_type"`
	CreatedAt   types.CustomTime `json:"created_at"` // 创建时间
}

type Route struct {
	Method string `json:"method"`
	Path   string `json:"path"`
}
