package utils

import (
	"reflect"
)

type TagSort struct {
	Data   interface{} // 数据
	Length int         // 数据长度
}

func (d TagSort) Len() int {
	return d.Length
}

func (d TagSort) Swap(i, j int) {
	reflect.Swapper(d.Data)(i, j)
}

func (d TagSort) Less(i, j int) bool {
	v1 := getReflectSliceIntValue(d.Data, i, "Order")
	v2 := getReflectSliceIntValue(d.Data, j, "Order")
	if v1 == v2 {
		return getReflectSliceIntValue(d.Data, i, "Id") < getReflectSliceIntValue(d.Data, j, "Id")
	}

	return v1 > v2
}

func getReflectSliceIntValue(data interface{}, index int, name string) int {
	v := reflect.ValueOf(data).Index(index)
	if !v.FieldByName(name).IsValid() {
		return 0
	}

	switch v.FieldByName(name).Kind() {
	case reflect.Int:
		return int(v.FieldByName(name).Int())
	case reflect.Int32:
		return int(v.<PERSON>ByName(name).Int())
	case reflect.Int64:
		return int(v.FieldByName(name).Int())
	case reflect.Uint:
		return int(v.FieldByName(name).Uint())
	case reflect.Uint32:
		return int(v.FieldByName(name).Uint())
	case reflect.Uint64:
		return int(v.FieldByName(name).Uint())
	}

	return 0
}
