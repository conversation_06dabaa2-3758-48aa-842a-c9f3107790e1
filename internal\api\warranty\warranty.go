package warranty

import (
	"marketing/internal/model"
	"time"
)

type Warranty struct {
	ID                  uint      `json:"-"`
	UID                 uint      `json:"uid"`
	ModelID             int       `json:"model_id"`
	Model               string    `json:"model"`
	Barcode             string    `json:"barcode"`
	Number              string    `json:"number"`
	Imei                string    `json:"imei"`
	ExtBarcode          string    `json:"ext_barcode"`
	Source              int64     `json:"source"`
	Endpoint            int       `json:"endpoint"`
	BuyDate             time.Time `json:"buy_date"`
	WarrantyPeriod      time.Time `json:"warranty_period"`
	ProductDate         time.Time `json:"product_date"` // 出厂日期（mes入库时间）
	CreatedAtNew        time.Time `json:"created_at_new"`
	CreatedAt           time.Time `json:"created_at"`
	ActivatedAtOld      time.Time `json:"activated_at_old"`
	ActivatedID         uint      `json:"activated_id"`
	Realsale            int8      `json:"realsale" binding:"required"` // 1-是，0-否
	Assessment          uint      `json:"assessment" binding:"required"`
	Status              int8      `json:"status"` // -1-虚卡翻新，0-虚卡，1-正常，2-换机，3-退机，4-其他，5-翻新
	SalesmanID          int       `json:"salesman_id"`
	Salesman            string    `json:"salesman"`
	CustomerPrice       float64   `json:"customer_price"`
	CustomerName        string    `json:"customer_name" binding:"required"`
	CustomerSex         string    `json:"customer_sex"` // m-男，f-女
	CustomerPhone       string    `json:"customer_phone" binding:"required"`
	CustomerAddr        string    `json:"customer_addr" binding:"required"`
	StudentUID          int       `json:"student_uid"`
	StudentName         string    `json:"student_name"`
	StudentSex          string    `json:"student_sex" binding:"required"`
	StudentSchoolID     int       `json:"student_school_id" binding:"required"`
	StudentSchool       string    `json:"student_school"`
	StudentSchoolAdcode int       `json:"student_school_adcode"`
	StudentGrade        string    `json:"student_grade"`
	StudentBirthday     time.Time `json:"student_birthday"`
	CallBack            int8      `json:"call_back" binding:"required"`
	PurchaseWay         string    `json:"purchase_way" binding:"required"`
	Recommender         string    `json:"recommender"`
	RecommenderPhone    string    `json:"recommender_phone"`
	State               int8      `json:"state"`
	InBillDate          string    `json:"in_bill_date"`
	OutBillDate         string    `json:"out_bill_date"`
	OutCustCode         string    `json:"out_cust_code"`
	Type                int8      `json:"type" binding:"required"`      // 1正常，-1-已冻结
	Prototype           int8      `json:"prototype" binding:"required"` // 0为非样机  1为样机
	UpdatedAt           string    `json:"updated_at"`
	EcCreatedAt         string    `json:"ec_created_at"`
	EcType              int8      `json:"ec_type"`
	EcEndpoint          int       `json:"ec_endpoint"`
	Channel             string    `json:"channel" binding:"required"`
	ActivatedStatus     int8      `json:"activated_status" binding:"required"`
	ActivatedAt         string    `json:"activated_at"`
	LngActivated        string    `json:"lng_activated"`
	LatActivated        string    `json:"lat_activated"`
	Lng                 string    `json:"lng"`
	Lat                 string    `json:"lat"`
}

type CreateWarrantyReq struct {
	Barcode         string `json:"barcode" binding:"required"`
	EndpointID      uint   `json:"endpoint_id" binding:"required"`
	Date            string `json:"buy_date"`
	Phone           string `json:"customer_phone" binding:"required"`
	Name            string `json:"customer_name" binding:"required"`
	Sex             string `json:"customer_sex"`
	PurchaseWay     string `json:"purchase_way"`
	StudentName     string `json:"student_name"`
	StudentSchool   string `json:"student_school"`
	StudentGrade    string `json:"student_grade"`
	StudentBirthday string `json:"student_birthday"`
	StudentSex      string `json:"student_sex"`
	Contact         int    `json:"contact"`
}

type CreateWarrantyResp struct {
	Barcode         string    `json:"barcode"`
	ExtBarcode      string    `json:"ext_barcode"`
	Salesman        string    `json:"salesman"`
	CustomerName    string    `json:"customer_name"`
	CustomerPhone   string    `json:"customer_phone"`
	CustomerAddr    string    `json:"customer_addr"`
	Model           string    `json:"model"`
	Endpoint        int       `json:"endpoint"`
	BuyDate         time.Time `json:"buy_date"`
	StudentName     string    `json:"student_name"`
	StudentSchool   string    `json:"student_school"`
	StudentGrade    string    `json:"student_grade"`
	StudentBirthday time.Time `json:"student_birthday"`
	CreatedAt       time.Time `json:"created_at"`
	CustomerPrice   float64   `json:"customer_price"`
	CustomerSex     string    `json:"customer_sex"` // m-男，f-女
	StudentSex      string    `json:"student_sex"`
	SalesmanID      int       `json:"salesman_id"`
}

type EditReq struct {
	ExtBarcode      string `json:"ext_barcode"`
	Salesman        string `json:"salesman"`
	CustomerName    string `json:"customer_name"`
	CustomerPhone   string `json:"customer_phone"`
	CustomerSex     string `json:"customer_sex"` // m-男，f-女
	PurchaseWay     string `json:"purchase_way"`
	StudentName     string `json:"student_name"`
	StudentSchool   string `json:"student_school"`
	StudentGrade    string `json:"student_grade"`
	StudentBirthday string `json:"student_birthday"`
	StudentSex      string `json:"student_sex"`
	Assessment      int    `json:"assessment"`
}

type ReturnReq struct {
	Barcode  string `json:"barcode" binding:"required"`
	Reason   string `json:"reason" binding:"required"`
	ReturnAt string `json:"return_at"`
}

type ExchangeReq struct {
	Barcode    string `json:"barcode" binding:"required"`
	NewBarcode string `json:"new_barcode" binding:"required"`
	Reason     string `json:"reason" binding:"required"`
}

type GetEndpointResp struct {
	ID   uint   `json:"id"`
	Text string `json:"text"`
}

type UpdExchange struct {
	ID             int
	Barcode        string
	BarcodeNew     string
	ExtBarcodeNew  string
	Reason         string
	Warranty       *model.Warranty
	Uid            uint
	EndpointID     int
	Number         string
	ExchangedAt    time.Time
	WarrantyPeriod time.Time
}

type Exchange struct {
	Barcode        string
	BarcodeNew     string
	ExtBarcodeNew  string
	Reason         string
	Warranty       *model.Warranty
	Uid            uint
	EndpointID     int
	Number         string
	ExchangedAt    time.Time
	WarrantyPeriod time.Time
}

type PrototypeCancel struct {
	Barcode string
	Number  string
}

type PrototypeCancelData struct {
	Number     string
	Status     int
	CategoryID int
}
