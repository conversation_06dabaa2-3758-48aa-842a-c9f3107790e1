package app_auth

import (
	"marketing/internal/model"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// AppAuthDao 第三方应用认证数据访问接口
type AppAuthDao interface {
	GetByAppID(c *gin.Context, appID string) (*model.AppAuth, error)
	GetByAppKey(c *gin.Context, appKey string) (*model.AppAuth, error)
	GetByAppIDAndAppKey(c *gin.Context, appID, appKey string) (*model.AppAuth, error)
}

type appAuthDao struct {
	db *gorm.DB
}

// NewAppAuthDao 创建AppAuth DAO实例
func NewAppAuthDao(db *gorm.DB) AppAuthDao {
	return &appAuthDao{
		db: db,
	}
}

// GetByAppID 根据AppID获取应用认证信息
func (a *appAuthDao) GetByAppID(c *gin.Context, appID string) (*model.AppAuth, error) {
	var appAuth model.AppAuth
	err := a.db.WithContext(c).Where("appid = ? AND status = 0", appID).First(&appAuth).Error
	if err != nil {
		return nil, err
	}
	return &appAuth, nil
}

// GetByAppKey 根据AppKey获取应用认证信息（这里的AppKey实际上是AppID）
func (a *appAuthDao) GetByAppKey(c *gin.Context, appKey string) (*model.AppAuth, error) {
	var appAuth model.AppAuth
	err := a.db.WithContext(c).Where("appid = ? AND status = 0", appKey).First(&appAuth).Error
	if err != nil {
		return nil, err
	}
	return &appAuth, nil
}

// GetByAppIDAndAppKey 根据AppID和AppKey同时验证应用认证信息
func (a *appAuthDao) GetByAppIDAndAppKey(c *gin.Context, appID, appKey string) (*model.AppAuth, error) {
	var appAuth model.AppAuth
	err := a.db.WithContext(c).Where("appid = ? AND appkey = ? AND status = 0", appID, appKey).First(&appAuth).Error
	if err != nil {
		return nil, err
	}
	return &appAuth, nil
}
