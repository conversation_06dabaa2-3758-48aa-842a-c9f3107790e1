package dao

import (
	"encoding/json"
	"fmt"
	"gorm.io/gorm"
	"marketing/internal/model"
	"marketing/internal/pkg/db"
	"strconv"
	"strings"
	"time"
)

type GetDeclareParam struct {
	TopAgency    int    `json:"top_agency"`    // 一代
	SecondAgency int    `json:"second_agency"` // 二代
	Date         string `json:"date"`          // 日期
	RegionName   string `json:"region_name"`   //
	PageNum      int    `json:"page_num"`      // 页码
	PageSize     int    `json:"page_size" `    // 页幅
	DeclareType  int    `json:"type"`          //
	Sort         string `json:"sort" `         //
	DataType     string `json:"data_type"`     //
	Channel      string `json:"channel"`       //
	CategoryId   int    `json:"category_id"`   //
}

type DeclareList struct {
	TopAgency    int       `gorm:"column:top_agency" json:"top_agency"`
	SecondAgency int       `gorm:"column:second_agency" json:"second_agency"`
	Endpoint     int       `gorm:"column:endpoint" json:"endpoint"`
	Name         string    `gorm:"column:name" json:"name"`
	Time         time.Time `gorm:"column:time" json:"time"`
	Inventory    int       `gorm:"column:inventory" json:"inventory"`
	OldMachine   int       `gorm:"column:old_machine" json:"old_machine"`
	Next         int       `gorm:"column:next" json:"next"` // 1--自有  2--其他二代的
	//List         []DeclareListByModel `gorm:"column:-" json:"list"`
}

type DeclareListRet struct {
	TopAgency    int                  `json:"top_agency"`
	SecondAgency int                  `json:"second_agency"`
	Endpoint     int                  `json:"endpoint"`
	Name         string               `json:"name"`
	Time         string               `json:"time"`
	Inventory    int                  `json:"inventory"`
	OldMachine   int                  `json:"old_machine"`
	Next         int                  `json:"next"` // 1--自有  2--其他二代的
	List         []DeclareListByModel `json:"list"`
}

type DeclareListByModel struct {
	ModelName  string `gorm:"coulumn:model_name" json:"model_name"`
	Inventory  int    `gorm:"column:inventory" json:"inventory"`
	OldMachine int    `gorm:"column:old_machine" json:"old_machine"`
}

type DeclareDao interface {
	ManageDeclareListPage(topAgency int, secondAgency int, date string, regionName string, page int, count int, Type int, Sort string, dataType string, channel string, models []string) (interface{}, interface{})
	GetDeclareList(req *GetDeclareParam) (interface{}, interface{})
	DeclareTimeLimit() (int, int)
}

// DeclareDaoImpl 实现 DeclareDao 接口
type DeclareDaoImpl struct {
	db *gorm.DB
}

// NewDeclareDao 创建 DeclareDao 实例
func NewDeclareDao() DeclareDao {
	return &DeclareDaoImpl{
		db: db.GetDB(""),
	}
}

func (d *DeclareDaoImpl) ManageDeclareListPage(topAgency int, secondAgency int, date string, regionName string, page int,
	count int, Type int, Sort string, dataType string, channel string, models []string) (interface{}, interface{}) {
	offset := (page - 1) * count
	data := make([]DeclareList, 0)
	data2 := DeclareList{}
	declare := " AND dr.`declare` = 1 "
	if Type == 1 {
		declare = " "
	}
	if len(models) > 0 {
		modelsStr := strings.Join(models, ",")
		declare += " AND dr.model_name in (" + modelsStr + ") "
	}
	channelWhere := " "
	if channel != "" {
		channelWhere = " AND a.channel = '" + channel + "' "
	}
	//所有总代信息
	if topAgency == 0 { // 多级连接
		where := " WHERE 1 = 1 "
		if regionName != "" {
			where += " AND b.name = '" + regionName + "' "
		}
		//CONVERT(a.`name` USING GBK) ASC
		// 总代数据  union all 二代数据   union all 此总代下所有终端
		sql1 := "SELECT b.top_agency, 0 second_agency, b.name, 1 next, IFNULL(SUM(inventory),-1) inventory, " +
			"IFNULL(SUM(old_machine), -1) old_machine FROM (" +
			"SELECT a.id top_agency, 0 second_agency, a.name, 1 next, SUM(inventory) inventory, " +
			"SUM(old_machine) old_machine " +
			"FROM agency a LEFT JOIN drp_warehouse w ON a.id = w.top_agency " +
			"LEFT JOIN drp_declare_record dr ON w.id = dr.warehouse AND dr.time = ? " + declare +
			"AND w.type = 'top_agency' " +
			"WHERE a.level = 1 and a.deleted_at is null " + channelWhere + " GROUP BY a.id " +
			"UNION ALL " +
			"SELECT a2.id top_agency, 0 second_agency, a2.name, 1 next, SUM(inventory) inventory, " +
			"SUM(old_machine) old_machine " +
			"FROM agency a2 LEFT JOIN agency a ON a2.id = a.pid AND a.deleted_at IS NULL " +
			"LEFT JOIN drp_warehouse w ON a.id = w.second_agency " +
			"LEFT JOIN drp_declare_record dr ON w.id = dr.warehouse AND dr.time = ? " + declare +
			"AND w.type = 'second_agency' " +
			"WHERE a2.`level` = 1 AND a2.deleted_at is null " + channelWhere + " GROUP BY a2.id " +
			"UNION ALL " +
			"SELECT a.id top_agency, 0 second_agency, a.name, 1 next, SUM(inventory) inventory, " +
			"SUM(old_machine) old_machine " +
			"FROM agency a LEFT JOIN endpoint e ON a.id = e.top_agency AND e.`status` = 1 " +
			"LEFT JOIN drp_warehouse w ON e.id = w.endpoint LEFT JOIN drp_declare_record dr ON w.id = dr.warehouse " +
			"AND dr.time = ?  " + declare + " AND w.type = 'endpoint' " +
			"WHERE a.level = 1 and a.deleted_at is null " + channelWhere +
			"GROUP BY a.id) b "
		d.db.Raw(sql1+where+" GROUP BY b.top_agency ORDER BY "+dataType+" "+Sort+" limit ?,?",
			date, date, date, offset, count).Find(&data)
	} else if secondAgency == 0 { // 某个总代自有仓库和这个总代的直营汇总和总代下的所有二代
		sql1 := "SELECT a.id top_agency, 0 second_agency, CONCAT_WS('',a.`name`,'自有仓库') `name`, " +
			"0 as next, IFNULL(SUM(inventory),-1) inventory, IFNULL(SUM(old_machine), -1) old_machine FROM agency a " +
			"LEFT JOIN drp_warehouse w ON a.id = w.top_agency " +
			"LEFT JOIN drp_declare_record dr ON w.id = dr.warehouse AND w.type = 'top_agency' " +
			"AND dr.time = ? " + declare +
			"WHERE a.id = ? "
		d.db.Raw(sql1, date, topAgency).First(&data2)
		// 二代自有仓库union all 二代底下终端汇总
		sql2 := "SELECT a.top_agency, a.second_agency, a.`name`, 1 next, IFNULL(SUM(inventory),-1) inventory, " +
			"IFNULL(SUM(old_machine), -1) old_machine FROM (" +
			"SELECT a.pid top_agency, a.id second_agency, a.name, 1 next, SUM(inventory) inventory, " +
			"SUM(old_machine) old_machine " +
			"FROM agency a LEFT JOIN drp_warehouse w ON a.id = w.second_agency " +
			"LEFT JOIN drp_declare_record dr ON w.id = dr.warehouse AND dr.time = ? " + declare +
			"AND w.type = 'second_agency' WHERE a.`level` = 2 AND a.deleted_at is null AND a.pid = ? GROUP BY a.id " +
			"UNION ALL " +
			"SELECT a.pid top_agency, a.id second_agency, a.name, 1 next, SUM(inventory) inventory, " +
			"SUM(old_machine) old_machine " +
			"FROM agency a LEFT JOIN endpoint e ON a.id = e.second_agency AND e.`status` = 1 " +
			"LEFT JOIN drp_warehouse w ON e.id = w.endpoint LEFT JOIN drp_declare_record dr ON w.id = dr.warehouse " +
			"AND dr.time = ? " + declare + " AND w.type = 'endpoint' " +
			"WHERE a.level = 2 and a.deleted_at is null AND a.pid = ? GROUP BY a.id) a GROUP BY a.second_agency "

		sql3 := "SELECT a.id top_agency, -1 second_agency, CONCAT_WS('',a.`name`,'直营汇总') `name`, 1 as next, " +
			"IFNULL(SUM(inventory), -1) inventory, IFNULL(SUM(old_machine), -1) old_machine FROM endpoint e " +
			"LEFT JOIN drp_warehouse w ON e.id = w.endpoint " +
			"LEFT JOIN agency a ON e.top_agency = a.id " +
			"LEFT JOIN drp_declare_record dr ON w.id = dr.warehouse AND dr.time = ? AND w.type = 'endpoint' " +
			declare +
			"WHERE e.top_agency = ? " +
			"AND e.second_agency = ? AND e.status = 1 GROUP BY a.id "
		sql := "SELECT * FROM (" + sql2 + " union ALL " + sql3 + ") a where 1 = 1 "
		if regionName != "" {
			sql += " AND a.name = '" + regionName + "' "
		}
		sql += " ORDER BY " + dataType + " " + Sort + " limit ?,?"
		d.db.Raw(sql, date, topAgency, date, topAgency, date, topAgency, 0, offset, count).Find(&data)
	} else if secondAgency == -1 { // 总代下的直营汇总下的终端
		where := " e.top_agency = ? and e.second_agency = 0 and e.status = 1 "
		if regionName != "" {
			where += " AND e.name = '" + regionName + "' "
		}
		//CONVERT(a.`name` USING GBK) ASC
		d.db.Table("endpoint e").
			Joins("LEFT JOIN drp_warehouse w ON e.id = w.endpoint ").
			Joins("LEFT JOIN drp_declare_record dr ON w.id = dr.warehouse AND dr.time = ? "+declare, date).
			Where(where, topAgency).
			Select("e.top_agency, e.second_agency, e.id endpoint, e.name, 0 next, " +
				"IFNULL(SUM(inventory),-1) inventory, IFNULL(SUM(old_machine), -1) old_machine").
			Offset(offset).Limit(count).Group("e.id").
			Order(dataType + " " + Sort).Find(&data)
	} else { // 二代自有仓库和底下终端
		sql1 := "SELECT a.pid top_agency, a.id second_agency, CONCAT_WS('',a.`name`,'自有仓库') `name`, " +
			"0 as next, IFNULL(SUM(inventory),-1) inventory,IFNULL(SUM(old_machine), -1) old_machine FROM agency a " +
			"LEFT JOIN drp_warehouse w ON a.id = w.second_agency " +
			"LEFT JOIN drp_declare_record dr ON w.id = dr.warehouse " +
			"AND w.type = 'second_agency' AND dr.time = ? " + declare +
			"WHERE a.pid = ? AND a.id = ? "
		d.db.Raw(sql1, date, topAgency, secondAgency).First(&data2)

		where := " e.top_agency = ? and e.second_agency = ? AND e.status = 1 "
		if regionName != "" {
			where += " AND e.name = '" + regionName + "' "
		}
		//CONVERT(a.`name` USING GBK) ASC
		d.db.Table("endpoint e").
			Joins("LEFT JOIN drp_warehouse w ON e.id = w.endpoint").
			Joins("LEFT JOIN drp_declare_record dr ON w.id = dr.warehouse AND dr.time = ? "+declare, date).
			Where(where, topAgency, secondAgency).
			Select("e.top_agency, e.second_agency, e.id endpoint, e.name, 0 next, " +
				"IFNULL(SUM(inventory),-1) inventory, IFNULL(SUM(old_machine), -1) old_machine").
			Offset(offset).Limit(count).Group("e.id").
			Order(dataType + " " + Sort).Find(&data)
	}

	OtherInfo := make([]DeclareListRet, 0)
	for _, v := range data {
		declareListByModel := make([]DeclareListByModel, 0)
		// 总代详细分类
		if topAgency == 0 {
			sql := "SELECT a.model_name, SUM(a.inventory) inventory, SUM(a.old_machine) old_machine FROM (" +
				"SELECT dr.model_name, SUM(dr.inventory) inventory, SUM(dr.old_machine) old_machine " +
				"FROM agency a RIGHT JOIN drp_warehouse w ON a.id = w.top_agency " +
				"RIGHT JOIN drp_declare_record dr ON w.id = dr.warehouse AND dr.time = ? " + declare +
				"AND w.type = 'top_agency' WHERE a.id = ? AND a.level = 1 AND a.deleted_at is null " +
				"GROUP BY dr.model_name " +
				"UNION ALL " +
				"SELECT dr.model_name, SUM(dr.inventory) inventory, SUM(dr.old_machine) old_machine " +
				"FROM agency a " +
				"RIGHT JOIN drp_warehouse w ON a.id = w.second_agency " +
				"RIGHT JOIN drp_declare_record dr ON w.id = dr.warehouse AND dr.time = ? " + declare +
				"AND w.type = 'second_agency' WHERE a.`level` = 2 AND a.deleted_at is null and a.pid = ? " +
				"GROUP BY dr.model_name " +
				"UNION ALL " +
				"SELECT dr.model_name, SUM(dr.inventory) inventory, SUM(dr.old_machine) old_machine " +
				"FROM agency a RIGHT JOIN endpoint e ON a.id = e.top_agency " +
				"RIGHT JOIN drp_warehouse w ON e.id = w.endpoint " +
				"RIGHT JOIN drp_declare_record dr ON w.id = dr.warehouse " +
				"AND dr.time = ? " + declare + " AND w.type = 'endpoint' WHERE a.id = ? and e.status = 1 " +
				"GROUP BY dr.model_name) a GROUP BY a.model_name"
			d.db.Raw(sql, date, v.TopAgency, date, v.TopAgency, date, v.TopAgency).
				Find(&declareListByModel)
		} else if secondAgency == 0 && v.SecondAgency == -1 { // 直营汇总详细分类
			d.db.Table("drp_declare_record dr").
				Joins("left join drp_warehouse w on dr.warehouse = w.id").
				Joins("left join endpoint e on w.endpoint = e.id").
				Where("e.top_agency = ? and e.second_agency = ? AND dr.time = ? "+
					"AND e.status = 1 "+declare,
					v.TopAgency, 0, date).
				Select("dr.model_name, SUM(dr.inventory) inventory, SUM(dr.old_machine) old_machine").
				Group("dr.model_name").
				Find(&declareListByModel)
		} else if secondAgency == 0 && v.SecondAgency > 0 { // 总代下各二代详细信息
			sql := "SELECT dr.model_name, SUM(dr.inventory) inventory, SUM(dr.old_machine) old_machine " +
				"FROM drp_declare_record dr " +
				"right join drp_warehouse w on dr.warehouse = w.id " +
				"WHERE w.second_agency = ? AND dr.time = ? " + declare + " GROUP BY dr.model_name"
			sql2 := "SELECT dr.model_name, SUM(dr.inventory) inventory, SUM(dr.old_machine) old_machine " +
				"FROM drp_declare_record dr " +
				"right join drp_warehouse w on dr.warehouse = w.id " +
				"RIGHT JOIN endpoint e ON w.endpoint = e.id " +
				"WHERE e.second_agency = ? AND dr.time = ? AND e.`status` = 1 " + declare + " GROUP BY dr.model_name"
			d.db.Raw("SELECT a.model_name, SUM(a.inventory) inventory, SUM(a.old_machine) old_machine FROM "+
				"("+sql+" UNION ALL "+sql2+") a GROUP BY a.model_name",
				v.SecondAgency, date, v.SecondAgency, date).Find(&declareListByModel)
		} else if secondAgency == -1 { // 直营底下终端
			d.db.Table("drp_declare_record dr").
				Joins("right join drp_warehouse w on dr.warehouse = w.id").
				Joins("right join endpoint e on w.endpoint = e.id").
				Where("w.endpoint = ? AND dr.time = ? AND e.status = 1 "+declare,
					v.Endpoint, date).
				Select("dr.model_name, SUM(dr.inventory) inventory, SUM(dr.old_machine) old_machine").
				Group("dr.model_name").
				Find(&declareListByModel)
		} else { // 二代底下终端
			d.db.Table("drp_declare_record dr").
				Joins("right join drp_warehouse w on dr.warehouse = w.id").
				Joins("right join endpoint e on w.endpoint = e.id").
				Where("w.endpoint = ? AND dr.time = ? AND e.status = 1 "+declare,
					v.Endpoint, date).
				Select("dr.model_name, SUM(dr.inventory) inventory, SUM(dr.old_machine) old_machine").
				Group("dr.model_name").
				Find(&declareListByModel)
		}

		OtherInfo = append(OtherInfo, DeclareListRet{
			TopAgency:    v.TopAgency,
			SecondAgency: v.SecondAgency,
			Endpoint:     v.Endpoint,
			Name:         v.Name,
			Time:         date,
			Inventory:    v.Inventory,
			OldMachine:   v.OldMachine,
			Next:         v.Next,
			List:         declareListByModel,
		})
	}

	OwnInfo := make([]DeclareListRet, 0)
	if data2.TopAgency != 0 {
		declareListByModel := make([]DeclareListByModel, 0)
		// 总代详细分类
		if data2.SecondAgency == 0 {
			d.db.Table("drp_declare_record dr").
				Joins("right join drp_warehouse w on dr.warehouse = w.id").
				Where("w.top_agency = ? and w.second_agency = 0 AND dr.time = ? and w.type = 'top_agency' "+declare,
					data2.TopAgency, date).
				Select("dr.model_name, SUM(dr.inventory) inventory, SUM(dr.old_machine) old_machine").
				Group("dr.model_name").
				Find(&declareListByModel)
		} else {
			d.db.Table("drp_declare_record dr").
				Joins("right join drp_warehouse w on dr.warehouse = w.id").
				Where("w.second_agency = ? AND dr.time = ? and w.type='second_agency' "+declare,
					data2.SecondAgency, date).
				Select("dr.model_name, SUM(dr.inventory) inventory, SUM(dr.old_machine) old_machine").
				Group("dr.model_name").
				Find(&declareListByModel)
		}

		OwnInfo = append(OwnInfo, DeclareListRet{
			TopAgency:    data2.TopAgency,
			SecondAgency: data2.SecondAgency,
			Endpoint:     data2.Endpoint,
			Name:         data2.Name,
			Time:         date,
			Inventory:    data2.Inventory,
			OldMachine:   data2.OldMachine,
			Next:         data2.Next,
			List:         declareListByModel,
		})
	}

	//if len(OwnInfo) > 0 {
	//	return OwnInfo[0], OtherInfo
	//} else {
	//	return nil, OtherInfo
	//}

	marshal, err := json.Marshal(OtherInfo)
	if err != nil {
		return nil, nil
	}

	fmt.Println("AAAAAA")
	fmt.Println(string(marshal))
	fmt.Println("BBBBBB")

	return nil, nil
}

func (d *DeclareDaoImpl) GetDeclareList(req *GetDeclareParam) (interface{}, interface{}) {
	// 设定默认参数,开发完成需要删除
	req.DeclareType = 1

	data := make([]DeclareList, 0)
	data2 := DeclareList{}

	//所有总代信息
	if req.TopAgency == 0 {
		where := ""
		if len(req.RegionName) > 0 {
			where += " and t.name = '" + req.RegionName + "' "
		}

		d.db.Raw("SELECT t.top_agency,0 second_agency,t.name,1 next,IFNULL(SUM(inventory),-1) inventory,"+
			"IFNULL(SUM(old_machine),-1) old_machine FROM (? UNION ALL ? UNION ALL ?) as t "+where+" GROUP BY t.top_agency ORDER BY "+req.DataType+" "+req.Sort+" limit ?,?",
			d.GetTopAgency(req),
			d.GetChildAgency(req),
			d.GetEndpoint(req),
			(req.PageNum-1)*req.PageSize,
			req.PageSize,
		).Scan(&data)
	} else if req.SecondAgency == 0 { // 某个总代自有仓库和这个总代的直营汇总和总代下的所有二代
		// 代理表拼接仓库表
		query := d.db.Select("agency.id top_agency,0 second_agency,CONCAT_WS('',agency.`name`,'自有仓库') `name`,1 next,IFNULL(SUM(inventory),-1) inventory,IFNULL(SUM(old_machine), -1) old_machine").
			Model(&model.Agency{}).
			Joins("left join drp_warehouse on agency.id = drp_warehouse.top_agency")

		// 拼接申报记录表
		if req.DeclareType == 0 || req.DeclareType == 1 {
			query = query.Joins("left join drp_declare_record on drp_warehouse.id = drp_declare_record.warehouse and drp_declare_record.time = ? and drp_declare_record.declare = ?", req.Date, req.DeclareType)
		} else {
			query = query.Joins("left join drp_declare_record on drp_warehouse.id = drp_declare_record.warehouse and drp_declare_record.time = ?", req.Date)
		}

		query.Where("agency.id = ?", req.TopAgency).First(&data2)

		sql := "SELECT * FROM ( ? union ALL ? ) t"
		if req.RegionName != "" {
			sql += " where t.name = '" + req.RegionName + "'"
		}

		sql += " order by " + req.DataType + " " + req.Sort + " limit ?,?"

		d.db.Raw(sql, d.GetSecondAgency(req), d.GetSecondAgencyEndpoint(req), (req.PageNum-1)*req.PageSize, req.PageSize).Find(&data)
	} else if req.SecondAgency == -1 {
		query := d.db.Select("endpoint.top_agency,endpoint.second_agency,endpoint.id endpoint,endpoint.name,0 next,IFNULL(SUM(inventory),-1) inventory,IFNULL(SUM(old_machine), -1) old_machine").
			Model(&model.Endpoint{}).
			Joins("left join drp_warehouse on endpoint.id = drp_warehouse.endpoint")

		// 拼接申报记录表
		if req.DeclareType == 0 || req.DeclareType == 1 {
			query = query.Joins("left join drp_declare_record on drp_warehouse.id = drp_declare_record.warehouse and drp_declare_record.time = ? and drp_declare_record.declare = ?", req.Date, req.DeclareType)
		} else {
			query = query.Joins("left join drp_declare_record on drp_warehouse.id = drp_declare_record.warehouse and drp_declare_record.time = ?", req.Date)
		}

		if req.RegionName != "" {
			query = query.Where("endpoint.name = ?", req.RegionName)
		}

		query.Where("endpoint.top_agency = ? and endpoint.second_agency = 0 and endpoint.status = 1", req.TopAgency).
			Offset((req.PageNum - 1) * req.PageSize).Limit(req.PageSize).
			Group("endpoint.id").
			Order(req.DataType + " " + req.Sort).
			Find(&data)
	} else {
		// 代理表拼接仓库表
		query1 := d.db.Select("agency.pid top_agency,agency.id second_agency,CONCAT_WS('',agency.`name`,'自有仓库') `name`,0 next,IFNULL(SUM(inventory),-1) inventory,IFNULL(SUM(old_machine), -1) old_machine").
			Model(&model.Agency{}).
			Joins("left join drp_warehouse on agency.id = drp_warehouse.second_agency and drp_warehouse.type = 'second_agency'")

		// 拼接申报记录表
		if req.DeclareType == 0 || req.DeclareType == 1 {
			query1 = query1.Joins("left join drp_declare_record on drp_warehouse.id = drp_declare_record.warehouse and drp_declare_record.time = ? and drp_declare_record.declare = ?", req.Date, req.DeclareType)
		} else {
			query1 = query1.Joins("left join drp_declare_record on drp_warehouse.id = drp_declare_record.warehouse and drp_declare_record.time = ?", req.Date)
		}

		query1.Where("agency.pid = ? and agency.id = ?", req.TopAgency, req.SecondAgency).First(&data2)

		query2 := d.db.Select("endpoint.top_agency,endpoint.second_agency,endpoint.id endpoint,endpoint.name,0 next,IFNULL(SUM(inventory),-1) inventory,IFNULL(SUM(old_machine), -1) old_machine").
			Model(&model.Endpoint{}).
			Joins("left join drp_warehouse on endpoint.id = drp_warehouse.endpoint")

		// 拼接申报记录表
		if req.DeclareType == 0 || req.DeclareType == 1 {
			query2 = query2.Joins("left join drp_declare_record on drp_warehouse.id = drp_declare_record.warehouse and drp_declare_record.time = ? and drp_declare_record.declare = ?", req.Date, req.DeclareType)
		} else {
			query2 = query2.Joins("left join drp_declare_record on drp_warehouse.id = drp_declare_record.warehouse and drp_declare_record.time = ?", req.Date)
		}

		if req.RegionName != "" {
			query2 = query2.Where("endpoint.name = ?", req.RegionName)
		}

		query2.Where("endpoint.top_agency = ? and endpoint.second_agency = ? and endpoint.status = 1", req.TopAgency, req.SecondAgency).
			Offset((req.PageNum - 1) * req.PageSize).Limit(req.PageSize).
			Group("endpoint.id").
			Order(req.DataType + " " + req.Sort).
			Find(&data)
	}

	otherInfo := make([]DeclareListRet, 0)
	for _, v := range data {
		declareListByModel := make([]DeclareListByModel, 0)

		switch {
		case req.TopAgency == 0:
			query1 := d.db.Select("drp_declare_record.model_name,SUM(drp_declare_record.inventory) inventory,SUM(drp_declare_record.old_machine) old_machine").
				Model(&model.Agency{}).
				Joins("right join drp_warehouse on agency.id = drp_warehouse.top_agency and drp_warehouse.type = 'top_agency'")

			// 拼接申报记录表
			if req.DeclareType == 0 || req.DeclareType == 1 {
				query1 = query1.Joins("right join drp_declare_record on drp_warehouse.id = drp_declare_record.warehouse and drp_declare_record.time = ? and drp_declare_record.declare = ?", req.Date, req.DeclareType)
			} else {
				query1 = query1.Joins("right join drp_declare_record on drp_warehouse.id = drp_declare_record.warehouse and drp_declare_record.time = ?", req.Date)
			}

			query1 = query1.Where("agency.id = ? and agency.level = 1 and agency.deleted_at is null", v.TopAgency).Group("drp_declare_record.model_name")

			query2 := d.db.Select("drp_declare_record.model_name,SUM(drp_declare_record.inventory) inventory,SUM(drp_declare_record.old_machine) old_machine").
				Model(&model.Agency{}).
				Joins("right join drp_warehouse on agency.id = drp_warehouse.second_agency and drp_warehouse.type = 'second_agency'")

			// 拼接申报记录表
			if req.DeclareType == 0 || req.DeclareType == 1 {
				query2 = query2.Joins("right join drp_declare_record on drp_warehouse.id = drp_declare_record.warehouse and drp_declare_record.time = ? and drp_declare_record.declare = ?", req.Date, req.DeclareType)
			} else {
				query2 = query2.Joins("right join drp_declare_record on drp_warehouse.id = drp_declare_record.warehouse and drp_declare_record.time = ?", req.Date)
			}

			query2 = query2.Where("agency.pid = ? and agency.level = 2 and agency.deleted_at is null", v.TopAgency).Group("drp_declare_record.model_name")

			query3 := d.db.Select("drp_declare_record.model_name,SUM(drp_declare_record.inventory) inventory,SUM(drp_declare_record.old_machine) old_machine").
				Model(&model.Agency{}).
				Joins("right join endpoint on agency.id = endpoint.top_agency").
				Joins("right join drp_warehouse on endpoint.id = drp_warehouse.endpoint and drp_warehouse.type = 'endpoint'")

			// 拼接申报记录表
			if req.DeclareType == 0 || req.DeclareType == 1 {
				query3 = query3.Joins("right join drp_declare_record on drp_warehouse.id = drp_declare_record.warehouse and drp_declare_record.time = ? and drp_declare_record.declare = ?", req.Date, req.DeclareType)
			} else {
				query3 = query3.Joins("right join drp_declare_record on drp_warehouse.id = drp_declare_record.warehouse and drp_declare_record.time = ?", req.Date)
			}

			query3 = query3.Where("agency.id = ? and endpoint.status = 1", v.TopAgency).Group("drp_declare_record.model_name")

			d.db.Raw("SELECT t.model_name, SUM(t.inventory) inventory, SUM(t.old_machine) old_machine FROM (? UNION ALL ? UNION ALL ?) t GROUP BY t.model_name",
				query1, query2, query3).
				Find(&declareListByModel)
		case req.SecondAgency == 0 && v.SecondAgency == -1:
			query := d.db.Select("drp_declare_record.model_name,SUM(drp_declare_record.inventory) inventory,SUM(drp_declare_record.old_machine) old_machine").
				Model(&model.DrpDeclareRecord{}).
				Joins("right join drp_warehouse on drp_declare_record.warehouse = drp_warehouse.id").
				Joins("right join endpoint on drp_warehouse.endpoint = endpoint.id")

			if req.DeclareType == 0 || req.DeclareType == 1 {
				query = query.Where("drp_declare_record.time = ? and drp_declare_record.declare = ?", req.Date, req.DeclareType)
			} else {
				query = query.Where("drp_declare_record.time = ?", req.Date)
			}

			query.Where("endpoint.top_agency = ? and endpoint.second_agency = ? and endpoint.status = 1", v.TopAgency, 0).
				Group("drp_declare_record.model_name").
				Find(&declareListByModel)
		case req.SecondAgency == 0 && v.SecondAgency > 0:
			query1 := d.db.Select("drp_declare_record.model_name,SUM(drp_declare_record.inventory) inventory,SUM(drp_declare_record.old_machine) old_machine").
				Model(&model.DrpDeclareRecord{}).
				Joins("right join drp_warehouse on drp_declare_record.warehouse = drp_warehouse.id").
				Where("drp_warehouse.second_agency = ?")

			if req.DeclareType == 0 || req.DeclareType == 1 {
				query1 = query1.Where("drp_declare_record.time = ? and drp_declare_record.declare = ?", req.Date, req.DeclareType)
			} else {
				query1 = query1.Where("drp_declare_record.time = ?", req.Date)
			}

			query1 = query1.Where("drp_warehouse.second_agency = ?", v.SecondAgency).Group("drp_declare_record.model_name")

			query2 := d.db.Select("drp_declare_record.model_name,SUM(drp_declare_record.inventory) inventory,SUM(drp_declare_record.old_machine) old_machine").
				Model(&model.DrpDeclareRecord{}).
				Joins("right join drp_warehouse on drp_declare_record.warehouse = drp_warehouse.id").
				Joins("right join endpoint on drp_warehouse.endpoint = endpoint.id").
				Where("drp_warehouse.second_agency = ?")

			if req.DeclareType == 0 || req.DeclareType == 1 {
				query2 = query2.Where("drp_declare_record.time = ? and drp_declare_record.declare = ?", req.Date, req.DeclareType)
			} else {
				query2 = query2.Where("drp_declare_record.time = ?", req.Date)
			}

			query2 = query2.Where("endpoint.second_agency = ? and endpoint.status = 1", v.SecondAgency).Group("drp_declare_record.model_name")

			d.db.Raw("SELECT t.model_name, SUM(t.inventory) inventory, SUM(t.old_machine) old_machine FROM  ? UNION ALL ? )", query1, query2).
				Group("t.model_name").
				Find(&declareListByModel)
		default:
			query := d.db.Select("drp_declare_record.model_name,SUM(drp_declare_record.inventory) inventory,SUM(drp_declare_record.old_machine) old_machine").
				Model(&model.DrpDeclareRecord{}).
				Joins("right join drp_warehouse on drp_declare_record.warehouse = drp_warehouse.id").
				Joins("right join endpoint on drp_warehouse.endpoint = endpoint.id")

			if req.DeclareType == 0 || req.DeclareType == 1 {
				query = query.Where("drp_declare_record.time = ? and drp_declare_record.declare = ?", req.Date, req.DeclareType)
			} else {
				query = query.Where("drp_declare_record.time = ?", req.Date)
			}

			query.Where("drp_warehouse.endpoint = ? and endpoint.status = 1", v.Endpoint).Group("drp_declare_record.model_name").Find(&declareListByModel)
		}

		otherInfo = append(otherInfo, DeclareListRet{
			TopAgency:    v.TopAgency,
			SecondAgency: v.SecondAgency,
			Endpoint:     v.Endpoint,
			Name:         v.Name,
			Time:         req.Date,
			Inventory:    v.Inventory,
			OldMachine:   v.OldMachine,
			Next:         v.Next,
			List:         declareListByModel,
		})
	}

	ownInfo := make([]DeclareListRet, 0)
	if data2.TopAgency != 0 {
		declareListByModel := make([]DeclareListByModel, 0)

		query := d.db.Select("drp_declare_record.model_name,SUM(drp_declare_record.inventory) inventory,SUM(drp_declare_record.old_machine) old_machine").
			Model(&model.DrpDeclareRecord{}).
			Joins("right join drp_warehouse on drp_declare_record.warehouse = drp_warehouse.id")

		// 拼接申报记录表
		if req.DeclareType == 0 || req.DeclareType == 1 {
			query = query.Where("drp_declare_record.time = ? and drp_declare_record.declare = ?", req.Date, req.DeclareType)
		} else {
			query = query.Where("drp_declare_record.time = ?", req.Date)
		}

		// 总代详细分类
		if data2.SecondAgency == 0 {
			query = query.Where("drp_warehouse.top_agency = ? and drp_warehouse.second_agency = 0 and drp_warehouse.type = 'top_agency'", data2.TopAgency)
		} else {
			query = query.Where("drp_warehouse.second_agency = ? and drp_warehouse.type = 'second_agency'", data2.SecondAgency)
		}

		query.Group("drp_declare_record.model_name").Find(&declareListByModel)

		ownInfo = append(ownInfo, DeclareListRet{
			TopAgency:    data2.TopAgency,
			SecondAgency: data2.SecondAgency,
			Endpoint:     data2.Endpoint,
			Name:         data2.Name,
			Time:         req.Date,
			Inventory:    data2.Inventory,
			OldMachine:   data2.OldMachine,
			Next:         data2.Next,
			List:         declareListByModel,
		})
	}

	marshal, err := json.Marshal(otherInfo)
	if err != nil {
		return nil, nil
	}

	fmt.Println("AAAAAA")
	fmt.Println(string(marshal))
	fmt.Println("BBBBBB")

	return nil, nil
}

func (d *DeclareDaoImpl) DeclareTimeLimit() (int, int) {
	var declare struct {
		Value string `gorm:"column:value" json:"value"`
	}

	d.db.Table("config").Where("`key` = ?", "declare_time").Select("value").First(&declare)
	if declare.Value == "" {
		return 26, 4
	}

	ret := strings.Split(declare.Value, "-")
	if len(ret) < 2 {
		return 26, 4
	}

	start, _ := strconv.Atoi(ret[0])
	end, _ := strconv.Atoi(ret[1])
	if start > 29 {
		start = 26
	}

	return start, end
}

func (d *DeclareDaoImpl) GetTopAgency(req *GetDeclareParam) *gorm.DB {
	// 代理表拼接仓库表
	query := d.db.Select("agency.id top_agency,0 second_agency,agency.name,1 next,sum(inventory) inventory,sum(old_machine) old_machine").
		Model(&model.Agency{}).
		Joins("left join drp_warehouse on agency.id = drp_warehouse.top_agency and drp_warehouse.type = 'top_agency'")

	// 拼接申报记录表
	if req.DeclareType == 0 || req.DeclareType == 1 {
		query = query.Joins("left join drp_declare_record on drp_warehouse.id = drp_declare_record.warehouse and drp_declare_record.time = ? and drp_declare_record.declare = ?", req.Date, req.DeclareType)
	} else {
		query = query.Joins("left join drp_declare_record on drp_warehouse.id = drp_declare_record.warehouse and drp_declare_record.time = ?", req.Date)
	}

	if len(req.Channel) > 0 {
		query = query.Where("channel = '" + req.Channel + "'")
	}

	return query.Where("agency.level = 1 and agency.deleted_at is null").Group("agency.id")
}

func (d *DeclareDaoImpl) GetChildAgency(req *GetDeclareParam) *gorm.DB {
	// 代理表拼接仓库表
	query := d.db.Select("agency.id top_agency,0 second_agency,agency.name,1 next,sum(inventory) inventory,sum(old_machine) old_machine").
		Model(&model.Agency{}).
		Joins("left join agency as a on agency.id = a.pid and a.deleted_at is null").
		Joins("left join drp_warehouse on a.id = drp_warehouse.second_agency and drp_warehouse.type = 'second_agency'")

	// 拼接申报记录表
	if req.DeclareType == 0 || req.DeclareType == 1 {
		query = query.Joins("left join drp_declare_record on drp_warehouse.id = drp_declare_record.warehouse and drp_declare_record.time = ? and drp_declare_record.declare = ?", req.Date, req.DeclareType)
	} else {
		query = query.Joins("left join drp_declare_record on drp_warehouse.id = drp_declare_record.warehouse and drp_declare_record.time = ?", req.Date)
	}

	if len(req.Channel) > 0 {
		query = query.Where("channel = '" + req.Channel + "'")
	}

	return query.Where("agency.level = 1 and agency.deleted_at is null").Group("agency.id")
}

func (d *DeclareDaoImpl) GetSecondAgency(req *GetDeclareParam) *gorm.DB {
	// 代理表拼接仓库表
	query1 := d.db.Select("agency.pid top_agency,agency.id second_agency,agency.name,1 next,sum(inventory) inventory,sum(old_machine) old_machine").
		Model(&model.Agency{}).
		Joins("left join drp_warehouse on agency.id = drp_warehouse.second_agency and drp_warehouse.type = 'second_agency'")

	// 拼接申报记录表
	if req.DeclareType == 0 || req.DeclareType == 1 {
		query1 = query1.Joins("left join drp_declare_record on drp_warehouse.id = drp_declare_record.warehouse and drp_declare_record.time = ? and drp_declare_record.declare = ?", req.Date, req.DeclareType)
	} else {
		query1 = query1.Joins("left join drp_declare_record on drp_warehouse.id = drp_declare_record.warehouse and drp_declare_record.time = ?", req.Date)
	}

	if len(req.Channel) > 0 {
		query1 = query1.Where("channel = '" + req.Channel + "'")
	}

	query1 = query1.Where("agency.level = 2 and agency.deleted_at is null and agency.pid = ?", req.TopAgency).Group("agency.id")

	query2 := d.db.Select("agency.pid top_agency,agency.id second_agency,agency.name,1 next,sum(inventory) inventory,sum(old_machine) old_machine").
		Model(&model.Agency{}).
		Joins("left join endpoint on agency.id = endpoint.top_agency and endpoint.status = 1").
		Joins("left join drp_warehouse on endpoint.id = drp_warehouse.endpoint and drp_warehouse.type = 'endpoint'")

	// 拼接申报记录表
	if req.DeclareType == 0 || req.DeclareType == 1 {
		query2 = query2.Joins("left join drp_declare_record on drp_warehouse.id = drp_declare_record.warehouse and drp_declare_record.time = ? and drp_declare_record.declare = ?", req.Date, req.DeclareType)
	} else {
		query2 = query2.Joins("left join drp_declare_record on drp_warehouse.id = drp_declare_record.warehouse and drp_declare_record.time = ?", req.Date)
	}

	query2 = query2.Where("agency.level = 2 and agency.deleted_at is null and agency.pid = ?", req.TopAgency).Group("agency.id")

	return d.db.Raw("SELECT t.top_agency, t.second_agency, t.name, 1 next, IFNULL(SUM(inventory),-1) inventory, "+
		"IFNULL(SUM(old_machine), -1) old_machine FROM (? UNION ALL ?) as t GROUP BY t.second_agency",
		query1,
		query2,
	)
}

func (d *DeclareDaoImpl) GetEndpoint(req *GetDeclareParam) *gorm.DB {
	// 代理表拼接仓库表
	query := d.db.Select("agency.id top_agency,0 second_agency,agency.name,1 next,sum(inventory) inventory,sum(old_machine) old_machine").
		Model(&model.Agency{}).
		Joins("left join endpoint on agency.id = endpoint.top_agency and endpoint.status = 1").
		Joins("left join drp_warehouse on endpoint.id = drp_warehouse.endpoint and drp_warehouse.type = 'endpoint'")

	// 拼接申报记录表
	if req.DeclareType == 0 || req.DeclareType == 1 {
		query = query.Joins("left join drp_declare_record on drp_warehouse.id = drp_declare_record.warehouse and drp_declare_record.time = ? and drp_declare_record.declare = ?", req.Date, req.DeclareType)
	} else {
		query = query.Joins("left join drp_declare_record on drp_warehouse.id = drp_declare_record.warehouse and drp_declare_record.time = ?", req.Date)
	}

	if len(req.Channel) > 0 {
		query = query.Where("channel = '" + req.Channel + "'")
	}

	return query.Where("agency.level = 1 and agency.deleted_at is null").Group("agency.id")
}

func (d *DeclareDaoImpl) GetSecondAgencyEndpoint(req *GetDeclareParam) *gorm.DB {
	// 代理表拼接仓库表
	query := d.db.Select("agency.id top_agency,-1 second_agency,CONCAT_WS('',agency.`name`,'直营汇总') `name`,1 next,IFNULL(SUM(inventory), -1) inventory,IFNULL(SUM(old_machine), -1) old_machine").
		Model(&model.Endpoint{}).
		Joins("left join drp_warehouse on endpoint.id = drp_warehouse.endpoint and drp_warehouse.type = 'endpoint'").
		Joins("left join agency on endpoint.top_agency = agency.id")

	// 拼接申报记录表
	if req.DeclareType == 0 || req.DeclareType == 1 {
		query = query.Joins("left join drp_declare_record on drp_warehouse.id = drp_declare_record.warehouse and drp_declare_record.time = ? and drp_declare_record.declare = ?", req.Date, req.DeclareType)
	} else {
		query = query.Joins("left join drp_declare_record on drp_warehouse.id = drp_declare_record.warehouse and drp_declare_record.time = ?", req.Date)
	}

	if len(req.Channel) > 0 {
		query = query.Where("channel = '" + req.Channel + "'")
	}

	return query.Where("endpoint.top_agency = ? and endpoint.second_agency = ? and endpoint.status = 1", req.TopAgency, 0).Group("agency.id")
}
