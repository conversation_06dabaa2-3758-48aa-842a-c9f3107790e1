package model

import "marketing/internal/pkg/types"

// PromotionalProduct represents a promotional product in the database
type PromotionalProduct struct {
	ID                    int              `json:"id" gorm:"primaryKey"`
	PolicyID              int              `json:"policy_id" gorm:"column:policy_id"`                               // 政策ID
	Name                  string           `json:"name" gorm:"column:name"`                                         // 产品名称
	Norm                  string           `json:"norm" gorm:"column:norm"`                                         // 产品规格
	Unit                  string           `json:"unit" gorm:"column:unit"`                                         // 产品单位
	IncludeTaxPrice       float64          `json:"include_tax_price" gorm:"column:include_tax_price"`               // 含税价格
	ExcludeTaxPrice       float64          `json:"exclude_tax_price" gorm:"column:exclude_tax_price"`               // 不含税价格
	ReimbursementPrice    float64          `json:"reimbursement_price" gorm:"column:reimbursement_price"`           // 核销价格
	IncludeTaxAccountInfo string           `json:"include_tax_account_info" gorm:"column:include_tax_account_info"` // 含税账户信息
	ExcludeTaxAccountInfo string           `json:"exclude_tax_account_info" gorm:"column:exclude_tax_account_info"` // 不含税账户信息
	CommunicationLetter   string           `json:"communication_letter" gorm:"column:communication_letter"`         // 售后交流函
	Preview               string           `json:"preview" gorm:"column:preview"`                                   // 预览图
	CreatedAt             types.CustomTime `json:"created_at" gorm:"column:created_at"`                             // 创建时间
}

func (*PromotionalProduct) TableName() string {
	return "reimbursement_promotional_products"
}
