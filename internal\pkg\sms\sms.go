package sms

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"marketing/internal/pkg/log"
	"math/rand"
	"net/url"
	"os"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"
)

const serverAddress = "https://api-sms.readboy.com/index.php?s=/Sms/Api/send"
const oaServerAddress = "https://oa.readboy.com/index.php?s="
const HttpTimeout = 30 * time.Second

type Response struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}

type wecomMsgResponse struct {
	ErrCode any    `json:"errcode"`
	ErrMsg  string `json:"errmsg"`
}

func makeURLCode(recNum, signName, templateCode string, templateParam map[string]any) (string, string) {
	ts := strconv.FormatInt(time.Now().Unix(), 10)
	key := "8e517d999976168979f81dc73d010c90"
	code := fmt.Sprintf("%06d", rand.Intn(900000)+100000)
	paramStringJSON, _ := json.Marshal(templateParam)
	authKey := fmt.Sprintf("%s-%s-%s", ts, code, md5Hash(fmt.Sprintf("%s-%s-%s", ts, code, key)))

	parameters := url.Values{}
	parameters.Set("authKey", authKey)
	parameters.Set("appName", "care.readboy.com")
	parameters.Set("signName", signName)
	parameters.Set("templateCode", templateCode)
	parameters.Set("phoneNumber", recNum)
	parameters.Set("templateParam", string(paramStringJSON))

	requestURL := fmt.Sprintf("%s&%s", serverAddress, parameters.Encode())
	return requestURL, code
}

func md5Hash(text string) string {
	hash := md5.Sum([]byte(text))
	return hex.EncodeToString(hash[:])
}

func SendSMS(phone, signName, templateCode string, templateParam map[string]any) (bool, string) {
	// 验证手机号码格式
	phoneRegex := regexp.MustCompile(`^1[3-9]\d{9}$`)
	if !phoneRegex.MatchString(phone) {
		log.Error(fmt.Sprintf("Invalid phone number: %s\n", phone))
		return false, "无效的手机号码"
	}

	client := resty.New().SetTimeout(HttpTimeout)
	requestURL, code := makeURLCode(phone, signName, templateCode, templateParam)
	msg := "发送验证码失败，请重新发送"
	data := false

	fmt.Println(requestURL)
	resp, err := client.R().
		Get(requestURL)

	if err != nil {
		log.Error(fmt.Sprintf("phone code send error: phone=%s, code=%s, error=%s\n", phone, code, err))
		return false, msg
	}
	fmt.Println(string(resp.Body()))

	var ret Response
	if err := json.Unmarshal(resp.Body(), &ret); err != nil {
		log.Error(fmt.Sprintf("phone code send error: phone=%s, code=%s, error=%s\n", phone, code, err))
		return false, msg
	}

	if ret.Code == "200" {
		msg = "success"
		data = true
	} else {
		log.Error(fmt.Sprintf("phone code send error: phone=%s, code=%s, response:%s %s\n ", phone, code, ret.Code, ret.Message))
		msg = ret.Message
		data = false
	}

	return data, msg
}

func OaSendWecomMsg(jobNumbers []string, msg string) error {
	if len(jobNumbers) == 0 {
		return nil
	}

	// 准备表单数据

	formData, err := json.Marshal(map[string]any{
		"tousername": jobNumbers,
		"content":    msg,
	})
	if err != nil {
		log.Error(fmt.Sprintf("oa wecom send msg error: jobNumbers=%s, msg=%s, error=%s\n", strings.Join(jobNumbers, ","), msg, err))
		return err
	}
	data := make(map[string]string)
	data["data"] = string(formData)
	authKey := generateOaAuthKey()
	requestURL := fmt.Sprintf("%s/Weixin/Api/send.html", oaServerAddress)
	client := resty.New().SetTimeout(HttpTimeout)
	resp, err := client.R().
		SetFormData(data). // 设置表单数据
		SetQueryParam("auth_key", authKey).
		Post(requestURL) // 使用 POST 方法

	if err != nil {
		log.Error(fmt.Sprintf("oa wecom send msg error: jobNumbers=%s, msg=%s, error=%s\n", strings.Join(jobNumbers, ","), msg, err))
		return err
	}
	// 解析返回的JSON
	var result wecomMsgResponse
	if err := json.Unmarshal(resp.Body(), &result); err != nil {
		log.Error(fmt.Sprintf("解析响应失败: 响应内容=%s, error=%s", string(resp.Body()), err))
		return fmt.Errorf("解析响应失败: %w", err)
	}

	if result.ErrMsg != "ok" {
		errMsg := fmt.Sprintf("发送失败 (errcode=%v): %s", result.ErrCode, result.ErrMsg)
		log.Error(fmt.Sprintf("消息发送失败: jobNumbers=%s, msg=%s, %s", strings.Join(jobNumbers, ","), msg, errMsg))
		return fmt.Errorf(errMsg)
	}

	log.Info(fmt.Sprintf("消息发送成功: jobNumbers=%s, msg=%s", strings.Join(jobNumbers, ","), msg))
	return nil
}

// 生成OA系统认证密钥
func generateOaAuthKey() string {
	apiKey := os.Getenv("OA_API_KEY")
	if apiKey == "" {
		log.Error("OA_API_KEY not set")
		return ""
	}

	// 1. 获取当前时间戳（秒级）
	timestamp := time.Now().Unix()

	// 2. 生成 100000-999999 的随机数
	randNum := rand.Intn(900000) + 100000

	// 3. 计算API密钥的MD5哈希
	apiKeyHash := fmt.Sprintf("%x", md5.Sum([]byte(apiKey)))

	// 4. 计算组合字符串的MD5哈希
	combined := fmt.Sprintf("%d-%d-%s", timestamp, randNum, apiKeyHash)
	combinedHash := fmt.Sprintf("%x", md5.Sum([]byte(combined)))

	// 5. 构建最终认证密钥
	return fmt.Sprintf("%d-%d-%s", timestamp, randNum, combinedHash)
}
