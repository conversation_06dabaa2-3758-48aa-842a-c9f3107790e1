package system

import (
	"encoding/json"
	"errors"
	"fmt"
	api2 "marketing/internal/api/system"
	"marketing/internal/consts"
	"marketing/internal/dao/admin_user"
	app "marketing/internal/dao/app_system"
	"marketing/internal/dao/auth"
	"marketing/internal/model"
	appError "marketing/internal/pkg/errors"
	"marketing/internal/pkg/log"
	"marketing/internal/pkg/types"
	"marketing/internal/pkg/utils"
	"marketing/internal/pkg/wecom"
	"regexp"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/xuri/excelize/v2"

	"github.com/gin-gonic/gin"
	"github.com/go-sql-driver/mysql"
	"github.com/spf13/cast"
	"go.uber.org/zap"
	"golang.org/x/exp/maps"
	"gorm.io/gorm"
)

type AdminUserInterface interface {
	Lists(c *gin.Context, param api2.AdminUserReq) (gin.H, error)
	Add(c *gin.Context, req api2.AddUserReq) (uint, error)
	AddAgencyUser(c *gin.Context, req api2.AddUserReq) error
	AddEndpointUser(c *gin.Context, req api2.AddUserReq) error
	Update(c *gin.Context, req api2.AddUserReq) error
	Get(c *gin.Context, id uint) (*api2.AdminUserResp, error)
	UpdateStatus(c *gin.Context, req api2.UpdateStatusReq) error
	ResetPassword(c *gin.Context, req api2.ResetPasswordReq) error
	GetUserLogs(c *gin.Context, userID uint, page, pageSize int) ([]api2.AdminUserLogResp, int64, error)
	UpdateTags(c *gin.Context, UID uint, tags []uint) error
	BindWecom(c *gin.Context, code string) error
	GetAgencies(c *gin.Context) ([]map[string]any, error)
	Delete(c *gin.Context, id uint) error
	SyncUser(c *gin.Context, ids []int, tagIDs []uint) error
	GetUserAgency(c *gin.Context, userID uint) (*model.Agency, error)
	ExportUser(c *gin.Context, req api2.AdminUserReq) error
	ImportUsers(c *gin.Context, rows [][]string) (int, int, []ImportResult, error)
	SearchUsers(c *gin.Context, params *api2.SearchUserReq) ([]api2.UserSearchResult, error)
	SyncDepartment(c *gin.Context, id uint, level int) error
}

// AdminUserSvc 管理
type adminUserSvc struct {
	UserCache    admin_user.UserCacheInterface
	db           *gorm.DB
	authCache    auth.AuthCacheInterface
	userDao      admin_user.UserDao
	wecomSvc     WecomSyncService
	appSystemDao app.AppSystem
	adminUserLog admin_user.AdminUserLog
}

// NewAdminUserSvc 创建 AdminRoleSvc 实例
func NewAdminUserSvc(userCache admin_user.UserCacheInterface,
	db *gorm.DB,
	userDao admin_user.UserDao,
	wecomSvc WecomSyncService,
	appSystemDao app.AppSystem,
	adminUserLog admin_user.AdminUserLog,
	authCache auth.AuthCacheInterface) AdminUserInterface {
	return &adminUserSvc{
		UserCache:    userCache,
		db:           db,
		authCache:    authCache,
		userDao:      userDao,
		wecomSvc:     wecomSvc,
		appSystemDao: appSystemDao,
		adminUserLog: adminUserLog,
	}
}

func (svc *adminUserSvc) Lists(c *gin.Context, param api2.AdminUserReq) (gin.H, error) {

	var total, syncTotal int64
	var list []api2.AdminUserResp

	var query *gorm.DB
	switch param.Type {
	case "agency":
		query = svc.buildAgencyListQuery(c, param)
	case "endpoint":
		query = svc.buildEndpointListQuery(c, param)
	default:
		query = svc.buildListQuery(c, param)
	}

	query = query.Session(&gorm.Session{})
	// 总数
	err := query.Count(&total).Error
	if err != nil {
		return gin.H{}, err
	}
	// 查询同步总数
	syncErr := query.Where("u.qw_userid IS NOT NULL").Count(&syncTotal).Error
	if syncErr != nil {
		return gin.H{}, syncErr
	}

	// 分页查询
	limit, offset := param.PageSize, (param.Page-1)*param.PageSize
	err = query.Limit(limit).Offset(offset).Scan(&list).Error
	if err != nil {
		return gin.H{}, err
	}
	//标签处理成数组
	for i := range list {
		list[i].TagIDs = cast.ToIntSlice(strings.Split(list[i].ITags, ","))
		list[i].TagNames = strings.Split(list[i].ITagNames, ",")
	}
	//角色处理
	list, err = svc.roleToSlice(c, list)
	if err != nil {
		return gin.H{}, err
	}

	return gin.H{
		"data": list,
		//"page_size": param.PageSize,
		//"page":      param.Page,
		"syncTotal": syncTotal,
		"total":     total,
	}, nil
}

func (svc *adminUserSvc) Add(c *gin.Context, req api2.AddUserReq) (uint, error) {
	var uid uint
	err := svc.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		var err error
		uid, err = svc.add(c, tx, req)
		return err
	})
	return uid, err
}

// AddAgencyUser 添加代理用户
func (svc *adminUserSvc) AddAgencyUser(c *gin.Context, req api2.AddUserReq) error {
	//判断用户名或者手机号是否已经存在
	var existUser model.AdminUsers
	var err error
	var agencyID, uid uint
	if req.SecondAgency > 0 {
		agencyID = req.SecondAgency
	} else {
		agencyID = req.TopAgency
	}
	if agencyID == 0 {
		return appError.NewErr("经销商不能为空")
	}
	if req.Phone != "" && req.Username != "" {
		err = svc.db.WithContext(c).Where("username =? OR phone =?", req.Username, req.Phone).First(&existUser).Error
	}
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}

	//判断用户是否可用
	if existUser.ID > 0 {
		if *existUser.Status == 0 && !existUser.DeletedAt.Valid {
			err = svc.Delete(c, existUser.ID)
			if err != nil {
				return err
			}
			//记录日志
			svc.addUserLog(c, existUser.ID, existUser.Username, *existUser.Phone, "unregister", "新增总代自动注销用户删除用户")
		}
		// 用户如果已经存在，且状态正常(更新用户)
		if *existUser.Status == 1 && !existUser.DeletedAt.Valid {
			req.ID = existUser.ID
			// 不要删除用户原有的角色
			existRoleIDs, _ := svc.userDao.GetUserRoleIDs(c, existUser.ID)
			req.RoleIDs = append(req.RoleIDs, existRoleIDs...)
			req.UpdateType = consts.AgencyPrefix
			return svc.Update(c, req)
		}
	}
	//创建用户
	err = svc.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		if uid == 0 {
			uid, err = svc.add(c, tx, req)
			if err != nil {
				return err
			}
		}
		//同步到user agency
		userAgency := model.UserAgency{
			UID:          uid,
			TopAgency:    req.TopAgency,
			SecondAgency: req.SecondAgency,
			Partition:    req.Partition,
			RoleID:       req.RoleIDs[0],
			Status:       1,
		}
		return tx.Create(&userAgency).Error
	})
	if err != nil {
		return err
	}
	// 同步到企微(不要放到事务中)
	if req.SyncWecomUser {
		QWUserID, err := svc.wecomSvc.SyncUser(c, uid, 0)
		if err != nil {
			return err
		}
		//更新用户企微id

		if err := svc.db.WithContext(c).Model(&model.AdminUsers{}).Where("id = ?", uid).Update("qw_userid", QWUserID).Error; err != nil {
			return err
		}
		//添加企微标签
		if len(*req.TagIDs) > 0 {
			var wecomTags []model.WecomUserTag
			for _, tagID := range *req.TagIDs {
				wecomTags = append(wecomTags, model.WecomUserTag{
					UserID: uid,
					TagID:  tagID,
				})
				err = svc.wecomSvc.AddUserTag(c, int(tagID), []string{QWUserID})
				if err != nil {
					return err
				}
			}
			return svc.db.WithContext(c).Create(&wecomTags).Error
		}
	}

	return nil
}

// AddEndpointUser 添加代理用户
func (svc *adminUserSvc) AddEndpointUser(c *gin.Context, req api2.AddUserReq) error {
	//判断用户名或者手机号是否已经存在
	var existUser model.AdminUsers
	var err error
	var endpointID, uid uint
	endpointID = req.EndpointID
	if endpointID == 0 {
		return appError.NewErr("终端ID不能为空")
	}
	if req.Phone != "" && req.Username != "" {
		err = svc.db.WithContext(c).Where("username =? OR phone =?", req.Username, req.Phone).First(&existUser).Error
	}
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}
	//获取终端角色
	if req.EndpointRole == "" {
		return appError.NewErr("终端角色不能为空")
	}
	if req.EndpointRole != consts.RoleEndpoint && req.EndpointRole != consts.RoleSalesclerk {
		return appError.NewErr("终端角色不正确")
	}
	//查询角色ID 直接查数据库只要ID
	var roleID uint
	err = svc.db.WithContext(c).
		Table("admin_roles").
		Select("id").
		Where("slug = ?", req.EndpointRole).
		Where("deleted_at IS NULL").
		Pluck("id", &roleID).Error
	if err != nil {
		return fmt.Errorf("查询角色ID失败: %w", err)
	}
	if roleID == 0 {
		return appError.NewErr("角色不存在")
	}
	req.RoleIDs = []uint{roleID}

	//判断用户是否可用
	if existUser.ID > 0 {
		if *existUser.Status == 0 && !existUser.DeletedAt.Valid {
			err = svc.Delete(c, existUser.ID)
			if err != nil {
				return err
			}
			//记录日志
			svc.addUserLog(c, existUser.ID, existUser.Username, *existUser.Phone, "unregister", "新增终端账号自动注销用户删除用户")
		}
		// 用户如果已经存在，且状态正常(更新用户)
		if *existUser.Status == 1 && !existUser.DeletedAt.Valid {
			//判断是否已经绑定终端
			var userEndpoint model.UserEndpoint
			err = svc.db.WithContext(c).Where("uid = ?", existUser.ID).Where("endpoint = ?", req.EndpointID).First(&userEndpoint).Error
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return appError.NewErr("查询终端用户失败" + err.Error()).WithStack()
			}
			if userEndpoint.UID > 0 {
				return appError.NewErr("用户已经绑定终端,无需再次新增")
			}
			req.ID = existUser.ID
			// 不要删除用户原有的角色
			existRoleIDs, _ := svc.userDao.GetUserRoleIDs(c, existUser.ID)
			req.RoleIDs = append(req.RoleIDs, existRoleIDs...)
			req.UpdateType = consts.EndpointPrefix
			return svc.Update(c, req)
		}
	}

	//创建用户
	err = svc.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		if uid == 0 {
			uid, err = svc.add(c, tx, req)
			if err != nil {
				return err
			}
		}
		//同步到user agency
		ueRole := consts.UeRoleAssistant
		if req.EndpointRole == consts.RoleEndpoint {
			ueRole = consts.UeRoleManager
		}
		userEndpoint := model.UserEndpoint{
			UID:      uid,
			Endpoint: req.EndpointID,
			Role:     ueRole,
			UserType: "user",
		}
		return tx.Create(&userEndpoint).Error
	})
	if err != nil {
		return err
	}
	// 同步到企微(不要放到事务中)
	if req.SyncWecomUser {
		QWUserID, err := svc.wecomSvc.SyncUser(c, uid, 0)
		if err != nil {
			return err
		}
		//更新用户企微id

		if err := svc.db.WithContext(c).Model(&model.AdminUsers{}).Where("id = ?", uid).Update("qw_userid", QWUserID).Error; err != nil {
			return err
		}
		//添加企微标签
		if len(*req.TagIDs) > 0 {
			var wecomTags []model.WecomUserTag
			for _, tagID := range *req.TagIDs {
				wecomTags = append(wecomTags, model.WecomUserTag{
					UserID: uid,
					TagID:  tagID,
				})
				err = svc.wecomSvc.AddUserTag(c, int(tagID), []string{QWUserID})
				if err != nil {
					return err
				}
			}
			return svc.db.WithContext(c).Create(&wecomTags).Error
		}
	}

	return nil
}

func (svc *adminUserSvc) Update(c *gin.Context, req api2.AddUserReq) error {
	// 判断用户是否存在
	var existUser model.AdminUsers
	if err := svc.db.WithContext(c).Where("id = ?", req.ID).First(&existUser).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return appError.NewErr("用户不存在")
		}
		return err
	}
	//资源分组处理
	if err := svc.checkResourceGroupPermission(c, existUser.ResourceID); err != nil {
		return err
	}

	//基本信息处理
	existUser.Name = req.Name
	existUser.Avatar = &req.Avatar
	if req.Status == nil {
		// Status 为空值
	} else if *req.Status == 0 {
		existUser.Status = req.Status
		// 如果传入的值为 0，表示禁用,需要清除相关的token
		if err := svc.UserCache.DeleteUserCache(c, req.ID, consts.AdminPrefix); err != nil { // 删除管理端缓存
			log.Error("删除用户缓存失败", zap.Error(err))
		}
		if err := svc.authCache.DeleteTokenByUID(c, cast.ToInt64(req.ID)); err != nil { // 删除代理端缓存
			log.Error("删除用户缓存失败", zap.Error(err))
		}
	} else {
		existUser.Status = req.Status
	}

	// 如果给个默认值会设置为空字符串，默认值为null
	if req.Phone != "" {
		existUser.Phone = &req.Phone
	}
	// 只有当密码不为空时才更新密码
	if req.Password != "" {
		existUser.Password = utils.HashPassword(req.Password)
	}

	err := svc.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		// 更新用户信息
		if err := tx.Save(&existUser).Error; err != nil {
			return err
		}

		if req.RoleIDs != nil && len(req.RoleIDs) > 0 {
			// 对 RoleIDs 进行去重，并过滤掉值为0的角色ID
			uniqueRoleIDs := make([]uint, 0)
			roleIDMap := make(map[uint]struct{})
			for _, roleID := range req.RoleIDs {
				if roleID > 0 {
					if _, exists := roleIDMap[roleID]; !exists {
						roleIDMap[roleID] = struct{}{}
						uniqueRoleIDs = append(uniqueRoleIDs, roleID)
					}
				}
			}
			req.RoleIDs = uniqueRoleIDs

			// 获取当前用户的所有角色
			var existingRoles []model.AdminRoleUser
			if err := tx.Where("user_id = ?", req.ID).Find(&existingRoles).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return err
			}

			// 创建一个 map 来存储当前角色 ID
			existingRoleMap := make(map[uint]struct{})
			for _, item := range existingRoles {
				existingRoleMap[item.RoleID] = struct{}{}
			}

			// 准备要添加的新角色
			var rolesToAdd []model.AdminRoleUser

			// 检查是否是从Excel导入的更新操作
			isImportUpdate := false
			if req.UpdateType == consts.AgencyPrefix && len(req.RoleIDs) > 0 {
				// 检查请求来源，如果是从Excel导入的更新操作，只添加新角色，不删除现有角色
				if strings.Contains(c.FullPath(), "/import") {
					isImportUpdate = true
				}
			}

			if isImportUpdate {
				// 只添加新角色，不删除现有角色
				for _, newRole := range req.RoleIDs {
					if _, exists := existingRoleMap[newRole]; !exists {
						rolesToAdd = append(rolesToAdd, model.AdminRoleUser{UserID: req.ID, RoleID: newRole})
					}
				}
			} else {
				// 正常更新操作，替换所有角色
				var rolesToDelete []uint

				// 检查需要添加的新角色
				for _, newRole := range req.RoleIDs {
					if _, exists := existingRoleMap[newRole]; !exists {
						rolesToAdd = append(rolesToAdd, model.AdminRoleUser{UserID: req.ID, RoleID: newRole})
					} else {
						delete(existingRoleMap, newRole)
					}
				}

				// 收集需要删除的角色
				for roleID := range existingRoleMap {
					rolesToDelete = append(rolesToDelete, roleID)
				}

				// 删除不再需要的角色
				if len(rolesToDelete) > 0 {
					if err := tx.Where("user_id = ? AND role_id IN ?", req.ID, rolesToDelete).Delete(&model.AdminRoleUser{}).Error; err != nil {
						return err
					}
				}
			}

			// 添加新角色
			if len(rolesToAdd) > 0 {
				if err := tx.Create(&rolesToAdd).Error; err != nil {
					return err
				}
			}
		}
		//用户组处理
		if req.GroupID != nil {
			if *req.GroupID == 0 {
				// 删除原来的用户组
				if err := tx.Where("user_id = ?", req.ID).Delete(&model.AdminUserUserGroupV2{}).Error; err != nil {
					return err
				}
			} else {
				var userGroup model.AdminUserUserGroupV2
				err := tx.Where("user_id = ?", req.ID).First(&userGroup).Error
				if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
					return err
				}
				userGroup.UserID = req.ID
				userGroup.UserGroupID = *req.GroupID

				// 更新或插入用户组
				if err = tx.Save(&userGroup).Error; err != nil {
					return err
				}
			}
		}
		// 代理用户处理
		if req.UpdateType == consts.AgencyPrefix {
			// 获取当前用户的代理信息
			var userAgency model.UserAgency
			if err := tx.Where("uid = ?", req.ID).Where("status = ?", 1).First(&userAgency).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return err
			}
			// 如果不存在，创建新的代理信息
			if userAgency.ID == 0 {
				userAgency.UID = req.ID
				userAgency.TopAgency = req.TopAgency
				userAgency.SecondAgency = req.SecondAgency
				userAgency.Partition = req.Partition
				if len(req.RoleIDs) > 0 {
					userAgency.RoleID = req.RoleIDs[0]
				} else {
					userAgency.RoleID = 0
				}
				userAgency.Status = 1
				if err := tx.Create(&userAgency).Error; err != nil {
					return err
				}
			} else if userAgency.TopAgency != req.TopAgency || userAgency.SecondAgency != req.SecondAgency || userAgency.Partition != req.Partition {
				// 更新代理信息

				userAgency.TopAgency = req.TopAgency
				userAgency.SecondAgency = req.SecondAgency
				userAgency.Partition = req.Partition
				if len(req.RoleIDs) > 0 {
					userAgency.RoleID = req.RoleIDs[0]
				} else {
					userAgency.RoleID = 0
				}
				userAgency.UpdatedAt = types.CustomTime(time.Now())
				if err := tx.Save(&userAgency).Error; err != nil {
					return err
				}
			}
		}
		//终端用户处理
		if req.UpdateType == consts.EndpointPrefix {
			// 获取当前用户的代理信息
			var userEndpoint model.UserEndpoint
			if err := tx.Where("uid = ?", req.ID).First(&userEndpoint).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return err
			}
			ueRole := consts.UeRoleAssistant
			if req.EndpointRole == consts.RoleEndpoint {
				ueRole = consts.UeRoleManager
			}
			// 如果不存在，创建新的终端信息
			if userEndpoint.UID == 0 {
				userEndpoint.UID = req.ID
				userEndpoint.Endpoint = req.EndpointID
				userEndpoint.Role = ueRole
				userEndpoint.UserType = "user"
				if err := tx.Create(&userEndpoint).Error; err != nil {
					return err
				}
			} else if userEndpoint.Endpoint != req.EndpointID || userEndpoint.Role != req.EndpointRole {
				// 更新终端信息
				tx.Model(&userEndpoint).Where("uid = ?", req.ID).Updates(map[string]any{
					"endpoint":  req.EndpointID,
					"role":      ueRole,
					"user_type": "user",
				})
			}
		}

		return nil
	})

	if err != nil {
		return appError.NewErr("更新用户失败" + err.Error())
	}

	// 同步到企微(需要用户状态正常才更新)
	// 检查用户状态是否正常（如果Status为nil，则使用existUser.Status）
	isStatusActive := (req.Status != nil && *req.Status == 1) || (req.Status == nil && existUser.Status != nil && *existUser.Status == 1)
	if existUser.QWUserID != nil && isStatusActive && (req.TopAgency > 0 || req.SecondAgency > 0 || req.EndpointID > 0) {

		QWUserID, err := svc.wecomSvc.SyncUser(c, existUser.ID, 0)
		if err != nil {
			return err
		}
		// 更新用户企微id
		if QWUserID != *existUser.QWUserID {
			//更新用户企微id
			if err := svc.db.Model(&model.AdminUsers{}).Where("id =?", req.ID).Update("qw_userid", QWUserID).Error; err != nil {
				return err
			}
		}
	}

	if err := svc.UserCache.DeleteUserCache(c, req.ID, consts.AdminPrefix); err != nil {
		log.Error("删除用户缓存失败", zap.Error(err))
		// 缓存删除失败不影响业务
	}
	if err := svc.UserCache.DeleteUserCache(c, req.ID, consts.AgencyPrefix); err != nil {
		log.Error("删除用户缓存失败", zap.Error(err))
		// 缓存删除失败不影响业务
	}
	return nil
}

func (svc *adminUserSvc) Get(c *gin.Context, id uint) (*api2.AdminUserResp, error) {

	var data api2.AdminUserResp
	//资源分组处理
	var resourceGroup []uint
	var ok bool
	if value, exists := c.Get("resource_group"); exists {
		if resourceGroup, ok = value.([]uint); !ok {
			log.Error("资源分组数据类型错误")
		}
	}
	var selectField = []string{
		"u.id", "u.username", "u.name", "u.phone", "u.status", "u.qw_userid", "u.avatar", "u.created_at",
		"u.actived_at", "u.updated_at", "u.deleted_at",
		"COALESCE(GROUP_CONCAT(DISTINCT r.slug), '') as i_roles",
		"g.name as group_name", "g.id as group_id",
		"COALESCE(GROUP_CONCAT(DISTINCT t.id), '') as i_tags",
		"COALESCE(GROUP_CONCAT(DISTINCT t.tag_name), '') as i_tag_names",
	}

	query := svc.db.WithContext(c).Table("admin_users u").
		Select("u.id,u.username,u.name,u.phone,u.status,u.created_at,u.actived_at,u.updated_at,GROUP_CONCAT(r.slug) as i_roles").
		Joins("left join admin_role_users as ru on u.id = ru.user_id").
		Joins("left join admin_roles as r on ru.role_id = r.id and r.deleted_at IS NULL").
		Joins("left join admin_user_user_group_v2 as ug on ug.user_id = u.id").
		Joins("left join admin_user_group_v2 as g on g.id = ug.user_group_id").
		Joins("left join wecom_user_tag as wt on u.id = wt.user_id").
		Joins("left join wecom_tags as t on wt.tag_id = t.id and t.deleted_at IS NULL").Where("u.id = ?", id)
	//资源分组处理
	if !c.GetBool("IsSuperAdmin") {
		fmt.Println(resourceGroup)
		//query = query.Where("u.resource_id in (?)", resourceGroup)
	}
	err := query.Group("u.id").Select(selectField).First(&data).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, appError.NewErr("用户不存在")
	}
	if err != nil {
		return &data, err
	}
	//总代查询
	var agencyData api2.AdminUserResp
	err = svc.db.WithContext(c).Table("user_agency AS ua").
		Joins("left join agency as ta on ua.top_agency = ta.id").
		Joins("left join agency as sa on ua.second_agency = sa.id").
		Select("ua.top_agency as top_agency,ua.second_agency as second_agency,ta.name as top_agency_name,sa.name as second_agency_name").
		Where("ua.status =?", 1).
		Where("ua.uid =?", id).Scan(&agencyData).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	data.TopAgency = agencyData.TopAgency
	data.TopAgencyName = agencyData.TopAgencyName
	data.SecondAgency = agencyData.SecondAgency
	data.SecondAgencyName = agencyData.SecondAgencyName
	//终端查询
	var endpointData api2.AdminUserResp
	err = svc.db.WithContext(c).Table("user_endpoint AS ue").
		Joins("left join endpoint as e on ue.endpoint = e.id").
		Select("e.name as endpoint_name").
		Where("ue.uid =?", id).Scan(&endpointData).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	data.EndpointName = endpointData.EndpointName

	//标签数据格式
	data.TagIDs = cast.ToIntSlice(strings.Split(data.ITags, ","))
	data.TagNames = strings.Split(data.ITagNames, ",")

	//角色处理
	var list []api2.AdminUserResp
	list = append(list, data)
	list, err = svc.roleToSlice(c, list)
	return &list[0], err
}

// GetAgencies 获取所有代理信息
func (svc *adminUserSvc) GetAgencies(c *gin.Context) ([]map[string]any, error) {
	type Agency struct {
		ID       uint   `json:"id"`
		Name     string `json:"name"`
		ParentId uint   `json:"parentId"`
	}

	var agencies []Agency
	err := svc.db.WithContext(c).Table("agency").Select("id, name,pid as parent_id").
		Where("deleted_at is null").Find(&agencies).Error
	if err != nil {
		return nil, err
	}

	// Convert the slice of structs to a slice of maps
	var result []map[string]any
	for _, agency := range agencies {
		result = append(result, map[string]any{
			"id":       strconv.Itoa(int(agency.ID)),
			"name":     agency.Name,
			"parentId": agency.ParentId,
		})
	}

	return result, nil
}

// SyncUser 同步经销商用户到微信
func (svc *adminUserSvc) SyncUser(c *gin.Context, ids []int, tagIDs []uint) error {
	// 先查询用户信息
	var users []model.AdminUsers
	err := svc.db.WithContext(c).Where("id in (?)", ids).Find(&users).Error
	if err != nil {
		return err
	}
	//标签
	var wecomTags []model.WecomUserTag
	var QWUserIDs []string
	for i := range users {
		// 同步到企微
		QWUserID, err := svc.wecomSvc.SyncUser(c, users[i].ID, 0)
		if err != nil {
			return err
		}
		// 更新用户企微id
		users[i].QWUserID = &QWUserID
		users[i].UpdatedAt = time.Now()
		QWUserIDs = append(QWUserIDs, QWUserID)

		if len(tagIDs) > 0 {
			for _, tagID := range tagIDs {
				wecomTags = append(wecomTags, model.WecomUserTag{
					UserID: users[i].ID,
					TagID:  tagID,
				})
			}
		}
	}
	// 同步用户标签（新增）
	var tagLogs []struct {
		ID   uint   `json:"id"`
		Name string `json:"tag_name"`
	}
	if len(wecomTags) > 0 {
		for _, tag := range tagIDs {
			err = svc.wecomSvc.AddUserTag(c, int(tag), QWUserIDs)
			if err != nil {
				return err
			}
			tagLogs = append(tagLogs, struct {
				ID   uint   `json:"id"`
				Name string `json:"tag_name"`
			}{
				ID:   tag,
				Name: fmt.Sprintf("标签ID:%d", tag),
			})
		}
		err = svc.db.WithContext(c).Save(&wecomTags).Error
		if err != nil {
			return err
		}
	}

	// 批量保存用户信息到数据库
	if err := svc.db.WithContext(c).Save(&users).Error; err != nil {
		return err
	}
	go func() {
		var logs []model.AdminUserLog
		for _, userID := range ids {
			var dataLog api2.UserLog
			dataLog.ID = uint(userID)
			dataLog.Tags = &tagLogs

			dataLogStr := utils.JSONMaskFields(
				utils.ToJSON(dataLog),
				[]string{},
			)
			logs = append(logs, model.AdminUserLog{
				UID:      userID,
				Username: "",
				OpType:   "edit",
				Phone:    "",
				Remark:   "批量同步用户到企微",
				After:    dataLogStr,
			})
		}
		svc.adminUserLog.AddLogs(c, logs)
	}()
	return nil
}

func (svc *adminUserSvc) SyncDepartment(c *gin.Context, id uint, level int) error {
	_, err := svc.wecomSvc.SyncDepartment(c, id, level)
	return err
}

func (svc *adminUserSvc) roleToSlice(c *gin.Context, data []api2.AdminUserResp) ([]api2.AdminUserResp, error) {
	//获取所有的角色
	var list []api2.AdminUserResp
	allRoles := make(map[string]struct{}) // 使用结构体避免重复
	for _, v := range data {
		tempRole := strings.Split(v.IRoles, ",")
		for _, role := range tempRole {
			allRoles[role] = struct{}{} // 存储唯一角色
		}
	}

	//查出相关的角色
	var roles []model.AdminRoles
	err := svc.db.WithContext(c).Where("slug in (?)", utils.Keys(allRoles)).Find(&roles).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return list, err
	}

	// 构建角色映射
	roleMap := make(map[string]gin.H)
	for _, role := range roles {
		roleMap[role.Slug] = gin.H{
			"id":   role.ID,
			"slug": role.Slug,
			"name": role.Name,
		}
	}

	// 填充用户角色
	for _, user := range data {
		var userRoles []any // 新的角色切片
		tempRole := strings.Split(user.IRoles, ",")
		for _, roleSlug := range tempRole {
			if role, exists := roleMap[roleSlug]; exists {
				userRoles = append(userRoles, role)
			}
		}
		user.Roles = userRoles    // 赋值回去
		list = append(list, user) // 添加到返回列表
	}
	return list, nil
}

// UpdateStatus 更新用户状态
func (svc *adminUserSvc) UpdateStatus(c *gin.Context, req api2.UpdateStatusReq) error {
	// 判断用户是否存在
	var existUser model.AdminUsers
	if err := svc.db.WithContext(c).Where("id = ?", req.ID).First(&existUser).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return appError.NewErr("用户不存在")
		}
		return err
	}
	if err := svc.checkResourceGroupPermission(c, existUser.ResourceID); err != nil {
		return err
	}

	// 更新状态
	existUser.Status = &req.Status
	return svc.db.WithContext(c).Save(&existUser).Error
}

// Delete 注销用户
func (svc *adminUserSvc) Delete(c *gin.Context, id uint) error {
	if id == 0 {
		return appError.NewErr("用户ID不能为空")
	}
	// 判断用户是否存在
	var existUser model.AdminUsers
	if err := svc.db.WithContext(c).Where("id =?", id).Unscoped().First(&existUser).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return appError.NewErr("用户不存在")
		}
	}
	now := time.Now()
	var newUsername string
	isPhone := func(phone string) bool {
		re := regexp.MustCompile(`^1[3-9]\d{9}$`)
		return re.MatchString(phone)
	}
	if isPhone(existUser.Username) {
		newUsername = fmt.Sprintf("%s-cancel%s", existUser.Username, now.Format("20060102150405"))
	} else {
		newUsername = existUser.Username
	}
	var err error
	if existUser.DeletedAt.Valid {
		// 更新数据库
		err = svc.db.WithContext(c).Model(&model.AdminUsers{}).Unscoped().
			Where("id = ?", id).
			UpdateColumns(map[string]interface{}{
				"username":   newUsername,
				"status":     0,
				"phone":      nil,
				"updated_at": now,
			}).Error

	} else {
		// 更新数据库
		err = svc.db.WithContext(c).Model(&model.AdminUsers{}).Unscoped().
			Where("id = ?", id).
			UpdateColumns(map[string]interface{}{
				"username":   newUsername,
				"phone":      nil,
				"status":     0,
				"deleted_at": now,
				"updated_at": now,
			}).Error

	}

	return err
}

// ResetPassword 重置用户密码
func (svc *adminUserSvc) ResetPassword(c *gin.Context, req api2.ResetPasswordReq) error {
	// 判断用户是否存在
	var existUser model.AdminUsers
	if err := svc.db.WithContext(c).Where("id = ?", req.ID).First(&existUser).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return appError.NewErr("用户不存在")
		}
		return err
	}
	fmt.Println("重置密码", req.Password)
	// 更新密码
	existUser.Password = utils.HashPassword(req.Password)
	return svc.db.WithContext(c).Save(&existUser).Error
}

func (svc *adminUserSvc) GetUserLogs(c *gin.Context, userID uint, page, pageSize int) ([]api2.AdminUserLogResp, int64, error) {
	var logs []model.AdminUserLog
	var total int64

	db := svc.db.WithContext(c).Model(&model.AdminUserLog{})

	if userID > 0 {
		db = db.Where("uid = ?", userID)
	}

	// 获取总数
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	if err := db.Order("id DESC").
		Offset((page - 1) * pageSize).
		Limit(pageSize).
		Find(&logs).Error; err != nil {
		return nil, 0, err
	}
	// 获取所有操作人的信息
	operatorIDs := make([]uint, len(logs))
	for i, l := range logs {
		operatorIDs[i] = uint(l.Operator)
	}

	operators, err := svc.userDao.GetByIDs(c, operatorIDs)
	if err != nil {
		return nil, 0, err
	}

	operatorMap := make(map[uint]string)
	for _, operator := range operators {
		operatorMap[operator.ID] = operator.Name
	}
	var res []api2.AdminUserLogResp
	for i := range logs {
		if name, exists := operatorMap[uint(logs[i].Operator)]; exists {
			logs[i].OperatorName = name
		}
		logs[i].CreatedAtStr = logs[i].CreatedAt.Format(time.DateTime)

		var requestJson, afterJson, beforeJson api2.UserLog
		if logs[i].Request != "" {
			err := json.Unmarshal([]byte(logs[i].Request), &requestJson)
			if err != nil {
				log.Error(fmt.Sprintf("Request用户:%d 日志json解析失败:%s", userID, logs[i].OpType))
			}
		}

		if logs[i].After != "" {
			err = json.Unmarshal([]byte(logs[i].After), &afterJson)
			if err != nil {
				log.Error(fmt.Sprintf("After用户:%d 日志json解析失败:%s", userID, logs[i].OpType))
			}
		}

		if logs[i].Before != "" {
			err = json.Unmarshal([]byte(logs[i].Before), &beforeJson)
			if err != nil {
				log.Error(fmt.Sprintf("Before用户:%d 日志json解析失败:%s", userID, logs[i].OpType))
			}
		}
		temp := api2.AdminUserLogResp{
			AdminUserLog: logs[i],
			RequestJson:  requestJson,
			AfterJson:    afterJson,
			BeforeJson:   beforeJson,
		}

		res = append(res, temp)
	}

	return res, total, nil
}

// UpdateTags 更新用户标签
func (svc *adminUserSvc) UpdateTags(c *gin.Context, UID uint, tags []uint) error {
	// 判断用户是否存在
	var existUser model.AdminUsers
	if err := svc.db.WithContext(c).Where("id = ?", UID).First(&existUser).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return appError.NewErr("用户不存在")
		}
		return err
	}
	if existUser.QWUserID == nil {
		return appError.NewErr("用户没有同步企微")
	}

	// 获取当前角色的所有标签
	var existingTags []model.WecomUserTag
	query := svc.db.WithContext(c).Table("wecom_user_tag as ut").Select("ut.*")
	query = query.Where("ut.user_id = ?", UID)
	query = query.Joins("left join wecom_tags t on ut.tag_id=t.id")
	query = query.Where("t.deleted_at is null")
	if err := query.Find(&existingTags).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}

	// 创建一个 map 来存储当前权限 ID
	existingTagsMap := make(map[uint]uint)
	for _, item := range existingTags {
		existingTagsMap[item.TagID] = item.TagID
	}

	// 准备要添加的新标签
	var tagsToAdd []model.WecomUserTag
	for _, newTag := range tags {
		if _, exists := existingTagsMap[newTag]; !exists {
			tagsToAdd = append(tagsToAdd, model.WecomUserTag{UserID: UID, TagID: newTag})
		} else {
			delete(existingTagsMap, newTag)
		}
	}

	// 开启事务
	return svc.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		// 删除不再需要的标签
		if len(existingTagsMap) > 0 {
			if err := tx.Where("user_id = ? AND tag_id IN ?", UID, maps.Keys(existingTagsMap)).Delete(&model.WecomUserTag{}).Error; err != nil {
				return err
			}
			// 同步删除企微标签
			for tagID := range existingTagsMap {
				if err := svc.wecomSvc.DeleteUserTag(c, int(tagID), []string{*existUser.QWUserID}); err != nil {
					return err
				}
			}
		}

		// 添加新标签
		if len(tagsToAdd) > 0 {
			if err := tx.Create(&tagsToAdd).Error; err != nil {
				return err
			}
			//调用企微接口给用户添加标签
			for _, tag := range tagsToAdd {
				if err := svc.wecomSvc.AddUserTag(c, int(tag.TagID), []string{*existUser.QWUserID}); err != nil {
					return err
				}
			}
		}

		// 批量删除用户缓存
		if err := svc.UserCache.DeleteUserCache(c, UID, consts.AgencyPrefix); err != nil {
			log.Error("删除用户缓存失败", zap.Error(err))
			// 缓存删除失败不影响业务
		}

		return nil
	})
}

func (svc *adminUserSvc) BindWecom(c *gin.Context, code string) error {
	uid := cast.ToUint(c.MustGet("uid"))
	appSystem, err := svc.appSystemDao.GetAppSystemByKey(c, consts.AdminPrefix)
	if err != nil {
		return err
	}
	//根据用户名判断用户是否可用
	weComClient := wecom.NewWeComClient(appSystem.CorpID, appSystem.CorpSecret)
	//根据用户名判断用户是否可用
	jobNumber, err := weComClient.GetUserIDByCode(code)
	if err != nil {
		return appError.NewErr("非读书郎教育科技用户不允许绑定")
	}
	//根据企微工号查出户，用employ_user表
	user, err := svc.userDao.GetByJobNumber(c, jobNumber)
	if err != nil {
		return err
	}
	if user.ID != 0 {
		if user.ID != uid {
			return appError.NewErr("该企微ID已被其他用户绑定，请联系管理员")
		} else {
			return appError.NewErr("已经绑定，请勿重复绑定")
		}
	}
	//更新用户企微信息
	var newEmployUser = &model.EmployeeUser{
		UID:       uid,
		JobNumber: jobNumber,
	}
	err = svc.userDao.UpdateOrCreateEmployeeUser(c, newEmployUser)
	if err != nil {
		return err
	}
	//清理用户缓存数据
	if err := svc.UserCache.DeleteUserCache(c, uid, consts.AdminPrefix); err != nil {
		log.Error("删除用户缓存失败", zap.Error(err))
		// 缓存删除失败不影响业务
	}
	return nil
}

// GetUserAgency 获取用户代理
func (svc *adminUserSvc) GetUserAgency(c *gin.Context, uid uint) (*model.Agency, error) {
	return svc.userDao.UserAgency(c, uid)
}

// ExportUser 导出用户
func (svc *adminUserSvc) ExportUser(c *gin.Context, param api2.AdminUserReq) error {
	param.PageSize = 100000
	data, err := svc.Lists(c, param)
	if err != nil {
		return err
	}
	// 创建一个新的 Excel 文件
	f := excelize.NewFile()
	sheetName := "Sheet1"
	index, err := f.NewSheet(sheetName)
	if err != nil {
		return err
	}
	f.SetActiveSheet(index)

	// 设置表头
	headers := []string{
		"ID", "用户名", "角色", "姓名", "用户组", "手机号", "一级经销商", "二级经销商", "终端", "企微", "创建时间",
	}

	// 创建标题行样式
	titleStyle, err := f.NewStyle(&excelize.Style{
		Font:      &excelize.Font{Color: "1f7f3b", Bold: true, Family: "Microsoft YaHei"},
		Fill:      excelize.Fill{Type: "pattern", Color: []string{"E6F4EA"}, Pattern: 1},
		Alignment: &excelize.Alignment{Vertical: "center"},
		Border: []excelize.Border{
			{Type: "top", Style: 2, Color: "1f7f3b"},
			{Type: "bottom", Style: 1, Color: "1f7f3b"},
			{Type: "left", Style: 1, Color: "1f7f3b"},
		},
	})
	if err != nil {
		return err
	}

	// 设置标题行内容
	if err = f.SetSheetRow(sheetName, "A1", &headers); err != nil {
		return err
	}

	// 设置标题行行高
	if err = f.SetRowHeight(sheetName, 1, 30); err != nil {
		return err
	}

	// 设置标题行单元格样式
	if err = f.SetCellStyle(sheetName, "A1", "K1", titleStyle); err != nil {
		return err
	}

	// 设置列宽
	if err = f.SetColWidth(sheetName, "A", "K", 20); err != nil {
		return err
	}

	// 填充数据
	for rowIndex, item := range data["data"].([]api2.AdminUserResp) {
		var roleNames []string
		for _, r := range item.Roles {
			if roleName, ok := r.(gin.H)["name"].(string); ok {
				roleNames = append(roleNames, roleName)
			}
		}
		row := []any{
			item.ID,
			item.Username,
			strings.Join(roleNames, ","),
			item.Name,
			item.GroupName,
			item.Phone,
			item.TopAgencyName,
			item.SecondAgencyName,
			item.EndpointName,
			item.QwUserid,
			item.CreatedAt,
		}
		for colIndex, value := range row {
			cell, err := excelize.CoordinatesToCellName(colIndex+1, rowIndex+2)
			if err != nil {
				return err
			}
			err = f.SetCellValue(sheetName, cell, value)
			if err != nil {
				return err
			}
		}
	}

	// 设置响应头
	fileName := fmt.Sprintf("用户_%s.xlsx", time.Now().Format("20060102_150405"))
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", fileName))
	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")

	// 直接将 Excel 文件内容写入响应
	if err = f.Write(c.Writer); err != nil {
		return err
	}

	return nil
}

// ImportResult 导入结果
type ImportResult struct {
	RowIndex      int    `json:"row_index"`                // 行号（从1开始）
	Phone         string `json:"phone"`                    // 手机号
	Nickname      string `json:"nickname"`                 // 昵称
	AgencyName    string `json:"agency_name"`              // 代理名称
	RoleName      string `json:"role_name"`                // 角色名称
	GroupName     string `json:"group_name"`               // 用户组名称
	TagNames      string `json:"tag_names"`                // 企微标签名称
	Success       bool   `json:"success"`                  // 是否成功
	FailureReason string `json:"failure_reason,omitempty"` // 失败原因
}

// UserImportData 导入用户的数据
type UserImportData struct {
	Phone       string
	Nickname    string
	AgencyName  string
	RoleName    string
	GroupName   string
	TagNames    string
	RoleID      uint
	AgencyID    uint
	AgencyLevel int
}

// 验证导入行数据
func (svc *adminUserSvc) validateImportRow(rowIndex int, row []string, roleNameToID, agencyNameToID map[string]uint, agencyIDToLevel map[uint]int) (ImportResult, *UserImportData, bool) {
	// 创建导入结果对象
	result := ImportResult{
		RowIndex: rowIndex + 1, // 行号从1开始
		Success:  false,        // 默认为失败，成功时再设置为true
	}

	// 检查行数据是否完整
	if len(row) < 4 {
		result.FailureReason = "格式错误：列数不足"
		return result, nil, false
	}

	// 解析行数据
	phone := strings.TrimSpace(row[0])
	nickname := strings.TrimSpace(row[1])
	agencyName := strings.TrimSpace(row[2])
	roleName := strings.TrimSpace(row[3])

	// 获取可选的用户组和企微标签
	var groupName, tagNames string
	if len(row) > 4 {
		groupName = strings.TrimSpace(row[4])
	}
	if len(row) > 5 {
		tagNames = strings.TrimSpace(row[5])
	}

	// 填充导入结果对象的基本信息
	result.Phone = phone
	result.Nickname = nickname
	result.AgencyName = agencyName
	result.RoleName = roleName
	result.GroupName = groupName
	result.TagNames = tagNames

	// 验证手机号
	if phone == "" {
		result.FailureReason = "手机号为空"
		return result, nil, false
	}

	// 验证手机号格式
	phoneRegex := regexp.MustCompile(`^1[3-9]\d{9}$`)
	if !phoneRegex.MatchString(phone) {
		result.FailureReason = "手机号格式无效"
		return result, nil, false
	}

	// 检查角色是否存在
	roleID, ok := roleNameToID[roleName]
	if !ok {
		result.FailureReason = "角色不存在：" + roleName
		return result, nil, false
	}

	// 检查代理商是否存在
	agencyID, ok := agencyNameToID[agencyName]
	if !ok {
		result.FailureReason = "代理商不存在：" + agencyName
		return result, nil, false
	}

	// 确定代理商级别
	agencyLevel, ok := agencyIDToLevel[agencyID]
	if !ok {
		result.FailureReason = "代理商级别未知：" + agencyName
		return result, nil, false
	}

	// 创建用户导入数据
	userData := &UserImportData{
		Phone:       phone,
		Nickname:    nickname,
		AgencyName:  agencyName,
		RoleName:    roleName,
		GroupName:   groupName,
		TagNames:    tagNames,
		RoleID:      roleID,
		AgencyID:    agencyID,
		AgencyLevel: agencyLevel,
	}

	return result, userData, true
}

// 导入用户的数据准备结果
type importDataPrepareResult struct {
	RoleNameToID        map[string]uint
	GroupNameToID       map[string]uint
	TagNameToID         map[string]uint
	AgencyNameToID      map[string]uint
	AgencyIDToLevel     map[uint]int
	SecondToTopAgencyID map[uint]uint
}

// 准备导入用户所需的数据
func (svc *adminUserSvc) prepareImportData(c *gin.Context) (*importDataPrepareResult, error) {
	result := &importDataPrepareResult{
		RoleNameToID:        make(map[string]uint),
		GroupNameToID:       make(map[string]uint),
		TagNameToID:         make(map[string]uint),
		AgencyNameToID:      make(map[string]uint),
		AgencyIDToLevel:     make(map[uint]int),
		SecondToTopAgencyID: make(map[uint]uint),
	}

	// 获取所有角色
	var roles []model.AdminRoles
	if err := svc.db.WithContext(c).Where("deleted_at IS NULL").Find(&roles).Error; err != nil {
		return nil, appError.NewErr("获取角色列表失败: " + err.Error())
	}
	for _, role := range roles {
		result.RoleNameToID[role.Name] = role.ID
	}

	// 获取所有用户组
	var userGroups []model.AdminUserGroupV2
	if err := svc.db.WithContext(c).Where("deleted_at IS NULL").Find(&userGroups).Error; err != nil {
		return nil, appError.NewErr("获取用户组列表失败: " + err.Error())
	}
	for _, group := range userGroups {
		result.GroupNameToID[group.Name] = group.ID
	}

	// 获取所有企微标签
	var wecomTags []model.WecomTag
	if err := svc.db.WithContext(c).Where("deleted_at IS NULL").Find(&wecomTags).Error; err != nil {
		return nil, appError.NewErr("获取企微标签列表失败: " + err.Error())
	}
	for _, tag := range wecomTags {
		result.TagNameToID[tag.TagName] = tag.ID
	}

	// 获取所有代理商
	var agencies []model.Agency
	if err := svc.db.WithContext(c).Where("deleted_at IS NULL").Find(&agencies).Error; err != nil {
		return nil, appError.NewErr("获取代理商列表失败: " + err.Error())
	}
	for _, agency := range agencies {
		result.AgencyNameToID[agency.Name] = agency.ID
		// 使用 Level 字段判断代理商级别
		result.AgencyIDToLevel[agency.ID] = agency.Level
		// 如果是二级代理商，记录其上级代理商ID
		if agency.Level == 2 {
			result.SecondToTopAgencyID[agency.ID] = uint(agency.PID)
		}
	}

	return result, nil
}

// ImportUsers 导入用户
func (svc *adminUserSvc) ImportUsers(c *gin.Context, rows [][]string) (int, int, []ImportResult, error) {
	successCount := 0
	failedCount := 0
	results := make([]ImportResult, 0, len(rows))

	// 记录失败原因统计
	failureReasons := map[string]int{
		"格式错误":       0,
		"手机号无效":     0,
		"角色不存在":     0,
		"代理商不存在":   0,
		"代理商级别未知": 0,
		"用户查询失败":   0,
		"用户更新失败":   0,
		"用户创建失败":   0,
	}

	// 记录开始导入的日志
	startTime := time.Now()
	log.Info("开始导入用户数据",
		zap.Int("total_rows", len(rows)),
		zap.String("operator", c.GetString("username")),
		zap.String("operator_ip", c.ClientIP()))

	// 准备导入数据
	prepareResult, err := svc.prepareImportData(c)
	if err != nil {
		return 0, 0, nil, err
	}

	// 从准备结果中获取映射
	roleNameToID := prepareResult.RoleNameToID
	groupNameToID := prepareResult.GroupNameToID
	tagNameToID := prepareResult.TagNameToID
	agencyNameToID := prepareResult.AgencyNameToID
	agencyIDToLevel := prepareResult.AgencyIDToLevel
	secondToTopAgencyID := prepareResult.SecondToTopAgencyID

	// 处理每一行数据
	for rowIndex, row := range rows {
		// 解析和验证行数据
		result, userData, valid := svc.validateImportRow(rowIndex, row, roleNameToID, agencyNameToID, agencyIDToLevel)
		if !valid {
			failedCount++
			failureReasons[result.FailureReason]++
			results = append(results, result)
			continue
		}

		// 检查用户是否已存在
		var existUser model.AdminUsers
		err := svc.db.WithContext(c).Where("phone = ?", userData.Phone).First(&existUser).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			failedCount++
			failureReasons["用户查询失败"]++
			result.FailureReason = "用户查询失败：" + err.Error()
			results = append(results, result)
			continue
		}

		// 如果用户已存在，则更新用户信息
		if existUser.ID > 0 {
			// 更新用户
			if err := svc.updateExistingUser(c, existUser, userData, result, roleNameToID, groupNameToID, tagNameToID, secondToTopAgencyID); err != nil {
				failedCount++
				failureReasons["用户更新失败"]++
				result.FailureReason = "用户更新失败：" + err.Error()
				results = append(results, result)
				continue
			}

			// 记录成功结果
			result.Success = true
			results = append(results, result)
			successCount++
		} else {
			// 创建新用户
			if err := svc.createNewUser(c, userData, result, roleNameToID, groupNameToID, tagNameToID, secondToTopAgencyID); err != nil {
				failedCount++
				failureReasons["用户创建失败"]++
				result.FailureReason = "用户创建失败：" + err.Error()
				results = append(results, result)
				continue
			}

			// 记录成功结果
			result.Success = true
			results = append(results, result)
			successCount++
		}
	}

	// 记录导入完成的日志
	duration := time.Since(startTime)

	// 记录详细的日志信息
	log.Info("用户数据导入完成",
		zap.Int("success_count", successCount),
		zap.Int("failed_count", failedCount),
		zap.Duration("duration", duration),
		zap.String("operator", c.GetString("username")),
		zap.String("operator_ip", c.ClientIP()))

	// 记录导入结果到系统日志表
	logData, err := json.Marshal(results)
	fmt.Println("导入结果", string(logData))

	return successCount, failedCount, results, nil
}

// 更新现有用户
func (svc *adminUserSvc) updateExistingUser(c *gin.Context, existUser model.AdminUsers, userData *UserImportData, result ImportResult, roleNameToID, groupNameToID, tagNameToID map[string]uint, secondToTopAgencyID map[uint]uint) error {
	// 更新用户信息
	req := api2.AddUserReq{
		ID:         existUser.ID,
		Name:       userData.Nickname,
		Phone:      userData.Phone,
		UpdateType: consts.AgencyPrefix,
	}

	// 获取用户现有的角色
	var existingRoles []model.AdminRoleUser
	if err := svc.db.WithContext(c).Where("user_id = ?", existUser.ID).Find(&existingRoles).Error; err != nil {
		return fmt.Errorf("获取用户现有角色失败：%w", err)
	}

	// 检查新角色是否已存在
	roleExists := false
	secondAgency, topAgency := false, false
	for _, role := range existingRoles {
		if userData.RoleName == "二代助理[渠道]" && role.RoleID == 11 {
			roleExists = true
			secondAgency = true
			break
		}
		if userData.RoleName == "总代助理[渠道]" && role.RoleID == 10 {
			roleExists = true
			topAgency = true
			break
		}
		if role.RoleID == userData.RoleID {
			roleExists = true
			break
		}
	}

	// 如果新角色不存在，则添加到用户的角色列表中
	if !roleExists {
		// 获取所有现有角色ID
		var roleIDs []uint
		for _, role := range existingRoles {
			roleIDs = append(roleIDs, role.RoleID)
		}
		// 添加新角色
		roleIDs = append(roleIDs, userData.RoleID)
		req.RoleIDs = roleIDs
	} else {
		// 如果角色已存在，不做任何改变
		// 但我们需要设置一个空的RoleIDs，以避免Update方法认为我们要清空角色
		req.RoleIDs = []uint{}
	}

	// 根据代理商级别设置一级或二级代理商
	if userData.AgencyLevel == 1 {
		// 一级代理商
		req.TopAgency = userData.AgencyID
	} else if userData.AgencyLevel == 2 {
		// 二级代理商
		req.SecondAgency = userData.AgencyID
		// 获取对应的一级代理商ID
		if topAgencyID, exists := secondToTopAgencyID[userData.AgencyID]; exists {
			req.TopAgency = topAgencyID
		}
	}

	// 处理用户组
	if userData.GroupName != "" {
		if groupID, ok := groupNameToID[userData.GroupName]; ok {
			req.GroupID = &groupID
		}
	}

	// 处理企微标签 - 只添加新标签，不删除现有标签
	var newTagIDs []uint
	var logNewTagIDs []string
	if userData.TagNames != "" {
		// 获取用户现有的标签
		var existingTags []model.WecomUserTag
		query := svc.db.WithContext(c).Table("wecom_user_tag as ut").Select("ut.*")
		query = query.Where("ut.user_id = ?", existUser.ID)
		query = query.Joins("left join wecom_tags t on ut.tag_id=t.id")
		query = query.Where("t.deleted_at is null")
		if err := query.Find(&existingTags).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("获取用户现有标签失败：%w", err)
		}

		// 创建现有标签ID的映射
		existingTagsMap := make(map[uint]struct{})
		for _, tag := range existingTags {
			existingTagsMap[tag.TagID] = struct{}{}
		}

		tagNameList := strings.Split(userData.TagNames, ",")
		for _, tagName := range tagNameList {
			tagName = strings.TrimSpace(tagName)
			if tagName == "C16二代助理" {
				if _, ok := existingTagsMap[10]; ok {
					continue
				} else {
					if secondAgency {
						newTagIDs = append(newTagIDs, 10)
					}
				}
			}
			if tagName == "C15总代助理" {
				if _, ok := existingTagsMap[9]; ok {
					continue
				} else {
					if topAgency {
						newTagIDs = append(newTagIDs, 9)
					}
				}
			}
			if tagID, ok := tagNameToID[tagName]; ok {
				// 只添加用户没有的标签
				if _, exists := existingTagsMap[tagID]; !exists {
					logNewTagIDs = append(logNewTagIDs, tagName)
					newTagIDs = append(newTagIDs, tagID)
				}
			}
		}

		// 如果有新标签需要添加
		if len(newTagIDs) > 0 {
			// 创建标签关联记录
			var tagsToAdd []model.WecomUserTag
			for _, tagID := range newTagIDs {
				tagsToAdd = append(tagsToAdd, model.WecomUserTag{
					UserID: existUser.ID,
					TagID:  tagID,
				})
			}

			// 保存到数据库
			if err := svc.db.WithContext(c).Create(&tagsToAdd).Error; err != nil {
				return fmt.Errorf("添加新标签失败：%w", err)
			}

			// 如果用户尚未同步到企微，先同步用户
			if existUser.QWUserID == nil || *existUser.QWUserID == "" {
				QWUserID, err := svc.wecomSvc.SyncUser(c, existUser.ID, 0)
				if err != nil {
					log.Error("同步用户到企微失败", zap.Error(err), zap.Uint("user_id", existUser.ID))
					// 继续处理，不中断导入
				} else {
					// 更新用户企微ID
					existUser.QWUserID = &QWUserID
					if err := svc.db.WithContext(c).Model(&model.AdminUsers{}).Where("id = ?", existUser.ID).Update("qw_userid", QWUserID).Error; err != nil {
						log.Error("更新用户企微ID失败", zap.Error(err), zap.Uint("user_id", existUser.ID))
					}
				}
			}

			// 如果用户已同步到企微，则同步标签
			if existUser.QWUserID != nil && *existUser.QWUserID != "" {
				for _, tagID := range newTagIDs {
					if err := svc.wecomSvc.AddUserTag(c, int(tagID), []string{*existUser.QWUserID}); err != nil {
						log.Error("同步企微标签失败", zap.Error(err), zap.Uint("user_id", existUser.ID), zap.Uint("tag_id", tagID))
						// 继续处理，不中断导入
					}
				}
			}
		}
	}

	// 更新用户
	if err := svc.Update(c, req); err != nil {
		return err
	}

	// 构建详细的日志信息
	var logParts []string

	// 添加基本信息
	logParts = append(logParts, "通过Excel导入更新用户信息")

	// 添加代理信息
	if userData.AgencyName != "" {
		logParts = append(logParts, "代理："+userData.AgencyName)
	}

	// 添加角色信息
	if userData.RoleName != "" {
		if roleExists {
			logParts = append(logParts, "角色："+userData.RoleName+" (已存在)"+"角色ID："+fmt.Sprintf("%d", userData.RoleID))
		} else {
			logParts = append(logParts, "角色："+userData.RoleName+" (新增)")
		}
	}

	// 添加用户组信息
	if userData.GroupName != "" {
		logParts = append(logParts, "用户组："+userData.GroupName)
	}

	// 添加企微标签信息
	if userData.TagNames != "" {
		logParts = append(logParts, "企微标签："+userData.TagNames+"| 企微标签ID："+strings.Join(logNewTagIDs, ","))
	}

	// 记录用户更新日志
	svc.addUserLog(c, existUser.ID, existUser.Username, userData.Phone, "edit", strings.Join(logParts, " | "))

	return nil
}

// 创建新用户
func (svc *adminUserSvc) createNewUser(c *gin.Context, userData *UserImportData, result ImportResult, roleNameToID, groupNameToID, tagNameToID map[string]uint, secondToTopAgencyID map[uint]uint) error {
	// 创建新用户
	username := "user_" + userData.Phone               // 使用手机号作为用户名前缀
	password := userData.Phone[len(userData.Phone)-6:] // 使用手机号后6位作为初始密码

	req := api2.AddUserReq{
		Username: username,
		Password: password,
		Name:     userData.Nickname,
		Phone:    userData.Phone,
		RoleIDs:  []uint{userData.RoleID},
		Status:   func() *int8 { i := int8(1); return &i }(), // 启用状态
	}

	// 根据代理商级别设置一级或二级代理商
	if userData.AgencyLevel == 1 {
		// 一级代理商
		req.TopAgency = userData.AgencyID
	} else if userData.AgencyLevel == 2 {
		// 二级代理商
		req.SecondAgency = userData.AgencyID
		// 获取对应的一级代理商ID
		if topAgencyID, exists := secondToTopAgencyID[userData.AgencyID]; exists {
			req.TopAgency = topAgencyID
		}
	}

	// 处理用户组
	if userData.GroupName != "" {
		if groupID, ok := groupNameToID[userData.GroupName]; ok {
			req.GroupID = &groupID
		}
	}

	// 处理企微标签 - 对于新用户，直接设置标签
	if userData.TagNames != "" {
		var tagIDs []uint
		// 按逗号分隔标签名称
		tagNameList := strings.Split(userData.TagNames, ",")
		for _, tagName := range tagNameList {
			tagName = strings.TrimSpace(tagName)
			if tagID, ok := tagNameToID[tagName]; ok {
				tagIDs = append(tagIDs, tagID)
			}
		}
		if len(tagIDs) > 0 {
			req.TagIDs = &tagIDs
			req.SyncWecomUser = true // 启用企微同步
		}
	}

	// 如果设置了企微标签，确保启用同步到企微
	if userData.TagNames != "" && userData.TagNames != "0" {
		req.SyncWecomUser = true
	}

	// 创建用户
	if err := svc.AddAgencyUser(c, req); err != nil {
		return err
	}

	// 查询新创建的用户ID
	var newUser model.AdminUsers
	if err := svc.db.WithContext(c).Where("phone = ?", userData.Phone).First(&newUser).Error; err == nil && newUser.ID > 0 {
		// 构建详细的日志信息
		var logParts []string

		// 添加基本信息
		logParts = append(logParts, "通过Excel导入创建新代理用户")

		// 添加代理信息
		if userData.AgencyName != "" {
			logParts = append(logParts, "代理："+userData.AgencyName)
		}

		// 添加角色信息
		if userData.RoleName != "" {
			logParts = append(logParts, "角色："+userData.RoleName)
		}

		// 添加用户组信息
		if userData.GroupName != "" {
			logParts = append(logParts, "用户组："+userData.GroupName)
		}

		// 添加企微标签信息
		if userData.TagNames != "" {
			logParts = append(logParts, "企微标签："+userData.TagNames)
		}

		// 记录用户创建日志
		svc.addUserLog(c, newUser.ID, username, userData.Phone, "create", strings.Join(logParts, " | "))
	}

	return nil
}

func (svc *adminUserSvc) checkResourceGroupPermission(c *gin.Context, resourceID uint) error {
	if !c.GetBool("IsSuperAdmin") {
		var resourceGroup []uint
		var ok bool
		if value, exists := c.Get("resource_group"); exists {
			if resourceGroup, ok = value.([]uint); !ok {
				log.Error("资源分组数据类型错误")
				return appError.NewErr("资源分组数据类型错误")
			}
		}
		if slices.Contains(resourceGroup, resourceID) {
			return appError.NewErr("没有权限")
		}
	}
	return nil
}

// 方便不同地方调用的事务回滚
func (svc *adminUserSvc) add(c *gin.Context, tx *gorm.DB, req api2.AddUserReq) (uint, error) {
	//判断用户名或者手机号是否已经存在
	var existUser model.AdminUsers
	var err error
	if req.Phone != "" {
		err = tx.WithContext(c).Where("username = ? OR phone = ?", req.Username, req.Phone).First(&existUser).Error
	} else {
		err = tx.WithContext(c).Where("username = ?", req.Username).First(&existUser).Error
	}

	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return 0, err
	}
	if existUser.ID > 0 {
		return 0, appError.NewErr("用户名或手机号已经存在")
	}
	//创建用户
	var userData = &model.AdminUsers{
		Username: req.Username,
		Name:     req.Name,
		Avatar:   &req.Avatar,
	}
	//如果给个默认值会设置为空字符串，默认值为null
	if req.Phone != "" {
		userData.Phone = &req.Phone
	}
	if req.Password != "" {
		userData.Password = utils.HashPassword(req.Password)
	}
	// 移除原有的事务处理
	if err := tx.Create(userData).Error; err != nil {
		var mysqlErr *mysql.MySQLError
		if errors.As(err, &mysqlErr) {
			// 检查错误码,新增前先做检查，这样处理会导致自增id不连续
			if mysqlErr.Number == 1062 {
				// 处理重复键错误
				return 0, appError.NewErr("用户名或者手机号已经存在，form database")
			}
		}
		return 0, err
	}
	//用户角色处理
	if req.RoleIDs != nil && len(req.RoleIDs) > 0 {
		var roles []model.AdminRoleUser
		for _, roleID := range req.RoleIDs {
			roles = append(roles, model.AdminRoleUser{
				RoleID: roleID,
				UserID: userData.ID,
			})
		}
		if err := tx.Create(&roles).Error; err != nil {
			return 0, err
		}
	}
	//用户组处理
	if req.GroupID != nil && *req.GroupID != 0 {
		var userGroup model.AdminUserUserGroupV2
		userGroup.UserID = userData.ID
		userGroup.UserGroupID = *req.GroupID
		if err := tx.Create(&userGroup).Error; err != nil {
			return 0, err
		}
	}

	return userData.ID, nil
}

func (svc *adminUserSvc) addUserLog(c *gin.Context, uid uint, username, phone, OpType, remark string) {
	logInfo := model.AdminUserLog{
		UID:        int(uid),
		Username:   username,
		OpType:     OpType,
		Phone:      phone,
		Remark:     remark,
		Method:     c.Request.Method,
		Path:       c.FullPath(),
		Operator:   int(c.GetUint("uid")),
		OperatorIP: c.ClientIP(),
		CreatedAt:  time.Now(),
	}
	if err := svc.db.Create(&logInfo).Error; err != nil {
		log.Error("添加用户日志失败", zap.Error(err), zap.Any("logInfo", logInfo))
	}
}

func (svc *adminUserSvc) queryListConditions(query *gorm.DB, param api2.AdminUserReq) *gorm.DB {
	// 条件处理
	if param.Name != "" {
		query = query.Where("u.name like ?", "%"+param.Name+"%")
	}
	if param.Username != "" {
		query = query.Where("u.username like ?", "%"+param.Username+"%")
	}
	if param.Phone != "" {
		query = query.Where("u.phone = ?", param.Phone)
	}
	if param.Role != 0 {
		subQuery := svc.db.Table("admin_role_users ru2").
			Joins("JOIN admin_roles r2 ON ru2.role_id = r2.id").
			Where("ru2.user_id = u.id AND r2.id = ?", param.Role)
		query = query.Where("EXISTS (?)", subQuery)
	}
	if param.Status != nil {
		query = query.Where("u.status = ?", param.Status)
	}
	if param.GroupID > 0 {
		query = query.Where("ug.user_group_id = ?", param.GroupID)
	}
	if param.TagID > 0 {
		subQuery := svc.db.Table("wecom_user_tag wt2").
			Where("wt2.user_id = u.id AND wt2.tag_id = ?", param.TagID)
		query = query.Where("EXISTS (?)", subQuery)
	}
	if param.ID > 0 {
		query = query.Where("u.id = ?", param.ID)
	}
	if param.IDs != "" {
		// 将逗号分隔的字符串转为整型切片
		idSlice := cast.ToIntSlice(strings.Split(param.IDs, ","))
		if len(idSlice) > 0 {
			query = query.Where("u.id IN (?)", idSlice)
		}
	}
	if param.Roles != "" {
		// 将逗号分隔的字符串转为整型切片
		roleIdSlice := cast.ToIntSlice(strings.Split(param.Roles, ","))
		if len(roleIdSlice) > 0 {
			subQuery := svc.db.Table("admin_role_users ru").
				Where("ru.user_id = u.id AND ru.role_id IN (?)", roleIdSlice)
			query = query.Where("EXISTS (?)", subQuery)
		}
	}
	if param.ActivedAt != "" {
		activedTime, err := time.Parse(time.DateTime, param.ActivedAt)
		if err == nil {
			query = query.Where("u.actived_at > ?", activedTime)
		}
	}
	if param.AgencyID > 0 {
		query = query.Where("ta.id =? OR sa.id = ?", param.AgencyID, param.AgencyID)
	}
	if param.TopAgency > 0 {
		query = query.Where("ta.id =? ", param.TopAgency)
	}
	if param.SecondAgency > 0 {
		query = query.Where("sa.id =? ", param.SecondAgency)
	}
	if param.Channel != "" {
		query = query.Where("ta.channel =? or sa.channel = ?", param.Channel, param.Channel)
	}
	if param.Type == consts.AgencyPrefix {
		query = query.Where("ua.id is not null")
	} else if param.Type == consts.EndpointPrefix {
		query = query.Where("e.id is not null")
		if param.EndpointID > 0 {
			query = query.Where("e.id = ?", param.EndpointID)
		}
	}

	if param.IsWecom > 0 {
		if param.IsWecom == 1 {
			query = query.Where("u.qw_userid is not null")
		} else {
			query = query.Where("u.qw_userid is null")
		}
	}
	// 按用户分组
	query = query.Group("u.id").Order("u.id desc")
	return query
}

// buildQuery 构建查询对象
func (svc *adminUserSvc) buildListQuery(c *gin.Context, param api2.AdminUserReq) *gorm.DB {
	var selectField = []string{
		"u.id", "u.username", "u.name", "u.phone", "u.status", "u.qw_userid", "u.avatar", "u.created_at",
		"u.actived_at", "u.updated_at", "u.deleted_at",
		"COALESCE(GROUP_CONCAT(DISTINCT r.slug), '') as i_roles",
		"g.name as group_name", "g.id as group_id",
		"COALESCE(GROUP_CONCAT(DISTINCT t.id), '') as i_tags",
		"COALESCE(GROUP_CONCAT(DISTINCT t.tag_name), '') as i_tag_names",
	}

	query := svc.db.WithContext(c).Table("admin_users u")

	query = query.Select(selectField).
		Joins("left join admin_role_users as ru on u.id = ru.user_id").
		Joins("left join admin_roles as r on ru.role_id = r.id AND r.deleted_at IS NULL").
		Joins("left join admin_user_user_group_v2 as ug on ug.user_id = u.id").
		Joins("left join admin_user_group_v2 as g on g.id = ug.user_group_id").
		Joins("left join wecom_user_tag as wt on u.id = wt.user_id").
		Joins("left join wecom_tags as t on wt.tag_id = t.id and t.deleted_at IS NULL")

	// 资源分组处理
	if !c.GetBool("IsSuperAdmin") {
		//var resourceGroup []uint
		//var ok bool
		//if value, exists := c.Get("resource_group"); exists {
		//	if resourceGroup, ok = value.([]uint); !ok {
		//		log.Error("资源分组数据类型错误")
		//	}
		//}
		//query = query.Where("u.resource_id in (?)", resourceGroup)
	}
	query = svc.queryListConditions(query, param)
	return query
}

func (svc *adminUserSvc) buildAgencyListQuery(c *gin.Context, param api2.AdminUserReq) *gorm.DB {
	var selectField = []string{
		"u.id", "u.username", "u.name", "u.phone", "u.status", "u.qw_userid", "u.avatar", "u.created_at",
		"u.actived_at", "u.updated_at", "u.deleted_at",
		"COALESCE(GROUP_CONCAT(DISTINCT r.slug), '') as i_roles",
		"g.name as group_name", "g.id as group_id",
		"COALESCE(GROUP_CONCAT(DISTINCT t.id), '') as i_tags",
		"COALESCE(GROUP_CONCAT(DISTINCT t.tag_name), '') as i_tag_names",
		"ta.name as top_agency_name", "ua.top_agency", "sa.name as second_agency_name", "ua.second_agency",
	}

	query := svc.db.WithContext(c).Table("admin_users u")

	query = query.Select(selectField).
		Joins("left join admin_role_users as ru on u.id = ru.user_id").
		Joins("left join admin_roles as r on ru.role_id = r.id AND r.deleted_at IS NULL").
		Joins("left join admin_user_user_group_v2 as ug on ug.user_id = u.id").
		Joins("left join admin_user_group_v2 as g on g.id = ug.user_group_id").
		Joins("left join wecom_user_tag as wt on u.id = wt.user_id").
		Joins("left join wecom_tags as t on wt.tag_id = t.id and t.deleted_at IS NULL").
		Joins("left join user_agency as ua on ua.uid = u.id and ua.status = 1").
		Joins("left join agency as ta on ua.top_agency = ta.id").
		Joins("left join agency as sa on ua.second_agency = sa.id")

	// 资源分组处理
	if !c.GetBool("IsSuperAdmin") {
		//var resourceGroup []uint
		//var ok bool
		//if value, exists := c.Get("resource_group"); exists {
		//	if resourceGroup, ok = value.([]uint); !ok {
		//		log.Error("资源分组数据类型错误")
		//	}
		//}
		//query = query.Where("u.resource_id in (?)", resourceGroup)
	}
	query = svc.queryListConditions(query, param)
	return query
}

func (svc *adminUserSvc) buildEndpointListQuery(c *gin.Context, param api2.AdminUserReq) *gorm.DB {
	var selectField = []string{
		"u.id", "u.username", "u.name", "u.phone", "u.status", "u.qw_userid", "u.avatar", "u.created_at",
		"u.actived_at", "u.updated_at", "u.deleted_at",
		"COALESCE(GROUP_CONCAT(DISTINCT r.slug), '') as i_roles",
		"g.name as group_name", "g.id as group_id",
		"COALESCE(GROUP_CONCAT(DISTINCT t.id), '') as i_tags",
		"COALESCE(GROUP_CONCAT(DISTINCT t.tag_name), '') as i_tag_names",
		"ta.name as top_agency_name", "e.top_agency", "sa.name as second_agency_name", "e.second_agency",
		"e.name as endpoint_name", "e.id as endpoint_id",
	}

	query := svc.db.WithContext(c).Table("admin_users u")

	query = query.Select(selectField).
		Joins("left join admin_role_users as ru on u.id = ru.user_id").
		Joins("left join admin_roles as r on ru.role_id = r.id AND r.deleted_at IS NULL").
		Joins("left join admin_user_user_group_v2 as ug on ug.user_id = u.id").
		Joins("left join admin_user_group_v2 as g on g.id = ug.user_group_id").
		Joins("left join wecom_user_tag as wt on u.id = wt.user_id").
		Joins("left join wecom_tags as t on wt.tag_id = t.id and t.deleted_at IS NULL").
		Joins("left join user_endpoint as ue on ue.uid = u.id").
		Joins("left join endpoint as e on ue.endpoint = e.id").
		Joins("left join agency as ta on e.top_agency = ta.id").
		Joins("left join agency as sa on e.second_agency = sa.id")

	// 资源分组处理
	if !c.GetBool("IsSuperAdmin") {
		var resourceGroup []uint
		var ok bool
		if value, exists := c.Get("resource_group"); exists {
			if resourceGroup, ok = value.([]uint); !ok {
				log.Error("资源分组数据类型错误")
			}
		}
		fmt.Println("终端用户-资源分组", resourceGroup)
		//query = query.Where("u.resource_id in (?)", resourceGroup)
	}
	query = svc.queryListConditions(query, param)
	return query
}

func (svc *adminUserSvc) SearchUsers(c *gin.Context, params *api2.SearchUserReq) ([]api2.UserSearchResult, error) {
	var users []api2.UserSearchResult

	// 构建查询
	query := svc.db.WithContext(c).
		Table("admin_users").
		Select("id, name, username").
		Where("username LIKE ? OR name LIKE ?",
			"%"+params.Keyword+"%",
			"%"+params.Keyword+"%")

	// 分页处理
	offset := (params.Page - 1) * params.PageSize

	err := query.
		Limit(params.PageSize).
		Offset(offset).
		Scan(&users).Error

	if err != nil {
		return nil, err
	}

	return users, nil
}
