// Package router 该包主要负责定义和管理与路由相关的逻辑，
// 本文件 router.go 具体用于配置和注册相关的路由组,总的路由入口。
package router

import (
	"marketing/internal/middleware"
	"marketing/internal/pkg/log"

	ginzap "github.com/gin-contrib/zap"
	"github.com/gin-gonic/gin"
)

// Router 主路由器
// 管理所有路由组及全局中间件
type Router struct {
	engine      *gin.Engine
	groups      []IRouterGroup
	middlewares []gin.HandlerFunc
}

// RouterOption 用于配置全局选项
type RouterOption func(*Router)

// NewRouter 创建路由器，支持全局中间件配置
func NewRouter(groups []IRouterGroup, options ...RouterOption) *Router {
	engine := gin.New()
	logger := log.Default()

	// 默认的全局中间件
	defaultMiddlewares := []gin.HandlerFunc{
		middleware.Cors(),
		middleware.Logger(),
		ginzap.RecoveryWithZap(logger, true),
		middleware.SetBody(),
	}

	router := &Router{
		engine:      engine,
		groups:      groups,
		middlewares: defaultMiddlewares,
	}

	// 应用自定义配置选项
	for _, option := range options {
		option(router)
	}

	return router
}

// InitRoutes 初始化所有路由
func (r *Router) InitRoutes() {
	// 注册全局中间件
	r.engine.Use(r.middlewares...)

	// 注册所有路由组
	for _, group := range r.groups {
		routerGroup := r.engine.Group(group.Prefix())
		// 应用路由组特定的中间件
		routerGroup.Use(group.Middlewares()...)
		group.Register(routerGroup)
	}
}

// Engine 获取gin引擎
func (r *Router) Engine() *gin.Engine {
	return r.engine
}
