package mkb

import (
	"marketing/internal/api"
	"marketing/internal/pkg/types"
)

type WarrantyReq struct {
	api.PaginationParams
	Barcode            string `json:"barcode" form:"barcode"`
	BuyDateStart       string `json:"buy_date_start" form:"buy_date_start"`
	BuyDateEnd         string `json:"buy_date_end" form:"buy_date_end"`
	ActivatedDateStart string `json:"activated_at_start" form:"activated_at_start"`
	ActivatedDateEnd   string `json:"activated_at_end" form:"activated_at_end"`
}

type WarrantyResp struct {
	Barcode           string           `json:"barcode" form:"barcode"`
	BuyDate           types.CustomTime `json:"buy_date,omitempty" form:"buy_date"`
	Model             string           `json:"model" form:"model"`
	EndpointName      string           `json:"endpoint_name,omitempty" form:"endpoint_name"`
	ActivatedProvince string           `json:"activated_province,omitempty" form:"activated_province"`
	ActivatedCity     string           `json:"activated_city,omitempty" form:"activated_city"`
	ActivatedDate     types.CustomTime `json:"activated_date,omitempty" form:"activated_date"`
}

type PrototypeReq struct {
	api.PaginationParams
	ModelID        int    `json:"model_id" form:"model_id"`
	Status         *int   `json:"status" form:"status"`
	CreatedAtStart string `json:"created_at_start" form:"created_at_start"`
	CreatedAtEnd   string `json:"created_at_end" form:"created_at_end"`
}

type PrototypeResp struct {
	ID           int              `json:"id" form:"id"`
	ModelID      int              `json:"model_id" form:"model_id"`
	Model        string           `json:"model" form:"model"`
	Barcode      string           `json:"barcode" form:"barcode"`
	Number       string           `json:"number" form:"number"`
	Imei         string           `json:"imei" form:"imei"`
	TopAgency    int              `json:"top_agency" form:"top_agency"`
	SecondAgency int              `json:"second_agency" form:"second_agency"`
	Endpoint     string           `json:"endpoint" form:"endpoint"`
	UserName     string           `json:"user_name" form:"user_name"`
	Type         string           `json:"type" form:"type"`
	Status       int              `json:"status" form:"status"`
	TabletStatus int              `json:"tablet_status" form:"tablet_status"`
	CreatedAt    types.CustomTime `json:"created_at" form:"created_at"`
	UpdatedAt    types.CustomTime `json:"updated_at" form:"updated_at"`
	RemovedAt    types.CustomTime `json:"removed_at" form:"removed_at"`
}
