package endpoint

import (
	"marketing/internal/api"
	"marketing/internal/model"
	"marketing/internal/pkg/types"
)

// GetEndpointImageReq 查询终端角色入参
type GetEndpointImageReq struct {
	api.PaginationParams
	Month        string `json:"month" form:"month" binding:"required"`
	Name         string `json:"name" form:"name"`
	Code         string `json:"code" form:"code"`
	TopAgency    uint   `json:"top_agency" form:"top_agency"`       // 一级代理商名称
	SecondAgency uint   `json:"second_agency" form:"second_agency"` // 二级代理商名称
	Status       string `json:"status" form:"status"`               // 终端状态，updated 为更新，not_updated 为未更新
	// manual_to_audit: 人工待审, manual_approved: 人工审核通过, manual_rejected: 人工审核不通过, approved: 自动审核通过, rejected: 自动审核不通过
	AuditStatus string `json:"audit_status" form:"audit_status"`
}

type ListEndpointImageRes struct {
	model.EndpointImageApply
	Name            string           `json:"name"`
	Code            string           `json:"code"`
	EndpointAddress string           `json:"endpoint_address"`
	EndpointTime    types.CustomTime `json:"endpoint_time"`
	AuditUser       string           `json:"audit_user"`
	ChannelLevel    uint8            `json:"channel_level" gorm:"type:tinyint(3);not null;default:0"` // 渠道等级，0-未知，1-省会级，2-地市级，3-县级，4-镇级
	TopAgency       int              `json:"top_agency"`                                              // 一级代理
	SecondAgency    int              `json:"second_agency"`                                           // 二级代理
	AgencyName      string           `json:"agency_name"`
	Distance        *int64           `json:"distance"`
}

type AuditImageReq struct {
	ID           uint   `json:"id"`
	Status       string `json:"status" binding:"required,oneof=approved rejected"`
	AuditOpinion string `json:"audit_opinion" binding:"required_if=Status rejected,max=100"`
}
