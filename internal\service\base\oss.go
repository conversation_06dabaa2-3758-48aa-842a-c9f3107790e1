package base

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/api/base"
	"marketing/internal/config"
	appError "marketing/internal/pkg/errors"
	"marketing/internal/pkg/oss"
)

// OssService OSS服务接口
type OssService interface {
	// GetStsToken 获取OSS上传临时凭证
	GetStsToken(c *gin.Context) (*base.OssSTSResp, error)
}

type ossServiceImpl struct {
	stsClient *oss.STSClient
	cfg       *config.Config
}

// NewOssService 创建OSS服务实例
func NewOssService(cfg *config.Config) OssService {
	return &ossServiceImpl{
		stsClient: oss.NewSTSClient(),
		cfg:       cfg,
	}
}

// GetStsToken 获取OSS上传临时凭证
func (s *ossServiceImpl) GetStsToken(c *gin.Context) (*base.OssSTSResp, error) {
	// 获取STS凭证
	creds := s.stsClient.GetCredentials()
	if creds == nil {
		return nil, appError.NewErr("获取OSS STS凭证失败")
	}

	// 构建响应
	resp := &base.OssSTSResp{
		AccessKeyId:     *creds.AccessKeyId,
		AccessKeySecret: *creds.AccessKeySecret,
		SecurityToken:   *creds.SecurityToken,
		Expiration:      *creds.Expiration,
		Bucket:          s.cfg.OSS.Bucket,
		Region:          "oss-cn-shenzhen",
		Path:            s.cfg.OSS.Path,
	}

	return resp, nil
}
