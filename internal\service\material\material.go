package material

import (
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/xuri/excelize/v2"
	"marketing/internal/dao"
	"marketing/internal/model"
	"marketing/internal/pkg/utils"
)

type TMaterialSvc interface {
	GetMaterialList(c *gin.Context, param *dao.GetMaterialListParam) (infos []*model.Material, total int64)
	GetRepairMaterialList(c *gin.Context, param *dao.GetRepairMaterialListParam) (infos []*model.RepairMaterial, total int64)
	ExportRepairMaterialList(c *gin.Context, param *dao.GetRepairMaterialListParam) *excelize.File
	GetMaterialById(c *gin.Context, id int) *model.Material
	EditMaterial(c *gin.Context, m *model.Material) error
	DeleteMaterial(c *gin.Context, id int) error
}

type TMaterialSvcImpl struct {
	materialRepo dao.MaterialDao
	categoryRepo dao.MaterialCategoryDao
}

func NewMaterialService(materialRepo dao.MaterialDao, categoryRepo dao.MaterialCategoryDao) TMaterialSvc {
	return &TMaterialSvcImpl{
		materialRepo: materialRepo,
		categoryRepo: categoryRepo,
	}
}

func (s *TMaterialSvcImpl) GetMaterialList(c *gin.Context, param *dao.GetMaterialListParam) (infos []*model.Material, total int64) {
	return s.materialRepo.GetMaterialList(c, param)
}

func (s *TMaterialSvcImpl) GetRepairMaterialList(c *gin.Context, param *dao.GetRepairMaterialListParam) (infos []*model.RepairMaterial, total int64) {
	return s.materialRepo.GetRepairMaterialList(c, param)
}

func (s *TMaterialSvcImpl) ExportRepairMaterialList(c *gin.Context, param *dao.GetRepairMaterialListParam) *excelize.File {
	list, _ := s.GetRepairMaterialList(c, param)

	sheetName := "物料表"

	// 新建一个excel文件,并添加表
	file := excelize.NewFile()
	_, _ = file.NewSheet(sheetName)

	// 设置所有列居中
	style, _ := file.NewStyle(&excelize.Style{
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
		Font: &excelize.Font{
			Size: 12,
		},
	})

	_ = file.SetCellValue(sheetName, "A1", "物料编码")
	_ = file.SetCellStyle(sheetName, "A1", "A1", style)
	_ = file.SetColWidth(sheetName, "A", "A", 10.0)
	_ = file.SetCellValue(sheetName, "B1", "物料名称")
	_ = file.SetCellStyle(sheetName, "B1", "B1", style)
	_ = file.SetColWidth(sheetName, "B", "B", 15.0)
	_ = file.SetCellValue(sheetName, "C1", "物料规格")
	_ = file.SetCellStyle(sheetName, "C1", "C1", style)
	_ = file.SetColWidth(sheetName, "C", "C", 50.0)
	_ = file.SetCellValue(sheetName, "D1", "总代价格")
	_ = file.SetCellStyle(sheetName, "D1", "D1", style)
	_ = file.SetColWidth(sheetName, "D", "D", 25.0)
	_ = file.SetCellValue(sheetName, "E1", "二代价格")
	_ = file.SetCellStyle(sheetName, "E1", "E1", style)
	_ = file.SetColWidth(sheetName, "E", "E", 15.0)
	_ = file.SetCellValue(sheetName, "F1", "顾客价格")
	_ = file.SetCellStyle(sheetName, "F1", "F1", style)
	_ = file.SetColWidth(sheetName, "F", "F", 15.0)

	for i, l := range list {
		index := i + 2
		_ = file.SetCellValue(sheetName, fmt.Sprintf("A%d", index), l.Code)
		_ = file.SetCellStyle(sheetName, fmt.Sprintf("A%d", index), fmt.Sprintf("A%d", index), style)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("B%d", index), l.Name)
		_ = file.SetCellStyle(sheetName, fmt.Sprintf("B%d", index), fmt.Sprintf("B%d", index), style)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("C%d", index), l.Specification)
		_ = file.SetCellStyle(sheetName, fmt.Sprintf("C%d", index), fmt.Sprintf("C%d", index), style)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("D%d", index), l.PriceFirst)
		_ = file.SetCellStyle(sheetName, fmt.Sprintf("D%d", index), fmt.Sprintf("D%d", index), style)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("E%d", index), l.PriceSecond)
		_ = file.SetCellStyle(sheetName, fmt.Sprintf("E%d", index), fmt.Sprintf("E%d", index), style)
		_ = file.SetCellValue(sheetName, fmt.Sprintf("F%d", index), l.Price)
		_ = file.SetCellStyle(sheetName, fmt.Sprintf("F%d", index), fmt.Sprintf("E%d", index), style)
	}

	// 这一步是删除默认创建的Sheet1表
	_ = file.DeleteSheet("Sheet1")

	return file
}

func (s *TMaterialSvcImpl) GetMaterialById(c *gin.Context, id int) *model.Material {
	m := s.materialRepo.GetMaterialById(c, id)
	m.CreateTime = utils.GetTimeStr(m.CreatedAt)
	m.UpdateTime = utils.GetTimeStr(m.UpdatedAt)
	return m
}

func (s *TMaterialSvcImpl) EditMaterial(c *gin.Context, m *model.Material) error {
	if len(m.ProductionNumber) == 0 {
		return errors.New("编辑物料:物料编号为空")
	}

	if s.categoryRepo.GetMaterialCategoryById(c, m.Category) == nil {
		return errors.New("编辑物料:物料标签不存在")
	}

	oldMaterial := s.materialRepo.GetMaterialByProNum(c, m.ProductionNumber)
	if oldMaterial == nil {
		return s.materialRepo.CreateMaterial(c, m)
	}

	uMap := make(map[string]interface{}, 0)
	uMap["name"] = m.Name
	if m.IsPutAway != -1 {
		uMap["is_putaway"] = m.IsPutAway
	}
	uMap["grant_type"] = m.GrantType
	uMap["category"] = m.Category
	uMap["production_number"] = m.ProductionNumber
	uMap["specification"] = m.Specification
	uMap["price"] = m.Price
	uMap["stock"] = m.Stock
	if m.IsRecommend != -1 {
		uMap["is_recommend"] = m.IsRecommend
	}
	if m.IsNew != -1 {
		uMap["is_new"] = m.IsNew
	}
	uMap["pic"] = m.Pic
	uMap["thumbnail"] = m.Thumbnail
	uMap["description"] = m.Description
	uMap["pic_other"] = m.PicOther
	uMap["order"] = m.Order
	uMap["unit"] = m.Unit
	if m.UsedByActivity != -1 {
		uMap["used_by_activity"] = m.UsedByActivity
	}
	uMap["video"] = m.Video

	return s.materialRepo.UpdateMaterial(c, oldMaterial.Id, uMap)
}

func (s *TMaterialSvcImpl) DeleteMaterial(c *gin.Context, id int) error {
	return s.materialRepo.DeleteMaterial(c, id)
}
