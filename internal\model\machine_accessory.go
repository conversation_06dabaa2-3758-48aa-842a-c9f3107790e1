package model

import (
	"time"
)

type MachineAccessory struct {
	Id          int       `json:"id" gorm:"id"`                   // id
	Title       string    `json:"title" gorm:"title"`             // 标题
	Description string    `json:"description" gorm:"description"` // 描述
	ParentId    int       `json:"parent_id" gorm:"parent_id"`     // 父id
	Order       int       `json:"order" gorm:"order"`             // 排序
	CreatedAt   time.Time `json:"-" gorm:"created_at"`            // CreatedAt 创建时间
	CreateTime  string    `json:"create_time" gorm:"-"`           // 创建时间
	UpdatedAt   time.Time `json:"-" gorm:"updated_at"`            // UpdatedAt 修改时间
	UpdateTime  string    `json:"update_time" gorm:"-"`           // 修改时间
}

func (MachineAccessory) TableName() string {
	return "machine_accessory"
}
