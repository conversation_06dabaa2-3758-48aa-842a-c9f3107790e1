package convertor

import "sync"

var (
	k3InStockConvertor *K3InStockConvertor
	inStockOnce        sync.Once
)

func NewK3InStockConvertor() *K3InStockConvertor {
	inStockOnce.Do(func() {
		k3InStockConvertor = new(K3InStockConvertor)
	})
	return k3InStockConvertor
}

var (
	k3OutStockConvertor *K3OutStockConvertor
	outStockOnce        sync.Once
)

func NewK3OutStockConvertor() *K3OutStockConvertor {
	outStockOnce.Do(func() {
		k3OutStockConvertor = new(K3OutStockConvertor)
	})
	return k3OutStockConvertor
}

var (
	k3ReturnStockConvertor *K3ReturnStockConvertor
	returnStockOnce        sync.Once
)

func NewK3ReturnStockConvertor() *K3ReturnStockConvertor {
	returnStockOnce.Do(func() {
		k3ReturnStockConvertor = new(K3ReturnStockConvertor)
	})
	return k3ReturnStockConvertor
}

var (
	k3AllotStockConvertor *K3AllotStockConvertor
	allotStockOnce        sync.Once
)

func NewK3AllotStockConvertor() *K3AllotStockConvertor {
	allotStockOnce.Do(func() {
		k3AllotStockConvertor = new(K3AllotStockConvertor)
	})
	return k3AllotStockConvertor
}

var (
	packInfoConvertor *PackInfoConvertor
	packInfoOnce      sync.Once
)

func NewPackInfoConvertor() *PackInfoConvertor {
	packInfoOnce.Do(func() {
		packInfoConvertor = new(PackInfoConvertor)
	})
	return packInfoConvertor
}

var (
	activatedDeviceInfoConvertor *ActivatedDeviceInfoConvertor
	activatedDeviceInfoOnce      sync.Once
)

func NewActivatedDeviceInfoConvertor() *ActivatedDeviceInfoConvertor {
	activatedDeviceInfoOnce.Do(func() {
		activatedDeviceInfoConvertor = new(ActivatedDeviceInfoConvertor)
	})
	return activatedDeviceInfoConvertor
}
