package model

type KingDeeAgency struct {
	Id        int    `json:"id" gorm:"column:id"`                 // id
	Name      string `json:"name" gorm:"column:name"`             // 客户名称
	ShortName string `json:"short_name" gorm:"column:short_name"` // 客户简称
}

func (KingDeeAgency) TableName() string {
	return "kingdee_agency"
}

type KingDeeAgencyInfo struct {
	ID          uint   `gorm:"column:id"`
	CompanyName string `gorm:"column:name"`
	Code        string `gorm:"column:code"`
	TopAgency   uint   `gorm:"column:top_agency"`
	AgencyName  string `gorm:"column:agency_name"`
	Level       int    `gorm:"column:level"`
}
