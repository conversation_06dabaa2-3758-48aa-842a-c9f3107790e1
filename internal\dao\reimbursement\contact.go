package reimbursement

import (
	"errors"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	api "marketing/internal/api/reimbursement"
	"marketing/internal/model"
)

type ContactRepository interface {
	CreateContact(c *gin.Context, uid uint, topAgency uint, req *api.CreateContactReq) error
	GetContacts(c *gin.Context, topAgency uint) ([]map[string]interface{}, error)
	UpdateContact(c *gin.Context, uid uint, topAgency uint, req *api.UpdateContactReq) (bool, error)
	DeleteContact(c *gin.Context, topAgency uint, contactID uint) (bool, error)
	SetDefaultContact(c *gin.Context, topAgency uint, contactID uint) (bool, error)
	GetDefaultContact(c *gin.Context, topAgency uint) (map[string]interface{}, error)
}

type contact struct {
	db *gorm.DB
}

func NewContactRepository(db *gorm.DB) ContactRepository {
	return &contact{db: db}
}

// CreateContact 创建联系人
func (r *contact) CreateContact(c *gin.Context, uid uint, topAgency uint, req *api.CreateContactReq) error {
	return r.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		// 如果设置为默认联系人，先将其他联系人设为非默认
		if req.Default == 1 {
			err := tx.Model(&model.ReimbursementContact{}).
				Where("top_agency = ? AND `default` = 1", int(topAgency)).
				Update("`default`", 0).Error
			if err != nil {
				return err
			}
		}

		// 创建新联系人
		topAgencyInt := int(topAgency)
		contact := &model.ReimbursementContact{
			UID:       uid,
			TopAgency: &topAgencyInt,
			Name:      req.Name,
			Phone:     req.Phone,
			Province:  req.Province,
			City:      req.City,
			District:  req.District,
			Address:   req.Address,
			Default:   req.Default,
		}

		return tx.Create(contact).Error
	})
}

// GetContacts 获取联系人列表
func (r *contact) GetContacts(c *gin.Context, topAgency uint) ([]map[string]interface{}, error) {
	var contacts []map[string]interface{}

	err := r.db.WithContext(c).
		Table("reimbursement_contact cr").
		Select(`cr.id, cr.name, cr.phone, cr.province, rp.region_name AS province_name,
				cr.city, rc.region_name AS city_name, cr.district, rd.region_name AS district_name,
				cr.address, cr.default`).
		Joins("LEFT JOIN region rp ON cr.province = rp.region_id").
		Joins("LEFT JOIN region rc ON cr.city = rc.region_id").
		Joins("LEFT JOIN region rd ON cr.district = rd.region_id").
		Where("cr.top_agency = ?", topAgency).
		Order("cr.default DESC, cr.created_at DESC").
		Scan(&contacts).Error

	if err != nil {
		return nil, err
	}

	return contacts, nil
}

// UpdateContact 修改联系人
func (r *contact) UpdateContact(c *gin.Context, uid uint, topAgency uint, req *api.UpdateContactReq) (bool, error) {
	return r.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		// 如果设置为默认联系人，先将其他联系人设为非默认
		if req.Default == 1 {
			err := tx.Model(&model.ReimbursementContact{}).
				Where("top_agency = ? AND `default` = 1", int(topAgency)).
				Update("`default`", 0).Error
			if err != nil {
				return err
			}
		}

		// 构建更新数据
		updateData := map[string]interface{}{
			"name":     req.Name,
			"phone":    req.Phone,
			"province": req.Province,
			"city":     req.City,
			"district": req.District,
			"address":  req.Address,
		}

		// 如果设置了默认值，添加到更新数据中
		if req.Default == 1 {
			updateData["default"] = req.Default
		}

		// 更新联系人信息
		result := tx.Model(&model.ReimbursementContact{}).
			Where("id = ? AND top_agency = ?", req.ContactID, int(topAgency)).
			Updates(updateData)

		if result.Error != nil {
			return result.Error
		}

		// 检查是否有记录被更新
		if result.RowsAffected == 0 {
			return errors.New("联系人不存在或无权限修改")
		}

		return nil
	}) == nil, nil
}

// DeleteContact 删除联系人
func (r *contact) DeleteContact(c *gin.Context, topAgency uint, contactID uint) (bool, error) {
	// 删除联系人
	result := r.db.WithContext(c).
		Where("top_agency = ? AND id = ?", int(topAgency), contactID).
		Delete(&model.ReimbursementContact{})

	if result.Error != nil {
		return false, result.Error
	}

	// 检查是否有记录被删除
	if result.RowsAffected == 0 {
		return false, errors.New("联系人不存在或无权限删除")
	}

	return true, nil
}

// SetDefaultContact 设置默认联系人
func (r *contact) SetDefaultContact(c *gin.Context, topAgency uint, contactID uint) (bool, error) {
	return r.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		// 先将所有联系人设为非默认
		err := tx.Model(&model.ReimbursementContact{}).
			Where("top_agency = ? AND `default` = 1", int(topAgency)).
			Update("`default`", 0).Error
		if err != nil {
			return err
		}

		// 将指定联系人设为默认
		result := tx.Model(&model.ReimbursementContact{}).
			Where("id = ? AND top_agency = ?", contactID, int(topAgency)).
			Update("`default`", 1)

		if result.Error != nil {
			return result.Error
		}

		// 检查是否有记录被更新
		if result.RowsAffected == 0 {
			return errors.New("联系人不存在或无权限设置")
		}

		return nil
	}) == nil, nil
}

// GetDefaultContact 获取默认联系人
func (r *contact) GetDefaultContact(c *gin.Context, topAgency uint) (map[string]interface{}, error) {
	var contacts map[string]interface{}

	err := r.db.WithContext(c).
		Table("reimbursement_contact cr").
		Select(`cr.id, cr.name, cr.phone, cr.province, rp.region_name AS province_name,
				cr.city, rc.region_name AS city_name, cr.district, rd.region_name AS district_name,
				cr.address, cr.default`).
		Joins("LEFT JOIN region rp ON cr.province = rp.region_id").
		Joins("LEFT JOIN region rc ON cr.city = rc.region_id").
		Joins("LEFT JOIN region rd ON cr.district = rd.region_id").
		Where("cr.top_agency = ? AND cr.default = 1", topAgency).
		Take(&contacts).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 没有找到默认联系人，返回空的map而不是错误
			return make(map[string]interface{}), nil
		}
		return nil, err
	}

	return contacts, nil
}
