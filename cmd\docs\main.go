package main

import (
	"flag"
	"fmt"
	"log"
	"net/http"
	"os"
	"path/filepath"
)

var (
	port = flag.Int("port", 3000, "服务端口")
	dir  = flag.String("dir", "docs", "文档目录路径")
)

func main() {
	flag.Parse()

	// 获取文档目录的绝对路径
	docPath, err := filepath.Abs(*dir)
	if err != nil {
		log.Fatal(err)
	}

	// 检查文档目录是否存在
	if _, err := os.Stat(docPath); os.IsNotExist(err) {
		log.Fatalf("文档目录不存在: %s", docPath)
	}

	// 创建文件服务器
	fs := http.FileServer(http.Dir(docPath))
	http.Handle("/", fs)

	addr := fmt.Sprintf(":%d", *port)
	log.Printf("文档服务启动在 http://localhost%s", addr)
	log.Printf("文档目录: %s", docPath)

	if err := http.ListenAndServe(addr, nil); err != nil {
		log.Fatal(err)
	}
} 