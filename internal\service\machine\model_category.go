package machine

import (
	"errors"
	"github.com/gin-gonic/gin"
	"marketing/internal/dao"
	"marketing/internal/model"
	"sync"
)

type ModelCategoryInfo struct {
	Id        int                 `json:"id"`         // id
	Name      string              `json:"name"`       // 名称
	ChildList []ModelCategoryInfo `json:"child_list"` // 子标签列表
}

var (
	modelCategoryArray      []ModelCategoryInfo
	modelCategoryArrayMutex sync.RWMutex
	modelCategoryArrayOnce  sync.Once // 单例锁
	modelCategoryVersion    int       // 版本号
)

type ModelCategorySvcInterface interface {
	GetAllModelCategory(c *gin.Context) (infos []ModelCategoryInfo)
	GetAllRepairModelCategory(c *gin.Context) (list []*model.RepairMachineCategory)
	RefreshModelCategory(c *gin.Context) (infos []ModelCategoryInfo)
	EditModelCategory(c *gin.Context, id, pId int, name string) error
	AddModelCategory(c *gin.Context, pId int, name string) error
	DelModelCategory(c *gin.Context, id int) error
}

type ModelCategorySvc struct {
	categoryRepo dao.ModelCategoryDao
}

func NewModelCategoryService(categoryRepo dao.ModelCategoryDao) ModelCategorySvcInterface {
	svc := &ModelCategorySvc{
		categoryRepo: categoryRepo,
	}

	modelCategoryArrayOnce.Do(func() {
		svc.RefreshModelCategory(new(gin.Context))
	})

	return svc
}

func (s *ModelCategorySvc) GetAllModelCategory(c *gin.Context) (infos []ModelCategoryInfo) {
	if modelCategoryVersion != dao.GetCacheVersion(dao.CvModelCategory) {
		return s.RefreshModelCategory(c)
	}

	return modelCategoryArray
}

func (s *ModelCategorySvc) GetAllRepairModelCategory(c *gin.Context) (list []*model.RepairMachineCategory) {
	return s.categoryRepo.GetAllRepairModelCategory(c)
}

func (s *ModelCategorySvc) RefreshModelCategory(c *gin.Context) (infos []ModelCategoryInfo) {
	modelCategoryArrayMutex.Lock()
	defer modelCategoryArrayMutex.Unlock()

	modelCategoryArray = make([]ModelCategoryInfo, 0)
	categoryList := s.categoryRepo.GetAllModelCategory(c)
	for _, category := range categoryList {
		parentIds := make([]int, 0)
		if category.Level > 1 {
			parentIds = s.getModelCategoryParentIds(category.Pid, categoryList)
			if len(parentIds) == 0 {
				continue
			}
		}
		s.addCategory(parentIds, &modelCategoryArray, ModelCategoryInfo{
			Id:        category.Id,
			Name:      category.Name,
			ChildList: make([]ModelCategoryInfo, 0),
		})
	}

	// 修改版本号
	modelCategoryVersion = dao.GetCacheVersion(dao.CvModelCategory)

	return modelCategoryArray
}

func (s *ModelCategorySvc) EditModelCategory(c *gin.Context, id, pId int, name string) error {
	modelCategoryArrayMutex.Lock()
	defer modelCategoryArrayMutex.Unlock()

	if len(name) == 0 {
		return errors.New("编辑标签:名称为空")
	}

	category := s.categoryRepo.GetModelCategoryById(c, id)
	if category == nil {
		return errors.New("编辑标签:标签不存在")
	}

	oldPid := 0
	childList := make([]ModelCategoryInfo, 0)
	uMap := make(map[string]interface{})

	if pId == category.Pid {
		category.Name = name
		uMap["name"] = category.Name
	} else {
		parentCategory := s.categoryRepo.GetModelCategoryById(c, pId)
		if parentCategory == nil {
			return errors.New("编辑标签:父标签不存在")
		}

		category.Name = name
		oldPid = category.Pid
		category.Pid = parentCategory.Id
		category.Level = parentCategory.Level + 1
		uMap["name"] = category.Name
		uMap["pid"] = category.Pid
		uMap["level"] = category.Level
	}

	// 修改db
	dbErr := s.categoryRepo.UpdateModelCategory(c, category.Id, uMap)
	if dbErr != nil {
		return nil
	}

	if oldPid != category.Pid {
		// 获取父id
		oldsParentIds := s.getModelCategoryParentIdsFromDb(oldPid)
		// 获取子节点
		childList = s.getChildCategory(oldsParentIds, &modelCategoryArray, category.Id)
		s.delCategory(oldsParentIds, &modelCategoryArray, category.Id)
	}

	s.addCategory(s.getModelCategoryParentIdsFromDb(category.Pid), &modelCategoryArray, ModelCategoryInfo{
		Id:        category.Id,
		Name:      category.Name,
		ChildList: childList,
	})

	// 增加版本号
	s.addVersionNum()

	return nil
}

func (s *ModelCategorySvc) AddModelCategory(c *gin.Context, pId int, name string) error {
	modelCategoryArrayMutex.Lock()
	defer modelCategoryArrayMutex.Unlock()

	if len(name) == 0 {
		return errors.New("添加标签:名称为空")
	}

	category := &model.TModelCategory{
		Name:  name,
		Pid:   pId,
		Level: 1,
	}

	if pId != 0 {
		parentCategory := s.categoryRepo.GetModelCategoryById(c, pId)
		if parentCategory == nil {
			return errors.New("添加标签:父标签不存在")
		}

		category.Level = parentCategory.Level + 1
	}

	dbErr := s.categoryRepo.CreateModelCategory(c, category)
	if dbErr != nil {
		return dbErr
	}

	s.addCategory(s.getModelCategoryParentIdsFromDb(category.Pid), &modelCategoryArray, ModelCategoryInfo{
		Id:        category.Id,
		Name:      category.Name,
		ChildList: make([]ModelCategoryInfo, 0),
	})

	// 增加版本号
	s.addVersionNum()

	return nil
}

func (s *ModelCategorySvc) DelModelCategory(c *gin.Context, id int) error {
	modelCategoryArrayMutex.Lock()
	defer modelCategoryArrayMutex.Unlock()

	category := s.categoryRepo.GetModelCategoryById(c, id)
	if category == nil {
		return errors.New("删除标签:标签不存在")
	}

	dbErr := s.categoryRepo.DeleteModelCategory(c, id)
	if dbErr != nil {
		return dbErr
	}

	s.delCategory(s.getModelCategoryParentIdsFromDb(category.Pid), &modelCategoryArray, category.Id)

	// 增加版本号
	s.addVersionNum()

	return nil
}

func (s *ModelCategorySvc) addCategory(parentIds []int, categoryArray *[]ModelCategoryInfo, category ModelCategoryInfo) {
	if len(parentIds) > 0 {
		for i, c := range *categoryArray {
			if c.Id == parentIds[0] {
				s.addCategory(parentIds[1:], &(*categoryArray)[i].ChildList, category)
				return
			}
		}

		parentCategory := ModelCategoryInfo{
			Id:        parentIds[0],
			ChildList: make([]ModelCategoryInfo, 0),
		}

		// 加入子标签
		s.addCategory(parentIds[1:], &parentCategory.ChildList, category)
		// 加入父标签
		*categoryArray = append(*categoryArray, parentCategory)

		return
	}

	for i, c := range *categoryArray {
		if c.Id == category.Id {
			(*categoryArray)[i].Name = category.Name
			return
		}
	}

	*categoryArray = append(*categoryArray, category)

	return
}

func (s *ModelCategorySvc) delCategory(parentIds []int, categoryArray *[]ModelCategoryInfo, id int) {
	if len(parentIds) > 0 {
		for i, c := range *categoryArray {
			if c.Id == parentIds[0] {
				s.delCategory(parentIds[1:], &(*categoryArray)[i].ChildList, id)
			}
		}
	}

	childList := make([]ModelCategoryInfo, 0)
	for _, c := range *categoryArray {
		if c.Id != id {
			childList = append(childList, c)
		}
	}

	*categoryArray = childList

	return
}

func (s *ModelCategorySvc) getChildCategory(parentIds []int, categoryArray *[]ModelCategoryInfo, id int) []ModelCategoryInfo {
	if len(parentIds) > 0 {
		for i, c := range *categoryArray {
			if c.Id == parentIds[0] {
				return s.getChildCategory(parentIds[1:], &(*categoryArray)[i].ChildList, id)
			}
		}
	}

	for _, c := range *categoryArray {
		if c.Id == id {
			return c.ChildList
		}
	}

	return make([]ModelCategoryInfo, 0)
}

func (s *ModelCategorySvc) getModelCategoryParentIds(pid int, categoryList []*model.TModelCategory) []int {
	parentIds := make([]int, 0)

	for _, category := range categoryList {
		if category.Id == pid {
			if category.Level > 1 {
				parentIds = append(parentIds, s.getModelCategoryParentIds(category.Pid, categoryList)...)
				// 判断parentIds是否为空,为空说明父标签已被删除
				if len(parentIds) == 0 {
					return make([]int, 0)
				}
			}
			parentIds = append(parentIds, category.Id)
		}
	}

	return parentIds
}

func (s *ModelCategorySvc) getModelCategoryParentIdsFromDb(id int) []int {
	parentIds := make([]int, 0)

	category := s.categoryRepo.GetModelCategoryById(new(gin.Context), id)
	if category == nil {
		return parentIds
	}

	if category.Level > 1 {
		parentIds = append(parentIds, s.getModelCategoryParentIdsFromDb(category.Pid)...)
	}

	return append(parentIds, category.Id)
}

func (s *ModelCategorySvc) addVersionNum() {
	// 修改db中的版本号
	if dao.AddCacheVersion(dao.CvModelCategory) == nil {
		modelCategoryVersion += 1
	}
}
