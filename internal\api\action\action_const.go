package action

// status 活动状态 对应数据库的action_status表

type Status uint8

const (
	// ActionStatusUnknown 未知状态
	ActionStatusUnknown Status = iota
	// ActionStatusPending 等待审核
	ActionStatusPending
	// ActionStatusApproved 大区审核通过
	ActionStatusApproved
	// ActionStatusApprovedByLeader 领导审核通过
	ActionStatusApprovedByLeader
	// ActionStatusFinished 已完成活动
	ActionStatusFinished
	// ActionStatusWriteOff 核销通过
	ActionStatusWriteOff
	// ActionStatusRejected 审核失败
	ActionStatusRejected
	// ActionStatusUnfinished 未完成活动
	ActionStatusUnfinished
	// ActionStatusWriteOffFailed 核销失败
	ActionStatusWriteOffFailed
	// ActionStatusEntrySuccess 入账成功
	ActionStatusEntrySuccess
)

func (a Status) ToUint() uint8 {
	return uint8(a)
}

func (a Status) ToString() string {
	switch a {
	case 0:
		return "未知状态"
	case 1:
		return "等待审核"
	case 2:
		return "大区审核通过"
	case 3:
		return "领导审核通过"
	case 4:
		return "已完成活动"
	case 5:
		return "核销通过"
	case 6:
		return "审核失败"
	case 7:
		return "未完成活动"
	case 8:
		return "核销失败"
	case 9:
		return "入账成功"
	}
	return "错误状态"
}

// Level 活动等级
type Level int

const (
	// LevelUnknown 未知等级
	LevelUnknown Level = iota
	// ProvinceLevel 省
	ProvinceLevel
	// CityLevel 市
	CityLevel
	// CountyLevel 区县
	CountyLevel
	//TownshipLevel 乡镇
	TownshipLevel
)

func (l Level) ToString() string {
	switch l {
	case 0:
		return ""
	case 1:
		return "省级"
	case 2:
		return "市级"
	case 3:
		return "区县"
	case 4:
		return "乡镇"
	}
	return ""
}

// TypeName 活动类型
type TypeName string

const (
	// TypeUnknown 未知类型
	TypeUnknown TypeName = "普通类型"
	// OutReach   外展活动
	OutReach TypeName = "外展活动"
	// CityComplexActivity 城市综合体大型活动
	CityComplexActivity TypeName = "城市综合体大型活动"
	// ThemeActivity 大型主题活动
	ThemeActivity TypeName = "大型主题活动"
)

func (t TypeName) ToSlug() string {
	switch t {
	case TypeUnknown:
		return "ORDINARY"
	case OutReach:
		return "OUTREACH"
	case CityComplexActivity:
		return "CITY_COMPLEX_ACTIVITY"
	case ThemeActivity:
		return "THEME_ACTIVITY"
	default:
		return "ORDINARY"
	}
}
func (t TypeName) ToString() string {
	return string(t)
}
func (t TypeName) TrancesString() string {
	switch t {
	case "OUTREACH":
		return "外展活动"
	case "CITY_COMPLEX_ACTIVITY":
		return "城市综合体大型活动"
	case "THEME_ACTIVITY":
		return "大型主题活动"
	default:
		return "普通类型"
	}
}
func (t TypeName) Range() []string {
	return []string{
		TypeUnknown.ToString(),
		OutReach.ToString(),
		CityComplexActivity.ToString(),
		ThemeActivity.ToString(),
	}
}
