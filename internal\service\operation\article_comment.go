package operation

import (
	"errors"
	"github.com/gin-gonic/gin"
	"marketing/internal/api/operation"
	operationDao "marketing/internal/dao/operation"
	"marketing/internal/model"
	"marketing/internal/pkg/oss"
	"marketing/internal/pkg/utils"
	"strconv"
)

type OpArticleCommentSvcInterface interface {
	GetOpArticleCommentStatistics(c *gin.Context, commentType, pageNum, pageSize int) (infos []operation.OpArticleCommentStatistics, total int64)
	GetOpArticleList(c *gin.Context, articleId int) (infos []operation.OpArticleComment)
	UpdateOpArticleCommentStatus(c *gin.Context, aid, visible, selected, top, discarded int) error
	ReplyOpArticleComment(c *gin.Context, id, articleId int, context string, files []string, createdBy int) error
}

type OpArticleCommentService struct {
	articleRepo        operationDao.OpArticleDao
	articleCommentRepo operationDao.OpArticleCommentDao
}

func NewOpArticleCommentService(articleRepo operationDao.OpArticleDao, articleCommentRepo operationDao.OpArticleCommentDao) OpArticleCommentSvcInterface {
	return &OpArticleCommentService{
		articleRepo:        articleRepo,
		articleCommentRepo: articleCommentRepo,
	}
}

func (s *OpArticleCommentService) GetOpArticleCommentStatistics(c *gin.Context, commentType, pageNum, pageSize int) (infos []operation.OpArticleCommentStatistics, total int64) {
	articleList, total := s.articleRepo.GetOpArticleByCommentType(c, commentType, pageNum, pageSize)

	aIds := make([]uint, 0)
	for _, article := range articleList {
		aIds = append(aIds, article.ID)
	}

	comments := s.articleCommentRepo.GetOpArticleCommentInfos(c, aIds)

	for _, article := range articleList {
		info := operation.OpArticleCommentStatistics{
			ArticleId:  article.ID,
			Title:      article.Title,
			CreateTime: utils.GetTimeStr(article.CreatedAt),
		}

		for _, comment := range comments {
			if article.ID == comment.Id {
				info.CommentNum = int(comment.Num)
				break
			}
		}

		infos = append(infos, info)
	}

	return
}

func (s *OpArticleCommentService) GetOpArticleList(c *gin.Context, articleId int) (infos []operation.OpArticleComment) {
	list := s.articleCommentRepo.GetOpArticleCommentList(c, articleId)

	userIds := make([]uint, 0)
	for _, l := range list {
		if l.ReplyTo > 0 && !utils.UintHas(l.ReplyTo, userIds) {
			userIds = append(userIds, l.ReplyTo)
		}

		if l.CreatedBy > 0 && !utils.UintHas(l.CreatedBy, userIds) {
			userIds = append(userIds, l.CreatedBy)
		}
	}

	userList := s.articleCommentRepo.GetByIds(c, userIds)

	for _, l := range list {
		createName, replyName := "", ""
		for _, user := range userList {
			if len(createName) > 0 && len(replyName) > 0 {
				break
			}

			if l.CreatedBy == user.ID {
				createName = user.Name
			}

			if l.ReplyTo == user.ID {
				replyName = user.Name
			}
		}

		s.addOpArticleCommentInfo(l, createName, replyName, &infos)
	}

	return
}

func (s *OpArticleCommentService) ReplyOpArticleComment(c *gin.Context, id, articleId int, context string, files []string, createdBy int) error {
	// 过滤oss的域名
	commentFiles := make([]string, 0)
	for _, file := range files {
		v := oss.RemoveOssBaseUrl(file)
		if !utils.StringHas(oss.RemoveOssBaseUrl(file), commentFiles) {
			commentFiles = append(commentFiles, v)
		}
	}

	addComment := &model.OpArticleComment{
		Content:    context,
		ArticleID:  uint(articleId),
		Attachment: utils.JsonMarshals(commentFiles),
		CreatedBy:  uint(createdBy),
	}

	// 回复别人的评论
	if id > 0 {
		comment := s.articleCommentRepo.GetOpArticleComment(c, id)
		if comment == nil {
			return errors.New("评论不存在")
		}

		addComment.ArticleID = comment.ArticleID
		addComment.SourceID = comment.SourceID
		if comment.SourceID == 0 {
			addComment.SourceID = comment.ID
		}
		addComment.ParentID = comment.ID
		if len(comment.Ancestors) == 0 {
			addComment.Ancestors = strconv.Itoa(int(comment.ID))
		} else {
			addComment.Ancestors = comment.Ancestors + "," + strconv.Itoa(int(comment.ID))
		}
		addComment.ReplyTo = comment.CreatedBy
	}

	return s.articleCommentRepo.CreateOpArticleComment(c, addComment)
}

func (s *OpArticleCommentService) UpdateOpArticleCommentStatus(c *gin.Context, id, visible, selected, top, discarded int) error {
	uMap := make(map[string]interface{})
	if visible >= 0 {
		uMap["visible"] = visible
	}

	if selected >= 0 {
		uMap["selected"] = selected
	}

	if top >= 0 {
		uMap["top"] = top
	}

	if discarded >= 0 {
		uMap["discarded"] = discarded
	}

	return s.articleCommentRepo.UpdateOpArticleCommentStatus(c, id, uMap)
}

func (s *OpArticleCommentService) addOpArticleCommentInfo(c *model.OpArticleComment, createName, replyName string, infos *[]operation.OpArticleComment) {
	files := make([]string, 0)
	utils.JsonStrToObjectList(c.Attachment, &files)

	// 补充oss的域名
	commentFiles := make([]string, 0)
	for _, file := range files {
		commentFiles = append(commentFiles, oss.AddOssBaseUrl(file))
	}

	addInfo := operation.OpArticleComment{
		Id:         c.ID,
		Content:    c.Content,
		Files:      commentFiles,
		CreateName: createName,
		Visible:    c.Visible,
		Selected:   c.Selected,
		Top:        c.Top,
		Discarded:  c.Discarded,
		ChildList:  make([]operation.OpArticleComment, 0),
	}

	if c.SourceID == 0 && c.ParentID == 0 {
		*infos = append(*infos, addInfo)
		return
	}

	for i, info := range *infos {
		if info.Id == c.SourceID {
			if c.SourceID != c.ParentID {
				addInfo.ReplyName = replyName
			}

			(*infos)[i].ChildList = append((*infos)[i].ChildList, addInfo)
		}
	}
}
