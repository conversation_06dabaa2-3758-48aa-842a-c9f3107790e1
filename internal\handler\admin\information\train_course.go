package information

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/api/materials"
	"marketing/internal/handler"
	"marketing/internal/service"
	"strconv"
)

type TrainCourseHandle struct {
	svc service.TrainSubjectService
}

func (h *TrainCourseHandle) List(c *gin.Context) {
	var req materials.TrainSubjectListReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	if req.Page == 0 {
		req.Page = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 10
	}
	list, total, err := h.svc.List(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"list":      list,
		"total":     total,
		"page":      req.Page,
		"page_size": req.PageSize,
	})
}

func (h *TrainCourseHandle) Detail(c *gin.Context) {
	id := c.<PERSON>("id")
	idInt, err := strconv.Atoi(id)
	if err != nil {
		handler.Error(c, err)
		return
	}
	detail, err := h.svc.Detail(c, idInt)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"detail": detail,
	})
}

func (h *TrainCourseHandle) TypeTips(c *gin.Context) {
	tips, err := h.svc.TypeTips(c)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"tips": tips,
	})
}

func (h *TrainCourseHandle) EndpointTips(c *gin.Context) {
	tips, err := h.svc.EndpointTips(c)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"tips": tips,
	})
}

func (h *TrainCourseHandle) RoleTips(c *gin.Context) {
	tips, err := h.svc.RoleTips(c)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"tips": tips,
	})
}

func (h *TrainCourseHandle) Upsert(c *gin.Context) {
	var req materials.TrainSubjectAdd
	if err := c.ShouldBindJSON(&req); err != nil {
		handler.Error(c, err)
		return
	}
	id, err := h.svc.Upsert(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"id": id,
	})
}

func (h *TrainCourseHandle) RegionTips(c *gin.Context) {
	agency, err := h.svc.RegionTips(c)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"agency": agency,
	})
}

func (h *TrainCourseHandle) PracticeUpsert(c *gin.Context) {
	var param materials.TrainPractice
	if err := c.ShouldBindJSON(&param); err != nil {
		handler.Error(c, err)
		return
	}
	id, err := h.svc.PracticeUpsert(c, param)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"id": id,
	})
}

func (h *TrainCourseHandle) SWCDelete(c *gin.Context) {
	var swc struct {
		Subject int `json:"subject_id"`
		Course  int `json:"course_id"`
	}
	if err := c.ShouldBindJSON(&swc); err != nil {
		handler.Error(c, err)
		return
	}
	if err := h.svc.SWCDelete(c, swc.Subject, swc.Course); err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

// CourseCreate 批量新增课时
func (h *TrainCourseHandle) CourseCreate(c *gin.Context) {
	var param []materials.TrainCourse
	if err := c.ShouldBindJSON(&param); err != nil {
		handler.Error(c, err)
		return
	}
	ids, err := h.svc.CourseCreate(c, param, "")
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"ids": ids,
	})
}

// CourseUpdate 修改课时与课时关联
func (h *TrainCourseHandle) CourseUpdate(c *gin.Context) {
	var param materials.TrainCourse
	if err := c.ShouldBindJSON(&param); err != nil {
		handler.Error(c, err)
		return
	}
	if err := h.svc.CourseUpdate(c, param); err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

func (h *TrainCourseHandle) PracticeDelete(c *gin.Context) {
	var result struct {
		ID        int `json:"id" binding:"required"`
		SubjectID int `json:"subject_id" binding:"required"`
	}
	if err := c.ShouldBindJSON(&result); err != nil {
		handler.Error(c, err)
		return
	}
	if err := h.svc.PracticeDelete(c, result.ID, result.SubjectID); err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

func NewTrainCourseHandle(svc service.TrainSubjectService) *TrainCourseHandle {
	return &TrainCourseHandle{svc: svc}
}
