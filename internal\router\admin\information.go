package admin

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/dao"
	"marketing/internal/handler/admin/information"
	"marketing/internal/pkg/db"
	"marketing/internal/service"
)

type InformationRouter struct{}

func (i *InformationRouter) Register(r *gin.RouterGroup) {
	//营销资料
	informationRouter := r.Group("/information")
	{
		marketingDao := dao.NewGormMarketingDao(db.DB)
		marketingService := service.NewGormMarketingService(marketingDao)
		marketingController := information.NewMarketingHandle(marketingService)
		//类别
		//操作接口
		informationRouter.POST("/add", marketingController.Upsert)
		informationRouter.POST("/update", marketingController.Upsert)
		informationRouter.POST("/save", marketingController.Save)

		informationRouter.DELETE("/delete/:id", marketingController.Delete)
		//查询接口
		informationRouter.GET("/list", marketingController.List)
		informationRouter.GET("/detail/:id", marketingController.Detail)
		//提示接口
		informationRouter.GET("/tips", marketingController.Tips)
		informationRouter.GET("/all_tips", marketingController.AllTips)
		//资料
		//查询接口
		informationRouter.POST("/info/list", marketingController.InfoList)
		//操作接口
		informationRouter.DELETE("/info/delete/:id", marketingController.InfoDelete)
		informationRouter.POST("/info/update", marketingController.InfoUpdate)
		informationRouter.GET("/info/upload/:id", marketingController.Upload)
	}
	//推培资料
	trainRouter := r.Group("/train")
	{
		trainDao := dao.NewGormTrainDao(db.DB)
		trainService := service.NewGormTrainService(trainDao)
		trainController := information.NewTrainHandle(trainService)
		//类别
		//操作接口
		trainRouter.POST("/type/add", trainController.Upsert)
		trainRouter.POST("/type/update", trainController.Upsert)
		trainRouter.POST("/type/save", trainController.Save)
		trainRouter.DELETE("/type/delete/:id", trainController.Delete)
		//查询接口
		trainRouter.GET("/type/list", trainController.List)
		trainRouter.GET("/type/detail/:id", trainController.Detail)
		trainRouter.GET("/type/tips", trainController.Tips)
		trainRouter.GET("/type/all_tips", trainController.AllTips)

		//标签
		//操作接口
		trainCategoryController := information.NewTrainCategoryHandle(trainService)
		trainRouter.POST("/category/add", trainCategoryController.Upsert)
		trainRouter.POST("/category/update", trainCategoryController.Upsert)
		trainRouter.POST("/category/save", trainCategoryController.Save)
		trainRouter.DELETE("/category/delete/:id", trainCategoryController.Delete)
		//查询接口
		trainRouter.GET("/category/list", trainCategoryController.List)
		trainRouter.GET("/category/detail/:id", trainCategoryController.Detail)
		trainRouter.GET("/category/tips", trainCategoryController.Tips)
		trainRouter.GET("/category/all_tips", trainCategoryController.AllTips)

		//资料
		//查询接口
		trainRouter.POST("/info/list", trainController.InfoList)
		trainRouter.GET("/info/detail/:id", trainController.InfoDetail)
		//操作接口
		trainRouter.POST("/info/update", trainController.InfoUpdate)
		trainRouter.POST("/info/updates", trainController.InfoUpdates)
		trainRouter.GET("/info/upload/:id", trainController.Upload)

		//视频
		//操作接口
		trainRouter.POST("/video/add", trainController.VideoUpsert)
		trainRouter.POST("/video/update", trainController.VideoUpsert)
		trainRouter.DELETE("/video/delete/:id", trainController.VideoDelete)
		//查询接口
		trainRouter.POST("/video/list", trainController.VideoList)
		trainRouter.POST("/video/detail/:id", trainController.VideoDetail)
	}
	//终端培训
	terminalRouter := r.Group("/terminal")
	{
		terminalDao := dao.NewGormPaper(db.DB)
		terminalService := service.NewGormPaperService(terminalDao)
		terminalController := information.NewPaperHandle(terminalService)
		//试卷
		//操作接口
		terminalRouter.POST("/paper/add", terminalController.Upsert)
		terminalRouter.POST("/paper/update", terminalController.Upsert)
		terminalRouter.POST("/paper/status", terminalController.Status)
		terminalRouter.DELETE("/paper/delete/:id", terminalController.Delete)
		//查询接口
		terminalRouter.POST("/paper/list", terminalController.List)
		terminalRouter.GET("/paper/detail/:id", terminalController.Detail)
		//题目
		//操作接口
		terminalRouter.POST("/question/add", terminalController.QuestionUpsert)
		terminalRouter.POST("/question/update", terminalController.QuestionUpsert)
		terminalRouter.DELETE("/question/delete/:id", terminalController.QuestionDelete)
		//查询接口
		terminalRouter.POST("/question/list", terminalController.QuestionList)
		terminalRouter.GET("/question/detail/:id", terminalController.QuestionDetail)
		terminalRouter.GET("/question/tips", terminalController.QuestionTips)
		terminalRouter.GET("/question/type_tips", terminalController.QuestionTypeTips)

		courseDao := dao.NewGormTrainSubjectDao(db.DB)
		courseService := service.NewGormTrainSubjectService(courseDao)
		courseController := information.NewTrainCourseHandle(courseService)
		//专题
		//查询接口
		terminalRouter.POST("/subject/list", courseController.List)
		terminalRouter.GET("/subject/detail/:id", courseController.Detail)
		//提示接口
		terminalRouter.GET("/subject/type_tips", courseController.TypeTips)
		terminalRouter.GET("/subject/endpoint_tips", courseController.EndpointTips)
		terminalRouter.GET("/subject/role_tips", courseController.RoleTips)
		terminalRouter.GET("/subject/region_tips", courseController.RegionTips)
		//操作接口
		terminalRouter.POST("/subject/add", courseController.Upsert)
		terminalRouter.POST("/subject/update", courseController.Upsert)
		//课程与大实战
		terminalRouter.POST("/course/add", courseController.CourseCreate)
		terminalRouter.POST("/course/update", courseController.CourseUpdate)
		terminalRouter.POST("/practice/add", courseController.PracticeUpsert)
		terminalRouter.POST("/practice/delete", courseController.PracticeDelete)
		terminalRouter.POST("/practice/update", courseController.PracticeUpsert)
		//新接
		terminalRouter.POST("/swc/delete", courseController.SWCDelete)
	}
}

func NewInformationRouter() *InformationRouter {
	return &InformationRouter{}
}
