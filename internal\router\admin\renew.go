package admin

import (
	"github.com/gin-gonic/gin"
	userDao "marketing/internal/dao/admin_user"
	"marketing/internal/dao/endpoint"
	dao "marketing/internal/dao/renew"
	dao1 "marketing/internal/dao/warranty"
	"marketing/internal/handler/admin/renew"
	"marketing/internal/pkg/db"
	service "marketing/internal/service/renew"
)

type RenewRouter struct {
	renewHandler renew.ApplicationHandlerInterface
}

func NewRenewRouter() *RenewRouter {
	var Db = db.GetDB()
	userD := userDao.NewUserDao(Db)
	endpointDao := endpoint.NewEndpointDao(Db)
	renewDao := dao.NewApplicationDao(Db)
	warrantyDao := dao1.NewWarrantyDao(Db)
	renewService := service.NewApplicationService(userD, renewDao, endpointDao, warrantyDao)
	renewHandler := renew.NewApplicationHandler(renewService)
	return &RenewRouter{
		renewHandler: renewHandler,
	}
}

func (rr *RenewRouter) Register(r *gin.RouterGroup) {
	renewRouter := r.Group("renew/apply")
	{
		renewRouter.GET("", rr.renewHandler.Lists)
		renewRouter.GET("/:id", rr.renewHandler.GetInfo)
		renewRouter.PUT("/:id/audit", rr.renewHandler.Audit)
		renewRouter.GET("/issue", rr.renewHandler.GetIssue)
		renewRouter.GET("/status", rr.renewHandler.GetStatus)
		renewRouter.PUT("/completed", rr.renewHandler.Completed)
		renewRouter.GET("/export", rr.renewHandler.Export)
	}
}
