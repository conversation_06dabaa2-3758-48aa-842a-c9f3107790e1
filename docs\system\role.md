# 角色管理 API 文档

[TOC]

## 1. 获取角色列表

##### 简要描述
- 获取系统角色列表，支持分页和条件查询

##### 请求URL
- `/admin/system/roles`

##### 请求方式
- GET

##### 参数
| 参数名    | 必选 | 类型   | 说明                           |
|-----------|------|--------|--------------------------------|
| page      | 否   | int    | 页码，默认 1                   |
| page_size | 否   | int    | 每页数量，默认 20              |
| name      | 否   | string | 角色名称，支持模糊搜索         |

##### 返回示例
```json
{
    "ok": 1,
    "msg": "ok",
    "data": {
        "total": 100,
        "page": 1,
        "page_size": 20,
        "data": [
            {
                "id": 1,
                "name": "管理员",
                "slug": "administrator",
                "permissions": [
                    {
                        "id": 1,
                        "name": "用户管理",
                        "slug": "user-manage"
                    }
                ],
                "menus": [
                    {
                        "id": 1,
                        "title": "系统管理",
                        "path": "/system"
                    }
                ],
                "created_at": "2023-01-01 00:00:00",
                "updated_at": "2023-01-01 00:00:00"
            }
        ]
    }
}
```

## 2. 新增角色

##### 简要描述
- 新增系统角色

##### 请求URL
- `/admin/system/roles`

##### 请求方式
- POST

##### 参数
```json
{
    "name": "运营",
    "slug": "operator",
    "permission_ids": [1,2,3],
    "menu_ids": [1,2,3]
}
```

| 参数名         | 必选 | 类型           | 说明           |
|---------------|------|----------------|----------------|
| name          | 是   | string         | 角色名称       |
| slug          | 是   | string         | 角色标识       |
| permission_ids| 否   | array of uint  | 权限ID列表     |
| menu_ids      | 否   | array of uint  | 菜单ID列表     |

##### 返回示例
```json
{
    "ok": 1,
    "msg": "ok"
}
```

##### 备注
- slug 必须唯一
- slug 只能包含字母、数字和中划线

## 3. 更新角色

##### 简要描述
- 更新角色信息

##### 请求URL
- `/admin/system/roles/:id`

##### 请求方式
- PUT

##### 参数
```json
{
    "name": "运营",
    "slug": "operator",
    "permission_ids": [1,2,3],
    "menu_ids": [1,2,3]
}
```

| 参数名         | 必选 | 类型           | 说明           |
|---------------|------|----------------|----------------|
| name          | 是   | string         | 角色名称       |
| slug          | 是   | string         | 角色标识       |
| permission_ids| 否   | array of uint  | 权限ID列表     |
| menu_ids      | 否   | array of uint  | 菜单ID列表     |

##### 返回示例
```json
{
    "ok": 1,
    "msg": "ok"
}
```

## 4. 删除角色

##### 简要描述
- 删除系统角色

##### 请求URL
- `/admin/system/roles/:id`

##### 请求方式
- DELETE

##### 返回示例
```json
{
    "ok": 1,
    "msg": "ok"
}
```

##### 备注
- 如果角色下有用户，则不能删除
- 超级管理员角色不能删除

## 5. 更新角色权限

##### 简要描述
- 批量更新指定角色的权限列表

##### 请求URL
- `/admin/system/roles/:id/permission`

##### 请求方式
- POST

##### 参数
```json
{
    "permissions": [1, 2, 3]  // 权限ID列表
}
```

| 参数名      | 必选 | 类型           | 说明           |
|------------|------|----------------|----------------|
| permissions| 是   | array of uint  | 权限ID列表     |

##### 返回示例
```json
{
    "code": 0,
    "message": "success",
    "data": null
}
```

##### 备注
- 该接口会完全替换原有的权限列表
- 权限ID必须是已存在的权限
- 角色ID通过URL参数传递

## 6. 更新角色菜单

##### 简要描述
- 批量更新指定角色的菜单列表

##### 请求URL
- `/admin/system/roles/:id/menu`

##### 请求方式
- POST

##### 参数
```json
{
    "menus": [1, 2, 3]  // 菜单ID列表
}
```

| 参数名 | 必选 | 类型           | 说明           |
|--------|------|----------------|----------------|
| menus  | 是   | array of uint  | 菜单ID列表     |

##### 返回示例
```json
{
    "code": 0,
    "message": "success",
    "data": null
}
```

##### 备注
- 该接口会完全替换原有的菜单列表
- 菜单ID必须是已存在的菜单
- 角色ID通过URL参数传递
