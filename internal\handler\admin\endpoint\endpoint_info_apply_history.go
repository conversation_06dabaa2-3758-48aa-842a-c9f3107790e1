package endpoint

//import (
//	"github.com/gin-gonic/gin"
//	"marketing/internal/handler"
//	"marketing/internal/service"
//	"net/http"
//	"strconv"
//)
//
//type EndpointApplyHistoryController struct {
//	//Service *service.EndpointInfoApplyHistoryService
//}
//
//var HistoryController EndpointApplyHistoryController
//
////func (c *EndpointApplyHistoryController) NewEndpointApplyHistoryController(service *service.EndpointInfoApplyHistoryService) *EndpointApplyHistoryController {
////	return &EndpointApplyHistoryController{Service: service}
//
//// GetEndpointHistory handles the HTTP request for endpoint history.
//func (ctl *EndpointApplyHistoryController) GetEndpointHistory(c *gin.Context) {
//	endpointIDStr := c.Param("id")
//	// 将字符串类型的endpointID转换为int32类型
//	endpointID, err := strconv.ParseInt(endpointIDStr, 10, 32)
//	if err != nil {
//		// 如果转换失败，返回HTTP 400 Bad Request，并带上错误信息
//		c.JSON(http.StatusBadRequest, gin.H{
//			"error": "无效的终端ID",
//		})
//		// 记录错误日志（可选）
//		c.Error(err).SetMeta("Invalid endpoint ID")
//		return
//	}
//
//	data, err := service.EInfoApplyHistoryService.GetEndpointHistoryWithDetails(int(endpointID))
//	if err != nil {
//		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
//		return
//	}
//	handler.Success(c, gin.H{"data": data})
//}
