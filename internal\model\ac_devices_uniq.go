package model

type AcDevicesUniq struct {
	ID         int    `json:"id" gorm:"primary_key;AUTO_INCREMENT"`
	Series     int    `json:"series" gorm:"column:series"`
	ModelID    int    `json:"model_id" gorm:"column:model_id"`
	Model      string `json:"model" gorm:"column:model"`
	NavModel   string `json:"nav_model" gorm:"column:nav_model"`
	Uniqno     string `json:"uniqno" gorm:"column:uniqno"`
	Barcode    string `json:"barcode" gorm:"column:barcode"`
	Number     string `json:"number" gorm:"column:number"`
	Imei       string `json:"imei" gorm:"column:imei"`
	Province   string `json:"province" gorm:"column:province"`
	City       string `json:"city" gorm:"column:city"`
	District   string `json:"district" gorm:"column:district"`
	Address    string `json:"address" gorm:"column:address"`
	Adcode     string `json:"adcode" gorm:"column:adcode"`
	Area       string `json:"area" gorm:"column:area"`
	Subarea    string `json:"subarea" gorm:"column:subarea"`
	AgencyID   int    `json:"agency_id" gorm:"column:agency_id"`
	AgencyName string `json:"agency_name" gorm:"column:agency_name"`
	MesExists  int    `json:"mes_exists" gorm:"column:mes_exists"`
	Stored     int    `json:"stored" gorm:"column:stored"`
	CustCode   string `json:"cust_code" gorm:"column:cust_code"`
	CustName   string `json:"cust_name" gorm:"column:cust_name"`
	Channel    string `json:"channel" gorm:"column:channel"`
	BillDate   string `json:"bill_date" gorm:"column:bill_date"`
	Exchanged  int    `json:"exchanged" gorm:"column:exchanged"`
	Createat   string `json:"createat" gorm:"column:createat"`
	Status     int    `json:"status" gorm:"column:status"`
	DeletedAt  string `json:"deleted_at" gorm:"column:deleted_at"`
	Bindat     string `json:"bindat" gorm:"column:bindat"`
	Location   string `json:"location" gorm:"column:location"`
	Ip         string `json:"ip" gorm:"column:ip"`
	Origin     string `json:"origin" gorm:"column:origin"`
	Uid        int    `json:"uid" gorm:"column:uid"`
	Did        int    `json:"did" gorm:"column:did"`
}

func (a *AcDevicesUniq) TableName() string {
	return "ac_devices_uniq"
}
