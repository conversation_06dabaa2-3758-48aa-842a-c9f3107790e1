package dao

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"marketing/internal/model"
	"marketing/internal/pkg/db"
)

type MachineMalfunctionDao interface {
	CreateMachineMalfunction(c *gin.Context, MachineMalfunction *model.MachineMalfunction) error
	DeleteMachineMalfunction(c *gin.Context, id int) error
	UpdateMachineMalfunction(c *gin.Context, id int, uMap map[string]interface{}) error
	GetAllMachineMalfunction(c *gin.Context) (list []*model.MachineMalfunction)
	GetMachineMalfunctionById(c *gin.Context, id int) *model.MachineMalfunction
	GetMachineMalfunctionByIds(c *gin.Context, ids []int) (list []*model.MachineMalfunction)
	GetMachineMalfunctionInfoByBillId(c *gin.Context, ids []int) ([]model.RepairBillMachineMalfunctionInfo, error)
}

// MachineMalfunctionDaoImpl 实现 MachineMalfunctionDao 接口
type MachineMalfunctionDaoImpl struct {
	db *gorm.DB
}

// NewMachineMalfunctionDao 创建 MachineMalfunctionDao 实例
func NewMachineMalfunctionDao() MachineMalfunctionDao {
	return &MachineMalfunctionDaoImpl{
		db: db.GetDB(""),
	}
}

func (d *MachineMalfunctionDaoImpl) CreateMachineMalfunction(c *gin.Context, MachineMalfunction *model.MachineMalfunction) error {
	return d.db.WithContext(c).Create(MachineMalfunction).Error
}

func (d *MachineMalfunctionDaoImpl) DeleteMachineMalfunction(c *gin.Context, id int) error {
	return d.db.WithContext(c).Delete(&model.MachineMalfunction{}, "id = ?", id).Error
}

func (d *MachineMalfunctionDaoImpl) UpdateMachineMalfunction(c *gin.Context, id int, uMap map[string]interface{}) error {
	return d.db.WithContext(c).Model(&model.MachineMalfunction{}).Where("id = ?", id).Updates(uMap).Error
}

func (d *MachineMalfunctionDaoImpl) GetAllMachineMalfunction(c *gin.Context) (list []*model.MachineMalfunction) {
	d.db.WithContext(c).Model(&model.MachineMalfunction{}).Find(&list)
	return
}

func (d *MachineMalfunctionDaoImpl) GetMachineMalfunctionById(c *gin.Context, id int) *model.MachineMalfunction {
	var MachineMalfunction model.MachineMalfunction
	err := d.db.WithContext(c).Model(&model.MachineMalfunction{}).Where("id = ?", id).First(&MachineMalfunction).Error
	if err != nil {
		return nil
	}
	return &MachineMalfunction
}

func (d *MachineMalfunctionDaoImpl) GetMachineMalfunctionByIds(c *gin.Context, ids []int) (list []*model.MachineMalfunction) {
	d.db.WithContext(c).Model(&model.MachineMalfunction{}).Where("id in (?)", ids).Find(&list)
	return
}

func (d *MachineMalfunctionDaoImpl) GetMachineMalfunctionInfoByBillId(c *gin.Context, ids []int) ([]model.RepairBillMachineMalfunctionInfo, error) {
	var malfunctionList []model.RepairBillMachineMalfunctionInfo
	malfunctionSQL := `
			SELECT
			    rbmmr.repair_bill_id AS bill_id,
				mm_top.id AS malfunction_loc_id,
				mm_top.title AS malfunction_loc_name,
				mm_sec.id AS malfunction_type_id,
				mm_sec.title AS malfunction_type_name
			FROM repair_bill_machine_malfunction_relation AS rbmmr
			LEFT JOIN machine_malfunction AS mm_top ON rbmmr.malfunction_top = mm_top.id
			LEFT JOIN machine_malfunction AS mm_sec ON rbmmr.malfunction_id = mm_sec.id
			WHERE rbmmr.repair_bill_id IN (?)`
	err := d.db.WithContext(c).Raw(malfunctionSQL, ids).Scan(&malfunctionList).Error
	return malfunctionList, err
}
