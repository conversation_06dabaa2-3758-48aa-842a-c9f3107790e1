package service

import (
	"marketing/internal/dao"

	"github.com/gin-gonic/gin"
)

// DepartmentService 提供与分区相关的业务逻辑功能
type DepartmentService struct {
	dao *dao.DepartmentDAO // 数据访问对象，用于操作分区数据
}

// NewDepartmentService 创建并返回一个新的 DepartmentService 实例
func NewDepartmentService(dao *dao.DepartmentDAO) *DepartmentService {
	return &DepartmentService{dao: dao}
}

func (s *DepartmentService) GetPartitions(c *gin.Context) ([]dao.Department, int64, int, int, error) {
	name := c.Query("name") // 从请求中获取查询参数 "name"
	// 调用 DAO 的 FindDepartments 方法获取分区数据
	departments, total, page, pageSize, err := s.dao.FindDepartments(c, name)
	return departments, total, page, pageSize, err // 返回分区数据和分页信息
}

func (s *DepartmentService) GetDepartmentByID(id int64) (*dao.Department, error) {
	// 调用 DAO 的 GetDepartmentByID 方法获取分区
	return s.dao.GetDepartmentByID(id)
}
