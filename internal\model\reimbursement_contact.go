package model

import (
	"time"
)

// ReimbursementContact 报销联系人模型
type ReimbursementContact struct {
	ID           uint      `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	UID          uint      `gorm:"column:uid;not null;default:0;comment:用户id" json:"uid"`
	Name         string    `gorm:"column:name;type:varchar(100);not null;default:'';comment:姓名" json:"name"`
	TopAgency    *int      `gorm:"column:top_agency;comment:一级代理id" json:"top_agency"`
	SecondAgency int       `gorm:"column:second_agency;default:0;comment:二级代理id" json:"second_agency"`
	Phone        string    `gorm:"column:phone;type:varchar(20);not null;default:'';comment:电话" json:"phone"`
	Province     int       `gorm:"column:province;not null;comment:省" json:"province"`
	City         int       `gorm:"column:city;not null;comment:市" json:"city"`
	District     int       `gorm:"column:district;not null;comment:区" json:"district"`
	Address      string    `gorm:"column:address;type:varchar(500);not null;comment:地址" json:"address"`
	Default      int       `gorm:"column:default;type:tinyint(4);not null;default:0;comment:1--选中默认" json:"default"`
	CreatedAt    time.Time `gorm:"column:created_at;not null;default:'0000-00-00 00:00:00'" json:"created_at"`
	UpdatedAt    time.Time `gorm:"column:updated_at;not null" json:"updated_at"`
}

// TableName 设置表名
func (ReimbursementContact) TableName() string {
	return "reimbursement_contact"
}
