# 推送消息功能使用示例

## 概述
本文档展示如何使用新实现的企业微信推送消息功能，包括分页查询、筛选和推送操作。

## 1. 获取推送消息列表（分页）

### 基本分页查询
```bash
# 获取第1页，每页20条记录
curl -X GET "http://localhost:8080/admin/notice/push?page=1&page_size=20" \
  -H "Authorization: Bearer your_token"
```

### 带筛选条件的分页查询
```bash
# 按消息类型筛选
curl -X GET "http://localhost:8080/admin/notice/push?page=1&page_size=10&type_id=1" \
  -H "Authorization: Bearer your_token"

# 按内容关键词筛选
curl -X GET "http://localhost:8080/admin/notice/push?page=1&page_size=10&content=系统维护" \
  -H "Authorization: Bearer your_token"

# 组合筛选
curl -X GET "http://localhost:8080/admin/notice/push?page=2&page_size=15&type_id=1&content=通知" \
  -H "Authorization: Bearer your_token"
```

### 响应示例
```json
{
  "ok": 1,
  "msg": "success",
  "data": {
    "data": [
      {
        "id": 1,
        "type_name": "系统通知",
        "content": "系统将于今晚进行维护，请提前保存工作...",
        "platform": "[\"wecom\"]",
        "audience": "{\"tags\":[{\"id\":1,\"text\":\"管理员\"}]}",
        "total": 50,
        "fetched": 48,
        "read": 45,
        "checked": 40,
        "revoked": 0
      },
      {
        "id": 2,
        "type_name": "应用升级",
        "content": "新版本v2.1.0已发布，包含以下新功能...",
        "platform": "[\"wecom\"]",
        "audience": "{\"all\":\"all\"}",
        "total": 200,
        "fetched": 195,
        "read": 180,
        "checked": 150,
        "revoked": 0
      }
    ],
    "total": 25,
    "page": 1,
    "page_size": 20
  }
}
```

## 2. 发送推送消息

### 推送给所有用户
```bash
curl -X POST "http://localhost:8080/admin/notice/push" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_token" \
  -d '{
    "type_id": "notification",
    "content": "系统将于今晚22:00-24:00进行维护，请提前保存工作内容",
    "platform": ["wecom"],
    "audience": {
      "all": "all"
    },
    "action": "none",
    "popup": 1,
    "banner": 0
  }'
```

### 推送给指定标签用户
```bash
curl -X POST "http://localhost:8080/admin/notice/push" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_token" \
  -d '{
    "type_id": "notification",
    "content": "管理员专用通知：请及时处理待审核的申请",
    "platform": ["wecom"],
    "audience": {
      "tags": [
        {"id": 1, "text": "管理员"},
        {"id": 2, "text": "审核员"}
      ]
    },
    "action": "forward",
    "url": "/admin/applications/pending",
    "action_text": "查看详情",
    "popup": 1,
    "banner": 1,
    "banner_start_time": "2024-01-01 09:00:00",
    "banner_end_time": "2024-01-01 18:00:00"
  }'
```

### 推送给指定用户
```bash
curl -X POST "http://localhost:8080/admin/notice/push" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_token" \
  -d '{
    "type_id": "notification",
    "content": "您有新的任务分配，请及时查看",
    "platform": ["wecom"],
    "audience": {
      "users": [
        {"id": 1, "text": "张三"},
        {"id": 2, "text": "李四"}
      ]
    },
    "action": "forward",
    "url": "/tasks/assigned",
    "action_text": "查看任务",
    "popup": 1,
    "banner": 0
  }'
```

### 推送给指定角色用户
```bash
curl -X POST "http://localhost:8080/admin/notice/push" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_token" \
  -d '{
    "type_id": "notification",
    "content": "店长会议将于明天上午10点举行",
    "platform": ["wecom"],
    "audience": {
      "roles": [
        {"id": "manager", "text": "店长"},
        {"id": "assistant_manager", "text": "副店长"}
      ]
    },
    "action": "none",
    "popup": 1,
    "banner": 0
  }'
```

## 3. 搜索用户
```bash
curl -X GET "http://localhost:8080/admin/notice/push/users?search=张&page=1" \
  -H "Authorization: Bearer your_token"
```

## 4. 撤回推送消息
```bash
curl -X PUT "http://localhost:8080/admin/notice/push/1/revoke" \
  -H "Authorization: Bearer your_token"
```

## 5. 前端分页组件示例

### Vue.js 示例
```vue
<template>
  <div>
    <!-- 筛选条件 -->
    <div class="filters">
      <select v-model="filters.type_id">
        <option value="">全部类型</option>
        <option value="1">系统通知</option>
        <option value="2">应用升级</option>
      </select>
      <input v-model="filters.content" placeholder="搜索内容" />
      <button @click="loadData">搜索</button>
    </div>

    <!-- 数据列表 -->
    <table>
      <thead>
        <tr>
          <th>ID</th>
          <th>类型</th>
          <th>内容</th>
          <th>推送量</th>
          <th>状态</th>
          <th>操作</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="item in list" :key="item.id">
          <td>{{ item.id }}</td>
          <td>{{ item.type_name }}</td>
          <td>{{ item.content }}</td>
          <td>{{ item.total }}</td>
          <td>{{ item.revoked ? '已撤回' : '正常' }}</td>
          <td>
            <button v-if="!item.revoked" @click="revoke(item.id)">撤回</button>
          </td>
        </tr>
      </tbody>
    </table>

    <!-- 分页组件 -->
    <div class="pagination">
      <button @click="prevPage" :disabled="pagination.page <= 1">上一页</button>
      <span>第 {{ pagination.page }} 页，共 {{ totalPages }} 页</span>
      <button @click="nextPage" :disabled="pagination.page >= totalPages">下一页</button>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      list: [],
      pagination: {
        page: 1,
        page_size: 20,
        total: 0
      },
      filters: {
        type_id: '',
        content: ''
      }
    }
  },
  computed: {
    totalPages() {
      return Math.ceil(this.pagination.total / this.pagination.page_size)
    }
  },
  methods: {
    async loadData() {
      const params = {
        page: this.pagination.page,
        page_size: this.pagination.page_size,
        ...this.filters
      }
      
      const response = await this.$http.get('/admin/notice/push', { params })
      this.list = response.data.data
      this.pagination.total = response.data.total
    },
    prevPage() {
      if (this.pagination.page > 1) {
        this.pagination.page--
        this.loadData()
      }
    },
    nextPage() {
      if (this.pagination.page < this.totalPages) {
        this.pagination.page++
        this.loadData()
      }
    },
    async revoke(id) {
      await this.$http.put(`/admin/notice/push/${id}/revoke`)
      this.loadData() // 重新加载数据
    }
  },
  mounted() {
    this.loadData()
  }
}
</script>
```

## 6. 注意事项

1. **分页参数**: page从1开始，page_size建议设置为10-50之间
2. **筛选条件**: type_id和content可以单独使用或组合使用
3. **权限控制**: 确保用户有相应的推送权限
4. **企微配置**: 确保企业微信配置正确，用户已绑定企微账号
5. **撤回限制**: 只能撤回48小时内的推送消息
6. **内容长度**: 列表中的内容会自动截断，完整内容需要查看详情
