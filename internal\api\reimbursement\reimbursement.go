package reimbursement

import (
	"marketing/internal/api"
	"marketing/internal/model"
	"marketing/internal/pkg/types"
	"time"
)

// OrderReq 申请单ID请求
type OrderReq struct {
	ID     int  `form:"id" json:"id" binding:"required"` // 订单ID
	Agency uint `form:"agency" json:"agency"`
}

// PromotionalProductsQuickPickStatReq represents the request for promotional products quick pick statistics
type PromotionalProductsQuickPickStatReq struct {
	PolicyID             int    `form:"policy_id" json:"policy_id" binding:"required"`        // 政策ID
	OrderID              int    `form:"id" json:"id"`                                         // 订单ID
	TopAgency            int    `form:"top_agency" json:"top_agency"`                         // 顶级代理
	StartTime            string `form:"start_time" json:"start_time"`                         // 开始时间
	EndTime              string `form:"end_time" json:"end_time"`                             // 结束时间
	Status               int    `form:"status" json:"status"`                                 // 状态
	CompanyID            int    `form:"company_id" json:"company_id"`                         // 公司ID
	MaterialReturnStatus int    `form:"material_return_status" json:"material_return_status"` // 物料退回状态
	ExpressComeSn        string `form:"express_come_sn" json:"express_come_sn"`               // 快递单号
	ReimbursementStatus  int    `form:"reimbursement_status" json:"reimbursement_status"`     // 报销状态
	CompletionStatus     int    `form:"completion_status" json:"completion_status"`           // 完成状态
}

// PromotionalProductsQuickPickStatResp represents the response for promotional products quick pick statistics
type PromotionalProductsQuickPickStatResp struct {
	TotalCount         int `json:"total_count"`          // 总数量
	PendingAuditCount  int `json:"pending_audit_count"`  // 待审核数量（状态0且未回滚）
	FirstApprovedCount int `json:"first_approved_count"` // 上传收货单（状态1）
	FinalApprovedCount int `json:"final_approved_count"` // 收货单待审核（状态2）
	CompletedCount     int `json:"completed_count"`      // 收货单审核通过（状态3）
	RejectedCount      int `json:"rejected_count"`       // 申请审核不通过（状态-1）
	FailedCount        int `json:"failed_count"`         // 凭证审核不通过（状态-3）
	CancelledCount     int `json:"cancelled_count"`      // 已取消数量（状态-100）
}

// AdvertExpenseQuickPickStatReq represents the request for advert expense quick pick statistics
type AdvertExpenseQuickPickStatReq struct {
	PolicyID             int    `form:"policy_id" json:"policy_id" binding:"required"`        // 政策ID
	OrderID              int    `form:"id" json:"id"`                                         // 订单ID
	TopAgency            int    `form:"top_agency" json:"top_agency"`                         // 顶级代理
	StartTime            string `form:"start_time" json:"start_time"`                         // 开始时间
	EndTime              string `form:"end_time" json:"end_time"`                             // 结束时间
	Status               int    `form:"status" json:"status"`                                 // 状态
	CompanyID            int    `form:"company_id" json:"company_id"`                         // 公司ID
	MaterialReturnStatus int    `form:"material_return_status" json:"material_return_status"` // 物料退回状态
	ExpressComeSn        string `form:"express_come_sn" json:"express_come_sn"`               // 快递单号
	ReimbursementStatus  int    `form:"reimbursement_status" json:"reimbursement_status"`     // 报销状态
	CompletionStatus     int    `form:"completion_status" json:"completion_status"`           // 完成状态
}

// AdvertExpenseQuickPickStatResp represents the response for advert expense quick pick statistics
type AdvertExpenseQuickPickStatResp struct {
	TotalCount         int `json:"total_count"`          // 总数量
	PendingAuditCount  int `json:"pending_audit_count"`  // 待审核数量（状态0且未回滚）
	FirstApprovedCount int `json:"first_approved_count"` // 初审通过数量（状态1）
	FinalApprovedCount int `json:"final_approved_count"` // 终审通过数量（状态2）
	RejectedCount      int `json:"rejected_count"`       // 审核拒绝数量（状态-1）
	CancelledCount     int `json:"cancelled_count"`      // 已取消数量（状态-100）
}

// PromotionalProductsListReq 推广产品列表请求
type PromotionalProductsListReq struct {
	api.PaginationParams
	ID                   *int   `form:"id" json:"id"`                                         // 订单ID
	TopAgency            *int   `form:"top_agency" json:"top_agency"`                         // 顶级代理
	StartTime            string `form:"start_time" json:"start_time"`                         // 开始时间
	EndTime              string `form:"end_time" json:"end_time"`                             // 结束时间
	Status               *int   `form:"status" json:"status"`                                 // 状态
	PolicyID             int    `form:"policy_id" json:"policy_id" binding:"required"`        // 政策ID（必需）
	CompanyID            int    `form:"company_id" json:"company_id"`                         // 公司ID，默认0
	MaterialReturnStatus *int   `form:"material_return_status" json:"material_return_status"` // 物料退回状态
	ExpressComeSn        string `form:"express_come_sn" json:"express_come_sn"`               // 快递单号
	ReimbursementStatus  *int   `form:"reimbursement_status" json:"reimbursement_status"`     // 报销状态
	CompletionStatus     *int   `form:"completion_status" json:"completion_status"`           // 完成状态
}

// PromotionalProductsListResp 推广产品列表响应
type PromotionalProductsListResp struct {
	Total           int64                         `json:"total"`             // 统计信息
	ApplyOrderList  []PromotionalProductsListItem `json:"apply_order_list"`  // 列表数据
	ApplyOrderTotal *PromotionalProductsTotal     `json:"apply_order_total"` // 总计信息
}

// PromotionalProductsListStat 推广产品列表统计
type PromotionalProductsListStat struct {
	Count int `json:"count"` // 总数量
}

// PromotionalProductsListItem 推广产品列表项
type PromotionalProductsListItem struct {
	ID                       int             `json:"id"`                         // ID
	Name                     string          `json:"name"`                       // 政策名称
	TopAgency                int             `json:"top_agency"`                 // 顶级代理
	Amount                   float64         `json:"amount"`                     // 金额
	ActualAmount             float64         `json:"actual_amount"`              // 实际金额
	ReimbursementApplyAmount float64         `json:"reimbursement_apply_amount"` // 报销申请金额
	CreatedAt                string          `json:"created_at"`                 // 创建时间
	Status                   int             `json:"status"`                     // 状态
	AgencyName               string          `json:"agency_name"`                // 代理名称
	CompanyID                int             `json:"company_id"`                 // 公司ID
	Company                  string          `json:"company"`                    // 公司名称
	Code                     string          `json:"code"`                       // 编码
	ExpressComeSn            string          `json:"express_come_sn"`            // 快递单号
	ExpressComeCom           string          `json:"express_come_com"`           // 快递公司
	ExpressComeTime          *string         `json:"express_come_time"`          // 快递时间
	MaterialReturnStatus     int             `json:"material_return_status"`     // 物料退回状态
	CompletionStatus         int             `json:"completion_status"`          // 完成状态
	VoucherAuditRollback     int             `json:"voucher_audit_rollback"`     // 凭证审核回滚
	DataAuditRollback        int             `json:"data_audit_rollback"`        // 数据审核回滚
	ReimbursementStatus      *string         `json:"reimbursement_status"`       // 报销状态
	Products                 map[int]float64 `json:"products"`                   // 产品信息
}

// PromotionalProductsTotal 推广产品总计
type PromotionalProductsTotal struct {
	ProductTotals map[int]float64 `json:"product_totals"` // 各产品总计
	QuantityTotal float64         `json:"quantity_total"` // 数量总计
	AmountTotal   float64         `json:"amount_total"`   // 金额总计
}

// PromotionalProductsDetailResp 促销品申请单详情
type PromotionalProductsDetailResp struct {
	ID                   int                        `json:"id"`                        // ID
	SN                   string                     `json:"sn"`                        // 编号
	UID                  int                        `json:"uid"`                       // 用户ID
	TopAgency            int                        `json:"top_agency"`                // 顶级代理
	SecondAgency         int                        `json:"second_agency"`             // 二级代理
	CompanyID            int                        `json:"company_id"`                // 公司ID
	Amount               float64                    `json:"amount"`                    // 金额
	Company              string                     `json:"company"`                   // 公司名称
	ContactName          string                     `json:"contact_name"`              // 联系人姓名
	ContactPhone         string                     `json:"contact_phone"`             // 联系人电话
	Province             int                        `json:"-"`                         // 省份名称
	City                 int                        `json:"-"`                         // 城市名称
	District             int                        `json:"-"`                         // 区县名称
	ProvinceArr          Region                     `json:"province" gorm:"-"`         // 省份名称
	CityArr              Region                     `json:"city" gorm:"-"`             // 城市名称
	DistrictArr          Region                     `json:"district" gorm:"-"`         // 区县名称
	Address              string                     `json:"address"`                   // 地址
	Status               int                        `json:"status"`                    // 状态
	ActualAmount         float64                    `json:"actual_amount"`             // 实际金额
	CreatedAt            string                     `json:"created_at"`                // 创建时间
	VoucherAuditMan      string                     `json:"voucher_audit_man"`         // 凭证审核人
	VoucherAuditTime     types.CustomTime           `json:"voucher_audit_time"`        // 凭证审核时间
	VoucherAuditRemark   string                     `json:"voucher_audit_remark"`      // 凭证审核备注
	VoucherAuditRollback int                        `json:"voucher_audit_rollback"`    // 凭证审核回滚
	DataAuditMan         string                     `json:"data_audit_man"`            // 数据审核人
	DataVerifyTime       types.CustomTime           `json:"data_verify_time"`          // 数据验证时间
	DataAuditRemark      string                     `json:"data_audit_remark"`         // 数据审核备注
	DataAuditRollback    int                        `json:"data_audit_rollback"`       // 数据审核回滚
	PolicyID             int                        `json:"policy_id"`                 // 政策ID
	PolicyName           string                     `json:"policy_name"`               // 政策名称
	PolicyStartTime      types.CustomTime           `json:"policy_start_time"`         // 政策开始时间
	PolicyEndTime        types.CustomTime           `json:"policy_end_time"`           // 政策结束时间
	Archive              int                        `json:"archive"`                   // 归档状态
	Phone                string                     `json:"phone"`                     // 电话
	AgencyName           string                     `json:"agency_name"`               // 代理名称
	ExpressGoSN          string                     `json:"-"`                         // 发货快递单号（原计划是供应商填写现在不用了）
	ExpressGoCom         string                     `json:"-"`                         // 发货快递公司（原计划是供应商填写现在不用了）
	ExpressGoTime        types.CustomTime           `json:"-"`                         // 发货时间（原计划是供应商填写现在不用了）
	ExpressComeSN        string                     `json:"express_come_sn"`           // 收货快递单号
	ExpressComeCom       string                     `json:"express_come_com"`          // 收货快递公司
	ExpressComeTime      types.CustomTime           `json:"express_come_time"`         // 收货时间
	ApplicationInfo      []ApplicationInfo          `json:"application_info" gorm:"-"` // 申请信息
	Products             []PromotionalProductDetail `json:"products" gorm:"-"`         // 产品详情
}

type Region struct {
	Name string `json:"name"`
	Code int    `json:"code"`
}

// ApplicationInfo 申请信息
type ApplicationInfo struct {
	ID      int    `json:"id"`      // ID
	Explain string `json:"explain"` // 说明
	URL     string `json:"url"`     // 文件URL
	Type    int    `json:"type"`    // 类型
}

// PromotionalProductDetail 单项促销品详情
type PromotionalProductDetail struct {
	ID                    int              `json:"id"`                       // ID
	ProductID             int              `json:"product_id"`               // 产品ID
	Preview               []FileMetadata   `json:"preview"`                  // 预览图
	Name                  string           `json:"name"`                     // 产品名称
	Norm                  string           `json:"norm"`                     // 规格
	Unit                  string           `json:"unit"`                     // 单位
	PriceType             string           `json:"price_type"`               // 价格类型
	IncludeTaxPrice       float64          `json:"include_tax_price"`        // 含税价格
	ExcludeTaxPrice       float64          `json:"exclude_tax_price"`        // 不含税价格
	ReimbursementPrice    float64          `json:"reimbursement_price"`      // 核销价格
	Quantity              int              `json:"quantity"`                 // 数量
	Price                 float64          `json:"price"`                    // 价格
	TotalPrice            float64          `json:"total_price"`              // 总价格
	IncludeTaxAccountInfo []FileMetadata   `json:"include_tax_account_info"` // 含税账户信息
	ExcludeTaxAccountInfo []FileMetadata   `json:"exclude_tax_account_info"` // 不含税账户信息
	CommunicationLetter   []FileMetadata   `json:"communication_letter"`     // 售后交流函
	CreatedAt             types.CustomTime `json:"created_at"`
}

// AdvertExpenseListReq 广告费用列表请求
type AdvertExpenseListReq struct {
	api.PaginationParams
	ID                   *int   `form:"id" json:"id"`                                         // 订单ID
	TopAgency            *int   `form:"top_agency" json:"top_agency"`                         // 顶级代理
	StartTime            string `form:"start_time" json:"start_time"`                         // 开始时间
	EndTime              string `form:"end_time" json:"end_time"`                             // 结束时间
	Status               *int   `form:"status" json:"status"`                                 // 状态
	PolicyID             int    `form:"policy_id" json:"policy_id" binding:"required"`        // 政策ID（必需）
	CompanyID            int    `form:"company_id" json:"company_id"`                         // 公司ID，默认0
	MaterialReturnStatus *int   `form:"material_return_status" json:"material_return_status"` // 物料退回状态
	ExpressComeSn        string `form:"express_come_sn" json:"express_come_sn"`               // 快递单号
	ReimbursementStatus  *int   `form:"reimbursement_status" json:"reimbursement_status"`     // 报销状态
	CompletionStatus     *int   `form:"completion_status" json:"completion_status"`           // 完成状态
}

// AdvertExpenseListResp 广告费用列表响应
type AdvertExpenseListResp struct {
	ReimbursementListStat int64                   `json:"total"`              // 统计信息
	ReimbursementList     []AdvertExpenseListItem `json:"reimbursement_list"` // 列表数据
	ApplyOrderTotal       *AdvertExpenseTotal     `json:"apply_order_total"`  // 总计信息
}

// AdvertExpenseListStat 广告费用列表统计
type AdvertExpenseListStat struct {
	Count int `json:"count"` // 总数量
}

// AdvertExpenseListItem 广告费用列表项
type AdvertExpenseListItem struct {
	ID                   int     `json:"id"`                     // ID
	Code                 string  `json:"code"`                   // 编码
	Name                 string  `json:"name"`                   // 政策名称
	CompanyID            int     `json:"company_id"`             // 公司ID
	Company              string  `json:"company"`                // 公司名称
	Amount               float64 `json:"amount"`                 // 金额
	ActualAmount         float64 `json:"actual_amount"`          // 实际金额
	CreatedAt            string  `json:"created_at"`             // 创建时间
	Status               int     `json:"status"`                 // 状态
	AgencyName           string  `json:"agency_name"`            // 代理名称
	MaterialReturnStatus int     `json:"material_return_status"` // 物料退回状态
	ExpressComeSn        string  `json:"express_come_sn"`        // 快递单号
	CompletionStatus     int     `json:"completion_status"`      // 完成状态
	Rollback             int     `json:"rollback"`               // 回滚状态
	EstimateAmount       *int    `json:"estimate_amount"`        // 预估金额
	ReimbursementStatus  *string `json:"reimbursement_status"`   // 报销状态
}

// AdvertExpenseTotal 广告费用总计
type AdvertExpenseTotal struct {
	AmountTotal *float64 `json:"amount_total"` // 金额总计
}

// AdvertExpenseDetailResp 广告费用申请详情
type AdvertExpenseDetailResp struct {
	ID              int               `json:"id"`
	SN              string            `json:"sn"`
	UID             int               `json:"uid"`
	TopAgency       int               `json:"top_agency"`
	SecondAgency    int               `json:"second_agency"`
	CompanyID       int               `json:"company_id"`
	Company         string            `json:"company"`
	Status          string            `json:"status"`
	Amount          float64           `json:"amount"`
	ActualAmount    float64           `json:"actual_amount"`
	MaterielSource  int               `json:"materiel_source"`
	CreatedAt       types.CustomTime  `json:"created_at"`
	AuditTime       types.CustomTime  `json:"audit_time"`
	Rollback        int               `json:"rollback"`
	Remark          string            `json:"remark"`
	PolicyID        int               `json:"policy_id"`
	PolicyName      string            `json:"policy_name"`
	PolicyStartTime types.DateOnly    `json:"policy_start_time"`
	PolicyEndTime   types.DateOnly    `json:"policy_end_time"`
	Phone           string            `json:"phone"`
	AgencyName      string            `json:"agency_name"`
	ApplicationInfo []ApplicationInfo `json:"application_info" gorm:"-"` // 申请信息
}

// MaterialSignInReq 材料签收请求
type MaterialSignInReq struct {
	ID             int    `json:"id" form:"id" binding:"required"`                             // 申请单ID
	ApplyOrderType string `json:"apply_order_type" form:"apply_order_type" binding:"required"` // 申请单类型
}

type AuditAdvertExpenseReq struct {
	ID     int    `json:"id" form:"id" binding:"required"`         // 申请单ID
	Status int    `json:"status" form:"status" binding:"required"` // 审核状态
	Remark string `json:"remark" form:"remark"`                    // 审核备注
}

// SummaryShortcutStatReq 报销单汇总统计请求参数
type SummaryShortcutStatReq struct {
	api.PaginationParams
	CompanyID              int    `form:"company_id" json:"company_id"`                             // 公司ID
	PolicyID               int    `form:"policy_id" json:"policy_id" binding:"required"`            // 政策ID
	PolicyType             string `form:"policy_type" json:"policy_type"`                           // 政策类型
	TopAgency              int    `form:"top_agency" json:"top_agency"`                             // 顶级代理ID
	ReimbursementType      string `form:"reimbursement_type" json:"reimbursement_type"`             // 报销类型
	Status                 *int   `form:"status" json:"status"`                                     // 状态
	ReimbursementStartTime string `form:"reimbursement_start_time" json:"reimbursement_start_time"` // 报销开始时间
	ReimbursementEndTime   string `form:"reimbursement_end_time" json:"reimbursement_end_time"`     // 报销结束时间
	CompleteStartTime      string `form:"complete_start_time" json:"complete_start_time"`           // 完成开始时间
	CompleteEndTime        string `form:"complete_end_time" json:"complete_end_time"`               // 完成结束时间
	ReimbursementListID    int    `form:"reimbursement_list_id" json:"reimbursement_list_id"`       // 报销单ID
	CompletionStatus       *int   `form:"completion_status" json:"completion_status"`               // 完成状态
}

// ShortcutStatResp 快捷键统计返回结构
type ShortcutStatResp struct {
	TotalCount       int `json:"total_count"`       // 总数（不含拆分）
	PendingCount     int `json:"pending_count"`     // 0待核销
	ProcessingCount  int `json:"processing_count"`  // 1核销中
	CompletedCount   int `json:"completed_count"`   // 2已核销
	InvalidCount     int `json:"invalid_count"`     // -100已作废
	SplitCount       int `json:"split_count"`       // -3已拆分
	TransferredCount int `json:"transferred_count"` // -4转入/已转换
}

// SummaryListItem 核销小单列表项
type SummaryListItem struct {
	ID                       int              `json:"id"`                      // 唯一ID
	Code                     string           `json:"code"`                    // 单据编码
	ApplyOrderID             int              `json:"apply_order_id"`          // 申请单ID
	ApplyOrderType           string           `json:"apply_order_type"`        // 申请单类型 (promotional_products/advert_expense)
	CompanyID                int              `json:"company_id"`              // 公司ID
	Company                  string           `json:"company"`                 // 公司名称
	Amount                   float64          `json:"amount"`                  // 申请金额
	ReimbursementApplyAmount float64          `json:"reimbursement_amount"`    // 已核销金额
	ActualAmount             float64          `json:"actual_amount"`           // 实际金额/数量 (根据业务场景)
	NoReimbursementAmount    float64          `json:"no_reimbursement_amount"` // 未核销金额
	TurnAmount               float64          `json:"turn_amount"`             // 转单金额
	QuantityTotal            int              `json:"quantity_total"`          // 申请总数量
	ActualQuantity           int              `json:"actual_quantity"`         // 实际数量
	TopAgency                int              `json:"top_agency"`              // 顶级代理商ID
	AgencyName               string           `json:"agency_name"`             // 代理商名称
	PolicyID                 int              `json:"policy_id"`               // 政策ID
	PolicyName               string           `json:"policy_name"`             // 政策名称
	StandardType             string           `json:"standard_type"`           // 支持标准 (amount/quantity/balance)
	Status                   int              `json:"status"`                  // 流程状态 (-100~2)
	CompletionStatus         int              `json:"completion_status"`       // 完成状态
	CreatedAt                types.CustomTime `json:"created_at"`              // 创建时间
	ReimbursementTime        types.CustomTime `json:"reimbursement_time"`      // 核销时间
	CompletionTime           types.CustomTime `json:"completion_time"`         // 完成时间
	AuditTime                types.CustomTime `json:"audit_time"`              // 审核时间
	ReimbursementType        int              `json:"reimbursement_type"`      // 核销类型 (1-4)
	TurnType                 int              `json:"turn_type"`               // 转单类型 (0-未转,1-已转)
	SplitType                int              `json:"split_type"`              // 拆单类型 (0-未拆,1-已拆)
	ReimbursementListID      int              `json:"reimbursement_list_id"`   // 大单ID
	OrderID                  int              `json:"order_id"`                // 关联旧单ID
	InvalidAmount            float64          `json:"invalid_amount"`          // 作废金额
	MaterialReturnStatus     int              `json:"material_return_status"`  // 材料回寄状态 (0-2)
	InvalidRemark            string           `json:"invalid_remark"`          // 作废备注
}

type AdvertExpenseOrderSplitReq struct {
	ID          int       `json:"id" form:"id" binding:"required"`                     // 申请单ID
	SplitAmount []float64 `json:"split_amount" form:"split_amount" binding:"required"` // 拆分金额
}

type ApplyOrderSummaryInvalidReq struct {
	ID     int    `json:"id" form:"id" binding:"required"`         // 申请单ID
	Remark string `json:"remark" form:"remark" binding:"required"` // 作废备注
}

type ApplyOrderSummarySubmitReq struct {
	IDs                      []int   `json:"ids" form:"ids" binding:"required"`                                   // 申请单ID
	PolicyID                 int     `json:"policy_id" form:"policy_id" binding:"required"`                       // 政策ID
	CompanyID                int     `json:"company_id" form:"company_id" binding:"required"`                     // 公司ID
	Amount                   float64 `json:"amount" form:"amount" binding:"required"`                             // 申请金额
	ReimbursementApplyAmount float64 `json:"reimbursement_apply_amount" form:"reimbursement_apply_amount"`        // 已申请金额
	ReimbursementAmount      float64 `json:"reimbursement_amount" form:"reimbursement_amount" binding:"required"` // 本次核销金额
	ReimbursementQuantity    int     `json:"reimbursement_quantity" form:"reimbursement_quantity"`                // 核销数量,默认0
	Quantity                 int     `json:"quantity" form:"quantity"`                                            // 申请总数量
	UID                      uint
	TopAgency                uint
	Code                     string //客户编码
	Company                  string //客户名称
	StandardType             string //支持标准  standard_amount--支持金额标准   standard_quantity--标准数量   standard_balance_quota--广告费标准余额

}

type ReimbursementRetrialReq struct {
	ReimbursementID int   `json:"reimbursement_id" form:"reimbursement_id" binding:"required"` // 报销单ID
	SummaryIDs      []int `json:"summary_ids" form:"summary_ids" binding:"required"`           // 汇总单ID
}

type ReimbursementAuditReq struct {
	ID     int    `json:"id" form:"id" binding:"required"`         // 报销单ID
	Status int    `json:"status" form:"status" binding:"required"` // 审核状态
	Remark string `json:"remark" form:"remark"`                    // 审核备注
	Uid    uint   // 审核人ID
}

// AddExpressInfoReq 上传收货单（添加快递信息）请求
type AddExpressInfoReq struct {
	ID          int       `json:"id" form:"id" binding:"required"`                     // 申请单ID
	ExpressTime time.Time `json:"express_time" form:"express_time" binding:"required"` // 快递发出时间
	ExpressCom  string    `json:"express_com" form:"express_com" binding:"required"`   // 快递公司
	ExpressSN   string    `json:"express_sn" form:"express_sn" binding:"required"`     // 快递单号
}

// OrderSplitInfo 订单拆分信息
type OrderSplitInfo struct {
	Quantity int64   `json:"quantity"` // 数量
	Amount   float64 `json:"amount"`   // 金额
}

// PromotionalProductsSplitReq 促销品订单拆分请求
type PromotionalProductsSplitReq struct {
	OrderID        int              `json:"order_id" binding:"required"`         // 订单ID
	StandardType   string           `json:"standard_type"`                       // 标准类型
	OrderSplitInfo []OrderSplitInfo `json:"order_split_info" binding:"required"` // 拆分信息
}

// HomePageReq 报销首页请求
type HomePageReq struct {
	CompanyID int `json:"company_id" form:"company_id" binding:"required"` // 公司ID
}

// HomePageResp 报销首页响应
type HomePageResp struct {
	StandardBalanceQuota float64       `json:"standard_balance_quota"` // 剩余额度
	EstimateAmount       float64       `json:"estimate_amount"`        // 预估金额
	PendingTask          []PendingTask `json:"pending_task"`           // 待处理任务
}

// PendingTask 待处理任务
type PendingTask struct {
	PolicyID             int              `json:"policy_id"`              // 政策ID
	PolicyName           string           `json:"policy_name"`            // 政策名称
	PolicyType           string           `json:"policy_type"`            // 政策类型
	ID                   int              `json:"id"`                     // 申请单ID
	SN                   string           `json:"sn"`                     // 申请单号
	Status               int              `json:"status"`                 // 状态
	CreatedAt            types.CustomTime `json:"created_at"`             // 创建时间
	Amount               float64          `json:"amount"`                 // 金额
	Company              string           `json:"company"`                // 公司名称
	VoucherAuditRollback *int             `json:"voucher_audit_rollback"` // 凭证审核回滚
	DataAuditRollback    *int             `json:"data_audit_rollback"`    // 数据审核回滚
	Rollback             *int             `json:"rollback"`               // 回滚状态
	Type                 int              `json:"type"`                   // 类型：1-申请单，2-核销单
}

// ClientSummaryReq 客户端汇总请求
type ClientSummaryReq struct {
	api.PaginationParams
	CompanyID int `form:"company_id" json:"company_id" binding:"required"` // 公司ID
	Archive   int `form:"archive" json:"archive"`                          // 是否归档
	TopAgency uint
	Channel   string
}

// ClientSummaryResp 客户端汇总响应
type ClientSummaryResp struct {
	ID          int    `json:"id"`           // 政策ID
	Name        string `json:"name"`         // 政策名称
	PolicyType  string `json:"policy_type"`  // 政策类型
	Archive     int    `json:"archive"`      // 归档状态
	Applied     int    `json:"applied"`      // 已申请数量
	UnderUpload int    `json:"under_upload"` // 待上传数量（仅推广产品）
	UnderAudit  int    `json:"under_audit"`  // 待审核数量（仅推广产品）
	NoReimburse int    `json:"no_reimburse"` // 未核销数量
	NoReturn    int    `json:"no_return"`    // 未回款数量
	Reimbursed  int    `json:"reimbursed"`   // 已核销数量
}

type ClientOrdersReq struct {
	api.PaginationParams
	CompanyID int    `form:"company_id" json:"company_id" binding:"required"` // 公司ID
	PolicyID  int    `form:"policy_id" json:"policy_id" binding:"required"`   // 政策ID
	Status    int    `form:"status" json:"status"`                            // 状态
	StartTime string `form:"start_time" json:"start_time"`                    // 开始时间
	EndTime   string `form:"end_time" json:"end_time"`                        // 结束时间
	TopAgency uint   `form:"top_agency" json:"top_agency"`                    // 顶级代理ID
}

type ClientOrdersResp struct {
	PolicyID      int              `json:"policy_id"`
	PolicyName    string           `json:"policy_name"`
	PolicyType    string           `json:"policy_type"`
	ID            int              `json:"id"`
	SN            string           `json:"sn"`
	Status        int              `json:"status"`
	CreatedAt     types.CustomTime `json:"created_at"`
	Amount        float64          `json:"amount"`
	Company       string           `json:"company"`
	ConfirmStatus int              `json:"confirm_status"`
	Type          int              `json:"type"`
}

// ReimbursementDetail 核销详情
type ReimbursementDetail struct {
	ID                   int                     `json:"id"`
	SN                   string                  `json:"sn"`
	ReimbursementType    int                     `json:"reimbursement_type"`
	ReimbursementAmount  float64                 `json:"reimbursement_amount"`
	CompanyID            int                     `json:"company_id"`
	Company              string                  `json:"company"`
	Amount               float64                 `json:"amount"`
	Status               int                     `json:"status"`
	Remark               string                  `json:"remark"`
	PolicyType           string                  `json:"policy_type"`
	PolicyName           string                  `json:"policy_name"`
	UserName             string                  `json:"user_name"`
	Phone                string                  `json:"phone"`
	Province             string                  `json:"province"`
	City                 string                  `json:"city"`
	District             string                  `json:"district"`
	Address              string                  `json:"address"`
	Explain              string                  `json:"explain"`
	CreatedAt            types.CustomTime        `json:"created_at"`
	ConfirmStatus        int                     `json:"confirm_status"`
	Count                int                     `json:"count"`
	ReimbursementSummary []*ReimbursementSummary `json:"reimbursement_summary"`
}

// ReimbursementSummary 核销汇总信息
type ReimbursementSummary struct {
	ID              int                        `json:"id"`
	SN              string                     `json:"sn"`
	ApplyOrderID    int                        `json:"apply_order_id"`
	Company         string                     `json:"company"`
	AgencyName      string                     `json:"agency_name"`
	CreatedAt       types.CustomTime           `json:"created_at"`
	Name            string                     `json:"name"`
	Phone           string                     `json:"phone"`
	Amount          float64                    `json:"amount"`
	ActualAmount    float64                    `json:"actual_amount"`
	Status          int                        `json:"status"`
	ApplicationInfo []ApplicationInfo          `json:"application_info"`
	Products        []PromotionalProductDetail `json:"products,omitempty"`
}

type AdvertExpenseApplyReq struct {
	ID                int               `json:"id" form:"id"`                                                // 申请单ID
	PolicyID          int               `json:"policy_id" form:"policy_id"`                                  // 政策ID
	CompanyID         int               `json:"company_id" form:"company_id" binding:"required"`             // 公司ID
	CompanyName       string            `json:"company_name" form:"company_name"`                            // 公司名称
	MaterielSource    int               `json:"materiel_source" form:"materiel_source"`                      // 物料来源
	Amount            float64           `json:"amount" form:"amount" binding:"required" binding:"required"`  // 申请金额
	ApplicationInfo   []ApplicationInfo `json:"application_info" form:"application_info" binding:"required"` // 申请信息
	Code              string
	Agency            uint
	ReimbursementType int
	Uid               uint
}

type PromotionalProductsApplyReq struct {
	ID                int               `json:"id" form:"id"`
	PolicyID          int               `json:"policy_id" binding:"required"`
	Amount            float64           `json:"amount" binding:"required"`
	CompanyID         int               `json:"company_id" binding:"required"`
	Company           string            `json:"company_name" binding:"required"`
	Name              string            `json:"name" binding:"required"`
	Phone             string            `json:"phone" binding:"required"`
	Province          int               `json:"province" binding:"required"`
	City              int               `json:"city" binding:"required"`
	District          int               `json:"district" binding:"required"`
	Address           string            `json:"address" binding:"required"`
	QuantityTotal     int64             `json:"quantity_total" binding:"required"`
	ProductsInfo      []ProductInfo     `json:"products_info" binding:"required"`
	ApplicationInfo   []ApplicationInfo `json:"application_info" binding:"required"`
	CompanyCode       string
	Agency            uint
	ReimbursementType int
	Uid               uint
}

// ProductInfo 产品信息
type ProductInfo struct {
	ID        int     `json:"id"`         // 产品ID
	ProductID int     `json:"product_id"` // 产品ID
	Quantity  int     `json:"quantity"`   // 数量
	PriceType string  `json:"price_type"` // 价格类型(include_tax/exclude_tax)
	Price     float64 `json:"price"`      // 单价
	Norm      int     `json:"norm"`       // 规格
}

type UploadReceiptReq struct {
	ID              int               `json:"id" binding:"required"` // 产品ID
	ApplicationInfo []ApplicationInfo `json:"application_info" binding:"required"`
}

type AmountConfirmReq struct {
	ID             int    `json:"id" form:"id" binding:"required"`                             // 申请单ID
	ExpressComeSn  string `json:"express_come_sn" form:"express_come_sn" binding:"required"`   // 快递单号
	ExpressComeCom string `json:"express_come_com" form:"express_come_com" binding:"required"` // 快递公司
	SummaryOrder   []*model.ReimbursementApplyOrderSummary
}

// CreateContactReq 创建联系人请求
type CreateContactReq struct {
	Name     string `json:"name" form:"name" binding:"required"`         // 姓名
	Phone    string `json:"phone" form:"phone" binding:"required,phone"` // 手机号
	Province int    `json:"province" form:"province" binding:"required"` // 省份ID
	City     int    `json:"city" form:"city" binding:"required"`         // 城市ID
	District int    `json:"district" form:"district" binding:"required"` // 地区ID
	Address  string `json:"address" form:"address" binding:"required"`   // 详细地址
	Default  int    `json:"default" form:"default"`                      // 是否默认联系人 0-否 1-是
}

// UpdateContactReq 修改联系人请求
type UpdateContactReq struct {
	ContactID uint   `json:"contact_id" form:"contact_id" binding:"required"` // 联系人ID
	Name      string `json:"name" form:"name" binding:"required"`             // 姓名
	Phone     string `json:"phone" form:"phone" binding:"required,phone"`     // 手机号
	Province  int    `json:"province" form:"province" binding:"required"`     // 省份ID
	City      int    `json:"city" form:"city" binding:"required"`             // 城市ID
	District  int    `json:"district" form:"district" binding:"required"`     // 地区ID
	Address   string `json:"address" form:"address" binding:"required"`       // 详细地址
	Default   int    `json:"default" form:"default"`                          // 是否默认联系人 0-否 1-是
}

// DeleteContactReq 删除联系人请求
type DeleteContactReq struct {
	ContactID uint `json:"contact_id" form:"contact_id" binding:"required"` // 联系人ID
}

// SetDefaultContactReq 设置默认联系人请求
type SetDefaultContactReq struct {
	ContactID uint `json:"contact_id" form:"contact_id" binding:"required"` // 联系人ID
}
