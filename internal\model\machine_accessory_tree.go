package model

import (
	"time"
)

type MachineAccessoryTree struct {
	ID            uint       `gorm:"primaryKey;autoIncrement;column:id"`
	ModelID       uint       `gorm:"column:model_id;not null;default:0;index;comment:机型id"`
	Title         string     `gorm:"column:title;type:varchar(255);not null;comment:故障名称"`
	Description   *string    `gorm:"column:description;type:varchar(255);comment:具体描述"`
	ParentID      uint       `gorm:"column:parent_id;not null;default:0;index;comment:父ID"`
	Order         int        `gorm:"column:order;not null;default:0"`
	Discount      int8       `gorm:"column:discount;not null;default:0;comment:物料类型 0--默认 1--可打折"`
	AccessoryMark int8       `gorm:"column:accessory_mark;not null;default:0;comment:标记 0-正常 1-主板（使用时要求填写序列号）"`
	CreatedAt     *time.Time `gorm:"column:created_at"`
	UpdatedAt     *time.Time `gorm:"column:updated_at"`
}

func (m *MachineAccessoryTree) TableName() string {
	return "machine_accessory_tree"
}
