package model

import (
	"time"
)

// AppNotificationType 推送消息类型模型
type AppNotificationType struct {
	ID         int       `json:"id" gorm:"primaryKey;autoIncrement;column:id"`
	Name       string    `json:"name" gorm:"type:varchar(20);not null;column:name"`
	Icon       string    `json:"icon" gorm:"type:varchar(100);not null;column:icon"`
	Slug       string    `json:"slug" gorm:"type:varchar(30);not null;uniqueIndex;column:slug"`
	CreatedAt  time.Time `json:"created_at" gorm:"column:created_at"`
	UpdatedAt  time.Time `json:"updated_at" gorm:"column:updated_at"`
	Manual     int8      `json:"manual" gorm:"type:tinyint(3);not null;default:1;comment:是否可手动推送;column:manual"`
	Action     string    `json:"action" gorm:"type:enum('none','forward','check_app_upgrade');not null;comment:点击消息后的动作;column:action"`
	URL        string    `json:"url" gorm:"type:varchar(100);not null;default:'';comment:跳转的路径;column:url"`
	MediaURL   string    `json:"media_url" gorm:"type:varchar(100);not null;default:'';comment:非文本消息的素材地址;column:media_url"`
	MsgType    string    `json:"msg_type" gorm:"type:enum('text','image');not null;default:'text';comment:消息类型，text-文本消息，image-图文消息;column:msg_type"`
	ActionText string    `json:"action_text" gorm:"type:varchar(10);not null;default:'';comment:action不是none的时候，按钮的提示文字;column:action_text"`
	Popup      int8      `json:"popup" gorm:"type:tinyint(3);not null;default:0;comment:消息是否需要弹窗;column:popup"`
	Banner     int8      `json:"banner" gorm:"type:tinyint(3);not null;default:0;comment:是否需要在工作台显示横幅广告;column:banner"`
}

// TableName 设置表名
func (AppNotificationType) TableName() string {
	return "app_notification_type"
}
