// Package base 该包主要经销商
package base

import (
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"marketing/internal/handler"
	service "marketing/internal/service/endpoint"
)

type EndpointHandlerInterface interface {
	GetUserSecondAgency(c *gin.Context)
	GetAgencyUserEndpoint(c *gin.Context)
}

type endpoint struct {
	svc service.Endpoint
}

func NewEndpointHandler(svc service.Endpoint) EndpointHandlerInterface {
	return &endpoint{
		svc: svc,
	}
}

func (e *endpoint) GetUserSecondAgency(c *gin.Context) {
	data, err := e.svc.GetUserSecondAgency(c)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, data)
}

func (e *endpoint) GetAgencyUserEndpoint(c *gin.Context) {
	secondAgencyId := c.Query("second_agency_id")
	data, err := e.svc.GetAgencyUserEndpoint(c, cast.ToUint(secondAgencyId))
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, data)
}
