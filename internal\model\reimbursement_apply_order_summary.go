package model

import (
	"time"
)

type ReimbursementApplyOrderSummary struct {
	ID                       int        `gorm:"column:id;primaryKey;autoIncrement"`
	ApplyOrderID             int        `gorm:"column:apply_order_id"`             // 申请单id
	SN                       string     `gorm:"column:sn"`                         // 核销单号
	UID                      uint       `gorm:"column:uid"`                        // 用户id, 这个id来自admin_users表
	TopAgency                uint       `gorm:"column:top_agency"`                 // 一级代理id
	SecondAgency             uint       `gorm:"column:second_agency"`              // 二级代理id
	CompanyID                int        `gorm:"column:company_id"`                 // 公司id
	Code                     string     `gorm:"column:code"`                       // 客户编码
	Company                  string     `gorm:"column:company"`                    // 公司名称
	PolicyID                 int        `gorm:"column:policy_id"`                  // 政策id
	Amount                   float64    `gorm:"column:amount"`                     // 申请的核销金额
	ReimbursementApplyAmount float64    `gorm:"column:reimbursement_apply_amount"` // 申请核销金额
	ActualAmount             float64    `gorm:"column:actual_amount"`              // 实际核销金额
	QuantityTotal            int64      `gorm:"column:quantity_total"`             // 数量总计
	ActualQuantity           int64      `gorm:"column:actual_quantity"`            // 实际数量总计
	InvalidAmount            *float64   `gorm:"column:invalid_amount"`             // 作废金额
	Status                   int        `gorm:"column:status"`                     // 流程状态
	CreatedAt                time.Time  `gorm:"column:created_at"`                 // 创建时间
	UpdatedAt                *time.Time `gorm:"column:updated_at"`                 // 更新时间
	ApplyOrderType           string     `gorm:"column:apply_order_type"`           // 申请单类型
	AuditMan                 *int       `gorm:"column:audit_man"`                  // 核销审核人
	AuditTime                *time.Time `gorm:"column:audit_time"`                 // 审批时间
	Remark                   *string    `gorm:"column:remark"`                     // 审核不通过原因
	TurnType                 int        `gorm:"column:turn_type"`                  // 小单类型
	SplitType                int        `gorm:"column:split_type"`                 // 小单类型
	ReimbursementType        int        `gorm:"column:reimbursement_type"`         // 核销类型
	CompletionStatus         int        `gorm:"column:completion_status"`          // 完成状态
	OrderID                  *int       `gorm:"column:order_id"`                   // 用户转单或拆单前的订单id
	ReimbursementTime        *time.Time `gorm:"column:reimbursement_time"`         // 核销时间
	CompletionTime           *time.Time `gorm:"column:completion_time"`            // 完成时间
	MaterialReturnStatus     int        `gorm:"column:material_return_status"`     // 材料回寄状态
	InvalidRemark            *string    `gorm:"column:invalid_remark"`             // 作废备注
}

// TableName sets the insert table name for this struct type
func (ReimbursementApplyOrderSummary) TableName() string {
	return "reimbursement_apply_order_summary"
}
