package model

import (
	"gorm.io/gorm"
)

// AdminRoles 代表 admin_roles 表中的角色信息。
type AdminRoles struct {
	gorm.Model
	Name       string `json:"name" gorm:"column:name;uniqueIndex;size:50;not null;comment:角色名称"`
	Slug       string `json:"slug" gorm:"column:slug;uniqueIndex;size:50;not null;comment:角色标识"`
	Remark     string `json:"remark" gorm:"column:remark;size:255;not null;comment:注释"`
	Sort       *int   `json:"sort" gorm:"column:sort;default:null;comment:排序"`
	Status     int8   `json:"status" gorm:"column:status;default:1;comment:是否启用"`
	Type       string `json:"type" gorm:"column:type;type:enum('identity','business');not null;comment:角色类型，identity-身份，business-业务"`
	AppType    int8   `json:"app_type" gorm:"column:app_type;default:-1;comment:移动端入口，-1-不能登录，0-终端版，1-管理版，2-维修版，100-通用版本"`
	Employee   int8   `json:"employee" gorm:"column:employee;default:0;comment:公司用户：0否，1是"`
	Agency     int8   `json:"agency" gorm:"column:agency;default:0;comment:渠道用户：0否，1是"`
	Endpoint   int8   `json:"endpoint" gorm:"column:endpoint;default:0;comment:终端用户：0否，1是"`
	SystemType string `gorm:"size:50;not null"`
}
