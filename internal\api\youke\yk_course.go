package youke

type LiveListReq struct {
	Name    string `json:"name"`
	<PERSON>rads   string `json:"grads"`
	Subject string `json:"subject"`
	Enabled int    `json:"enabled"`
}

// LiveList 直播课列表信息
type LiveList struct {
	Course YKCourse
	//连接字段
	TeachersName string `json:"teachers_name"`
	StudentNum   int    `json:"student_num"`
	LessonNum    int    `json:"lesson_num"`
	ClassNum     int    `json:"class_num"`
}
type YKCourse struct {
	ID          uint   `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Cover       string `json:"cover"`
	Price       uint   `json:"price"`
	Grades      string `json:"grades"`
	Subject     string `json:"subject"`
	Enabled     uint8  `json:"enabled"`
	Sort        uint8  `json:"sort"`
	CreatedAt   string `json:"created_at"`
	UpdatedAt   string `json:"updated_at"`
	Banner      uint8  `json:"banner"`
}
