package materials

import "time"

// TrainInfoListReq 推培资料请求
type TrainInfoListReq struct {
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
	Status   string `json:"status"`
	Star     string `json:"star"`
	Top      string `json:"top"`
	Share    string `json:"share"`
	Banner   string `json:"banner"`
	Type     uint   `json:"type"`
	Category uint   `json:"category"`
	Label    string `json:"label"`
}

// TrainInfoList 推培资料信息
type TrainInfoList struct {
	ID                 uint      `json:"id"`
	Name               string    `json:"name"`
	Description        string    `json:"description"`
	Preview            string    `json:"preview"`
	Path               string    `json:"path"`
	ArticleLink        string    `json:"article_link"`
	TypeName           string    `json:"type_name"`
	CategoryName       string    `json:"category_name"`
	Status             uint8     `json:"status"`
	Top                uint8     `json:"top"`
	Share              uint8     `json:"share"`
	Star               uint8     `json:"star"`
	DownloadCount      uint      `json:"download_count"`
	UpdatedAt          time.Time `json:"updated_at"`
	Banner             uint8     `json:"banner"`
	Credit             float64   `json:"credit"`
	CreditLearningTime uint16    `json:"credit_learning_time"`
	CreatedBy          uint      `json:"created_by"`
	//连接字段
	PeopleNum int    `json:"num"`
	LearnNum  int    `json:"learn_num"`
	Admin     string `json:"admin"`
}
type TrainInfoUpdateReq struct {
	ID     int    `json:"id"`
	Status uint8  `json:"status"`
	Kind   string `json:"kind"`
}
type TrainInfoUpdate[T any] struct {
	Train    TrainUpdate[T] `json:"train"`
	Category []int          `json:"category"` //废字段,类别得看推培资料-标签关联表关联表
}
type TrainUpdate[T any] struct {
	ID                 uint    `json:"id"`
	Name               string  `json:"name"`
	Description        string  `json:"description"`
	Preview            T       `json:"preview"`      //预览图	多文件
	Path               string  `json:"path"`         //下载路径
	ArticleLink        string  `json:"article_link"` //文章链接	直接链接
	BannerImage        string  `json:"banner_image"` //banner图片 单文件
	Type               int     `json:"type"`
	Status             uint8   `json:"status"`
	Top                uint8   `json:"top"`
	Share              uint8   `json:"share"`
	Star               uint8   `json:"star"`
	Banner             uint8   `json:"banner"`
	Credit             float64 `json:"credit"`
	CreditLearningTime uint16  `json:"credit_learning_time"`
}

func (TrainUpdate[T]) TableName() string {
	return "train" // 手动指定表名，忽略泛型类型信息
}

type TrainInfoDetail[T any] struct {
	ID                 uint    `json:"id"`
	Name               string  `json:"name"`
	Description        string  `json:"description"`
	Preview            T       `json:"preview"`      //预览图	多文件
	Path               string  `json:"path"`         //下载路径
	ArticleLink        string  `json:"article_link"` //文章链接	直接链接
	BannerImage        string  `json:"banner_image"` //banner图片 单文件
	TypeName           string  `json:"type"`
	CategoryIDs        string  `json:"category_ids"`
	Status             uint8   `json:"status"`
	Top                uint8   `json:"top"`
	Share              uint8   `json:"share"`
	Star               uint8   `json:"star"`
	Banner             uint8   `json:"banner"`
	Credit             float64 `json:"credit"`
	CreditLearningTime uint16  `json:"credit_learning_time"`
}
