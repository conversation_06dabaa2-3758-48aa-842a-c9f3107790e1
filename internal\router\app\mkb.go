package app

import (
	"marketing/internal/handler/app"
	"marketing/internal/service/mkb"

	"github.com/gin-gonic/gin"
)

type MkbRouter struct {
	mkbHandler app.MkbHandler
}

func NewMkbRouter(mkbService mkb.MkbService) *MkbRouter {
	return &MkbRouter{
		mkbHandler: app.NewMkbHandler(mkbService),
	}
}

func (m *MkbRouter) Register(r *gin.RouterGroup) {
	mkbRouter := r.Group("/mkb")
	mkbRouter.GET("/warranty/lists", m.mkbHandler.GetWarrantyLists)
	mkbRouter.GET("/prototype/lists", m.mkbHandler.GetPrototypeLists)
	mkbRouter.GET("/activated/lists", m.mkbHandler.GetActivatedLists)
	mkbRouter.PUT("/prototype/out", m.mkbHandler.PrototypeOut)
}
