package model

import "time"

type YkCourse struct {
	ID           uint      `gorm:"column:id;type:int(10) unsigned;primaryKey;autoIncrement" json:"id"`
	Name         string    `gorm:"column:name;type:varchar(50);not null" json:"name"`
	Type         string    `gorm:"column:type;type:enum('live','record');not null;comment:live-直播课，record-录播课" json:"type"`
	Description  string    `gorm:"column:description;type:varchar(200);not null;comment:课程描述" json:"description"`
	Cover        string    `gorm:"column:cover;type:varchar(150);not null;comment:课程封面，图片" json:"cover"`
	Title        string    `gorm:"column:title;type:varchar(50);not null;comment:宣传标题" json:"title"`
	Detail       string    `gorm:"column:detail;type:text;not null;comment:课程详情" json:"detail"`
	DetailImages string    `gorm:"column:detail_images;type:text;not null;comment:详情，多个图片地址,json" json:"detail_images"`
	Price        uint      `gorm:"column:price;type:mediumint(8) unsigned;not null;comment:价格，0表示免费，分为单位" json:"price"`
	Grades       string    `gorm:"column:grades;type:varchar(23);not null;comment:适用年级" json:"grades"`
	Subject      string    `gorm:"column:subject;type:varchar(10);not null;comment:科目" json:"subject"`
	Enabled      uint8     `gorm:"column:enabled;type:tinyint(3) unsigned;not null;default:1" json:"enabled"`
	Sort         uint8     `gorm:"column:sort;type:tinyint(3) unsigned;not null;comment:排序" json:"sort"`
	CreatedAt    time.Time `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt    time.Time `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP;autoUpdateTime" json:"updated_at"`
	DeletedAt    time.Time `gorm:"column:deleted_at;type:datetime;not null;default:0000-00-00 00:00:00" json:"deleted_at"`
	SharePost    string    `gorm:"column:share_post;type:varchar(150);not null;comment:分享时图片" json:"share_post"`
	OnlineID     uint64    `gorm:"column:online_id;type:bigint(20) unsigned;not null;default:0;comment:关联的线上课程的id" json:"online_id"`
	SubjectID    uint      `gorm:"column:subject_id;type:int(10) unsigned;not null;default:0" json:"subject_id"`
	Tags         string    `gorm:"column:tags;type:varchar(100);not null;default:'';comment:课程标签，多个逗号分隔" json:"tags"`
	Global       uint8     `gorm:"column:global;type:tinyint(3) unsigned;not null;comment:课程是否全国可见" json:"global"`
	GlobalGrades uint8     `gorm:"column:global_grades;type:tinyint(3) unsigned;not null;default:0;comment:年级是否通用" json:"global_grades"`
	Triable      uint8     `gorm:"column:triable;type:tinyint(3) unsigned;not null;default:1;comment:录播课是否支持试看" json:"triable"`
	Banner       uint8     `gorm:"column:banner;type:tinyint(3) unsigned;not null;default:0;comment:课程是否显示在banner上" json:"banner"`
}

func (YkCourse) TableName() string {
	return "yk_course"
}
