package model

import (
	"time"
)

type V3Instock struct {
	ID         int        `gorm:"primaryKey;column:id" json:"id"`
	ModelID    int        `gorm:"column:model_id;comment:机型id" json:"model_id"`
	Model      string     `gorm:"column:model;size:32;comment:机型" json:"model"`
	Barcode    string     `gorm:"column:barcode;size:32;not null;index" json:"barcode"`
	Number     string     `gorm:"column:number;size:48;index" json:"number"`
	Imei       string     `gorm:"column:imei;size:16;index" json:"imei"`
	MatID      int        `gorm:"column:mat_id;comment:物料ID" json:"mat_id"`
	MatCode    string     `gorm:"column:mat_code;size:16;comment:物料编码" json:"mat_code"`
	MatModel   string     `gorm:"column:mat_model;size:16;comment:物料规格" json:"mat_model"`
	MoID       int        `gorm:"column:mo_id;comment:金蝶生产任务行ID" json:"mo_id"`
	Comment    string     `gorm:"column:comment;size:255;comment:入库备注" json:"comment"`
	BillID     uint       `gorm:"column:bill_id;comment:金蝶单据ID" json:"bill_id"` // Changed to uint to match unsigned
	BillRowID  int        `gorm:"column:bill_row_id;not null;comment:金蝶单据行ID，用于更新或删除判定;index" json:"bill_row_id"`
	BillNo     string     `gorm:"column:bill_no;size:32;comment:金蝶单据编号" json:"bill_no"`
	BillDate   *time.Time `gorm:"column:bill_date;type:date;index" json:"bill_date"` // Use time.Time for date
	AddTime    *time.Time `gorm:"column:add_time;default:0000-00-00 00:00:00;comment:记录创建时间" json:"add_time"`
	UpdateTime *time.Time `gorm:"column:update_time;index:idx_updatetime;comment:记录更新时间" json:"update_time"`
}

func (V3Instock) TableName() string {
	return "v3_instock"
}
