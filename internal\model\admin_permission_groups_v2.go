package model

import (
	"gorm.io/gorm"
	"time"
)

type AdminPermissionGroup struct {
	ID          uint           `gorm:"primaryKey;autoIncrement" json:"id"`   // 权限组 ID，自增主键
	Name        string         `gorm:"size:100;unique;not null" json:"name"` // 权限组名称，不能为空且唯一
	Description string         `gorm:"type:text" json:"description"`         // 权限组描述
	SystemType  string         `gorm:"size:50;not null"`
	CreatedAt   time.Time      `gorm:"autoCreateTime" json:"created_at"` // 创建时间
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"deleted_at"`          // 删除时间，允许为 NULL
}

// TableName 设置表的别名
func (AdminPermissionGroup) TableName() string {
	return "admin_permission_groups_v2"
}
