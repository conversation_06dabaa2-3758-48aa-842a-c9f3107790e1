package consts

import (
	"sort"
	"strconv"
)

// Dropdown 定义一个结构体来存储状态和对应的中文描述
type Dropdown struct {
	Value string `json:"value"`
	Label string `json:"label"`
}

const (
	// PrototypeTypeSelfDestructing 自拆样机
	PrototypeTypeSelfDestructing = 0
	// PrototypeTypePolicy 政策样机
	PrototypeTypePolicy = 1
	// PrototypeTypeExchange 换机样机
	PrototypeTypeExchange = 2
	// PrototypeTypeReturn 退机样机
	PrototypeTypeReturn = 3
	// PrototypeTypeDemo 演示样机
	PrototypeTypeDemo = 4
)

const (
	PrototypeListingOn  = 0 // 正常上市
	PrototypeListingOff = 1 // 已下市
)

var prototypeTypeToNameMap = map[int]string{
	0: "自拆样机",
	1: "政策样机",
	2: "换机样机",
	3: "退机样机",
	4: "演示样机",
}

func GetPrototypeTypeName() map[int]string {
	// 创建一个新的 map 来存储副本
	copyMap := make(map[int]string)
	// 复制原始 map 的键值对到新的 map 中
	for key, value := range prototypeTypeToNameMap {
		copyMap[key] = value
	}
	return copyMap
}

// GetPrototypeTypeSlice 定义一个函数将 map 转换为切片并排序
func GetPrototypeTypeSlice() []Dropdown {
	var statusSlice []Dropdown
	// 将 map 中的元素添加到切片中
	for v, l := range prototypeTypeToNameMap {
		if l == "换机样机" || l == "退机样机" {
			continue
		}
		statusSlice = append(statusSlice, Dropdown{Value: strconv.Itoa(v), Label: l})
	}

	sort.Slice(statusSlice, func(i, j int) bool {
		// 直接比较 statusSlice 中元素的 Value 字段
		return statusSlice[i].Value < statusSlice[j].Value
	})

	return statusSlice
}
