package warranty

import (
	"marketing/internal/api"
	"time"
)

type DevicesLimitAccountReq struct {
	api.PaginationParams
	Barcode string `form:"barcode,omitempty" json:"barcode,omitempty"`
	Number  string `form:"number,omitempty" json:"number,omitempty"`
	Phone   string `form:"phone,omitempty" json:"phone,omitempty"`
	Model   string `form:"model,omitempty" json:"model,omitempty"`
	Status  *int   `form:"status,omitempty" json:"status,omitempty"`
}

type DevicesLimitAccount struct {
	ID        uint32     `json:"id"`
	Barcode   string     `json:"barcode"`
	Number    string     `json:"number"`
	Phone     string     `json:"phone"`
	Remark    string     `json:"remark"`
	Status    int        `json:"status"`
	Model     string     `json:"model"`
	CreatedAt *time.Time `json:"created_at"`
	UpdatedAt *time.Time `json:"updated_at"`
}

type DevicesLimitAccountUpdReq struct {
	ID     int    `json:"id"`
	Phone  string `json:"phone"`
	Status *int   `json:"status"`
}
