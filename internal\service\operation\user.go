package operation

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/api/operation"
	articleDao "marketing/internal/dao/operation"
	"marketing/internal/model"
	"marketing/internal/pkg/utils"
)

type OpUserSvcInterface interface {
	GetOpUserList(c *gin.Context, Keyword string, blocked, pageNum, pageSize int) (list []operation.OpUserInfo, total int64)
	Statistics(c *gin.Context) (total, blockNum int64)
	BlockOpUser(c *gin.Context, userIds []int, block int) error
}

type OpUserService struct {
	opUserRepo articleDao.OpUserDao
}

func NewOpUserService(opUserRepo articleDao.OpUserDao) OpUserSvcInterface {
	return &OpUserService{
		opUserRepo: opUserRepo,
	}
}

func (s *OpUserService) GetOpUserList(c *gin.Context, Keyword string, blocked, pageNum, pageSize int) (list []operation.OpUserInfo, total int64) {
	return s.opUserRepo.GetOpUserList(c, Keyword, blocked, pageNum, pageSize)
}

func (s *OpUserService) Statistics(c *gin.Context) (total, blockNum int64) {
	total = s.opUserRepo.CountOpUserNum(c)
	blockNum = s.opUserRepo.CountBlockedOpUserNum(c)
	return
}

func (s *OpUserService) BlockOpUser(c *gin.Context, userIds []int, block int) error {
	// 检查uid的合法性
	userIds = s.opUserRepo.CheckAdminUserIs(c, userIds)

	hasUserIds := make([]int, 0)
	opUserList := s.opUserRepo.GetOpUserByUserIds(c, userIds)
	for _, o := range opUserList {
		hasUserIds = append(hasUserIds, int(o.UserID))
	}

	// 修改部分
	uMap := make(map[string]interface{})
	uMap["blocked"] = block

	_ = s.opUserRepo.UpdateOpUser(c, hasUserIds, uMap)

	//新增部分
	for _, userId := range userIds {
		if !utils.IntHas(userId, hasUserIds) {
			opUser := &model.OpUser{
				UserID:  uint(userId),
				Blocked: uint(block),
			}
			_ = s.opUserRepo.CreateOpUser(c, opUser)
		}
	}

	return nil
}
