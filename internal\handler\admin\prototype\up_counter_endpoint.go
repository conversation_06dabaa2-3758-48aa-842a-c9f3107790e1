package prototype

import (
	"marketing/internal/api/prototype"
	"marketing/internal/handler"
	service "marketing/internal/service/prototype"

	"github.com/gin-gonic/gin"
)

// UpCounterEndpointHandler 上柜终端控制器接口
type UpCounterEndpointHandler interface {
	GetList(c *gin.Context)
	Approve(c *gin.Context)
	GetListEndpointMain(c *gin.Context)
}

type upCounterEndpointHandler struct {
	service service.UpCounterEndpointService
}

// NewUpCounterEndpointHandler 创建上柜终端控制器
func NewUpCounterEndpointHandler(service service.UpCounterEndpointService) UpCounterEndpointHandler {
	return &upCounterEndpointHandler{
		service: service,
	}
}

// GetList 获取上柜终端列表
func (h *upCounterEndpointHandler) GetList(c *gin.Context) {
	var req prototype.UpCounterEndpointSearch
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	req.SetDefaults()

	list, count, err := h.service.GetList(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, gin.H{
		"list":  list,
		"count": count,
	})
}

// Approve 审核上柜终端
func (h *upCounterEndpointHandler) Approve(c *gin.Context) {
	var req prototype.UpCounterApprove
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	err := h.service.Approve(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c)
}

// GetListEndpointMain 获取上柜终端列表
func (h *upCounterEndpointHandler) GetListEndpointMain(c *gin.Context) {
	var req prototype.UpCounterEndpointSearch
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	req.SetDefaults()
	req.IsEndpointMain = true

	list, count, err := h.service.GetList(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, gin.H{
		"list":  list,
		"count": count,
	})
}
