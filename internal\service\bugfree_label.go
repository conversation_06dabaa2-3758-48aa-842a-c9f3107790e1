package service

import (
	"errors"
	"github.com/gin-gonic/gin"
	"marketing/internal/dao"
	"marketing/internal/model"
	"marketing/internal/pkg/utils"
	"strings"
)

type BugFreeLabelInfo struct {
	Id         string `json:"id"`          // id
	Name       string `json:"name"`        // 名称
	Type       int    `json:"type"`        // 类型
	Category   int    `json:"category"`    // 父id
	CreateTime string `json:"create_time"` // 创建时间
	UpdateTime string `json:"update_time"` // 修改时间
}

type BugFreeLabelSvcInterface interface {
	GetBugFreeLabelList(c *gin.Context, labelType, category int) (labelList []*BugFreeLabelInfo)
	EditBugFreeLabel(c *gin.Context, id, name string, labelType, category int, categoryKey string, order int, visibility int) error
	DeleteBugFreeLabel(c *gin.Context, id string) error
}

type BugFreeLabelSvcImpl struct {
	bugFreeLabelRepo dao.BugFreeLabelDao
}

func NewBugFreeLabelService(bugFreeLabelRepo dao.BugFreeLabelDao) BugFreeLabelSvcInterface {
	return &BugFreeLabelSvcImpl{
		bugFreeLabelRepo: bugFreeLabelRepo,
	}
}

func (s *BugFreeLabelSvcImpl) GetBugFreeLabelList(c *gin.Context, labelType, category int) (labelList []*BugFreeLabelInfo) {
	labelList = make([]*BugFreeLabelInfo, 0)

	if labelType == 0 && category == 0 {
		labelList = append(labelList, &BugFreeLabelInfo{
			Name: "终端服务反馈标签",
			Type: 1,
		})
		labelList = append(labelList, &BugFreeLabelInfo{
			Name: "平板反馈标签",
			Type: 2,
		})
	} else if labelType == 1 && category == 0 {
		list := s.bugFreeLabelRepo.GetBugFreeLabelCategory(c)
		for _, l := range list {
			labelList = append(labelList, &BugFreeLabelInfo{
				Id:         "category_" + utils.IntToString(l.Id),
				Name:       l.Name,
				Type:       1,
				Category:   l.Id,
				CreateTime: l.CreateTime,
				UpdateTime: l.UpdateTime,
			})
		}
	} else {
		list := s.bugFreeLabelRepo.GetBugFreeLabel(c, labelType, category)
		for _, l := range list {
			labelList = append(labelList, &BugFreeLabelInfo{
				Id:         utils.IntToString(l.Id),
				Name:       l.Name,
				Type:       l.Type,
				Category:   l.Id,
				CreateTime: l.CreateTime,
				UpdateTime: l.UpdateTime,
			})
		}
	}

	return labelList
}

func (s *BugFreeLabelSvcImpl) EditBugFreeLabel(c *gin.Context, id, name string, labelType, category int, categoryKey string, order, visibility int) error {
	if labelType != 1 && labelType != 2 {
		return errors.New("标签类型错误")
	}

	if len(s.getCategoryKeyName(categoryKey)) == 0 {
		return errors.New("标签类型key错误")
	}

	if len(id) == 0 {
		return s.addBugFreeLabel(c, name, labelType, category, categoryKey, order, visibility)
	}

	uMap := make(map[string]interface{})

	if len(id) >= 8 && id[0:8] == "category" {
		ids := strings.Split(id, "_")
		if len(ids) >= 2 {
			label := s.bugFreeLabelRepo.GetBugFreeLabelCategoryById(c, utils.StringToInt(ids[1]))
			if label == nil {
				return errors.New("标签不存在")
			}

			uMap["name"] = name
			uMap["key"] = categoryKey
			uMap["order"] = order
			uMap["visibility"] = visibility

			return s.bugFreeLabelRepo.UpdateBugFreeLabelCategory(c, utils.StringToInt(ids[1]), uMap)
		}
	} else {
		label := s.bugFreeLabelRepo.GetBugFreeLabelById(c, utils.StringToInt(id))
		if label == nil {
			return errors.New("标签不存在")
		}

		if category > 0 {
			parentLabel := s.bugFreeLabelRepo.GetBugFreeLabelByTyIdWithType(c, category, labelType)
			if parentLabel == nil {
				return errors.New("父标签不存在")
			}
		}

		uMap["name"] = name
		uMap["type"] = labelType
		uMap["category"] = category
		uMap["category_key"] = categoryKey
		uMap["category_name"] = s.getCategoryKeyName(categoryKey)
		uMap["order"] = order
		uMap["visibility"] = visibility

		return s.bugFreeLabelRepo.UpdateBugFreeLabel(c, utils.StringToInt(id), uMap)
	}

	return nil
}

func (s *BugFreeLabelSvcImpl) addBugFreeLabel(c *gin.Context, name string, labelType, category int, categoryKey string, order, visibility int) error {
	if labelType == 1 && category == 0 {
		return s.bugFreeLabelRepo.CreateBugFreeLabelCategory(c, &model.BugFreeLabelCategory{
			Name:       name,
			Key:        categoryKey,
			Order:      order,
			Visibility: 1,
		})
	}

	if category > 0 {
		parentLabel := s.bugFreeLabelRepo.GetBugFreeLabelByTyIdWithType(c, category, labelType)
		if parentLabel == nil {
			return errors.New("父标签不存在")
		}
	}

	return s.bugFreeLabelRepo.CreateBugFreeLabel(c, &model.BugFreeLabel{
		Name:         name,
		Type:         labelType,
		Category:     category,
		CategoryKey:  categoryKey,
		CategoryName: s.getCategoryKeyName(categoryKey),
		Order:        order,
		Visibility:   visibility,
	})
}

func (s *BugFreeLabelSvcImpl) DeleteBugFreeLabel(c *gin.Context, id string) error {
	if len(id) >= 8 && id[0:8] == "category" {
		ids := strings.Split(id, "_")
		if len(ids) >= 2 {
			return s.bugFreeLabelRepo.DeleteBugFreeLabelCategory(c, utils.StringToInt(ids[1]))
		}
	} else {
		return s.bugFreeLabelRepo.DeleteBugFreeLabel(c, utils.StringToInt(id))
	}

	return nil
}

func (s *BugFreeLabelSvcImpl) getCategoryKeyName(categoryKey string) string {
	switch categoryKey {
	case "textbook":
		return "教材资源"
	case "software":
		return "软件问题"
	case "hardware":
		return "硬件问题"
	case "suggestion":
		return "意见建议"
	case "study_desk":
		return "学习桌椅"
	case "competitor":
		return "竞品功能推荐"
	case "question":
		return "错题反馈"
	default:
		return ""
	}

	return ""
}
