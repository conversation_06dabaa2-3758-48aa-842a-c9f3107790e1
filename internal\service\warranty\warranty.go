package warranty

import (
	"github.com/gin-gonic/gin"
	apiTp "marketing/internal/api/third_party"
	repo "marketing/internal/dao/warranty"
	"marketing/internal/pkg/types"
)

// InterfaceWarranty 定义了保修相关的接口
type InterfaceWarranty interface {
	// GetByPhone 根据手机号查询保修信息
	GetByPhone(c *gin.Context, phone string) ([]*apiTp.WarrantyThirdPartyResponse, error)
}
type warranty struct {
	repo repo.InterfaceWarranty
}

// NewWarrantyService 创建一个新的保修数据访问对象
func NewWarrantyService(repo repo.InterfaceWarranty) InterfaceWarranty {
	return &warranty{
		repo: repo,
	}
}

// GetByPhone 根据手机号查询保修信息
func (w *warranty) GetByPhone(c *gin.Context, phone string) ([]*apiTp.WarrantyThirdPartyResponse, error) {
	// 查询保修信息
	warranties, err := w.repo.GetByPhone(c, phone)
	if err != nil {
		return nil, err
	}
	// 转换为响应格式
	var results []*apiTp.WarrantyThirdPartyResponse
	for _, v := range warranties {
		result := &apiTp.WarrantyThirdPartyResponse{
			Barcode:          v.Barcode,
			Number:           v.Number,
			Imei:             v.Imei,
			Salesman:         v.Salesman,
			CustomerSex:      v.CustomerSex,
			CustomerName:     v.CustomerName,
			CustomerPhone:    v.CustomerPhone,
			CustomerAddr:     v.CustomerAddr,
			StudentName:      v.StudentName,
			StudentSchool:    v.StudentSchool,
			StudentSex:       v.StudentSex,
			StudentGrade:     v.StudentGrade,
			Model:            v.Model,
			PurchaseWay:      v.PurchaseWay,
			Recommender:      v.Recommender,
			RecommenderPhone: v.RecommenderPhone,
		}
		if !v.BuyDate.IsZero() {
			result.BuyDate = types.CustomTime(v.BuyDate)
		}
		if !v.ProductDate.IsZero() {
			result.ProductDate = types.CustomTime(v.ProductDate)
		}

		results = append(results, result)
	}
	return results, nil
}
