package operation

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"marketing/internal/model"
	"marketing/internal/pkg/db"
)

type OpLikeDao interface {
	GetOpArticleLikeInfos(c *gin.Context, aids []uint, target int) (list []*model.OpArticleLikeCountInfo)
}

// OpLikeDaoImpl 实现 OpLikeDao 接口
type OpLikeDaoImpl struct {
	db *gorm.DB
}

// NewOpLikeDao 创建 OpLikeDao 实例
func NewOpLikeDao() OpLikeDao {
	return &OpLikeDaoImpl{
		db: db.GetDB(""),
	}
}

func (d *OpLikeDaoImpl) GetOpArticleLikeInfos(c *gin.Context, aids []uint, target int) (list []*model.OpArticleLikeCountInfo) {
	d.db.WithContext(c).Model(&model.OpLike{}).
		Where("target = ? and target_id in (?)", target, aids).
		Select("target_id,count(*) num").
		Group("target_id").
		Find(&list)
	return
}
