package model

import (
	"gorm.io/gorm"
)

type ResourceGroup struct {
	gorm.Model
	Name             string  `json:"name" gorm:"column:name;not null;unique"`
	Description      string  `json:"description" gorm:"column:description;type:text"`
	AllocationPolicy string  `json:"allocation_policy" gorm:"column:allocation_policy;type:text"`
	QWPartyID        *int    `json:"qw_partyid" gorm:"column:qw_partyid"`            // 使用指针来允许 NULL 值
	QWName           *string `json:"qw_name" gorm:"column:qw_name;type:varchar(20)"` // 使用指针来允许 NULL 值
}

func (ResourceGroup) TableName() string {
	return "resource_groups"
}
