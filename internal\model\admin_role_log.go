package model

import (
	"time"
)

// AdminRoleLog 角色操作日志
type AdminRoleLog struct {
	ID           uint      `json:"id" gorm:"primarykey"`
	RoleID       int       `json:"role_id" gorm:"column:role_id;not null;default:0;comment:角色ID"`
	RoleName     string    `json:"role_name" gorm:"column:role_name;size:50;not null;default:'';comment:角色名称"`
	OpType       string    `json:"op_type" gorm:"column:op_type;size:20;not null;default:'';comment:操作类型(create/edit/delete/status)"`
	Module       string    `json:"module" gorm:"column:module;size:50;not null;default:'';comment:模块"`
	Remark       string    `json:"remark" gorm:"column:remark;size:255;not null;default:'';comment:备注"`
	Method       string    `json:"method" gorm:"column:method;size:10;not null;default:'';comment:请求方法"`
	Path         string    `json:"path" gorm:"column:path;size:100;not null;default:'';comment:请求路径"`
	Operator     int       `json:"operator" gorm:"column:operator;not null;default:0;comment:操作人ID"`
	OperatorName string    `json:"operator_name" gorm:"-"`
	OperatorIP   string    `json:"operator_ip" gorm:"column:operator_ip;size:50;not null;default:'';comment:操作人IP"`
	Request      string    `json:"request" gorm:"column:request;type:text;comment:请求数据"`
	Before       string    `json:"before" gorm:"column:before;type:text;comment:修改前数据"`
	After        string    `json:"after" gorm:"column:after;type:text;comment:修改后数据"`
	CreatedAt    time.Time `json:"-" gorm:"column:created_at;comment:创建时间"`
	CreatedAtStr string    `gorm:"-" json:"created_at"`
}

// TableName 指定表名
func (AdminRoleLog) TableName() string {
	return "admin_role_logs"
}
