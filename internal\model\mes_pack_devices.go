package model

import (
	"time"
)

type MesPackDevices struct {
	ID         int        `gorm:"primaryKey;autoIncrement;column:id" json:"id"`
	ModelID    int        `gorm:"column:model_id;comment:机型id" json:"model_id"`
	Model      string     `gorm:"column:model;size:32;comment:机型" json:"model"`
	Barcode    string     `gorm:"column:barcode;size:32;not null;index" json:"barcode"`
	Number     string     `gorm:"column:number;size:48;index" json:"number"`
	Imei       string     `gorm:"column:imei;size:16;index" json:"imei"`
	PackTime   *time.Time `gorm:"column:pack_time;index" json:"pack_time"`
	TaskID     int        `gorm:"column:task_id;comment:包装任务id" json:"task_id"`
	UserID     int        `gorm:"column:user_id;comment:包装接线" json:"user_id"`
	AddTime    *time.Time `gorm:"column:add_time;default:0000-00-00 00:00:00;comment:记录创建时间" json:"add_time"`
	UpdateTime *time.Time `gorm:"column:update_time;autoUpdateTime;comment:记录更新时间" json:"update_time"`
}

func (MesPackDevices) TableName() string {
	return "mes_pack_devices"
}
