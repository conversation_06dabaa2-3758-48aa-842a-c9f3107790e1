package agency

import (
	"marketing/internal/dao"
	"marketing/internal/handler"
	"marketing/internal/service"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// DepartmentHandler 负责处理部门相关的 HTTP 请求
type DepartmentHandler struct {
	service *service.DepartmentService
}

// NewDepartmentHandler 创建并返回一个新的 DepartmentHandler 实例
func NewDepartmentHandler(svc *service.DepartmentService) *DepartmentHandler {
	return &DepartmentHandler{service: svc}
}

// GetDepartment 处理获取分区信息的请求
func (d *DepartmentHandler) GetDepartments(c *gin.Context) {
	// 调用服务方法获取分区数据
	data, total, page, pageSize, err := d.service.GetPartitions(c)
	if err != nil {
		handler.Error(c, err) // 如果发生错误，调用错误处理函数返回错误响应
		return
	}

	// 成功时，返回包含分区数据、总数、当前页和每页大小的响应
	handler.Success(c, gin.H{
		"data":     data,
		"total":    total,
		"page":     page,
		"pageSize": pageSize,
	})
}

// GetDepartmentByID 处理根据ID获取部门信息的请求
func (d *DepartmentHandler) GetDepartmentByID(id int64) (*dao.Department, error) {
	// 调用服务方法获取分区
	department, err := d.service.GetDepartmentByID(id)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 如果未找到记录，将 ActivityName 设置为 '未知'
			return &dao.Department{Name: "未知"}, nil
		}
		// 如果是其他错误，返回错误
		handler.Error(nil, err) // 这里需要传入合适的上下文或参数
		return nil, err
	}
	return department, nil
}
