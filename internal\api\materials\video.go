package materials

type VideoAddReq[T any] struct {
	ID          int    `json:"id"`
	Title       string `json:"title"`
	Preview     T      `json:"preview"` //多文件
	Path        string `json:"path"`    //单文件
	Description string `json:"description"`
	Status      uint8  `json:"status"`
}
type TrainVideoListReq struct {
	TrainID  int    `json:"train_id"`
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
	Status   string `json:"status"`
	Label    string `json:"label"`
}
