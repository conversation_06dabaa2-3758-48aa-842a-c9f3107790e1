package model

import (
	"time"
)

type MachineTypeRelation struct {
	ID               int        `json:"-" gorm:"column:id"`                                  // ID
	ModelID          int        `json:"model_id" gorm:"-"`                                   // 型号ID
	Name             string     `json:"name" gorm:"column:name"`                             // 型号
	CategoryID       int        `json:"category_id" gorm:"column:category_id"`               // 分类ID
	Declare          int        `json:"declare" gorm:"column:declare"`                       // 允许申报 0:不允许 1:允许
	Stock            int        `json:"stock" gorm:"column:stock"`                           // 允许备货 0:不允许 1:允许
	Delist           int        `json:"delist" gorm:"column:delist"`                         // 是否下架 0:不下架 1:下架
	DelistOnOffTime  *time.Time `json:"delist_on_off_time" gorm:"column:delist_on_off_time"` // 下架时间
	IsUpCounter      int        `json:"is_up_counter" gorm:"column:is_up_counter"`           // 是否上报 0:不上报 1:上报
	Price            float64    `json:"price" gorm:"column:price"`                           // 售价
	UpCounterTime    *time.Time `json:"up_counter_time" gorm:"column:up_counter_time"`       // 上报时间
	DownCounterTime  *time.Time `json:"down_counter_time" gorm:"column:down_counter_time"`   // 下报时间
	MarketDate       *time.Time `json:"market_date" gorm:"column:market_date"`               // 上市时间
	DelistTime       *time.Time `json:"delist_time" gorm:"column:delist_time"`               // 下架时间
	Discontinued     int        `json:"discontinued" gorm:"column:discontinued"`             // 演示样机是否下市 0:不 1:是
	DiscontinuedDate *time.Time `json:"discontinued_date" gorm:"column:discontinued_date"`   // 演示样机下市时间
	CreatedAt        time.Time  `json:"-" gorm:"column:created_at"`                          // 创建时间
	UpdatedAt        time.Time  `json:"-" gorm:"column:updated_at"`                          // 更新时间
}

func (MachineTypeRelation) TableName() string {
	return "machine_type_relation"
}
