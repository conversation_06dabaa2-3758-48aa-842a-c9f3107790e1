package model

type MaterialCategory struct {
	Id       uint   `json:"id" gorm:"column:id"`               // 物料id
	Name     string `json:"name" gorm:"column:name"`           // 物料名称
	Pid      int    `json:"pid" gorm:"column:pid"`             // 父id
	Level    int    `json:"level" gorm:"column:level"`         // 等级
	FullPath string `json:"full_path" gorm:"column:full_path"` // 父路径
	Order    int    `json:"order" gorm:"column:order"`         // 排序
	Image    string `json:"image" gorm:"column:image"`         // 物料图片
	Num      int    `json:"num" gorm:"-"`                      // 物料数量
}

func (MaterialCategory) TableName() string {
	return "material_category"
}
