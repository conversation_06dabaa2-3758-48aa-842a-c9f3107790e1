package service

import (
	"marketing/internal/dao"
	"marketing/internal/model"
	"marketing/internal/pkg/errors"
	"marketing/internal/pkg/types"
	"time"

	"github.com/gin-gonic/gin"
)

type EndpointNoticeSvc interface {
	GetEndpointNoticeList(c *gin.Context, title, author string, pageNum, pageSize int) ([]*model.EndpointNotice, int64, error)
	GetEndpointNoticeById(c *gin.Context, id int) (*model.EndpointNotice, error)
	CreateEndpointNotice(c *gin.Context, title, author, content string) (int, error)
	UpdateEndpointNotice(c *gin.Context, id int, title, author, content string) error
	DeleteEndpointNotice(c *gin.Context, id int) error
}

type endpointNoticeSvc struct {
	dao dao.EndpointNoticeDao
}

func NewEndpointNoticeSvc(dao dao.EndpointNoticeDao) EndpointNoticeSvc {
	return &endpointNoticeSvc{
		dao: dao,
	}
}

// GetEndpointNoticeList 获取终端通知列表
func (s *endpointNoticeSvc) GetEndpointNoticeList(c *gin.Context, title, author string, pageNum, pageSize int) ([]*model.EndpointNotice, int64, error) {
	return s.dao.GetEndpointNoticeList(c, title, author, pageNum, pageSize)
}

// GetEndpointNoticeById 根据ID获取终端通知
func (s *endpointNoticeSvc) GetEndpointNoticeById(c *gin.Context, id int) (*model.EndpointNotice, error) {
	return s.dao.GetEndpointNoticeById(c, id)
}

// CreateEndpointNotice 创建终端通知
func (s *endpointNoticeSvc) CreateEndpointNotice(c *gin.Context, title, author, content string) (int, error) {
	if title == "" {
		return 0, errors.NewErr("标题不能为空")
	}
	if author == "" {
		return 0, errors.NewErr("发布人不能为空")
	}
	if content == "" {
		return 0, errors.NewErr("内容不能为空")
	}

	notice := &model.EndpointNotice{
		Title:     title,
		Author:    author,
		Content:   content,
		CreatedAt: types.CustomTime(time.Now()),
		UpdatedAt: types.CustomTime(time.Now()),
	}

	err := s.dao.CreateEndpointNotice(c, notice)
	if err != nil {
		return 0, err
	}

	return notice.ID, nil
}

// UpdateEndpointNotice 更新终端通知
func (s *endpointNoticeSvc) UpdateEndpointNotice(c *gin.Context, id int, title, author, content string) error {
	if title == "" {
		return errors.NewErr("标题不能为空")
	}
	if author == "" {
		return errors.NewErr("发布人不能为空")
	}
	if content == "" {
		return errors.NewErr("内容不能为空")
	}

	// 检查通知是否存在
	notice, err := s.dao.GetEndpointNoticeById(c, id)
	if err != nil {
		return err
	}
	if notice == nil {
		return errors.NewErr("通知不存在")
	}

	updateMap := map[string]interface{}{
		"title":      title,
		"author":     author,
		"content":    content,
		"updated_at": time.Now(),
	}

	return s.dao.UpdateEndpointNotice(c, id, updateMap)
}

// DeleteEndpointNotice 删除终端通知
func (s *endpointNoticeSvc) DeleteEndpointNotice(c *gin.Context, id int) error {
	// 检查通知是否存在
	notice, err := s.dao.GetEndpointNoticeById(c, id)
	if err != nil {
		return err
	}
	if notice == nil {
		return errors.NewErr("通知不存在")
	}

	return s.dao.DeleteEndpointNotice(c, id)
}
