package app

import (
	userDao "marketing/internal/dao/admin_user"
	"marketing/internal/dao/endpoint"
	dao "marketing/internal/dao/renew"
	dao1 "marketing/internal/dao/warranty"
	"marketing/internal/handler/app"
	"marketing/internal/pkg/db"
	service "marketing/internal/service/renew"

	"github.com/gin-gonic/gin"
)

type RenewAppRouter struct {
	renewHandler app.RenewAppHandler
}

func NewRenewAppRouter() *RenewAppRouter {
	var Db = db.GetDB()
	userD := userDao.NewUserDao(Db)
	endpointDao := endpoint.NewEndpointDao(Db)
	renewDao := dao.NewApplicationDao(Db)
	warrantyDao := dao1.NewWarrantyDao(Db)
	renewService := service.NewApplicationService(userD, renewDao, endpointDao, warrantyDao)
	renewHandler := app.NewRenewAppHandler(renewService)
	return &RenewAppRouter{
		renewHandler: renewHandler,
	}
}

func (rr *RenewAppRouter) Register(r *gin.RouterGroup) {

	renewRouter := r.Group("/endpoint/renew")
	{
		renewRouter.POST("", rr.renewHandler.Add)
		renewRouter.PUT("/:id", rr.renewHandler.Update)
		renewRouter.GET("", rr.renewHandler.Lists)
		renewRouter.GET("/:id", rr.renewHandler.GetInfo)
		renewRouter.DELETE("/:id", rr.renewHandler.Delete)
		renewRouter.GET("/machine", rr.renewHandler.GetMachine)
		renewRouter.GET("/issue", rr.renewHandler.GetIssue)
		renewRouter.GET("/status", rr.renewHandler.GetStatus)
		renewRouter.GET("/warranty", rr.renewHandler.GetWarranties)
	}
}
