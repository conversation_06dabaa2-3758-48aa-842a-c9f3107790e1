package model

import "time"

// EndpointApplicationStatus represents the structure of the endpoint_application_status table.
type EndpointApplicationStatus struct {
	ID            uint      `gorm:"primaryKey;autoIncrement"` // 主键自增
	ApplicationID int       `gorm:"not null;comment:申请ID"`
	BeforeState   int       `gorm:"not null;comment:修改前的状态"`
	AfterState    int       `gorm:"not null;comment:修改后的状态"`
	Extend        string    `gorm:"type:text;comment:扩展信息,审核额外附加的信息,json格式"`
	HandlerID     int       `gorm:"not null;comment:处理人的User ID"`
	Remark        string    `gorm:"size:255;comment:处理人备注"`
	CreatedAt     time.Time `gorm:"default:CURRENT_TIMESTAMP"`
}

// TableName returns the table name for GORM.
func (EndpointApplicationStatus) TableName() string {
	return "endpoint_application_status"
}
