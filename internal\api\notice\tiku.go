package notice

// UploadPaperAndHomeworkReq 上传试卷和作业本请求
type UploadPaperAndHomeworkReq struct {
	// 文件通过multipart/form-data上传，字段名为"file"
}

// UploadPaperAndHomeworkRes 上传试卷和作业本响应
type UploadPaperAndHomeworkRes struct {
	AffectedRows int64 `json:"affected_rows"` // 影响的行数
}

// TextBookOptionsRes 教材类型选项响应
type TextBookOptionsRes struct {
	Name  string `json:"name"`  // 显示名称
	Value string `json:"value"` // 值
}

// TextBookHistoriesReq 教材历史记录请求
type TextBookHistoriesReq struct {
	Type     string `json:"type" form:"type" binding:"required,oneof=sszb_course msfd_course paper homework"` // 类型
	Page     int    `json:"page" form:"page"`         // 页码
	PageSize int    `json:"page_size" form:"page_size"` // 每页数量
}

// TextBookHistoryItem 教材历史记录项
type TextBookHistoryItem struct {
	Subject string      `json:"subject"` // 科目
	Grades  interface{} `json:"grades"`  // 年级（可能是字符串或数组）
	Name    string      `json:"name"`    // 名称
}

// TextBookHistoriesGroupItem 教材历史记录分组项
type TextBookHistoriesGroupItem struct {
	UpdatedAt string                 `json:"updated_at"` // 更新日期
	Items     []*TextBookHistoryItem `json:"items"`      // 记录项列表
}

// AppOptionsItemRes App选项响应项
type AppOptionsItemRes struct {
	Value string `json:"value"` // 包名
	Text  string `json:"text"`  // 应用名称
}

// AppUpdateHistoriesReq App更新历史请求
type AppUpdateHistoriesReq struct {
	PkgName  string `json:"pkg_name" form:"pkg_name" binding:"required"` // 包名
	Page     int    `json:"page" form:"page"`         // 页码
	PageSize int    `json:"page_size" form:"page_size"` // 每页数量
}

// AppUpdateHistoryItem App更新历史项
type AppUpdateHistoryItem struct {
	AppName        string `json:"app_name"`        // 应用名称
	VersionName    string `json:"version_name"`    // 版本名称
	UpdateContent  string `json:"update_content"`  // 更新内容
	Devices        string `json:"devices"`         // 设备信息
	UpdatedAt      string `json:"updated_at"`      // 更新时间
}
