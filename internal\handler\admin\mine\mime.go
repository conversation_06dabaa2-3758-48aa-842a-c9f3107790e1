package mine

import (
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"marketing/internal/api/system"
	"marketing/internal/handler"
	"marketing/internal/handler/admin/mine/dto"
	"marketing/internal/pkg/errors"
	system2 "marketing/internal/service/system"
)

// MineInterface 我的
type MineInterface interface {
	Update(c *gin.Context)
	ResetPassword(c *gin.Context)
	BindWecom(c *gin.Context)
}

type mine struct {
	adminUserSvc system2.AdminUserInterface
}

func NewMine(adminUserSvc system2.AdminUserInterface) MineInterface {
	return &mine{
		adminUserSvc: adminUserSvc,
	}
}

// Update 更新用户信息
func (a *mine) Update(c *gin.Context) {
	var req dto.UpdateMineReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	var param system.AddUserReq
	param.ID = cast.ToUint(c.GetUint("uid"))
	param.Name = req.Name
	param.Phone = req.Phone
	param.Avatar = req.Avatar
	err := a.adminUserSvc.Update(c, param)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

// ResetPassword 重置密码
func (a *mine) ResetPassword(c *gin.Context) {
	var req system.ResetPasswordReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	req.ID = c.GetUint("uid")
	if req.ID == 0 {
		handler.Error(c, errors.NewErr("id不能为空"))
		return
	}
	err := a.adminUserSvc.ResetPassword(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, nil)
}

// BindWecom 扫码绑定企微
func (a *mine) BindWecom(c *gin.Context) {
	var req system.BindWecomReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	err := a.adminUserSvc.BindWecom(c, req.Code)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c)
}
