package dao

import (
	"marketing/internal/pkg/utils"
	"time"
)

// Region 区域结构体
type Region struct {
	RegionId          int       `json:"region_id" gorm:"column:region_id"`
	ParentId          int       `json:"parent_id" gorm:"column:parent_id"`
	RegionName        string    `json:"region_name" gorm:"column:region_name"`
	ShortName         string    `json:"short_name" gorm:"column:shortname"`
	RegionType        int       `json:"region_type" gorm:"column:region_type"`
	RegionLevel       string    `json:"region_level" gorm:"column:region_level"`
	CityLevel         int       `json:"city_level" gorm:"column:city_level"`
	Population        int64     `json:"population" gorm:"column:population"`
	PrimarySchoolsNum int64     `json:"primary_schools_num" gorm:"column:primary_schools_num"`
	PupilsNum         int64     `json:"pupils_num" gorm:"column:pupils_num"`
	MiddleSchoolsNum  int64     `json:"middle_schools_num" gorm:"column:middle_schools_num"`
	JuniorsNum        int64     `json:"juniors_num" gorm:"column:juniors_num"`
	Lng               string    `json:"lng" gorm:"column:lng"`
	Lat               string    `json:"lat" gorm:"column:lat"`
	Remark            string    `json:"remark" gorm:"column:remark"`
	ChildRegionNum    int       `json:"child_region_num" gorm:"-"`
	CreatedAt         time.Time `json:"-" gorm:"column:created_at"`
	UpdatedAt         time.Time `json:"-" gorm:"column:updated_at"`
}

type ChildRegionNumInfo struct {
	ParentId       int `json:"parent_id" gorm:"column:parent_id"`
	ChildRegionNum int `json:"child_region_num" gorm:"child_region_num"`
}

func (Region) TableName() string {
	return "region"
}

// CreateRegion 保存区域
func CreateRegion(r *Region) error {
	t := time.Now()
	r.CreatedAt = t
	r.UpdatedAt = t
	return GetDB().Create(r).Error
}

// GetAllRegionTreesFromDB 从数据库中获取所有区域树
func GetAllRegionTreesFromDB(parentId, regionType int, regionName string, pageNum, pageSize int) ([]Region, int64) {
	db := GetDB().Model(&Region{})

	// 根据父标签id查询
	if parentId > 0 {
		db = db.Where("parent_id = ?", parentId)
	} else {
		if regionType >= 0 {
			db = db.Where("region_type = ?", regionType)
		}

		if len(regionName) > 0 {
			db = db.Where("region_name = ?", regionName)
		}
	}

	db = db.Where("deleted_at is null")

	data, total := utils.PaginateQueryV1(db, pageNum, pageSize, new([]Region))

	return *data, total
}

// GetRegionById 根据区域id查询区域
func GetRegionById(regionId int) (*Region, error) {
	var r Region
	err := GetDB().Model(&Region{}).Where("region_id = ? and deleted_at is null", regionId).First(&r).Error
	if err != nil {
		return nil, err
	}
	return &r, nil
}

// GetRegionsByIds 根据区域id查询区域列表
func GetRegionsByIds(regionIds []int) (list []*Region) {
	GetDB().Model(&Region{}).Where("region_id in (?) and deleted_at is null", regionIds).Find(&list)
	return
}

// UpdateRegion 根据区域id,编辑区域
func UpdateRegion(regionId int, uMap map[string]interface{}) error {
	return GetDB().Model(&Region{}).Where("region_id = ?", regionId).Updates(uMap).Error
}

// UpdateRegionByRegionIds 根据区域id数组,编辑区域
func UpdateRegionByRegionIds(regionIds []int, uMap map[string]interface{}) error {
	return GetDB().Model(&Region{}).Where("region_id in (?)", regionIds).Updates(uMap).Error
}

// GetChildRegionNumByParentIds 统计子分区数量
func GetChildRegionNumByParentIds(parentIds []int) (infos []*ChildRegionNumInfo) {
	GetDB().Model(&Region{}).
		Select("parent_id,count(*)child_region_num").
		Where("parent_id in (?) and deleted_at is null", parentIds).
		Group("parent_id").Find(&infos)
	return
}
