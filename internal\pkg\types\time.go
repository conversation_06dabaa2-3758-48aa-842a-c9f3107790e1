package types

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"
)

// CustomTime 用于自定义时间格式的类型
type CustomTime time.Time

// MarshalJSON 自定义时间格式化为 JSON
func (ct CustomTime) MarshalJSON() ([]byte, error) {
	t := time.Time(ct)
	// 如果时间为零值，则返回 null
	if t.IsZero() {
		return []byte(`""`), nil
	}
	return json.Marshal(t.Format(time.DateTime))
}

// UnmarshalJSON 解析 JSON 字符串为 CustomTime
func (ct *CustomTime) UnmarshalJSON(data []byte) error {
	var s *string
	if err := json.Unmarshal(data, &s); err != nil {
		return err
	}
	if s == nil {
		*ct = CustomTime(time.Time{}) // 设置为零值
		return nil
	}
	t, err := time.Parse(time.DateTime, *s)
	if err != nil {
		return fmt.Errorf("invalid date format: %v", err)
	}
	*ct = CustomTime(t)
	return nil
}

// Value 实现 driver.Valuer 接口
func (ct CustomTime) Value() (driver.Value, error) {
	// 转换为 time.Time 类型
	t := time.Time(ct)
	if t.IsZero() {
		return nil, nil
	}
	return t, nil
}

// Scan 实现 sql.Scanner 接口
func (ct *CustomTime) Scan(value interface{}) error {
	if value == nil {
		*ct = CustomTime(time.Time{})
		return nil
	}

	switch v := value.(type) {
	case time.Time:
		*ct = CustomTime(v)
		return nil
	case string:
		t, err := time.Parse(time.DateTime, v)
		if err != nil {
			return err
		}
		*ct = CustomTime(t)
		return nil
	case []byte:
		t, err := time.Parse(time.DateTime, string(v))
		if err != nil {
			return err
		}
		*ct = CustomTime(t)
		return nil
	}

	return fmt.Errorf("cannot convert %T to CustomTime", value)
}

// String 实现 Stringer 接口
func (ct CustomTime) String() string {
	t := time.Time(ct)
	if t.IsZero() {
		return ""
	}
	return t.Format(time.DateTime)
}

// DateOnly 用于仅显示日期部分的类型，继承自CustomTime
type DateOnly CustomTime

// MarshalJSON 自定义时间格式化为 JSON，只保留日期部分
func (d DateOnly) MarshalJSON() ([]byte, error) {
	t := time.Time(d)
	// 如果时间为零值，则返回空字符串
	if t.IsZero() {
		return []byte(`""`), nil
	}
	// 只保留日期部分，格式为 yyyy-MM-dd
	return json.Marshal(t.Format("2006-01-02"))
}

// UnmarshalJSON 解析 JSON 字符串为 DateOnly
func (d *DateOnly) UnmarshalJSON(data []byte) error {
	var ct CustomTime
	if err := ct.UnmarshalJSON(data); err != nil {
		return err
	}
	*d = DateOnly(ct)
	return nil
}

// Value 实现 driver.Valuer 接口
func (d DateOnly) Value() (driver.Value, error) {
	return CustomTime(d).Value()
}

// Scan 实现 sql.Scanner 接口
func (d *DateOnly) Scan(value interface{}) error {
	var ct CustomTime
	if err := ct.Scan(value); err != nil {
		return err
	}
	*d = DateOnly(ct)
	return nil
}

// String 实现 Stringer 接口
func (d DateOnly) String() string {
	t := time.Time(d)
	if t.IsZero() {
		return ""
	}
	return t.Format("2006-01-02")
}
