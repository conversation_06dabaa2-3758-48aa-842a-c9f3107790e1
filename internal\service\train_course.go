package service

import (
	"encoding/json"
	"marketing/internal/api/materials"
	"marketing/internal/dao"
	"marketing/internal/model"
	myErrors "marketing/internal/pkg/errors"
	"marketing/internal/pkg/log"
	"marketing/internal/pkg/utils"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
)

type TrainSubjectService interface {
	List(c *gin.Context, req materials.TrainSubjectListReq) ([]map[string]any, int64, error)
	Detail(c *gin.Context, id int) (map[string]any, error)
	Upsert(c *gin.Context, m materials.TrainSubjectAdd) (int, error)
	TypeTips(c *gin.Context) ([]map[string]any, error)
	EndpointTips(c *gin.Context) ([]map[string]any, error)
	RoleTips(c *gin.Context) ([]map[string]any, error)
	RegionTips(c *gin.Context) ([]materials.AgencyTips, error)
	CourseCreate(c *gin.Context, param []materials.TrainCourse, tp string) ([]uint, error)
	PracticeUpsert(c *gin.Context, param materials.TrainPractice) (int, error)
	SWCDelete(c *gin.Context, subject int, course int) error
	CourseUpdate(c *gin.Context, param materials.TrainCourse) error
	PracticeDelete(c *gin.Context, id int, SubjectID int) error
}
type GormTrainSubjectService struct {
	dao dao.TrainSubjectDao
}

func (g *GormTrainSubjectService) PracticeDelete(c *gin.Context, id int, SubjectID int) error {
	detail, err := g.dao.Detail(c, SubjectID)
	if err != nil {
		return myErrors.NewErr("专题已结束")
	}
	if detail.EndTime.Before(time.Now()) {
		return myErrors.NewErr("专题已结束")
	}
	return g.dao.PracticeDelete(c, id, SubjectID)
}

func (g *GormTrainSubjectService) CourseUpdate(c *gin.Context, param materials.TrainCourse) error {
	//基本字段
	baseCourse := model.TrainCourse{
		Name:        param.Name,
		IsUse:       param.IsUse,
		ResourceURL: param.Url,
	}
	var swc materials.SubjectWithPaper
	//专题结束，课时不再更新
	detail, err := g.dao.Detail(c, int(param.SubjectID))
	if err != nil {
		return myErrors.NewErr("专题已结束")
	}
	if detail.EndTime.Before(time.Now()) {
		return myErrors.NewErr("专题已结束")
	}
	//处理不同类型的课时
	switch detail.Type {
	case materials.CurseTypeLive.String():
		// 处理直播课时
		start, err := time.Parse("2006-01-02 15:04:05", param.LiveStartTime)
		if err != nil {
			return myErrors.NewErr("开始时间格式错误")
		}
		end, err := time.Parse("2006-01-02 15:04:05", param.LiveEndTime)
		if err != nil {
			return myErrors.NewErr("结束时间格式错误")
		}
		baseCourse.LiveStartTime = start
		baseCourse.LiveEndTime = end
	case materials.CurseTypeNormal.String():
		baseCourse.Issuer = param.Issuer
		baseCourse.Description = param.Description
		swc.Credit = param.Credit
		swc.CreditLearningTime = param.CreditLearningTime
	}
	return g.dao.CourseUpdate(c, baseCourse, swc)
}

func (g *GormTrainSubjectService) SWCDelete(c *gin.Context, subject int, course int) error {
	return g.dao.DeleteSubjectWithCourse(c, subject, course)
}

func (g *GormTrainSubjectService) PracticeUpsert(c *gin.Context, param materials.TrainPractice) (int, error) {
	if param.Name == "" {
		return 0, myErrors.NewErr("大实战名称不能为空")
	}
	if param.ID == 0 {
		return g.dao.PracticeCreate(c, param)
	}
	return g.dao.PracticeUpdate(c, param)
}

// CourseCreate 新建课程与课程关联
func (g *GormTrainSubjectService) CourseCreate(c *gin.Context, param []materials.TrainCourse, tp string) ([]uint, error) {
	var course []model.TrainCourse
	var swp []materials.SubjectWithPaper
	if len(param) == 0 {
		return nil, myErrors.NewErr("课程信息不能为空")
	}
	detail, err := g.dao.Detail(c, int(param[0].SubjectID))
	if err != nil {
		return nil, myErrors.NewErr("专题信息不存在")
	}
	//结束不再新增课程
	if detail.EndTime.Before(time.Now()) {
		return nil, myErrors.NewErr("专题已结束")
	}
	//专题类型
	tp = detail.Type

	//设置课程与课程关联
	for _, v := range param {
		// 创建基础课程信息
		baseCourse := model.TrainCourse{
			Name:        v.Name,
			IsUse:       v.IsUse,
			ResourceURL: v.Url,
		}

		// 创建基础课程关联信息
		baseSwp := materials.SubjectWithPaper{
			SubjectID: v.SubjectID,
			PaperID:   v.PaperID,
		}

		switch tp {
		case materials.CurseTypeLive.String():
			// 处理直播课时
			start, err := time.Parse("2006-01-02 15:04:05", v.LiveStartTime)
			if err != nil {
				return nil, myErrors.NewErr("开始时间格式错误")
			}
			end, err := time.Parse("2006-01-02 15:04:05", v.LiveEndTime)
			if err != nil {
				return nil, myErrors.NewErr("结束时间格式错误")
			}
			baseCourse.LiveStartTime = start
			baseCourse.LiveEndTime = end
			course = append(course, baseCourse)
			swp = append(swp, baseSwp)

		case materials.CurseTypeNormal.String():
			// 处理普通课时
			baseCourse.Issuer = v.Issuer
			baseCourse.Description = v.Description
			course = append(course, baseCourse)

			baseSwp.Credit = v.Credit
			baseSwp.CreditLearningTime = v.CreditLearningTime
			swp = append(swp, baseSwp)
		}
	}
	return g.dao.CourseCreate(c, course, swp)
}

func (g *GormTrainSubjectService) RegionTips(c *gin.Context) ([]materials.AgencyTips, error) {
	return g.dao.RegionTips(c)
}

func (g *GormTrainSubjectService) Upsert(c *gin.Context, m materials.TrainSubjectAdd) (int, error) {
	var subject model.TrainSubject
	Agencies, _ := json.Marshal(m.Agencies)
	start, err := time.Parse("2006-01-02 15:04:05", m.StartTime)
	if err != nil {
		return 0, myErrors.NewErr("开始时间格式错误")
	}
	end, err := time.Parse("2006-01-02 15:04:05", m.EndTime)
	if err != nil {
		return 0, myErrors.NewErr("结束时间格式错误")
	}
	// 通用字段
	subject.ID = m.SubjectID
	subject.StartTime = start
	subject.EndTime = end
	subject.Type = m.Type
	subject.Enabled = m.Enabled
	subject.Agencies = string(Agencies)
	subject.BriefIntro = m.BriefIntro
	subject.Tag = m.Tag
	subject.CompoundVisible = m.CompoundVisible
	subject.HasCertificate = m.HasCertificate
	subject.Title = m.Title
	// 根据不同的 Type 设置不同的字段
	switch subject.Type {
	case materials.CurseTypeLive.String():
		subject.Lecturer = m.Lecturer
		subject.LecturerAvatar = utils.DeletePrefix(m.LecturerAvatar)
	case materials.CurseTypeNormal.String():
		subject.Tactic = m.Tactic
	default:
		return 0, myErrors.NewErr("课程类型错误")
	}

	var id int
	if m.SubjectID == 0 {
		id, err = g.dao.Create(c, subject)
	} else {
		// 结束课程不能再修改
		detail, err := g.dao.Detail(c, int(m.SubjectID))
		if err != nil {
			return 0, myErrors.NewErr("课程不存在")
		}
		if detail.EndTime.Before(time.Now()) {
			return 0, myErrors.NewErr("课程已结束")
		}
		id, err = g.dao.Update(c, subject)
	}

	if err != nil {
		return 0, err
	}

	// 使用协程并发处理课时与大实战的保存
	var wg sync.WaitGroup
	wg.Add(2)

	// 处理课时与课程联系
	go func() {
		defer wg.Done()
		if err := g.dao.SaveToCourse(c, id, m.Courses, m.Type); err != nil {
			log.New().Error("[新增课程专题]：处理课时与课程联系错误: " + err.Error())
		}
	}()

	// 处理大实战
	go func() {
		defer wg.Done()
		m.Practices.SubjectID = uint(id)
		if err := g.dao.SaveToPractice(c, id, m.Practices); err != nil {
			log.New().Error("[新增课程专题]：处理大实战错误: " + err.Error())
		}
	}()

	// 等待所有协程完成
	wg.Wait()

	return id, err
}
func (g *GormTrainSubjectService) RoleTips(c *gin.Context) ([]map[string]any, error) {
	var role materials.RoleType
	tips := role.Range()
	return tips, nil
}

func (g *GormTrainSubjectService) EndpointTips(c *gin.Context) ([]map[string]any, error) {
	return g.dao.EndpointTypesTips(c)
}

func (g *GormTrainSubjectService) TypeTips(c *gin.Context) ([]map[string]any, error) {
	var CourseType materials.CourseType
	tips := CourseType.Range()
	return tips, nil
}

// Detail 获取专题详情
func (g *GormTrainSubjectService) Detail(c *gin.Context, id int) (map[string]any, error) {
	detail, err := g.dao.Detail(c, id)
	if err != nil {
		return nil, err
	}
	//获取创建人name
	var createdBy string
	if detail.CreatedBy != 0 {
		createdBy, _ = g.dao.AdminUser(c, id)
	}

	//获取可见终端
	var VisibleEndpointTypes string
	if detail.VisibleEndpointTypes != "" {
		ids := strings.Split(detail.VisibleEndpointTypes, ",")
		VisibleEndpointTypes, _ = g.dao.EndpointType(c, ids)
	}

	//获取参与地区
	var agencies map[string][]int
	if detail.Agencies != "" {
		err = json.Unmarshal([]byte(detail.Agencies), &agencies)
	}

	//
	var courses = make([]materials.TrainCourseDetail, len(detail.TrainCourse))
	for k, v := range detail.TrainCourse {
		courses[k].ID = v.ID
		courses[k].Name = v.Name
		courses[k].Description = v.Description
		courses[k].Issuer = v.Issuer
		courses[k].ResourceURL = v.ResourceURL
		courses[k].CreatedAt = v.CreatedAt.Format("2006-01-02 15:04:05")
		courses[k].UpdatedAt = v.UpdatedAt.Format("2006-01-02 15:04:05")
		courses[k].LiveStartTime = v.LiveStartTime.Format("2006-01-02 15:04:05")
		courses[k].LiveEndTime = v.LiveEndTime.Format("2006-01-02 15:04:05")
		courses[k].IsUse = v.IsUse
	}
	res := make(map[string]any)
	{
		res["agencies"] = agencies
		res["created_by"] = createdBy
		res["visible_endpoint_types"] = VisibleEndpointTypes
		res["title"] = detail.Title
		res["enabled"] = detail.Enabled
		res["lecturer"] = detail.Lecturer
		res["tag"] = detail.Tag
		res["tactic"] = detail.Tactic
		res["compound_visible"] = detail.CompoundVisible
		res["updated_at"] = detail.UpdatedAt.Format("2006-01-02 15:04:05")
		res["created_at"] = detail.CreatedAt.Format("2006-01-02 15:04:05")
		res["start_time"] = detail.StartTime.Format("2006-01-02 15:04:05")
		res["end_time"] = detail.EndTime.Format("2006-01-02 15:04:05")
		res["lecturer_avatar"] = utils.AddPrefix(detail.LecturerAvatar)
		//两个常量控制 类型和可见角色
		res["visible_roles"] = materials.RoleType(detail.VisibleRoles).StringToChineseRoleTypes()
		res["type"] = materials.CourseType(detail.Type).StringToChineseCurseType()
		//课程
		res["courses"] = courses
		res["practice"] = detail.Practice

	}
	return res, err
}

func (g *GormTrainSubjectService) List(c *gin.Context, req materials.TrainSubjectListReq) ([]map[string]any, int64, error) {
	list, total, err := g.dao.List(c, req)
	lists := make([]map[string]any, len(list))
	for k, v := range list {
		lists[k] = make(map[string]any)
		lists[k]["updated_at"] = v.UpdatedAt.Format("2006-01-02 15:04:05")
		lists[k]["created_at"] = v.CreatedAt.Format("2006-01-02 15:04:05")
		lists[k]["id"] = v.ID
		lists[k]["title"] = v.Title
		lists[k]["num_papers"] = v.NumPapers
		lists[k]["type"] = v.Type
		lists[k]["enabled"] = v.Enabled
		lists[k]["had_course"] = v.HadCourse
		lists[k]["had_certificate"] = v.HadCertificate
		lists[k]["had_practice"] = v.HadPractice
	}
	return lists, total, err
}

func sliceToMap(courses []model.TrainSubjectCourse) map[uint]bool {
	courseMap := make(map[uint]bool)
	for _, course := range courses {
		courseMap[course.CourseID] = false
	}
	return courseMap
}

func NewGormTrainSubjectService(dao dao.TrainSubjectDao) TrainSubjectService {
	return &GormTrainSubjectService{dao: dao}
}
