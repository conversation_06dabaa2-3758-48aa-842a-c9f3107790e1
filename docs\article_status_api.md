# 文章状态更新API文档

## 简介
提供三个API接口用于更新文章的分享状态、发布状态和置顶状态。

## 1. 更新文章分享状态

### 请求URL
```
POST /admin/operation/article/update-shareable
```

### 请求方法
POST

### 参数

| 参数名 | 是否必需 | 类型 | 描述 |
|--------|----------|------|------|
| id | 必需 | int | 文章ID |
| shareable | 必需 | int | 是否可分享，0-不可分享，1-可分享 |

### 返回示例
```json
{
    "code": 200,
    "message": "success",
    "data": {}
}
```

### 返回参数说明

| 参数名 | 类型 | 描述 |
|--------|------|------|
| code | int | 状态码，200表示成功 |
| message | string | 返回消息 |
| data | object | 返回数据，成功时为空对象 |

### 注意事项
- shareable参数只能为0或1，其他值会返回错误
- 需要管理员权限

## 2. 更新文章发布状态

### 请求URL
```
POST /admin/operation/article/update-enabled
```

### 请求方法
POST

### 参数

| 参数名 | 是否必需 | 类型 | 描述 |
|--------|----------|------|------|
| id | 必需 | int | 文章ID |
| enabled | 必需 | int | 是否发布，0-未发布，1-发布 |

### 返回示例
```json
{
    "code": 200,
    "message": "success",
    "data": {}
}
```

### 返回参数说明

| 参数名 | 类型 | 描述 |
|--------|------|------|
| code | int | 状态码，200表示成功 |
| message | string | 返回消息 |
| data | object | 返回数据，成功时为空对象 |

### 注意事项
- enabled参数只能为0或1，其他值会返回错误
- 需要管理员权限

## 3. 更新文章置顶状态

### 请求URL
```
POST /admin/operation/article/update-top
```

### 请求方法
POST

### 参数

| 参数名 | 是否必需 | 类型 | 描述 |
|--------|----------|------|------|
| id | 必需 | int | 文章ID |
| top | 必需 | int | 是否置顶，0-不置顶，1-置顶 |

### 返回示例
```json
{
    "code": 200,
    "message": "success",
    "data": {}
}
```

### 返回参数说明

| 参数名 | 类型 | 描述 |
|--------|------|------|
| code | int | 状态码，200表示成功 |
| message | string | 返回消息 |
| data | object | 返回数据，成功时为空对象 |

### 注意事项
- top参数只能为0或1，其他值会返回错误
- 需要管理员权限
- 这是新增的置顶功能

## 数据库变更

### OpArticle表新增字段
```sql
ALTER TABLE op_article ADD COLUMN top TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否置顶';
```

## 使用示例

### 设置文章为可分享
```bash
curl -X POST "http://localhost:8080/admin/operation/article/update-shareable" \
  -H "Content-Type: application/json" \
  -d '{"id": 1, "shareable": 1}'
```

### 发布文章
```bash
curl -X POST "http://localhost:8080/admin/operation/article/update-enabled" \
  -H "Content-Type: application/json" \
  -d '{"id": 1, "enabled": 1}'
```

### 置顶文章
```bash
curl -X POST "http://localhost:8080/admin/operation/article/update-top" \
  -H "Content-Type: application/json" \
  -d '{"id": 1, "top": 1}'
```
