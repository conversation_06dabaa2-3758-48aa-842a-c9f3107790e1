package endpoint_application

import (
	"errors"
	api "marketing/internal/api/endpoint_application"
	"marketing/internal/consts"
	"marketing/internal/model"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type EndpointApplyDao interface {
	CreateEndpointApply(c *gin.Context, data *model.EndpointApplication) error
	CountEndpointApplyByPolicyID(c *gin.Context, policyID int, id int) (int, error)
	UpdateEndpointApply(c *gin.Context, id int, data map[string]any) error
	GetEndpointApplyByID(c *gin.Context, id int) (*model.EndpointApplication, error)
	GetEndpointApplyList(c *gin.Context, param *api.EndpointApplicationListReq) ([]*api.EndpointApplicationListResp, int64, error)
	CreateEndpointApplicationStatus(c *gin.Context, data *model.EndpointApplicationStatus) error
	CreateEndpointMaterialSupport(c *gin.Context, data []*model.EndpointMaterialSupport) error
	GetEndpointMaterialSupportById(c *gin.Context, id int) ([]*model.EndpointMaterialSupport, error)
	UpsertEndpointApplyPostback(c *gin.Context, applicationID int, data *model.EndpointApplicationPostback) error
	GetEndpointApplicationInstallment(c *gin.Context, applicationID int) ([]*model.EndpointApplicationInstallment, error)
	CreateEndpointApplicationInstallment(c *gin.Context, data []*model.EndpointApplicationInstallment) error
	DeleteEndpointApplicationInstallment(c *gin.Context, applicationID int) error
	WithTransaction(c *gin.Context, fn func(txRepo EndpointApplyDao) error) error
	GetDB(c *gin.Context) *gorm.DB
}

type endpointApplyDao struct {
	db *gorm.DB
}

func NewEndpointApplyDao(db *gorm.DB) EndpointApplyDao {
	return &endpointApplyDao{
		db: db,
	}
}

func (e *endpointApplyDao) CreateEndpointApply(c *gin.Context, data *model.EndpointApplication) error {
	now := time.Now()
	data.CreatedAt = now
	data.UpdatedAt = &now
	return e.db.WithContext(c).Create(data).Error
}

func (e *endpointApplyDao) CountEndpointApplyByPolicyID(c *gin.Context, policyID int, id int) (int, error) {
	var count int64
	query := e.db.WithContext(c).Model(&model.EndpointApplication{}).
		Where("policy_id = ?", policyID).
		Where("state not in (?)", []consts.EndpointApplicationState{consts.ApplicationRejected})
	if id > 0 {
		query.Where("id != ?", id)
	}
	err := query.Count(&count).Error
	if err != nil {
		return 0, err
	}
	return int(count), nil
}

func (e *endpointApplyDao) UpdateEndpointApply(c *gin.Context, id int, data map[string]any) error {
	now := time.Now()
	data["updated_at"] = now
	return e.db.WithContext(c).Model(&model.EndpointApplication{}).
		Where("id = ?", id).
		Updates(data).Error
}

func (e *endpointApplyDao) GetEndpointApplyByID(c *gin.Context, id int) (*model.EndpointApplication, error) {
	var data model.EndpointApplication
	err := e.db.WithContext(c).Model(&model.EndpointApplication{}).
		Where("id = ?", id).
		First(&data).Error
	if err != nil {
		return nil, err
	}
	return &data, nil
}

func (e *endpointApplyDao) GetEndpointApplyList(c *gin.Context, param *api.EndpointApplicationListReq) ([]*api.EndpointApplicationListResp, int64, error) {
	var data []*api.EndpointApplicationListResp
	query := e.db.WithContext(c).Model(&model.EndpointApplication{}).
		Joins("left join endpoint e on endpoint_application.add_to_endpoint_id = e.id").
		Select("endpoint_application.*, e.code as endpoint_code, e.id as endpoint_id")
	if param.PolicyID > 0 {
		query.Where("endpoint_application.policy_id = ?", param.PolicyID)
	}
	if param.State > 0 {
		query.Where("endpoint_application.state = ?", param.State)
	}
	if param.Name != "" {
		query.Where("endpoint_application.name like ?", "%"+param.Name+"%")
	}
	if param.Type > 0 {
		query.Where("endpoint_application.type = ?", param.Type)
	}
	if param.Code > 0 {
		query.Where("e.code = ?", param.Code)
	}
	var total int64
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}
	offset := (param.Page - 1) * param.PageSize
	query.Order("endpoint_application.id desc")
	err = query.Offset(offset).Limit(param.PageSize).Find(&data).Error
	if err != nil {
		return nil, 0, err
	}
	return data, total, nil
}

func (e *endpointApplyDao) CreateEndpointApplicationStatus(c *gin.Context, data *model.EndpointApplicationStatus) error {
	now := time.Now()
	data.CreatedAt = now
	return e.db.WithContext(c).Create(data).Error
}

func (e *endpointApplyDao) CreateEndpointMaterialSupport(c *gin.Context, data []*model.EndpointMaterialSupport) error {
	now := time.Now()
	for _, item := range data {
		item.CreatedAt = now
		item.UpdatedAt = now
	}
	return e.db.WithContext(c).Create(data).Error
}

func (e *endpointApplyDao) GetEndpointMaterialSupportById(c *gin.Context, id int) ([]*model.EndpointMaterialSupport, error) {
	var data []*model.EndpointMaterialSupport
	err := e.db.WithContext(c).Model(&model.EndpointMaterialSupport{}).
		Where("application_id = ?", id).
		Find(&data).Error
	if err != nil {
		return nil, err
	}
	return data, nil
}

func (e *endpointApplyDao) UpsertEndpointApplyPostback(c *gin.Context, applicationID int, data *model.EndpointApplicationPostback) error {
	// 先查询是否存在该记录
	var existing model.EndpointApplicationPostback
	err := e.db.WithContext(c).Model(&model.EndpointApplicationPostback{}).
		Where("application_id = ?", applicationID).
		First(&existing).Error
	//时间处理
	now := time.Now()
	data.UpdatedAt = now
	data.ConfirmDate = now
	if err != nil {
		// 记录不存在，执行插入操作
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 确保application_id正确设置
			data.CreatedAt = now
			data.ApplicationID = applicationID
			return e.db.WithContext(c).Create(&data).Error
		}
		// 其他查询错误
		return err
	}

	// 记录存在，执行更新操作
	// 使用Map更新非零值字段，或根据需要调整更新策略
	updateData := make(map[string]interface{})

	// 这里根据实际model字段进行调整，示例列出常见字段
	updateData["write_off_table"] = data.WriteOffTable
	updateData["lease_contract"] = data.LeaseContract
	updateData["annual_rent"] = data.AnnualRent
	updateData["design_renderings"] = data.DesignRenderings
	updateData["renovation_photos"] = data.RenovationPhotos
	updateData["renovation_videos"] = data.RenovationVideos
	updateData["diploma"] = data.Diploma
	updateData["endpoint_group_photo"] = data.EndpointGroupPhoto
	updateData["confirm_date"] = data.ConfirmDate
	updateData["extend"] = data.Extend
	updateData["updated_at"] = now

	return e.db.WithContext(c).Model(&model.EndpointApplicationPostback{}).
		Where("application_id = ?", applicationID).
		Updates(updateData).Error
}

func (e *endpointApplyDao) GetEndpointApplicationInstallment(c *gin.Context, applicationID int) ([]*model.EndpointApplicationInstallment, error) {
	var data []*model.EndpointApplicationInstallment
	err := e.db.WithContext(c).Model(&model.EndpointApplicationInstallment{}).
		Where("application_id = ?", applicationID).
		Find(&data).Error
	if err != nil {
		return nil, err
	}
	return data, nil
}

func (e *endpointApplyDao) CreateEndpointApplicationInstallment(c *gin.Context, data []*model.EndpointApplicationInstallment) error {
	now := time.Now()
	for _, item := range data {
		item.CreatedAt = now
		item.UpdatedAt = now
	}
	return e.db.WithContext(c).Create(data).Error
}

func (e *endpointApplyDao) DeleteEndpointApplicationInstallment(c *gin.Context, applicationID int) error {
	return e.db.WithContext(c).Model(&model.EndpointApplicationInstallment{}).
		Where("application_id = ?", applicationID).
		Delete(&model.EndpointApplicationInstallment{}).Error
}

func (e *endpointApplyDao) WithTransaction(ctx *gin.Context, fn func(txRepo EndpointApplyDao) error) error {
	return e.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		return fn(&endpointApplyDao{db: tx})
	})
}

func (e *endpointApplyDao) GetDB(c *gin.Context) *gorm.DB {
	return e.db.WithContext(c)
}
