package types

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
)

// JSONField 通用 JSON 字段类型
type JSONField[T any] struct {
	Data T
}

// Value 实现 driver.Valuer 接口
func (j JSONField[T]) Value() (driver.Value, error) {
	return json.Marshal(j.Data)
}

// Scan 实现 sql.Scanner 接口
func (j *JSONField[T]) Scan(value interface{}) error {
	if value == nil {
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, &j.Data)
}

// MarshalJSON 实现 json.Marshaler 接口
func (j JSONField[T]) MarshalJSON() ([]byte, error) {
	return json.Marshal(j.Data)
}

// UnmarshalJSON 实现 json.Unmarshaler 接口
func (j *JSONField[T]) UnmarshalJSON(data []byte) error {
	return json.Unmarshal(data, &j.Data)
}

// GetByKey 根据 key 获取字段值（仅当 T 为 map 类型时有效）
func (j *JSONField[T]) GetByKey(key any) (any, bool) {
	m, ok := any(j.Data).(map[any]any)
	if !ok {
		return nil, false
	}
	val, exists := m[key]
	return val, exists
}

// GetByValue 根据值查找 key（仅当 T 为 map 类型时有效）
func (j *JSONField[T]) GetByValue(value any) (any, bool) {
	m, ok := any(j.Data).(map[any]any)
	if !ok {
		return nil, false
	}
	for k, v := range m {
		if v == value {
			return k, true
		}
	}
	return nil, false
}
