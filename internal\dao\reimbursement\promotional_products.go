package reimbursement

import (
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"gorm.io/gorm"
	api "marketing/internal/api/reimbursement"
	"marketing/internal/model"
	"marketing/internal/pkg/utils"
	"strings"
	"time"
)

// GetPromotionalProductsQuickPickStat 获取促销产品快捷键统计
func (r *repo) GetPromotionalProductsQuickPickStat(c *gin.Context, req *api.PromotionalProductsQuickPickStatReq) (*api.PromotionalProductsQuickPickStatResp, error) {
	var result api.PromotionalProductsQuickPickStatResp

	// Build the base query
	var query *gorm.DB

	if req.ReimbursementStatus != 0 {
		// Query with reimbursement_apply_order_summary join
		query = r.db.WithContext(c).Table("reimbursement_promotional_products_list rpp").
			Select(`count(*) as total_count,
				SUM(CASE WHEN rpp.status = 0 AND rpp.voucher_audit_rollback = 0 THEN 1 ELSE 0 END) AS pending_audit_count,
				SUM(CASE WHEN rpp.status = 1 THEN 1 ELSE 0 END) AS first_approved_count,
				SUM(CASE WHEN rpp.status = 2 THEN 1 ELSE 0 END) AS final_approved_count,
				SUM(CASE WHEN rpp.status = 3 THEN 1 ELSE 0 END) AS completed_count,
				SUM(CASE WHEN rpp.status = -1 THEN 1 ELSE 0 END) AS rejected_count,
				SUM(CASE WHEN rpp.status = -3 THEN 1 ELSE 0 END) AS failed_count,
				SUM(CASE WHEN rpp.status = -100 THEN 1 ELSE 0 END) AS cancelled_count`).
			Joins("LEFT JOIN reimbursement_apply_order_summary raos ON rpp.id = raos.apply_order_id").
			Where("raos.status = ? AND raos.apply_order_type = ? AND rpp.policy_id = ?",
				req.ReimbursementStatus, "promotional_products", req.PolicyID)
	} else {
		// Query without join
		query = r.db.WithContext(c).Table("reimbursement_promotional_products_list rpp").
			Select(`count(*) as total_count,
				SUM(CASE WHEN rpp.status = 0 AND rpp.voucher_audit_rollback = 0 THEN 1 ELSE 0 END) AS pending_audit_count,
				SUM(CASE WHEN rpp.status = 1 THEN 1 ELSE 0 END) AS first_approved_count,
				SUM(CASE WHEN rpp.status = 2 THEN 1 ELSE 0 END) AS final_approved_count,
				SUM(CASE WHEN rpp.status = 3 THEN 1 ELSE 0 END) AS completed_count,
				SUM(CASE WHEN rpp.status = -1 THEN 1 ELSE 0 END) AS rejected_count,
				SUM(CASE WHEN rpp.status = -3 THEN 1 ELSE 0 END) AS failed_count,
				SUM(CASE WHEN rpp.status = -100 THEN 1 ELSE 0 END) AS cancelled_count`).
			Where("rpp.policy_id = ?", req.PolicyID)
	}

	// Add optional filters
	if req.OrderID != 0 {
		query = query.Where("rpp.id = ?", req.OrderID)
	}

	if req.TopAgency != 0 {
		query = query.Where("rpp.top_agency = ?", req.TopAgency)
	}

	if req.StartTime != "" && req.EndTime != "" {
		query = query.Where("rpp.created_at BETWEEN ? AND ?", req.StartTime, req.EndTime)
	}

	if req.Status != 0 {
		query = query.Where("rpp.status = ?", req.Status)
		if req.Status == 0 {
			query = query.Where("rpp.voucher_audit_rollback = 0")
		}
	}

	if req.CompanyID != 0 {
		query = query.Where("rpp.company_id = ?", req.CompanyID)
	}

	if req.MaterialReturnStatus != 0 {
		query = query.Where("rpp.material_return_status = ?", req.MaterialReturnStatus)
	}

	if req.ExpressComeSn != "" {
		query = query.Where("rpp.express_come_sn = ?", req.ExpressComeSn)
	}

	if req.CompletionStatus != 0 {
		query = query.Where("rpp.completion_status = ?", req.CompletionStatus)
	}

	// Execute the query
	var queryResult struct {
		TotalCount         int `json:"total_count"`
		PendingAuditCount  int `json:"pending_audit_count"`
		FirstApprovedCount int `json:"first_approved_count"`
		FinalApprovedCount int `json:"final_approved_count"`
		CompletedCount     int `json:"completed_count"`
		RejectedCount      int `json:"rejected_count"`
		FailedCount        int `json:"failed_count"`
		CancelledCount     int `json:"cancelled_count"`
	}

	err := query.Scan(&queryResult).Error
	if err != nil {
		return nil, err
	}

	// Map the result to response struct
	result.TotalCount = queryResult.TotalCount
	result.PendingAuditCount = queryResult.PendingAuditCount
	result.FirstApprovedCount = queryResult.FirstApprovedCount
	result.FinalApprovedCount = queryResult.FinalApprovedCount
	result.CompletedCount = queryResult.CompletedCount
	result.RejectedCount = queryResult.RejectedCount
	result.FailedCount = queryResult.FailedCount
	result.CancelledCount = queryResult.CancelledCount

	return &result, nil
}

// GetBackstagePromotionalProductsDetail 获取后台推广产品详情
func (r *repo) GetBackstagePromotionalProductsDetail(orderID int) (*api.PromotionalProductsDetailResp, error) {
	var result api.PromotionalProductsDetailResp

	err := r.db.Table("reimbursement_promotional_products_list rpp").
		Select(`rpp.id, rpp.sn, rpp.uid, rpp.top_agency, rpp.second_agency, rpp.company_id, rpp.amount,
			rpp.company, rpp.name AS contact_name, rpp.phone AS contact_phone,
			rpp.address, rpp.status, rpp.actual_amount, rpp.created_at, rpp.voucher_audit_man,
			rpp.voucher_audit_time, rpp.voucher_audit_remark, rpp.voucher_audit_rollback,
			rpp.data_audit_man, rpp.data_verify_time, rpp.data_audit_remark, rpp.data_audit_rollback, rpp.policy_id,
			rp.name AS policy_name, rp.start_time as policy_start_time, rp.end_time AS policy_end_time, rp.archive,
			au.phone, a.name as agency_name, rpp.express_go_sn, rpp.express_go_com, rpp.express_go_time,
			rpp.express_come_sn, rpp.express_come_com, rpp.express_come_time`).
		Joins("LEFT JOIN reimbursement_policy rp ON rpp.policy_id = rp.id").
		Joins("LEFT JOIN admin_users au ON rpp.uid = au.id").
		Joins("LEFT JOIN agency a ON rpp.top_agency = a.id").
		Where("rpp.id = ?", orderID).
		Scan(&result).Error
	if err != nil {
		return nil, err
	}

	return &result, nil
}

// GetPromotionalProductsDetail 获取推广产品详情
func (r *repo) GetPromotionalProductsDetail(c *gin.Context, req *api.OrderReq) (*api.PromotionalProductsDetailResp, error) {
	orderID := req.ID

	var detail *api.PromotionalProductsDetailResp

	err := r.db.WithContext(c).Table("reimbursement_promotional_products_list AS rpp").
		Select(`
			rpp.id, rpp.sn, rpp.code, rpp.uid, rpp.top_agency, rpp.company_id, rpp.amount,
			rpp.company, rpp.name AS contact_name, rpp.phone AS contact_phone,
			rpp.province, rpp.city,rpp.district, rpp.address, rpp.status,
			rpp.actual_amount, rpp.created_at, rpp.voucher_audit_man,
			rpp.voucher_audit_time, rpp.voucher_audit_remark, rpp.voucher_audit_rollback,
			rpp.data_audit_man, rpp.data_verify_time, rpp.data_audit_remark, rpp.data_audit_rollback, rpp.policy_id,
			rp.name AS policy_name, rp.start_time AS policy_start_time, rp.end_time AS policy_end_time, rp.archive,
			rp.user_name AS mr_user_name, rp.phone AS mr_phone, rp.province AS mr_province, rp.city AS mr_city,
			rp.district AS mr_district, rp.address AS mr_address, rp.explain,
			au.phone, a.name AS agency_name, rpp.express_go_sn, rpp.express_go_com, rpp.express_go_time,
			rpp.express_come_sn, rpp.express_come_com, rpp.express_come_time
		`).
		Joins("LEFT JOIN reimbursement_policy rp ON rpp.policy_id = rp.id").
		Joins("LEFT JOIN admin_users au ON rpp.uid = au.id").
		Joins("LEFT JOIN agency a ON rpp.top_agency = a.id").
		Where("rpp.top_agency = ? AND rpp.id = ?", req.Agency, orderID).
		Scan(&detail).Error

	if err != nil {
		return nil, err
	}

	return detail, nil
}

// GetReimbursementProducts 获取报销产品信息
func (r *repo) GetReimbursementProducts(c *gin.Context, orderID int) ([]api.PromotionalProductDetail, error) {
	var products []api.PromotionalProductDetail

	query := r.db.WithContext(c).Table("reimbursement_promotional_products_relation AS rppr").
		Select("rppr.id, rpp.id as product_id, rpp.preview, rpp.name, rppr.norm, rpp.unit, rppr.price_type, " +
			"rpp.include_tax_price, rpp.exclude_tax_price, rpp.reimbursement_price, rppr.quantity, rppr.price, " +
			"rppr.price*rppr.norm*rppr.quantity AS total_price, rpp.include_tax_account_info, rpp.exclude_tax_account_info, rpp.communication_letter").
		Joins("RIGHT JOIN reimbursement_promotional_products rpp ON rpp.id = rppr.product_id")

	var results []struct {
		ID                    int     `db:"id"`
		ProductID             int     `db:"product_id"`
		Preview               string  `db:"preview"`
		Name                  string  `db:"name"`
		Norm                  string  `db:"norm"`
		Unit                  string  `db:"unit"`
		PriceType             string  `db:"price_type"`
		IncludeTaxPrice       float64 `db:"include_tax_price"`
		ExcludeTaxPrice       float64 `db:"exclude_tax_price"`
		ReimbursementPrice    float64 `db:"reimbursement_price"`
		Quantity              int     `db:"quantity"`
		Price                 float64 `db:"price"`
		TotalPrice            float64 `db:"total_price"`
		IncludeTaxAccountInfo string  `db:"include_tax_account_info"`
		ExcludeTaxAccountInfo string  `db:"exclude_tax_account_info"`
		CommunicationLetter   string  `db:"communication_letter"`
	}

	err := query.Where("rppr.reimbursement_id = ?", orderID).Scan(&results).Error
	if err != nil {
		return nil, err
	}

	for _, result := range results {
		product := api.PromotionalProductDetail{
			ID:                 result.ID,
			ProductID:          result.ProductID,
			Name:               result.Name,
			Norm:               result.Norm,
			Unit:               result.Unit,
			PriceType:          result.PriceType,
			IncludeTaxPrice:    result.IncludeTaxPrice,
			ExcludeTaxPrice:    result.ExcludeTaxPrice,
			ReimbursementPrice: result.ReimbursementPrice,
			Quantity:           result.Quantity,
			Price:              result.Price,
			TotalPrice:         result.TotalPrice,
		}

		// 解析JSON字符串为FileMetadata数组
		product.IncludeTaxAccountInfo = loadFiles(result.IncludeTaxAccountInfo)
		product.ExcludeTaxAccountInfo = loadFiles(result.ExcludeTaxAccountInfo)
		product.CommunicationLetter = loadFiles(result.CommunicationLetter)
		product.Preview = loadFiles(result.Preview)

		products = append(products, product)
	}

	return products, nil
}

func (r *repo) GetPromotionalProductsOrder(c *gin.Context, orderID int, agency map[string]int) (*model.ReimbursementPromotionalProductsList, error) {
	var product model.ReimbursementPromotionalProductsList

	query := r.db.WithContext(c).Table("reimbursement_promotional_products_list").Where("id = ?", orderID)
	if agency != nil {
		query = query.Where("top_agency = ? AND second_agency = ?", agency["top_agency"], agency["second_agency"])
	}

	err := query.First(&product).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return &product, nil
}

// InvalidPromotionalProducts 促销品作废
func (r *repo) InvalidPromotionalProducts(c *gin.Context, orderID int) (bool, error) {
	// 更新状态为-100（作废）
	err := r.db.WithContext(c).Model(&model.ReimbursementPromotionalProductsList{}).
		Where("id = ?", orderID).
		Update("status", -100).Error

	if err != nil {
		return false, err
	}

	return true, nil
}

// ReviewVoucherPromotionalProducts 促销品凭证审核
func (r *repo) ReviewVoucherPromotionalProducts(c *gin.Context, req *api.ReimbursementAuditReq) error {
	now := time.Now()

	return r.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		// 1. 更新促销品申请记录状态
		updates := map[string]interface{}{
			"voucher_audit_man":    req.Uid,
			"voucher_audit_remark": req.Remark,
			"voucher_audit_time":   now,
			"updated_at":           now,
		}

		if req.Status == -1 {
			// 审核不通过，回滚到申请审核通过状态
			updates["status"] = 0
			updates["voucher_audit_rollback"] = 1
		} else {
			// 审核通过
			updates["status"] = 1
			updates["voucher_audit_rollback"] = 0
		}

		err := tx.Model(&model.ReimbursementPromotionalProductsList{}).
			Where("id = ?", req.ID).
			Updates(updates).Error
		if err != nil {
			return err
		}

		return nil
	})
}

// ReviewDataPromotionalProducts 促销品收货单审批
func (r *repo) ReviewDataPromotionalProducts(c *gin.Context, req *api.ReimbursementAuditReq, order *model.ReimbursementPromotionalProductsList) error {
	return r.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		now := time.Now()

		// 1. 更新促销品申请记录状态
		updates := map[string]interface{}{
			"data_audit_man":    req.Uid,
			"data_audit_remark": req.Remark,
			"data_verify_time":  now,
			"updated_at":        now,
		}

		if req.Status == -1 {
			// 审核不通过，回滚到步骤3
			updates["status"] = 1
			updates["data_audit_rollback"] = 1
		} else {
			// 审核通过
			updates["status"] = 3
			updates["data_audit_rollback"] = 0
		}

		err := tx.Model(&model.ReimbursementPromotionalProductsList{}).
			Where("id = ?", req.ID).
			Updates(updates).Error
		if err != nil {
			return err
		}

		// 2. 如果审核通过，创建汇总记录
		if req.Status != -1 {
			summary := &model.ReimbursementApplyOrderSummary{
				ApplyOrderID:             req.ID,
				SN:                       order.SN,
				UID:                      order.UID,
				TopAgency:                order.TopAgency,
				SecondAgency:             0,
				CompanyID:                order.CompanyID,
				Code:                     order.Code,
				Company:                  order.Company,
				PolicyID:                 order.PolicyID,
				Amount:                   order.Amount,
				ReimbursementApplyAmount: order.ReimbursementApplyAmount,
				QuantityTotal:            order.QuantityTotal,
				Status:                   0,
				ApplyOrderType:           "promotional_products",
				ReimbursementType:        order.ReimbursementType,
				CreatedAt:                order.CreatedAt,
				UpdatedAt:                &now,
			}

			if err := tx.Create(summary).Error; err != nil {
				return err
			}
		}

		return nil
	})
}

// AddExpressInfo 添加快递信息
func (r *repo) AddExpressInfo(c *gin.Context, req *api.AddExpressInfoReq) error {
	// 更新快递信息
	err := r.db.WithContext(c).Model(&model.ReimbursementPromotionalProductsList{}).
		Where("id = ?", req.ID).
		Updates(map[string]interface{}{
			"express_go_sn":   req.ExpressSN,   // 快递单号
			"express_go_com":  req.ExpressCom,  // 快递公司
			"express_go_time": req.ExpressTime, // 发货时间
			"updated_at":      time.Now(),      // 更新时间
		}).Error

	return err
}

// PromotionalProductsSplit 促销品订单拆分
func (r *repo) PromotionalProductsSplit(c *gin.Context, req *api.PromotionalProductsSplitReq) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		now := time.Now()

		// 1. 更新原订单状态为-3
		err := tx.Table("reimbursement_apply_order_summary").
			Where("id = ?", req.OrderID).
			Updates(map[string]interface{}{
				"status":     -3,
				"updated_at": now,
			}).Error
		if err != nil {
			return err
		}

		// 2. 获取原订单信息用于复制
		var order struct {
			ApplyOrderID         int64  `gorm:"column:apply_order_id"`
			UID                  int64  `gorm:"column:uid"`
			TopAgency            string `gorm:"column:top_agency"`
			SecondAgency         string `gorm:"column:second_agency"`
			CompanyID            string `gorm:"column:company_id"`
			Code                 string `gorm:"column:code"`
			Company              string `gorm:"column:company"`
			PolicyID             int64  `gorm:"column:policy_id"`
			ApplyOrderType       string `gorm:"column:apply_order_type"`
			ReimbursementType    int    `gorm:"column:reimbursement_type"`
			MaterialReturnStatus int    `gorm:"column:material_return_status"`
		}
		err = tx.Table("reimbursement_apply_order_summary").
			Where("id = ?", req.OrderID).
			First(&order).Error
		if err != nil {
			return err
		}

		// 3. 插入拆分后的新订单
		for _, item := range req.OrderSplitInfo {
			sn := utils.GenerateUniqueSN() // 需要实现生成唯一编号的函数
			newOrder := map[string]interface{}{
				"apply_order_id":         order.ApplyOrderID,
				"uid":                    order.UID,
				"sn":                     sn,
				"top_agency":             order.TopAgency,
				"second_agency":          order.SecondAgency,
				"company_id":             order.CompanyID,
				"code":                   order.Code,
				"company":                order.Company,
				"policy_id":              order.PolicyID,
				"amount":                 item.Amount,
				"quantity_total":         item.Quantity,
				"apply_order_type":       order.ApplyOrderType,
				"reimbursement_type":     order.ReimbursementType,
				"split_type":             1,
				"order_id":               req.OrderID,
				"material_return_status": order.MaterialReturnStatus,
				"created_at":             now,
				"updated_at":             now,
			}

			err = tx.Table("reimbursement_apply_order_summary").
				Create(newOrder).Error
			if err != nil {
				return err
			}
		}

		return nil
	})
}

func (r *repo) GetPendingPromotionalProducts(c *gin.Context, companyID int, topAgency uint) ([]api.PendingTask, error) {
	var results []api.PendingTask

	err := r.db.WithContext(c).
		Table("reimbursement_promotional_products_list rppl").
		Select(`rp.id AS policy_id, rp.name AS policy_name, rp.policy_type, 
                rppl.id, rppl.sn, rppl.status, rppl.created_at,
                rppl.amount/2 as amount, rppl.company,
                rppl.voucher_audit_rollback, rppl.data_audit_rollback, ? as type`, 1).
		Joins("LEFT JOIN reimbursement_policy rp ON rppl.policy_id = rp.id").
		Where(r.db.Where("rppl.voucher_audit_rollback = ? AND rppl.status = ?", 1, 0).
			Or("rppl.status = ?", 1)).
		Where("rppl.top_agency = ? AND rppl.company_id = ?", topAgency, companyID).
		Scan(&results).Error

	if err != nil {
		return nil, err
	}

	return results, nil
}

func (r *repo) GetPromotionalProductsOrders(c *gin.Context, req *api.ClientOrdersReq) ([]*api.ClientOrdersResp, error) {
	offset := (req.Page - 1) * req.PageSize

	// 状态转换
	dbStatus := 0
	switch req.Status {
	case 1:
		dbStatus = 0
	case 2:
		dbStatus = 1
	case 3:
		dbStatus = 2
	}

	var results []*api.ClientOrdersResp
	query := r.db.WithContext(c).Table("reimbursement_promotional_products_list rppl").
		Select(`
            rp.id as policy_id,
            rp.name as policy_name,
            rp.policy_type,
            rppl.id,
            rppl.sn,
            rppl.status,
            rppl.created_at,
            IF(rppl.reimbursement_apply_amount = 0, rppl.amount/2, rppl.reimbursement_apply_amount) as amount,
            rppl.company,
            ? as type`, 1).
		Joins("LEFT JOIN reimbursement_policy rp ON rppl.policy_id = rp.id").
		Where("rppl.top_agency = ? AND rppl.company_id = ? AND rppl.policy_id = ? AND rppl.status = ?",
			req.TopAgency, req.CompanyID, req.PolicyID, dbStatus)

	if req.StartTime != "" && req.EndTime != "" {
		query = query.Where("rppl.created_at BETWEEN ? AND ?", req.StartTime, req.EndTime)
	}

	err := query.Order("rppl.created_at desc").
		Offset(offset).
		Limit(req.PageSize).
		Scan(&results).Error
	if err != nil {
		return nil, err
	}

	return results, nil
}

func (r *repo) GetPromotionalProductsApplySummaryOrders(c *gin.Context, req *api.ClientOrdersReq) ([]*api.ClientOrdersResp, error) {
	offset := (req.Page - 1) * req.PageSize

	var results []*api.ClientOrdersResp
	query := r.db.WithContext(c).Table("reimbursement_apply_order_summary raos").
		Select(`
            rp.id as policy_id,
            rp.name as policy_name,
            rp.policy_type,
            raos.id,
            raos.apply_order_id,
            raos.sn,
            raos.status,
            raos.created_at,
            IF(raos.reimbursement_apply_amount = 0, raos.amount/2, raos.reimbursement_apply_amount) as amount,
            raos.company,
            raos.turn_type,
            raos.split_type,
            ? as type`, 3).
		Joins("LEFT JOIN reimbursement_policy rp ON raos.policy_id = rp.id").
		Where("(raos.status = 0 OR raos.status = -100) AND raos.top_agency = ? AND raos.company_id = ? AND raos.policy_id = ?",
			req.TopAgency, req.CompanyID, req.PolicyID)

	if req.StartTime != "" && req.EndTime != "" {
		query = query.Where("raos.created_at BETWEEN ? AND ?", req.StartTime, req.EndTime)
	}

	err := query.Order("raos.created_at desc").
		Offset(offset).
		Limit(req.PageSize).
		Scan(&results).Error
	if err != nil {
		return nil, err
	}

	return results, nil
}

// ApplyPromotionalProducts 促销品申请
func (r *repo) ApplyPromotionalProducts(c *gin.Context, req *api.PromotionalProductsApplyReq) error {
	// 开启事务
	tx := r.db.WithContext(c).Begin()
	if tx.Error != nil {
		return tx.Error
	}

	// 确保事务在函数返回时要么提交要么回滚
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	now := time.Now()
	sn := utils.GenerateUniqueSN()

	// 创建促销品申请主单
	promotionalProducts := &model.ReimbursementPromotionalProductsList{
		PolicyID:          req.PolicyID,
		Amount:            req.Amount,
		CompanyID:         req.CompanyID,
		Name:              req.Name,
		Phone:             req.Phone,
		Province:          req.Province,
		City:              req.City,
		District:          req.District,
		Address:           req.Address,
		QuantityTotal:     req.QuantityTotal,
		Code:              req.CompanyCode,
		SN:                sn,
		UID:               req.Uid,
		TopAgency:         req.Agency,
		Company:           req.Company,
		Status:            0,
		ReimbursementType: req.ReimbursementType,
		CreatedAt:         now,
	}

	if err := tx.Create(promotionalProducts).Error; err != nil {
		tx.Rollback()
		return err
	}

	var err error
	var actualAmount float64
	for _, product := range req.ProductsInfo {
		// 验证必填参数
		if product.ProductID == 0 || product.Quantity == 0 ||
			product.Norm == 0 || product.PriceType == "" || product.Price == 0 {
			tx.Rollback()
			return errors.New("促销品列表参数错误，参数不能为空")
		}

		// 创建促销品关联
		relation := &model.ReimbursementPromotionalProductsRelation{
			ReimbursementID: promotionalProducts.ID,
			ProductID:       product.ProductID,
			Quantity:        product.Quantity,
			Norm:            product.Norm,
			PriceType:       product.PriceType,
			Price:           product.Price,
		}

		if err = tx.Create(relation).Error; err != nil {
			tx.Rollback()
			return err
		}

		// 获取商品核销价格并计算实际核销金额
		var reimbursementPrice float64
		err = tx.Table("reimbursement_promotional_products").
			Select("reimbursement_price").
			Where("id = ?", product.ProductID).
			Scan(&reimbursementPrice).Error
		if err != nil {
			tx.Rollback()
			return err
		}

		actualAmount += reimbursementPrice * float64(product.Quantity) * float64(product.Norm)
	}

	// 4. 保存申请材料信息
	for _, info := range req.ApplicationInfo {
		applyInfo := &model.ReimbursementApplyInfo{
			ApplyOrderID:   promotionalProducts.ID,
			Explain:        info.Explain,
			URL:            info.URL,
			ApplyOrderType: "promotional_products",
			Type:           "1",
		}

		if err = tx.Create(applyInfo).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	// 5. 更新实际核销金额
	if err = tx.Model(&model.ReimbursementPromotionalProductsList{}).
		Where("id = ?", promotionalProducts.ID).
		Update("reimbursement_apply_amount", actualAmount).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 提交事务
	return tx.Commit().Error
}

func (r *repo) ChangePromotionalProducts(c *gin.Context, req *api.PromotionalProductsApplyReq) (bool, error) {
	tx := r.db.WithContext(c).Begin()
	if tx.Error != nil {
		return false, tx.Error
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	now := time.Now()

	// 1. 更新促销品申请主表
	updates := map[string]interface{}{
		"amount":                 req.Amount,
		"company_id":             req.CompanyID,
		"company":                req.Company,
		"name":                   req.Name,
		"phone":                  req.Phone,
		"province":               req.Province,
		"city":                   req.City,
		"district":               req.District,
		"address":                req.Address,
		"quantity_total":         req.QuantityTotal,
		"code":                   req.CompanyCode,
		"uid":                    req.Uid,
		"updated_at":             now,
		"voucher_audit_rollback": 0,
	}

	err := tx.Model(&model.ReimbursementPromotionalProductsList{}).
		Where("id = ?", req.ID).
		Updates(updates).Error
	if err != nil {
		tx.Rollback()
		return false, err
	}

	// 2. 处理产品关联信息
	var existingIds []int
	err = tx.Model(&model.ReimbursementPromotionalProductsRelation{}).
		Where("reimbursement_id = ?", req.ID).
		Pluck("id", &existingIds).Error
	if err != nil {
		tx.Rollback()
		return false, err
	}

	existingMap := make(map[int]bool)
	for _, id := range existingIds {
		existingMap[id] = true
	}

	// 计算实际报销金额
	var actualAmount float64

	// 处理每个产品
	for _, product := range req.ProductsInfo {
		if product.ProductID == 0 || product.Quantity == 0 || product.Norm == 0 {
			tx.Rollback()
			return false, fmt.Errorf("产品信息不完整")
		}

		// 获取产品报销价格
		var reimbursementPrice float64
		err = tx.Model(&model.PromotionalProduct{}).
			Where("id = ?", product.ProductID).
			Select("reimbursement_price").
			Scan(&reimbursementPrice).Error
		if err != nil {
			tx.Rollback()
			return false, err
		}

		actualAmount += reimbursementPrice * cast.ToFloat64(product.Quantity) * cast.ToFloat64(product.Norm)

		if product.ID > 0 {
			// 更新已存在的产品关联
			err = tx.Model(&model.ReimbursementPromotionalProductsRelation{}).
				Where("id = ?", product.ID).
				Updates(map[string]interface{}{
					"product_id": product.ProductID,
					"quantity":   product.Quantity,
					"norm":       product.Norm,
					"price_type": product.PriceType,
					"price":      product.Price,
				}).Error
			if err != nil {
				tx.Rollback()
				return false, err
			}
			delete(existingMap, product.ID)
		} else {
			// 创建新的产品关联
			err = tx.Create(&model.ReimbursementPromotionalProductsRelation{
				ReimbursementID: req.ID,
				ProductID:       product.ProductID,
				Quantity:        product.Quantity,
				Norm:            product.Norm,
				PriceType:       product.PriceType,
				Price:           product.Price,
			}).Error
			if err != nil {
				tx.Rollback()
				return false, err
			}
		}
	}

	// 删除不再使用的产品关联
	for id := range existingMap {
		err = tx.Delete(&model.ReimbursementPromotionalProductsRelation{}, id).Error
		if err != nil {
			tx.Rollback()
			return false, err
		}
	}

	// 3. 处理申请凭证
	err = r.handleApplicationInfo(tx, req.ID, "promotional_products", req.ApplicationInfo)
	if err != nil {
		tx.Rollback()
		return false, err
	}

	// 4. 更新实际报销金额
	err = tx.Model(&model.ReimbursementPromotionalProductsList{}).
		Where("id = ?", req.ID).
		Update("reimbursement_apply_amount", actualAmount).Error
	if err != nil {
		tx.Rollback()
		return false, err
	}

	return true, tx.Commit().Error
}

// handleApplicationInfo 处理申请凭证信息
func (r *repo) handleApplicationInfo(tx *gorm.DB, orderID int, orderType string, applicationInfo []api.ApplicationInfo) error {
	// 获取现有凭证
	var existingIDs []int
	err := tx.Model(&model.ReimbursementApplyInfo{}).
		Where("apply_order_id = ? AND apply_order_type = ? AND type = 1 AND deleted_at IS NULL",
			orderID, orderType).
		Pluck("id", &existingIDs).Error
	if err != nil {
		return err
	}

	existingMap := make(map[int]bool)
	for _, id := range existingIDs {
		existingMap[id] = true
	}

	// 处理每个凭证
	for _, info := range applicationInfo {
		if info.ID > 0 {
			// 更新现有凭证
			err = tx.Model(&model.ReimbursementApplyInfo{}).
				Where("id = ? AND type = 1 AND deleted_at IS NULL", info.ID).
				Updates(map[string]interface{}{
					"explain": info.Explain,
					"url":     info.URL,
				}).Error
			if err != nil {
				return err
			}
			delete(existingMap, info.ID)
		} else {
			// 创建新凭证
			err = tx.Create(&model.ReimbursementApplyInfo{
				ApplyOrderID:   orderID,
				Explain:        info.Explain,
				URL:            info.URL,
				ApplyOrderType: orderType,
				Type:           "1",
			}).Error
			if err != nil {
				return err
			}
		}
	}

	// 删除不再使用的凭证
	for id := range existingMap {
		err = tx.Delete(&model.ReimbursementApplyInfo{}, id).Error
		if err != nil {
			return err
		}
	}

	return nil
}

// GetPolicyQuantityQuota 返回: -1 代表不做限制, 0 代表没有额度，正数代表可用额度
func (r *repo) GetPolicyQuantityQuota(c *gin.Context, policyID int, orderID int) (int64, error) {
	// 1. 获取政策赠品限制数量
	var policy struct {
		GiftLimit int64 `gorm:"column:gift_limit"`
	}
	err := r.db.WithContext(c).Table("reimbursement_policy").
		Select("gift_limit").
		Where("id = ?", policyID).
		First(&policy).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return -1, nil
		}
		return 0, err
	}

	// 如果gift_limit不做限制则返回-1
	if policy.GiftLimit <= 0 {
		return -1, nil
	}

	// 2. 获取已申请总数
	var result struct {
		Total sql.NullInt64 `gorm:"column:total"`
	}

	query := r.db.WithContext(c).Table("reimbursement_promotional_products_list").
		Select("SUM(quantity_total) as total").
		Where("status NOT IN (-1,-100) AND policy_id = ?", policyID)

	// 如果有订单ID,需要排除当前订单
	if orderID > 0 {
		query = query.Where("id != ?", orderID)
	}

	err = query.Scan(&result).Error
	if err != nil {
		return 0, err
	}

	totalApplied := int64(0)
	if result.Total.Valid {
		totalApplied = result.Total.Int64
	}

	// 3. 计算剩余可用额度
	if policy.GiftLimit <= totalApplied {
		return 0, nil
	}

	return policy.GiftLimit - totalApplied, nil
}

func (r *repo) UploadReceipt(c *gin.Context, req *api.UploadReceiptReq) error {
	// 开启事务
	tx := r.db.WithContext(c).Begin()
	if tx.Error != nil {
		return tx.Error
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	now := time.Now()

	// 更新促销产品列表状态
	err := tx.Table("reimbursement_promotional_products_list").
		Where("id = ?", req.ID).
		Updates(map[string]interface{}{
			"status":              2,
			"data_audit_rollback": 0,
			"updated_at":          now,
		}).Error
	if err != nil {
		tx.Rollback()
		return err
	}

	// 获取现有的申请信息
	var existingApplyInfoIDs []int
	err = tx.Table("reimbursement_apply_info").
		Select("id").
		Where("apply_order_id = ? AND apply_order_type = ? AND type = ? AND deleted_at IS NULL",
			req.ID, "promotional_products", 2).
		Find(&existingApplyInfoIDs).Error
	if err != nil {
		tx.Rollback()
		return err
	}

	// 将现有ID转换为map以便快速查找
	existingIDMap := make(map[int]bool)
	for _, id := range existingApplyInfoIDs {
		existingIDMap[id] = true
	}

	// 处理每条申请信息
	for _, info := range req.ApplicationInfo {
		// 处理URL前缀
		url := info.URL

		if info.ID != 0 {
			// 更新现有记录
			err = tx.Table("reimbursement_apply_info").
				Where("id = ? AND type = ? AND deleted_at IS NULL", info.ID, 2).
				Updates(map[string]interface{}{
					"explain": info.Explain,
					"url":     url,
				}).Error
			if err != nil {
				tx.Rollback()
				return err
			}
			delete(existingIDMap, info.ID)
		} else {
			// 插入新记录
			err = tx.Table("reimbursement_apply_info").
				Create(map[string]interface{}{
					"apply_order_id":   req.ID,
					"explain":          info.Explain,
					"url":              url,
					"apply_order_type": "promotional_products",
					"type":             2,
				}).Error
			if err != nil {
				tx.Rollback()
				return err
			}
		}
	}

	// 删除未更新的旧记录
	if len(existingIDMap) > 0 {
		var idsToDelete []int
		for id := range existingIDMap {
			idsToDelete = append(idsToDelete, id)
		}
		err = tx.Table("reimbursement_apply_info").
			Where("id IN ?", idsToDelete).
			Delete(&model.ReimbursementApplyInfo{}).Error
		if err != nil {
			tx.Rollback()
			return err
		}
	}

	// 提交事务
	return tx.Commit().Error
}

// loadFiles 解析JSON字符串为FileMetadata数组
func loadFiles(jsonStr string) []api.FileMetadata {
	if jsonStr == "" {
		return []api.FileMetadata{}
	}

	var files []api.FileMetadata
	err := json.Unmarshal([]byte(jsonStr), &files)
	if err != nil {
		// 如果解析失败，返回空数组
		return []api.FileMetadata{}
	}

	// 为每个文件URL添加OSS域名前缀
	for i := range files {
		if files[i].URL != "" && !strings.HasPrefix(files[i].URL, "http") {
			files[i].URL = utils.AddPrefix(files[i].URL)
		}
	}

	return files
}
