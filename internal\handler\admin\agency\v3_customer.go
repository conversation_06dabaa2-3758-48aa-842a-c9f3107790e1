package agency

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/handler"
	"marketing/internal/pkg/e"
	"marketing/internal/pkg/errors"
	"marketing/internal/service"
)

type V3Customer struct {
	svc service.V3CustomerSvc
}

func NewV3Customer(svc service.V3CustomerSvc) *V3Customer {
	return &V3Customer{
		svc: svc,
	}
}

func (v *V3Customer) GetCustomerList(c *gin.Context) {
	channel := e.ReqParamStr(c, "channel")
	groupCode := e.ReqParamStr(c, "group_code")
	customerStatus := e.ReqParamInt(c, "customer_status")
	name := e.ReqParamStr(c, "name")
	pageNum := e.ReqParamInt(c, "page_num")
	pageSize := e.ReqParamInt(c, "page_size")

	list, total := v.svc.GetCustomerList(c, channel, groupCode, customerStatus, name, pageNum, pageSize)

	handler.Success(c, gin.H{
		"list":  list,
		"total": total,
	})
}

func (v *V3Customer) GetCustomerById(c *gin.Context) {
	customerId := e.ReqParamInt(c, "cust_id")
	info := v.svc.GetCustomerById(c, customerId)
	if info == nil {
		handler.Error(c, errors.NewErr("数据不存在"))
		return
	}
	handler.Success(c, info)
}

func (v *V3Customer) EditCustomer(c *gin.Context) {
	customerId := e.ReqParamInt(c, "cust_id")
	channel := e.ReqParamStr(c, "channel")
	groupId := e.ReqParamInt(c, "group_id")
	customerName := e.ReqParamStr(c, "cust_name")
	customerShortName := e.ReqParamStr(c, "cust_short_name")
	err := v.svc.EditCustomer(c, customerId, channel, groupId, customerName, customerShortName)
	if err != nil {
		handler.Error(c, errors.NewErr(err.Error()))
		return
	}

	handler.Success(c, gin.H{})
}
