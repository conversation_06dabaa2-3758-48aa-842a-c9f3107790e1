package dao

import (
	"fmt"
	"gorm.io/gorm"
	"marketing/internal/model"
	"marketing/internal/pkg/db"
)

// 返回的数据结构
type ResponseTree struct {
	TopAgencyName  string                 `json:"top_agency_name"`
	SecondAgencies []SecondAgencyResponse `json:"second_agencies,omitempty"`
	EndpointNames  []string               `json:"name"`
	//Endpoints      []EndpointMin          `json:"endpoints,omitempty"`
}

type SecondAgencyResponse struct {
	SecondAgencyName string   `json:"second_agency_name"`
	EndpointNames    []string `json:"name"`
	//Endpoints        []EndpointMin `json:"endpoints"`
}

// 查找二级代理
/*func FindSecondAgency(agencyPId int) (agency2 *Agency, err error) {
>>>>>>> test:internal/dao/endpointAdminUser.go
	db := GetDB()
	var agency model.AgencyData
	if err := db.Table("agency").Where("pid = ?", agencyPId).First(&agency).Error; err != nil {
		return nil, err
	}
	return &agency, nil
}*/

func GetDB() *gorm.DB {
	return db.GetDB("")
}

// 找到直营终端或者二级代理终端
func FindEndpoints(agencyPid int, agencyId int) (endpoint []model.Endpoint, err error) {
	db := GetDB()
	if agencyId != 0 {
		if err := db.Table("endpoint").Where("top_agency = ?", agencyPid).
			Where("second_agency=?", agencyId).Find(&endpoint).Error; err != nil {
			return nil, err
		}
		return endpoint, nil
	} else if agencyId == 0 {
		if err := db.Table("endpoint").Where("top_agency = ?", agencyPid).
			Where("second_agency = ?", agencyId).Find(&endpoint).Error; err != nil {
			return nil, err
		}
	}
	return endpoint, nil
}

func SearchOverallEndpoints(db *gorm.DB, keyword string) ([]model.Endpoint, error) {
	var endpoints []model.Endpoint

	// 使用 LIKE 进行模糊查询：查询 `name` 或 `code` 中包含 `keyword`
	err := db.Where("name LIKE ? OR code LIKE ?", "%"+keyword+"%", "%"+keyword+"%").
		Find(&endpoints).Error
	if err != nil {
		return nil, err
	}
	return endpoints, nil
}

func (adminusersDao *EndpointAdminUsersDao) GetAllAgencyTrees() (TreeResponse []ResponseTree, err error) {
	var response []ResponseTree
	db := GetDB()

	// 执行 JOIN 查询，获取代理和终端的映射
	var result []struct {
		TopAgencyID      int
		TopAgencyName    string
		SecondAgencyID   int
		SecondAgencyName string
		EndpointName     string
	}

	if err := db.Raw(`
		SELECT 
			t1.id AS top_agency_id,
			t1.name AS top_agency_name,
			t2.id AS second_agency_id,
			t2.name AS second_agency_name,
			e1.name AS endpoint_name
		FROM endpoint e1
		LEFT JOIN agency t1 ON e1.top_agency = t1.id
		LEFT JOIN agency t2 ON e1.second_agency = t2.id AND t2.pid = t1.id
		WHERE e1.top_agency IS NOT NULL
		ORDER BY t1.id, t2.id
	`).Scan(&result).Error; err != nil {
		return nil, fmt.Errorf("failed to query agencies and endpoints: %v", err)
	}

	// 构建树状结构
	agencyMap := make(map[int]*ResponseTree)

	// 遍历查询结果，将数据填充到树状结构中
	for _, row := range result {
		// 获取一级代理，如果不存在则创建
		topAgency, exists := agencyMap[row.TopAgencyID]
		if !exists {
			topAgency = &ResponseTree{
				TopAgencyName: row.TopAgencyName,
				EndpointNames: []string{}, // 一级代理的终端列表
			}
			agencyMap[row.TopAgencyID] = topAgency
		}

		// 如果没有二级代理（即 second_agency_id 为 0），将终端加入一级代理
		if row.SecondAgencyID == 0 && row.EndpointName != "" {
			topAgency.EndpointNames = append(topAgency.EndpointNames, row.EndpointName)
		}

		// 如果有二级代理，则处理二级代理
		if row.SecondAgencyID != 0 {
			var secondAgencyResponse *SecondAgencyResponse
			// 查找当前一级代理下是否已存在该二级代理
			for i, secondAgency := range topAgency.SecondAgencies {
				if secondAgency.SecondAgencyName == row.SecondAgencyName {
					// 找到已有的二级代理，添加终端
					topAgency.SecondAgencies[i].EndpointNames = append(topAgency.SecondAgencies[i].EndpointNames, row.EndpointName)
					secondAgencyResponse = &topAgency.SecondAgencies[i]
					break
				}
			}

			if secondAgencyResponse == nil {
				// 如果没有找到二级代理，创建新的二级代理并添加终端
				secondAgencyResponse = &SecondAgencyResponse{
					SecondAgencyName: row.SecondAgencyName,
					EndpointNames:    []string{row.EndpointName},
				}
				topAgency.SecondAgencies = append(topAgency.SecondAgencies, *secondAgencyResponse)
			}
		}
	}

	// 将结果从 map 转换为切片
	for _, tree := range agencyMap {
		response = append(response, *tree)
	}

	// 如果没有数据，返回空响应
	if len(response) == 0 {
		return nil, fmt.Errorf("no agency data found")
	}

	return response, nil
}

type EndpointAdminUsersDao struct {
}

var EndpointAdminusersDao EndpointAdminUsersDao

//url: GET /yxcontact/department/list
//params:
//access_token: 必填，用户token
//agency_id: 选填，经销商id
//channel: 选填，经销渠道，默认只返回经销商的，要返回全部，channel填空。agency 线下经销商，e_commerce 电商，operator 运营商， special 特约
//tree: 选填，是否返回树型结构， 默认否-0，1-是

// 找到一级代理：pid为0的agency √               1agency       11111111111111111111111111
//
// 点击一级代理：显示所有二级代理和直营终端  endpoint的second_agency为0
// 且top_agency为**当前一级代理id**+找到agency中所有pid为**当前一级代理id**的代理 1agency/2endpoint 111111111111111111
//
// 点击二级：显示终端门店  2endpoint(里面一二级代理字段) 1111111111111111111111

// 输入名称或门店编号，找到该门店，并显示其对应的一二级门店 endpoint 1111111111111
