package endpoint

import (
	"github.com/gin-gonic/gin"
	"marketing/internal/handler"
	"marketing/internal/pkg/e"
	"marketing/internal/pkg/errors"
	"marketing/internal/service"
)

type AfterSalesEndpoint struct {
	svc service.AfterSalesEndpointSvc
}

func NewAfterSalesEndpoint(svc service.AfterSalesEndpointSvc) *AfterSalesEndpoint {
	return &AfterSalesEndpoint{
		svc: svc,
	}
}

func (endpoint *AfterSalesEndpoint) GetAfterSalesEndpointList(c *gin.Context) {
	name := e.ReqParamStr(c, "name")
	pageNum := e.ReqParamInt(c, "page_num")
	pageSize := e.ReqParamInt(c, "page_size")

	list, total := endpoint.svc.GetAfterSalesEndpointList(c, name, pageNum, pageSize)

	handler.Success(c, gin.H{
		"list":  list,
		"total": total,
	})
}

func (endpoint *AfterSalesEndpoint) GetAfterSalesEndpointByAgency(c *gin.Context) {
	list := endpoint.svc.GetAfterSalesEndpointByAgency(c, e.ReqParamInt(c, "agency_id"))
	handler.Success(c, gin.H{
		"list": list,
	})
}

func (endpoint *AfterSalesEndpoint) GetAfterSalesEndpoint(c *gin.Context) {
	id := e.ReqParamInt(c, "id")

	data := endpoint.svc.GetAfterSalesEndpointById(c, id)

	handler.Success(c, data)
}

func (endpoint *AfterSalesEndpoint) EditAfterSalesEndpoint(c *gin.Context) {
	id := e.ReqParamInt(c, "id")
	name := e.ReqParamStr(c, "name")
	phone := e.ReqParamStr(c, "phone")
	address := e.ReqParamStr(c, "address")
	topAgency := e.ReqParamInt(c, "top_agency_id")
	secondAgency := e.ReqParamInt(c, "second_agency_id")
	status := e.ReqParamInt(c, "status")

	err := endpoint.svc.EditAfterSalesEndpoint(c, id, name, phone, address, topAgency, secondAgency, status)
	if err != nil {
		handler.Error(c, errors.NewErr("编辑售后终端错误:"+err.Error()))
		return
	}

	handler.Success(c, gin.H{})
}

func (endpoint *AfterSalesEndpoint) DeleteAfterSalesEndpoint(c *gin.Context) {
	err := endpoint.svc.DeleteAfterSalesEndpoint(c, e.ReqParamInt(c, "id"))
	if err != nil {
		handler.Error(c, errors.NewErr(err.Error()))
		return
	}

	handler.Success(c, gin.H{})
}
