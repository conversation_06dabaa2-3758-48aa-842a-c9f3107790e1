// Package router 该包主要负责定义和管理与路由相关的逻辑，
// 本文件 auth.go 具体用于配置和注册营销中台各个端登录（auth）相关的路由组。
// 它通过路由构建器统一管理认证相关路由。
// 所有的系统都统一登录，根据系统类型颁发不同的token，token上面携带系统类型，中间件根据系统类型进行鉴权
package router

import (
	"marketing/internal/handler/auth"
	"marketing/internal/handler/base"

	"github.com/gin-gonic/gin"
)

// NewAuthRouterGroup 创建认证路由组
// 免登录校验，不需要鉴权
func NewAuthRouterGroup(authHandler *auth.Auth, baseHandler base.CaptchaHandler) IRouterGroup {
	// 创建认证路由对象
	authRouter := NewAuthRouter(authHandler, baseHandler)

	// 使用路由构建器
	builder := NewRouterBuilder("")
	builder.AddRouter(authRouter)

	// 构建并返回路由组
	return builder.Build()
}

// AuthRouter 认证路由子路由组实现
type AuthRouter struct {
	authHandler *auth.Auth
	baseHandler base.CaptchaHandler
}

func NewAuthRouter(authHandler *auth.Auth,
	baseHandler base.CaptchaHandler) *AuthRouter {
	return &AuthRouter{
		authHandler: authHandler,
		baseHandler: baseHandler,
	}
}

func (a *AuthRouter) Register(r *gin.RouterGroup) {
	//验证码发送
	r.POST("/send/captcha", a.baseHandler.SendCaptcha)
	// 读书郎教育科技正式服
	r.GET("WW_verify_F3Vp9P4fj0DSzrea.txt", func(c *gin.Context) {
		c.String(200, "F3Vp9P4fj0DSzrea")
	})
	// Amber 测试服
	r.GET("WW_verify_bnVp74uQDKC0WKu6.txt", func(c *gin.Context) {
		c.String(200, "bnVp74uQDKC0WKu6")
	})
	//读书郎正式服
	r.GET("WW_verify_reiSmSlcAdIomRKp.txt", func(c *gin.Context) {
		c.String(200, "reiSmSlcAdIomRKp")
	})

	// 统一登录入口根据系统类型颁发不同的token，token上面携带系统类型，中间件根据系统类型进行鉴权
	authRouterNew := r.Group("auth")
	{
		// 账号密码登录
		authRouterNew.POST("/login", a.authHandler.Login)
		// 企业微信免登录
		authRouterNew.POST("/wecom-login", a.authHandler.WeComLogin)
		// 手机号登录
		authRouterNew.POST("/phone-login", a.authHandler.PhoneLogin)
		//token 刷新
		authRouterNew.POST("refresh-token", a.authHandler.RefreshToken)
	}
}
