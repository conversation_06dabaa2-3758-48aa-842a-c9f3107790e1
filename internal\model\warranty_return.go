package model

import (
	"time"
)

type WarrantyReturn struct {
	ID         int       `gorm:"column:id;primaryKey;autoIncrement"`
	Barcode    string    `gorm:"column:barcode;size:32;not null" json:"barcode"`
	WarrantyID int       `gorm:"column:warranty_id;not null" json:"warranty_id"`
	UID        int       `gorm:"column:uid;not null" json:"uid"`
	Source     int       `gorm:"column:source;not null;default:0" json:"source"`
	Endpoint   int       `gorm:"column:endpoint;not null" json:"endpoint"`
	IsOverdue  bool      `gorm:"column:is_overdue;not null;default:0" json:"is_overdue"`
	ReturnAt   time.Time `gorm:"column:return_at" json:"return_at"`
	CreatedAt  time.Time `gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	Reason     string    `gorm:"column:reason;size:512;not null" json:"reason"`
}

// TableName sets the insert table name for this struct type
func (WarrantyReturn) TableName() string {
	return "warranty_return"
}
